# Simple Class API - Makefile
# Comandos úteis para desenvolvimento e deploy

.PHONY: help setup install test run docs clean deploy production docker

# Default target
help:
	@echo "Simple Class API - Comandos Disponíveis"
	@echo "======================================"
	@echo ""
	@echo "🔧 Setup e Instalação:"
	@echo "  make setup          - Configuração inicial completa"
	@echo "  make install        - Instalar dependências"
	@echo "  make install-dev    - Instalar dependências de desenvolvimento"
	@echo ""
	@echo "🧪 Testes:"
	@echo "  make test           - Executar todos os testes"
	@echo "  make test-auth      - Executar testes de autenticação"
	@echo "  make test-class     - Executar testes de classificação"
	@echo "  make test-coverage  - Executar testes com cobertura"
	@echo ""
	@echo "🚀 Execução:"
	@echo "  make run            - Iniciar API em modo desenvolvimento"
	@echo "  make run-prod       - Iniciar API em modo produção"
	@echo "  make docs           - Iniciar API com documentação"
	@echo ""
	@echo "📦 Deploy:"
	@echo "  make deploy-dev     - Deploy para desenvolvimento"
	@echo "  make deploy-prod    - Deploy para produção"
	@echo "  make production     - Configurar ambiente de produção"
	@echo ""
	@echo "🐳 Docker:"
	@echo "  make docker-build   - Construir imagem Docker"
	@echo "  make docker-run     - Executar com Docker"
	@echo "  make docker-stop    - Parar containers Docker"
	@echo ""
	@echo "🧹 Limpeza:"
	@echo "  make clean          - Limpar arquivos temporários"
	@echo "  make clean-cache    - Limpar cache do Poetry"
	@echo "  make clean-logs     - Limpar logs"

# Setup e Instalação
setup:
	@echo "🔧 Configurando Simple Class API..."
	python scripts/setup.py
	@echo "✅ Configuração concluída!"

install:
	@echo "📦 Instalando dependências..."
	poetry install --only=main

install-dev:
	@echo "📦 Instalando dependências de desenvolvimento..."
	poetry install

# Testes
test:
	@echo "🧪 Executando todos os testes..."
	poetry run pytest -v

test-auth:
	@echo "🧪 Executando testes de autenticação..."
	poetry run pytest tests/test_auth.py -v

test-class:
	@echo "🧪 Executando testes de classificação..."
	poetry run pytest tests/test_classification.py -v

test-coverage:
	@echo "🧪 Executando testes com cobertura..."
	poetry run pytest --cov=src --cov-report=html --cov-report=term

# Execução
run:
	@echo "🚀 Iniciando API em modo desenvolvimento..."
	poetry run python scripts/start_api_docs.py --reload

run-prod:
	@echo "🚀 Iniciando API em modo produção..."
	poetry run python scripts/start_api_docs.py --env production --workers 4

docs:
	@echo "📚 Iniciando API com documentação..."
	poetry run python scripts/start_api_docs.py --reload
	@echo "📖 Documentação disponível em:"
	@echo "   • Swagger UI: http://localhost:8000/docs"
	@echo "   • ReDoc: http://localhost:8000/redoc"

# Deploy
deploy-dev:
	@echo "🚀 Deploy para desenvolvimento..."
	python scripts/deploy.py --environment development

deploy-prod:
	@echo "🚀 Deploy para produção..."
	python scripts/deploy.py --environment production

production:
	@echo "🏭 Configurando ambiente de produção..."
	python scripts/production_setup.py

# Docker
docker-build:
	@echo "🐳 Construindo imagem Docker..."
	docker build -t simple-class-api .

docker-run:
	@echo "🐳 Executando com Docker..."
	docker-compose -f docker-compose.prod.yml up -d

docker-stop:
	@echo "🐳 Parando containers Docker..."
	docker-compose -f docker-compose.prod.yml down

docker-logs:
	@echo "🐳 Visualizando logs do Docker..."
	docker-compose -f docker-compose.prod.yml logs -f

# Limpeza
clean:
	@echo "🧹 Limpando arquivos temporários..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf temp/*
	@echo "✅ Limpeza concluída!"

clean-cache:
	@echo "🧹 Limpando cache do Poetry..."
	poetry cache clear --all pypi

clean-logs:
	@echo "🧹 Limpando logs..."
	rm -rf logs/*
	@echo "✅ Logs limpos!"

# Desenvolvimento
dev-setup: setup install-dev
	@echo "🔧 Ambiente de desenvolvimento configurado!"

# Verificações
check:
	@echo "🔍 Verificando código..."
	poetry run python -m py_compile src/api/main.py
	@echo "✅ Código válido!"

health:
	@echo "🏥 Verificando saúde da API..."
	curl -f http://localhost:8000/health || echo "❌ API não está respondendo"

# Informações
info:
	@echo "ℹ️  Informações do projeto:"
	@echo "  • Python: $(shell python --version)"
	@echo "  • Poetry: $(shell poetry --version)"
	@echo "  • Diretório: $(shell pwd)"
	@echo "  • Branch: $(shell git branch --show-current 2>/dev/null || echo 'N/A')"

# Comandos de conveniência
start: run
stop:
	@echo "🛑 Parando processos da API..."
	pkill -f "uvicorn src.api.main:app" || echo "Nenhum processo encontrado"

restart: stop start

# Comandos de manutenção
update:
	@echo "🔄 Atualizando dependências..."
	poetry update

lint:
	@echo "🔍 Verificando estilo do código..."
	poetry run flake8 src/ || echo "⚠️  Flake8 não instalado"

format:
	@echo "🎨 Formatando código..."
	poetry run black src/ || echo "⚠️  Black não instalado"

# Backup
backup:
	@echo "💾 Criando backup..."
	tar -czf backup-$(shell date +%Y%m%d-%H%M%S).tar.gz \
		--exclude='.git' \
		--exclude='__pycache__' \
		--exclude='.pytest_cache' \
		--exclude='htmlcov' \
		--exclude='temp' \
		--exclude='logs' \
		.
	@echo "✅ Backup criado!"

# Comandos compostos
full-setup: clean setup install-dev test
	@echo "🎉 Setup completo finalizado!"

quick-test: test-auth test-class
	@echo "🎉 Testes rápidos concluídos!"

deploy-check: test check
	@echo "✅ Verificações de deploy concluídas!"
