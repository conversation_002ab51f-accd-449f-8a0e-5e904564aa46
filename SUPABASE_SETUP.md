# 🚀 Supabase Setup Guide

Este guia te ajudará a configurar o Supabase para a API Simple Class.

## ✅ Status Atual

- **Conexão**: ✅ Funcionando
- **Credenciais**: ✅ Configuradas
- **Tabelas**: ❌ Precisam ser criadas
- **Storage**: ❌ Precisa ser configurado

## 📋 Passos para Setup Completo

### 1. 🗄️ Criar Tabelas no Banco de Dados

1. **Acesse o Dashboard do Supabase**
   - Vá para: https://supabase.com/dashboard
   - Faça login na sua conta
   - Selecione o projeto: `yrauzdwlpnfinerqqech`

2. **Abra o SQL Editor**
   - No menu lateral, clique em "SQL Editor"
   - Clique em "New query"

3. **Execute o Script SQL**
   - Copie todo o conteúdo do arquivo `database/supabase_setup.sql`
   - Cole no editor SQL
   - Clique em "Run" para executar

4. **Verifique as Tabelas**
   - Vá para "Table Editor" no menu lateral
   - Você deve ver as tabelas: `files`, `processing_jobs`, `reports`

### 2. 📦 Configurar Storage

1. **Acesse Storage**
   - No menu lateral, clique em "Storage"

2. **Criar Bucket**
   - Clique em "New bucket"
   - Nome: `simple-class-files`
   - Público: **NÃO** (deixe privado)
   - Clique em "Create bucket"

3. **Configurar Políticas de Acesso**
   - Clique no bucket criado
   - Vá para "Policies"
   - As políticas já estão definidas no script SQL

### 3. 🔐 Configurar Autenticação (Opcional)

1. **Habilitar Providers**
   - Vá para "Authentication" > "Providers"
   - Habilite "Email" se ainda não estiver

2. **Configurar Políticas**
   - As políticas RLS já estão configuradas no script SQL
   - Elas garantem que usuários só acessem seus próprios dados

## 🧪 Testar a Configuração

Após completar os passos acima, execute:

```bash
poetry run python scripts/test_supabase.py
```

Você deve ver:
- ✅ Connection: OK
- ✅ Storage: OK

## 🚀 Executar a API

Com tudo configurado, execute:

```bash
poetry run python scripts/start_api.py
```

A API estará disponível em: http://localhost:8000

## 📊 Endpoints Disponíveis

### Anonimização
- `POST /api/v1/anonymization/text` - Anonimizar texto
- `POST /api/v1/anonymization/pdf` - Anonimizar PDF
- `GET /api/v1/anonymization/entities` - Entidades suportadas

### Classificação
- `POST /api/v1/classification/multilabel` - Classificação multilabel
- `POST /api/v1/classification/unilabel` - Classificação unilabel
- `POST /api/v1/classification/batch` - Processamento em lote
- `GET /api/v1/classification/subtemas/{area}` - Subtemas por área

### Relatórios
- `POST /api/v1/reports/individual` - Relatório individual
- `POST /api/v1/reports/overview` - Relatório de visão geral
- `GET /api/v1/reports/templates` - Templates disponíveis
- `GET /api/v1/reports/` - Listar relatórios
- `GET /api/v1/reports/{id}` - Download de relatório

### Arquivos
- `POST /api/v1/files/upload` - Upload de arquivo
- `GET /api/v1/files/` - Listar arquivos
- `GET /api/v1/files/{id}` - Detalhes do arquivo
- `GET /api/v1/files/{id}/download` - Download
- `DELETE /api/v1/files/{id}` - Excluir arquivo
- `GET /api/v1/files/stats/summary` - Estatísticas

## 🔧 Troubleshooting

### Problema: "relation does not exist"
- **Solução**: Execute o script SQL no dashboard

### Problema: "bucket not found"
- **Solução**: Crie o bucket manualmente no dashboard

### Problema: "authentication required"
- **Solução**: Por enquanto, a API usa dados mock para desenvolvimento

## 📝 Próximos Passos

1. ✅ Configurar Supabase (este guia)
2. 🔐 Implementar autenticação real
3. 🧪 Adicionar testes automatizados
4. 🚀 Deploy em produção

## 💡 Dicas

- **Desenvolvimento**: A API funciona com dados mock mesmo sem Supabase
- **Produção**: Supabase é necessário para persistência real
- **Logs**: Verifique os logs da API para debug
- **Documentação**: Acesse `/docs` para ver a documentação interativa
