"""
LangGraph-compatible PDF anonymization tool.

This tool extracts clean text from PDFs (removing headers/footers) and applies
Brazilian-specific anonymization using the existing Presidio framework.
"""

import fitz  # PyMuPDF
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from collections import Counter
import logging

from .anonymization_tool import AnonymizationTool


class PDFAnonymizationTool:
    """
    LangGraph-compatible tool for anonymizing PDF documents.
    
    Features:
    - Clean text extraction using PyMuPDF
    - Automatic header/footer removal
    - Brazilian-specific anonymization using Presidio
    - LLM-ready text output
    """
    
    def __init__(self, 
                 header_footer_threshold: float = 0.3,
                 min_text_length: int = 10,
                 preserve_structure: bool = True):
        """
        Initialize PDF anonymization tool.
        
        Args:
            header_footer_threshold: Minimum frequency (0-1) for text to be considered header/footer
            min_text_length: Minimum length for text blocks to be considered
            preserve_structure: Whether to preserve paragraph structure in output
        """
        self.anonymizer = AnonymizationTool()
        self.header_footer_threshold = header_footer_threshold
        self.min_text_length = min_text_length
        self.preserve_structure = preserve_structure
        self.logger = logging.getLogger(__name__)
    
    def extract_clean_text(self, pdf_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Extract clean text from PDF, removing headers and footers.
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            Dictionary with extracted text and metadata
        """
        pdf_path = Path(pdf_path)
        
        if not pdf_path.exists():
            return {
                "success": False,
                "error": f"PDF file not found: {pdf_path}",
                "text": "",
                "pages": 0,
                "headers_removed": [],
                "footers_removed": []
            }
        
        try:
            doc = fitz.open(str(pdf_path))
            
            if len(doc) == 0:
                return {
                    "success": False,
                    "error": "PDF contains no pages",
                    "text": "",
                    "pages": 0,
                    "headers_removed": [],
                    "footers_removed": []
                }
            
            # Collect potential headers and footers
            headers_candidates = []
            footers_candidates = []
            page_texts = []
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Get text blocks with position information
                blocks = page.get_text("blocks")
                if not blocks:
                    continue
                
                # Sort blocks by vertical position (y-coordinate)
                blocks = sorted(blocks, key=lambda b: b[1])
                
                # Filter out very short text blocks
                text_blocks = [
                    block for block in blocks 
                    if len(block[4].strip()) >= self.min_text_length
                ]
                
                if text_blocks:
                    # First block might be header
                    headers_candidates.append(text_blocks[0][4].strip())
                    
                    # Last block might be footer
                    if len(text_blocks) > 1:
                        footers_candidates.append(text_blocks[-1][4].strip())
                    
                    # Store all text for this page
                    page_text = page.get_text()
                    page_texts.append(page_text)
            
            doc.close()
            
            # Identify common headers and footers
            headers_removed = self._identify_common_elements(headers_candidates)
            footers_removed = self._identify_common_elements(footers_candidates)
            
            # Clean text by removing identified headers/footers
            clean_text = self._clean_text(page_texts, headers_removed, footers_removed)
            
            return {
                "success": True,
                "text": clean_text,
                "pages": len(page_texts),
                "headers_removed": headers_removed,
                "footers_removed": footers_removed,
                "error": None
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting text from PDF {pdf_path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "text": "",
                "pages": 0,
                "headers_removed": [],
                "footers_removed": []
            }
    
    def _identify_common_elements(self, candidates: List[str]) -> List[str]:
        """
        Identify common headers/footers based on frequency.
        
        Args:
            candidates: List of potential header/footer texts
            
        Returns:
            List of texts that appear frequently enough to be considered headers/footers
        """
        if not candidates:
            return []
        
        # Count frequency of each candidate
        counter = Counter(candidates)
        total_pages = len(candidates)
        
        # Identify elements that appear in at least threshold% of pages
        common_elements = []
        for text, count in counter.items():
            frequency = count / total_pages
            if frequency >= self.header_footer_threshold:
                common_elements.append(text)
        
        return common_elements
    
    def _clean_text(self, page_texts: List[str], headers: List[str], footers: List[str]) -> str:
        """
        Clean text by removing identified headers and footers.
        
        Args:
            page_texts: List of text from each page
            headers: List of header texts to remove
            footers: List of footer texts to remove
            
        Returns:
            Cleaned text ready for LLM processing
        """
        cleaned_pages = []
        
        for page_text in page_texts:
            lines = page_text.split('\n')
            cleaned_lines = []
            
            for line in lines:
                line_stripped = line.strip()
                
                # Skip empty lines
                if not line_stripped:
                    continue
                
                # Skip if line matches any header or footer
                if any(header in line_stripped for header in headers):
                    continue
                if any(footer in line_stripped for footer in footers):
                    continue
                
                cleaned_lines.append(line_stripped)
            
            if cleaned_lines:
                if self.preserve_structure:
                    # Join with single newlines to preserve paragraph structure
                    cleaned_pages.append('\n'.join(cleaned_lines))
                else:
                    # Join with spaces for continuous text
                    cleaned_pages.append(' '.join(cleaned_lines))
        
        # Join pages with double newlines
        return '\n\n'.join(cleaned_pages)
    
    def anonymize_pdf(self, pdf_path: Union[str, Path], language: str = "pt") -> Dict[str, Any]:
        """
        Complete PDF anonymization workflow: extract clean text and anonymize.
        
        Args:
            pdf_path: Path to PDF file
            language: Language code for anonymization
            
        Returns:
            Dictionary with anonymized text and processing metadata
        """
        # Step 1: Extract clean text
        extraction_result = self.extract_clean_text(pdf_path)
        
        if not extraction_result["success"]:
            return {
                "success": False,
                "error": extraction_result["error"],
                "original_text": "",
                "anonymized_text": "",
                "extraction_metadata": extraction_result,
                "anonymization_metadata": {}
            }
        
        # Step 2: Anonymize the clean text
        anonymization_result = self.anonymizer.anonymize_text(
            extraction_result["text"], 
            language=language
        )
        
        return {
            "success": anonymization_result["success"],
            "error": anonymization_result.get("error"),
            "original_text": extraction_result["text"],
            "anonymized_text": anonymization_result["anonymized_text"],
            "extraction_metadata": {
                "pages": extraction_result["pages"],
                "headers_removed": extraction_result["headers_removed"],
                "footers_removed": extraction_result["footers_removed"]
            },
            "anonymization_metadata": {
                "entities_found": anonymization_result["entities_found"],
                "entities_count": anonymization_result["entities_count"]
            }
        }


def create_pdf_anonymization_tool() -> callable:
    """
    Create a LangGraph-compatible PDF anonymization tool function.
    
    Returns:
        Callable tool function for use in LangGraph workflows
    """
    tool = PDFAnonymizationTool()
    
    def pdf_anonymization_tool(pdf_path: str, language: str = "pt") -> Dict[str, Any]:
        """
        LangGraph tool function for PDF anonymization.
        
        Args:
            pdf_path: Path to PDF file to anonymize
            language: Language code for anonymization (default: "pt")
            
        Returns:
            Dictionary with anonymization results
        """
        return tool.anonymize_pdf(pdf_path, language)
    
    return pdf_anonymization_tool
