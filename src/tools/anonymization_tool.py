"""
LangGraph-compatible anonymization tool based on official Inova-MPRJ/Anonymizer solution.

This tool provides Brazilian-specific data anonymization capabilities within the LangGraph framework,
incorporating the official MPRJ recognizers for CPF, schools, addresses, and other PII entities.
"""

from typing import Dict, Any, List, Optional
from presidio_analyzer import <PERSON><PERSON>zer<PERSON>ng<PERSON>, RecognizerRegistry
from presidio_anonymizer import AnonymizerEngine
from presidio_analyzer.predefined_recognizers import <PERSON><PERSON>Recog<PERSON><PERSON>, EmailRecog<PERSON>zer, PhoneRecognizer
from presidio_analyzer.nlp_engine import NlpEngineProvider
from presidio_analyzer.recognizer_result import RecognizerResult

from .recognizers.cpf_recognizer import CP<PERSON><PERSON><PERSON>ognizer
from .recognizers.escola_recognizer import EscolaRecognizer
from .recognizers.endereco_recognizer import EnderecoRecognizer


class AnonymizationTool:
    """
    LangGraph-compatible tool for anonymizing Brazilian text data.
    
    Based on the official Inova-MPRJ/Anonymizer solution with custom recognizers
    for CPF, educational institutions, addresses, and other Brazilian-specific PII.
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize the anonymization tool.

        Args:
            config_file: Optional path to language configuration file
        """
        self.name = "anonymization_tool"
        self.description = "Anonymizes Brazilian text data using official MPRJ recognizers"

        # Set default config file if not provided
        if config_file is None:
            config_file = "config/languages-config.yml"

        # Initialize NLP engine with Portuguese support
        self._setup_nlp_engine(config_file)

        # Setup recognizers registry
        self._setup_recognizers()

        # Initialize analyzer and anonymizer engines
        self._setup_engines()
    
    def _setup_nlp_engine(self, config_file: Optional[str] = None):
        """Setup NLP engine with Portuguese language support."""
        if config_file:
            provider = NlpEngineProvider(conf_file=config_file)
        else:
            # Default configuration for Portuguese
            configuration = {
                "nlp_engine_name": "spacy",
                "models": [{"lang_code": "pt", "model_name": "pt_core_news_lg"}],
            }
            provider = NlpEngineProvider(nlp_configuration=configuration)
        
        self.nlp_engine = provider.create_engine()
    
    def _setup_recognizers(self):
        """Setup custom recognizers registry with Brazilian-specific recognizers."""
        self.registry = RecognizerRegistry()
        self.registry.supported_languages = ["en", "pt"]
        
        # Portuguese-specific recognizers with context
        email_recognizer_pt = EmailRecognizer(
            supported_language="pt",
            context=["email", "correio eletrônico", "endereço de email"]
        )
        
        phone_recognizer_pt = PhoneRecognizer(
            supported_language="pt",
            context=["telefone", "celular", "número de telefone"]
        )
        
        spacy_recognizer_pt = SpacyRecognizer(
            supported_language='pt',
            supported_entities=["PERSON"],
            context=['nome', 'pessoa', 'chamado', 'filho', 'neto']
        )
        
        # Brazilian-specific custom recognizers
        cpf_recognizer = CPFRecognizer()
        escola_recognizer = EscolaRecognizer()
        endereco_recognizer = EnderecoRecognizer()
        
        # Add all recognizers to registry
        self.registry.add_recognizer(email_recognizer_pt)
        self.registry.add_recognizer(phone_recognizer_pt)
        self.registry.add_recognizer(spacy_recognizer_pt)
        self.registry.add_recognizer(cpf_recognizer)
        self.registry.add_recognizer(escola_recognizer)
        self.registry.add_recognizer(endereco_recognizer)
    
    def _setup_engines(self):
        """Setup analyzer and anonymizer engines."""
        self.analyzer = AnalyzerEngine(
            registry=self.registry,
            supported_languages=["en", "pt"],
            nlp_engine=self.nlp_engine
        )
        
        self.anonymizer = AnonymizerEngine()
    
    def anonymize_text(self, text: str, language: str = "pt") -> Dict[str, Any]:
        """
        Anonymize text using Brazilian-specific recognizers.
        
        Args:
            text: Text to anonymize
            language: Language code (default: "pt")
            
        Returns:
            Dictionary containing anonymized text and analysis results
        """
        if not text or not text.strip():
            return {
                "anonymized_text": text,
                "entities_found": [],
                "entities_count": 0,
                "success": True
            }
        
        try:
            # Analyze text for PII entities
            results = self.analyzer.analyze(
                text=text, 
                language=language, 
                return_decision_process=True
            )
            
            # Anonymize the text
            anonymized_result = self.anonymizer.anonymize(
                text=text,
                analyzer_results=results
            )
            
            # Prepare entity information
            entities_found = []
            for result in results:
                entities_found.append({
                    "entity_type": result.entity_type,
                    "start": result.start,
                    "end": result.end,
                    "score": result.score,
                    "text": text[result.start:result.end]
                })
            
            return {
                "anonymized_text": anonymized_result.text,
                "entities_found": entities_found,
                "entities_count": len(entities_found),
                "success": True
            }
            
        except Exception as e:
            return {
                "anonymized_text": text,
                "entities_found": [],
                "entities_count": 0,
                "success": False,
                "error": str(e)
            }
    
    def annotate_entities(self, text: str, analyze_results: List[RecognizerResult]) -> List:
        """
        Annotate text with identified PII entities.
        
        Args:
            text: Original text
            analyze_results: Results from analyzer engine
            
        Returns:
            List of tokens with entity annotations
        """
        tokens = []
        
        if not analyze_results:
            return [text]

        # Sort by start index
        results = sorted(analyze_results, key=lambda x: x.start)
        
        last_end = 0
        for res in results:
            # Add text before the entity
            if res.start > last_end:
                tokens.append(text[last_end:res.start])
            
            # Add the entity as a tuple (text, entity_type)
            tokens.append((text[res.start:res.end], res.entity_type))
            
            last_end = res.end
        
        # Add remaining text after the last entity
        if last_end < len(text):
            tokens.append(text[last_end:])
        
        return tokens
    
    def get_supported_entities(self) -> List[str]:
        """
        Get list of supported entity types.
        
        Returns:
            List of supported entity types
        """
        return [
            "PERSON",           # Person names (spaCy)
            "EMAIL_ADDRESS",    # Email addresses
            "PHONE_NUMBER",     # Phone numbers
            "CPF",              # Brazilian CPF
            "ESCOLA",           # Educational institutions
            "ENDEREÇO"          # Addresses
        ]
    
    def __call__(self, text: str, **kwargs) -> Dict[str, Any]:
        """
        Make the tool callable for LangGraph compatibility.
        
        Args:
            text: Text to anonymize
            **kwargs: Additional arguments
            
        Returns:
            Anonymization results
        """
        return self.anonymize_text(text, **kwargs)


# LangGraph tool factory function
def create_anonymization_tool(config_file: Optional[str] = None) -> AnonymizationTool:
    """
    Factory function to create an anonymization tool instance.
    
    Args:
        config_file: Optional path to language configuration file
        
    Returns:
        Configured AnonymizationTool instance
    """
    return AnonymizationTool(config_file=config_file)
