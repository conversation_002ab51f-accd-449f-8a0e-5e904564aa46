"""
Educational institution recognizer for Brazilian schools and universities.

Based on the official Inova-MPRJ/Anonymizer solution.
"""

from presidio_analyzer import PatternRecognizer, Pattern


class EscolaRecognizer(PatternRecognizer):
    """
    Custom recognizer for Brazilian educational institutions.
    
    Detects various types of educational institutions:
    - Municipal, state, and federal schools
    - Colleges and universities
    - Educational centers
    - Institutes and faculties
    """
    
    def __init__(self):
        # Patterns for detecting school names
        escola_patterns = [
            # Municipal, state, federal schools
            Pattern(
                name="Escola Municipal/Estadual/Federal",
                regex=r"\b(?:Escola\s+(?:Municipal|Estadual|Federal|Particular)\s+[A-Z][a-z\s]+)\b",
                score=0.9
            ),
            # Colleges
            Pattern(
                name="Colégio",
                regex=r"\bColégio\s+[A-Z][a-z\s]+\b",
                score=0.9
            ),
            # Educational centers
            Pattern(
                name="Centro Educacional",
                regex=r"\bCentro\s+Educacional\s+[A-Z][a-z\s]+\b",
                score=0.9
            ),
            # Institutes
            Pattern(
                name="Instituto",
                regex=r"\bInstituto\s+[A-Z][a-z\s]+\b",
                score=0.85
            ),
            # Universities
            Pattern(
                name="Universidade",
                regex=r"\bUniversidade\s+[A-Z][a-z\s]+\b",
                score=0.9
            ),
            # Faculties
            Pattern(
                name="Faculdade",
                regex=r"\bFaculdade\s+[A-Z][a-z\s]+\b",
                score=0.9
            ),
            # São Paulo specific abbreviations (EMEF, EMEI, CEI)
            Pattern(
                name="Escolas SP Abrev",
                regex=r"\b(?:EMEF|EMEI|CEI|CIEJA)\s+[A-Z][a-z\s]+\b",
                score=0.95
            ),
            # General abbreviations (E.M., E.E.)
            Pattern(
                name="Escolas Abrev Gerais",
                regex=r"\b(?:E\.M\.|E\.E\.|E\.F\.)\s+[A-Z][a-z\s]+\b",
                score=0.9
            ),
            # University campuses
            Pattern(
                name="Campus",
                regex=r"\bCampus\s+[A-Z][a-z\s]+\b",
                score=0.85
            ),
            # Rio de Janeiro specific patterns
            Pattern(
                name="EDI",
                regex=r"\bEDI\s+[A-Z][a-z\s]+\b",
                score=0.95
            ),
            # CIEP (Rio de Janeiro)
            Pattern(
                name="CIEP",
                regex=r"\bCIEP\s+[A-Z][a-z\s]+\b",
                score=0.95
            ),
            # FAETEC
            Pattern(
                name="FAETEC",
                regex=r"\b(?:FAETEC|Escola\s+FAETEC)\s+[A-Z][a-z\s]*\b",
                score=0.95
            ),
            # SEEDUC
            Pattern(
                name="SEEDUC",
                regex=r"\bSEEDUC\b",
                score=0.9
            ),
            # CRE (Coordenadoria Regional de Educação)
            Pattern(
                name="CRE",
                regex=r"\b\d+ª\s*CRE\b",
                score=0.9
            )
        ]
        
        super().__init__(
            supported_entity="ESCOLA",
            patterns=escola_patterns,
            supported_language="pt",
            context=[
                "escola", "colégio", "universidade", "faculdade", "instituto",
                "centro educacional", "campus", "educação", "ensino",
                "estudante", "aluno", "professor", "matrícula",
                "municipal", "estadual", "federal", "particular",
                "educacional", "pedagógico", "edi", "ciep", "faetec",
                "seeduc", "cre", "coordenadoria"
            ]
        )
