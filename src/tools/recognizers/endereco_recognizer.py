"""
Address recognizer for Brazilian addresses and locations.

Based on the official Inova-MPRJ/Anonymizer solution.
"""

from presidio_analyzer import PatternRecognizer, Pattern


class EnderecoRecognizer(PatternRecognizer):
    """
    Custom recognizer for Brazilian addresses and locations.
    
    Detects various types of addresses:
    - Complete addresses with street, number, neighborhood
    - Brazilian postal codes (CEP)
    - Rural addresses and farms
    - Condominiums and buildings
    - Highways and roads
    """
    
    def __init__(self):
        # Patterns for detecting Brazilian addresses
        endereco_patterns = [
            # Complete addresses: Rua das Flores, 123
            Pattern(
                name="Endereço Completo",
                regex=r"\b(?:rua|avenida|av|alameda|travessa|praça|largo|estrada|rodovia)\s+[a-záàáâãéêíóôõúç\s]+,?\s*\d+",
                score=0.9
            ),
            # Brazilian postal code (CEP)
            Pattern(
                name="CEP",
                regex=r"\b\d{5}-?\d{3}\b",
                score=0.95
            ),
            # Addresses with neighborhood and city
            Pattern(
                name="Endereço com Bairro",
                regex=r"\b(?:rua|avenida|av|alameda|travessa|praça|largo|estrada)\s+[a-záàáâãéêíóôõúç\s]+,?\s*\d+[a-záàáâãéêíóôõúç\s,]*(?:bairro|neighborhood)\s+[a-záàáâãéêíóôõúç\s]+",
                score=0.85
            ),
            # Apartment/suite numbers
            Pattern(
                name="Apartamento",
                regex=r"\b(?:apartamento|apto|ap|sala|sl)\s*\d+",
                score=0.8
            ),
            # Rural addresses
            Pattern(
                name="Endereço Rural",
                regex=r"\b(?:fazenda|sítio|chácara|granja)\s+[a-záàáâãéêíóôõúç\s]+",
                score=0.85
            ),
            # Condominiums
            Pattern(
                name="Condomínio",
                regex=r"\b(?:condomínio|residencial)\s+[a-záàáâãéêíóôõúç\s]+",
                score=0.85
            ),
            # Highways and roads
            Pattern(
                name="Rodovia",
                regex=r"\b(?:br|rj|sp|mg|rs|pr|sc|go|df|ba|pe|ce|pb|rn|al|se|pi|ma|to|ac|ro|rr|ap|am|pa|mt|ms)-?\d+",
                score=0.9
            ),
            # Km markers
            Pattern(
                name="Quilometragem",
                regex=r"\bkm\s*\d+",
                score=0.8
            ),
            # Case insensitive street patterns
            Pattern(
                name="Logradouro Case Insensitive",
                regex=r"(?i)\b(?:rua|avenida|av|alameda|travessa|praça|largo|estrada|rodovia)\s+[a-záàáâãéêíóôõúç\s]+,?\s*\d*",
                score=0.85
            ),
            # Specific Rio de Janeiro patterns
            Pattern(
                name="Endereço RJ",
                regex=r"(?i)\b(?:situada na|localizada na|localizada em|situada em)\s+[a-záàáâãéêíóôõúç\s,]+",
                score=0.8
            ),
            # Building/block patterns
            Pattern(
                name="Bloco/Quadra",
                regex=r"\b(?:bloco|quadra|qd|bl)\s*[a-z0-9]+",
                score=0.8
            ),
            # Lot patterns
            Pattern(
                name="Lote",
                regex=r"\blote\s*\d+",
                score=0.8
            )
        ]
        
        super().__init__(
            supported_entity="ENDEREÇO",
            patterns=endereco_patterns,
            supported_language="pt",
            context=[
                "endereço", "localizado", "situada", "rua", "avenida", "bairro",
                "cidade", "cep", "número", "apartamento", "sala", "bloco",
                "condomínio", "residencial", "fazenda", "sítio", "rodovia",
                "estrada", "praça", "largo", "alameda", "travessa", "km",
                "quilômetro", "lote", "quadra", "localizada", "situada"
            ]
        )
