"""
CPF (Cadastro de Pessoa Física) recognizer for Brazilian documents.

Based on the official Inova-MPRJ/Anonymizer solution.
"""

from presidio_analyzer import <PERSON>ternRecognizer, Pattern


class CPFRecognizer(PatternRecognizer):
    """
    Custom recognizer for Brazilian CPF (Cadastro de Pessoa Física).
    
    Detects CPF numbers in various formats:
    - With formatting: 123.456.789-00
    - Without formatting: 12345678900
    """
    
    def __init__(self):
        # CPF pattern - accepts formats with or without punctuation
        cpf_pattern = Pattern(
            name="CPF Pattern",
            regex=r"\b\d{3}\.?\d{3}\.?\d{3}-?\d{2}\b",
            score=0.85
        )
        
        super().__init__(
            supported_entity="CPF",
            patterns=[cpf_pattern],
            supported_language="pt",
            context=["cpf", "cadastro", "pessoa física", "documento"]
        )
