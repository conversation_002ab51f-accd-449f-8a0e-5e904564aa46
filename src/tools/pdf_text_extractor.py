"""
PDF Text Extraction with Intelligent Chunking.

This module provides intelligent text extraction and chunking for PDF files
in batch processing. It preserves paragraph boundaries and maintains page 
mapping for traceability without anonymization.
"""

import fitz  # PyMuPDF
import re
from typing import Dict, Any, List, Optional, Union, Tuple
from pathlib import Path
from dataclasses import dataclass
import logging
import math
from collections import Counter

from ..api.models.batch_processing import TextChunk


class PDFTextExtractor:
    """
    PDF text extractor with intelligent chunking capabilities.
    
    Features:
    - Clean text extraction using PyMuPDF
    - Automatic header/footer removal
    - Intelligent text chunking preserving paragraph boundaries
    - Page mapping for chunk traceability
    - Token-aware splitting with configurable overlap
    - Semantic context preservation at chunk boundaries
    """
    
    def __init__(self, 
                 chunk_size: int = 1000,
                 overlap: int = 200,
                 header_footer_threshold: float = 0.3,
                 min_text_length: int = 10,
                 preserve_structure: bool = True):
        """
        Initialize PDF text extractor with chunking parameters.
        
        Args:
            chunk_size: Target size for text chunks in tokens
            overlap: Number of tokens to overlap between chunks
            header_footer_threshold: Minimum frequency for header/footer detection
            min_text_length: Minimum length for text blocks
            preserve_structure: Whether to preserve paragraph structure
        """
        self.chunk_size = chunk_size
        self.overlap = overlap
        self.header_footer_threshold = header_footer_threshold
        self.min_text_length = min_text_length
        self.preserve_structure = preserve_structure
        self.logger = logging.getLogger(__name__)
        
        # Validate parameters
        if chunk_size < 100:
            raise ValueError("chunk_size must be at least 100 tokens")
        if overlap >= chunk_size:
            raise ValueError("overlap must be less than chunk_size")
        if overlap < 0:
            raise ValueError("overlap cannot be negative")
    
    def extract_and_chunk(self, pdf_path: Union[str, Path]) -> List[TextChunk]:
        """
        Extract text from PDF and split into intelligent chunks.
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            List of TextChunk objects with page mapping and metadata
        """
        pdf_path = Path(pdf_path)
        
        if not self.validate_pdf(str(pdf_path)):
            self.logger.error(f"PDF validation failed for: {pdf_path}")
            return []
        
        try:
            # Extract clean text with page information
            extraction_result = self._extract_text_with_pages(pdf_path)
            
            if not extraction_result["success"]:
                self.logger.error(f"Text extraction failed: {extraction_result['error']}")
                return []
            
            self.logger.debug(f"Extracted text length: {len(extraction_result['text'])}")
            self.logger.debug(f"Extracted text preview: {extraction_result['text'][:200]}...")
            
            # Create chunks from the extracted text
            chunks = self._create_chunks(
                extraction_result["text"],
                extraction_result["page_mapping"],
                str(pdf_path)
            )
            
            self.logger.info(f"Created {len(chunks)} chunks from {pdf_path}")
            return chunks
            
        except Exception as e:
            self.logger.error(f"Error extracting and chunking PDF {pdf_path}: {e}")
            return []
    
    def validate_pdf(self, pdf_path: str) -> bool:
        """
        Validate that PDF can be processed.
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            True if PDF is valid and processable, False otherwise
        """
        try:
            pdf_path = Path(pdf_path)
            
            # Check if file exists
            if not pdf_path.exists():
                return False
            
            # Check file extension
            if pdf_path.suffix.lower() != '.pdf':
                return False
            
            # Try to open with PyMuPDF
            doc = fitz.open(str(pdf_path))
            
            # Check if document has pages
            if len(doc) == 0:
                doc.close()
                return False
            
            # Try to extract text from first page
            first_page = doc[0]
            test_text = first_page.get_text()
            doc.close()
            
            # Check if we can extract meaningful text
            if len(test_text.strip()) < self.min_text_length:
                return False
            
            return True
            
        except Exception as e:
            self.logger.warning(f"PDF validation failed for {pdf_path}: {e}")
            return False
    
    def _extract_text_with_pages(self, pdf_path: Path) -> Dict[str, Any]:
        """
        Extract text from PDF with detailed page mapping.
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            Dictionary with extracted text and page mapping information
        """
        try:
            doc = fitz.open(str(pdf_path))
            
            # Collect text and page information
            page_texts = []
            page_mapping = {}  # char_position -> page_number
            current_char_pos = 0
            
            # Collect potential headers and footers for cleaning
            headers_candidates = []
            footers_candidates = []
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Get text blocks with position information for header/footer detection
                blocks = page.get_text("blocks")
                if blocks:
                    # Sort blocks by vertical position
                    blocks = sorted(blocks, key=lambda b: b[1])
                    
                    # Filter out very short text blocks
                    text_blocks = [
                        block for block in blocks 
                        if len(block[4].strip()) >= self.min_text_length
                    ]
                    
                    if text_blocks:
                        # First block might be header
                        headers_candidates.append(text_blocks[0][4].strip())
                        
                        # Last block might be footer
                        if len(text_blocks) > 1:
                            footers_candidates.append(text_blocks[-1][4].strip())
                
                # Get full page text
                page_text = page.get_text()
                
                # Map character positions to page numbers
                page_start_char = current_char_pos
                page_end_char = current_char_pos + len(page_text)
                
                for char_pos in range(page_start_char, page_end_char):
                    page_mapping[char_pos] = page_num + 1  # 1-indexed pages
                
                page_texts.append(page_text)
                current_char_pos = page_end_char + 2  # +2 for page separator
            
            doc.close()
            
            # Identify and remove headers/footers
            headers_removed = self._identify_common_elements(headers_candidates)
            footers_removed = self._identify_common_elements(footers_candidates)
            
            # Clean text
            clean_text = self._clean_text(page_texts, headers_removed, footers_removed)
            
            # Update page mapping for cleaned text
            updated_page_mapping = self._update_page_mapping_for_cleaned_text(
                clean_text, page_texts, page_mapping
            )
            
            return {
                "success": True,
                "text": clean_text,
                "page_mapping": updated_page_mapping,
                "total_pages": len(page_texts),
                "headers_removed": headers_removed,
                "footers_removed": footers_removed,
                "error": None
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting text with pages from {pdf_path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "text": "",
                "page_mapping": {},
                "total_pages": 0,
                "headers_removed": [],
                "footers_removed": []
            }
    
    def _update_page_mapping_for_cleaned_text(self, 
                                            clean_text: str, 
                                            original_page_texts: List[str],
                                            original_mapping: Dict[int, int]) -> Dict[int, int]:
        """
        Update page mapping after text cleaning.
        
        This is a simplified approach that maps character positions in cleaned text
        to approximate page numbers based on text distribution.
        
        Args:
            clean_text: The cleaned text
            original_page_texts: Original text from each page
            original_mapping: Original character position to page mapping
            
        Returns:
            Updated mapping for cleaned text
        """
        # Handle edge cases
        if not clean_text or not original_page_texts:
            return {}
        
        # Simple approach: distribute cleaned text proportionally across pages
        total_original_chars = sum(len(text) for text in original_page_texts)
        if total_original_chars == 0:
            # If no original text, map everything to page 1
            return {i: 1 for i in range(len(clean_text))}
        
        updated_mapping = {}
        chars_per_page = len(clean_text) / len(original_page_texts)
        
        for char_pos in range(len(clean_text)):
            # Estimate page based on character position
            if chars_per_page > 0:
                estimated_page = min(
                    int(char_pos / chars_per_page) + 1,
                    len(original_page_texts)
                )
            else:
                estimated_page = 1
            updated_mapping[char_pos] = estimated_page
        
        return updated_mapping
    
    def _create_chunks(self, 
                      text: str, 
                      page_mapping: Dict[int, int], 
                      pdf_path: str) -> List[TextChunk]:
        """
        Create intelligent text chunks preserving paragraph boundaries.
        
        Args:
            text: Clean text to chunk
            page_mapping: Character position to page number mapping
            pdf_path: Path to source PDF file
            
        Returns:
            List of TextChunk objects
        """
        if not text.strip():
            self.logger.debug("No text to chunk")
            return []
        
        # Split text into paragraphs first
        paragraphs = self._split_into_paragraphs(text)
        self.logger.debug(f"Split text into {len(paragraphs)} paragraphs")
        
        if not paragraphs:
            # If no paragraphs found, create a single chunk from the entire text
            self.logger.debug("No paragraphs found, creating single chunk from entire text")
            chunk = self._create_single_chunk(
                text.strip(),
                0,
                0,
                page_mapping,
                pdf_path
            )
            return [chunk] if chunk else []
        
        chunks = []
        current_chunk_text = ""
        current_chunk_start = 0
        chunk_id = 0
        
        for paragraph in paragraphs:
            paragraph_tokens = self._estimate_tokens(paragraph)
            current_tokens = self._estimate_tokens(current_chunk_text)
            
            # Check if adding this paragraph would exceed chunk size
            if current_tokens + paragraph_tokens > self.chunk_size and current_chunk_text:
                # Create chunk from current text
                chunk = self._create_single_chunk(
                    current_chunk_text,
                    chunk_id,
                    current_chunk_start,
                    page_mapping,
                    pdf_path
                )
                if chunk:
                    chunks.append(chunk)
                
                # Start new chunk with overlap
                overlap_text = self._get_overlap_text(current_chunk_text)
                current_chunk_text = overlap_text + "\n\n" + paragraph if overlap_text else paragraph
                current_chunk_start = self._find_text_position(text, current_chunk_text, current_chunk_start)
                chunk_id += 1
            else:
                # Add paragraph to current chunk
                if current_chunk_text:
                    current_chunk_text += "\n\n" + paragraph
                else:
                    current_chunk_text = paragraph
                    current_chunk_start = text.find(paragraph)
        
        # Create final chunk if there's remaining text
        if current_chunk_text.strip():
            chunk = self._create_single_chunk(
                current_chunk_text,
                chunk_id,
                current_chunk_start,
                page_mapping,
                pdf_path
            )
            if chunk:
                chunks.append(chunk)
        
        self.logger.debug(f"Created {len(chunks)} chunks")
        return chunks
    
    def _split_into_paragraphs(self, text: str) -> List[str]:
        """
        Split text into paragraphs preserving semantic boundaries.
        
        Args:
            text: Text to split
            
        Returns:
            List of paragraph strings
        """
        # Split on double newlines first (natural paragraph breaks)
        paragraphs = re.split(r'\n\s*\n', text)
        
        # Further split very long paragraphs at sentence boundaries
        refined_paragraphs = []
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # If paragraph is too long, split at sentence boundaries
            if self._estimate_tokens(paragraph) > self.chunk_size * 0.8:
                sentences = self._split_into_sentences(paragraph)
                current_para = ""
                
                for sentence in sentences:
                    if (self._estimate_tokens(current_para + sentence) > self.chunk_size * 0.8 
                        and current_para):
                        refined_paragraphs.append(current_para.strip())
                        current_para = sentence
                    else:
                        current_para += " " + sentence if current_para else sentence
                
                if current_para.strip():
                    refined_paragraphs.append(current_para.strip())
            else:
                refined_paragraphs.append(paragraph)
        
        return refined_paragraphs
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """
        Split text into sentences using simple heuristics.
        
        Args:
            text: Text to split into sentences
            
        Returns:
            List of sentence strings
        """
        # Simple sentence splitting on periods, exclamation marks, question marks
        # followed by whitespace and capital letter
        sentences = re.split(r'[.!?]+\s+(?=[A-ZÁÀÂÃÉÊÍÓÔÕÚÇ])', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _estimate_tokens(self, text: str) -> int:
        """
        Estimate number of tokens in text using simple heuristics.
        
        Args:
            text: Text to estimate tokens for
            
        Returns:
            Estimated token count
        """
        if not text:
            return 0
        
        # Simple estimation: ~4 characters per token for Portuguese
        # This is a rough approximation for Llama models
        return max(1, len(text) // 4)
    
    def _get_overlap_text(self, text: str) -> str:
        """
        Get overlap text from the end of current chunk, preserving semantic context.
        
        Args:
            text: Current chunk text
            
        Returns:
            Text to use as overlap for next chunk
        """
        if not text or self.overlap <= 0:
            return ""
        
        # Try to find a good semantic boundary for overlap
        sentences = self._split_into_sentences(text)
        if not sentences:
            return self._get_word_based_overlap(text)
        
        # Start from the end and work backwards to find sentences that fit in overlap
        overlap_text = ""
        overlap_tokens = 0
        
        for sentence in reversed(sentences):
            sentence_tokens = self._estimate_tokens(sentence)
            if overlap_tokens + sentence_tokens <= self.overlap:
                if overlap_text:
                    overlap_text = sentence + " " + overlap_text
                else:
                    overlap_text = sentence
                overlap_tokens += sentence_tokens
            else:
                break
        
        # If no complete sentences fit, fall back to word-based overlap
        if not overlap_text:
            return self._get_word_based_overlap(text)
        
        return overlap_text
    
    def _get_word_based_overlap(self, text: str) -> str:
        """
        Get word-based overlap when sentence-based overlap isn't possible.
        
        Args:
            text: Current chunk text
            
        Returns:
            Word-based overlap text
        """
        words = text.split()
        if not words:
            return ""
        
        # Estimate words needed for overlap tokens
        overlap_words = max(1, self.overlap // 4)  # Rough estimation
        overlap_words = min(overlap_words, len(words))
        
        return " ".join(words[-overlap_words:])
    
    def _find_text_position(self, full_text: str, chunk_text: str, start_hint: int) -> int:
        """
        Find the starting position of chunk text in full text.
        
        Args:
            full_text: Complete text
            chunk_text: Chunk text to find
            start_hint: Hint for where to start searching
            
        Returns:
            Starting character position of chunk in full text
        """
        # Remove overlap prefix if present
        chunk_without_overlap = chunk_text
        if self.overlap > 0:
            # Try to find where the new content starts
            lines = chunk_text.split('\n')
            if len(lines) > 2:
                # Skip first few lines that might be overlap
                chunk_without_overlap = '\n'.join(lines[2:])
        
        # Find position in full text
        pos = full_text.find(chunk_without_overlap, start_hint)
        return pos if pos >= 0 else start_hint
    
    def _create_single_chunk(self, 
                           text: str, 
                           chunk_id: int, 
                           char_start: int,
                           page_mapping: Dict[int, int], 
                           pdf_path: str) -> Optional[TextChunk]:
        """
        Create a single TextChunk object.
        
        Args:
            text: Chunk text content
            chunk_id: Unique identifier for this chunk
            char_start: Starting character position in original text
            page_mapping: Character position to page mapping
            pdf_path: Path to source PDF
            
        Returns:
            TextChunk object or None if creation fails
        """
        try:
            char_end = char_start + len(text)
            
            # Determine page range for this chunk
            page_start = page_mapping.get(char_start, 1)
            page_end = page_mapping.get(char_end - 1, page_start)
            
            # Ensure page_end is not less than page_start
            if page_end < page_start:
                page_end = page_start
            
            # Estimate token count
            token_count = self._estimate_tokens(text)
            
            return TextChunk(
                text=text.strip(),
                chunk_id=chunk_id,
                page_start=page_start,
                page_end=page_end,
                char_start=char_start,
                char_end=char_end,
                pdf_path=pdf_path,
                token_count=token_count
            )
            
        except Exception as e:
            self.logger.error(f"Error creating chunk {chunk_id}: {e}")
            return None
    
    def _identify_common_elements(self, candidates: List[str]) -> List[str]:
        """
        Identify common headers/footers based on frequency.
        
        Args:
            candidates: List of potential header/footer texts
            
        Returns:
            List of texts that appear frequently enough to be considered headers/footers
        """
        if not candidates:
            return []
        
        # Count frequency of each candidate
        counter = Counter(candidates)
        total_pages = len(candidates)
        
        # Identify elements that appear in at least threshold% of pages
        common_elements = []
        for text, count in counter.items():
            frequency = count / total_pages
            if frequency >= self.header_footer_threshold:
                common_elements.append(text)
        
        return common_elements
    
    def _clean_text(self, page_texts: List[str], headers: List[str], footers: List[str]) -> str:
        """
        Clean text by removing identified headers and footers.
        
        Args:
            page_texts: List of text from each page
            headers: List of header texts to remove
            footers: List of footer texts to remove
            
        Returns:
            Cleaned text ready for processing
        """
        cleaned_pages = []
        
        for page_text in page_texts:
            lines = page_text.split('\n')
            cleaned_lines = []
            
            for line in lines:
                line_stripped = line.strip()
                
                # Skip empty lines
                if not line_stripped:
                    continue
                
                # Only skip if line exactly matches or is very similar to header/footer
                should_skip = False
                for header in headers:
                    if header and len(header) > 5 and header in line_stripped:
                        should_skip = True
                        break
                
                if not should_skip:
                    for footer in footers:
                        if footer and len(footer) > 5 and footer in line_stripped:
                            should_skip = True
                            break
                
                if not should_skip:
                    cleaned_lines.append(line_stripped)
            
            if cleaned_lines:
                if self.preserve_structure:
                    # Join with single newlines to preserve paragraph structure
                    cleaned_pages.append('\n'.join(cleaned_lines))
                else:
                    # Join with spaces for continuous text
                    cleaned_pages.append(' '.join(cleaned_lines))
        
        # If no cleaned pages, return original text (fallback)
        if not cleaned_pages and page_texts:
            self.logger.warning("Header/footer cleaning removed all text, using original")
            return '\n\n'.join(page_texts)
        
        # Join pages with double newlines
        return '\n\n'.join(cleaned_pages)


def create_pdf_text_extractor(chunk_size: int = 1000, 
                            overlap: int = 200) -> PDFTextExtractor:
    """
    Factory function to create a PDFTextExtractor instance.
    
    Args:
        chunk_size: Target size for text chunks in tokens
        overlap: Number of tokens to overlap between chunks
        
    Returns:
        Configured PDFTextExtractor instance
    """
    return PDFTextExtractor(chunk_size=chunk_size, overlap=overlap)