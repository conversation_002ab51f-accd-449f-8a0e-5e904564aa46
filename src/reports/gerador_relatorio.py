#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para gerar um relatório tático com análise de casos classificados como positivos.

Este script processará apenas os registros do arquivo de resultados que foram 
classificados com valor 1 (positivos), extrairá os dados relevantes e usará o modelo Claude 3.7 Sonnet
para gerar um relatório tático em formato markdown com análise estatística, identificação de falhas
sistêmicas e exemplos ilustrativos.
"""

import os
import pandas as pd
from tqdm import tqdm
from anthropic import Anthropic
import logging

# Importações absolutas
from src.utils.config_utils import carregar_configuracao, carregar_variaveis_ambiente

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def carregar_dados_csv(nome_arquivo):
    """
    Carrega os dados do arquivo CSV e filtra apenas os casos positivos (classificados como 1)
    
    Args:
        nome_arquivo (str): Caminho para o arquivo CSV com os dados classificados
        
    Returns:
        DataFrame: DataFrame contendo apenas os casos positivos
    """
    try:
        df = pd.read_csv(nome_arquivo)
        casos_positivos = df[df['REFERE_ASSUNTO'] == 1]
        return casos_positivos
    except Exception as e:
        logging.error(f"Erro ao carregar arquivo CSV {nome_arquivo}: {e}")
        return None

def carregar_template_prompt(caminho_template='templates/template_relatorio.txt'):
    """
    Carrega o template do prompt de um arquivo externo
    
    Args:
        caminho_template (str): Caminho para o arquivo de template
        
    Returns:
        str: Conteúdo do template ou None se ocorrer erro
    """
    try:
        with open(caminho_template, 'r', encoding='utf-8') as file:
            template = file.read()
        return template
    except FileNotFoundError:
        logging.error(f"Erro: Arquivo de template '{caminho_template}' não encontrado.")
        logging.error(f"Certifique-se de que o arquivo '{caminho_template}' existe no diretório do projeto.")
        return None
    except Exception as e:
        logging.error(f"Erro ao carregar o template do prompt: {e}")
        return None

def gerar_prompt_para_relatorio(casos_positivos, assunto):
    """
    Gera o prompt para enviar ao modelo Claude com os casos positivos
    
    Args:
        casos_positivos (DataFrame): DataFrame contendo os casos positivos
        assunto (str): Assunto de classificação
        
    Returns:
        str: Prompt formatado para enviar ao modelo Claude
    """
    
    # Carregar o template do prompt
    template = carregar_template_prompt()
    if not template:
        logging.error("Erro: Não foi possível carregar o template do prompt.")
        return None
    
    # Extrair todos os relatos positivos
    relatos = casos_positivos['RELATO'].tolist()
    
    # Contar o total de casos positivos
    total_casos = len(relatos)
    
    # Formatar as denúncias com separadores
    denuncias_formatadas = ""
    for i, relato in enumerate(relatos, 1):
        denuncias_formatadas += f"### DENÚNCIA #{i}\n{relato}\n---\n\n"
    
    # Substituir as variáveis no template
    prompt = template.format(
        total_casos=total_casos,
        assunto=assunto,
        assunto_maiusculo=assunto.upper(),
        denuncias_formatadas=denuncias_formatadas
    )
    
    return prompt

def gerar_relatorio_com_claude(prompt, model_name, api_key):
    """
    Gera um relatório usando a API do Anthropic Claude
    
    Args:
        prompt (str): Prompt formatado para enviar ao modelo
        model_name (str): Nome do modelo Claude a ser utilizado
        api_key (str): Chave API da Anthropic
        
    Returns:
        str: Conteúdo do relatório gerado
    """
    try:
        cliente = Anthropic(api_key=api_key)
        
        # Fazer a chamada para a API
        resposta = cliente.messages.create(
            model=model_name,
            max_tokens=12000,
            temperature=0.7,
            system="Você é um analista especializado em identificação de problemas sistêmicos a partir de conjuntos de denúncias. Sua tarefa é analisar rigorosamente os dados apresentados e identificar padrões, causas raiz e recomendações práticas, sempre mantendo uma linguagem formal, objetiva e analítica. Você deve seguir estritamente a estrutura de relatório solicitada.",
            messages=[
                {"role": "user", "content": prompt}
            ]
        )
        
        # Extrair o conteúdo da resposta
        relatorio = resposta.content[0].text
        return relatorio
    except Exception as e:
        logging.error(f"Erro ao gerar relatório com Claude: {e}")
        return None

def salvar_relatorio(relatorio, arquivo_saida='relatorio_tatico.md'):
    """
    Salva o relatório gerado em um arquivo markdown
    
    Args:
        relatorio (str): Conteúdo do relatório
        arquivo_saida (str): Nome do arquivo de saída
        
    Returns:
        bool: True se salvou com sucesso, False caso contrário
    """
    try:
        # Criar diretório de resultados se não existir
        if not os.path.exists('data/output'):
            os.makedirs('data/output')
            
        caminho_completo = os.path.join('data/output', arquivo_saida)
        
        with open(caminho_completo, 'w', encoding='utf-8') as file:
            file.write(relatorio)
        logging.info(f"Relatório salvo com sucesso em {caminho_completo}")
        return True
    except Exception as e:
        logging.error(f"Erro ao salvar relatório: {e}")
        return False

def main():
    """Função principal para gerar o relatório"""
    
    # Carregar variáveis de ambiente
    env_vars = carregar_variaveis_ambiente()
    
    # Verificar variável de ambiente para API key
    api_key = env_vars.get("ANTHROPIC_API_KEY")
    if not api_key:
        logging.error("ERRO: Variável de ambiente ANTHROPIC_API_KEY não encontrada no arquivo .env.")
        print("Configure a chave API no arquivo .env: ANTHROPIC_API_KEY='sua-chave-api'")
        return
    
    # Carregar configurações
    config = carregar_configuracao()
    if not config:
        return
    
    # Exibir configuração carregada para depuração
    logging.info(f"Configuração carregada: {config}")
    
    # Obter o modelo do relatório e assunto
    report_model = config.get('REPORT_MODEL')
    assunto = config.get('ASSUNTO')
    
    logging.info(f"Modelo encontrado: {report_model}")
    logging.info(f"Assunto encontrado: {assunto}")
    
    if not report_model or not assunto:
        logging.error("Erro: Configurações incompletas no arquivo de configuração")
        print(f"Necessário definir ASSUNTO e REPORT_MODEL no arquivo config/config.yml.")
        return
    
    logging.info(f"Usando modelo: {report_model} para análise de casos relacionados a '{assunto}'")
    
    # Definir arquivo CSV específico para análise
    arquivo_csv = 'data/output/relatos_classificados_individual.csv'  # Apenas método individual
    
    if not os.path.exists(arquivo_csv):
        logging.warning(f"Arquivo não encontrado: {arquivo_csv}")
        logging.info("Verificando em pasta legada...")
        
        # Tentar localização alternativa
        arquivo_csv = 'resultados/relatos_classificados_individual.csv'
        if not os.path.exists(arquivo_csv):
            logging.error(f"Arquivo não encontrado em nenhuma localização: {arquivo_csv}")
            return
    
    # Carregar casos positivos
    logging.info(f"Carregando arquivo {arquivo_csv}...")
    df = pd.read_csv(arquivo_csv)
    casos_positivos = df[df['REFERE_ASSUNTO'] == 1]
    
    logging.info(f"Arquivo {arquivo_csv}: {len(casos_positivos)} casos positivos")
    
    if casos_positivos.empty:
        logging.error("Nenhum caso positivo encontrado no arquivo selecionado.")
        return
    
    total_casos = len(casos_positivos)
    logging.info(f"\nTotal de {total_casos} casos positivos carregados.")
    
    # Gerar prompt para o relatório
    prompt = gerar_prompt_para_relatorio(casos_positivos, assunto)
    
    if not prompt:
        logging.error("Erro ao gerar o prompt para o relatório. Verifique se o arquivo template_relatorio.txt existe.")
        return
    
    # Gerar relatório com Claude
    logging.info("Gerando relatório com Claude 3.7 Sonnet...")
    relatorio = gerar_relatorio_com_claude(prompt, report_model, api_key)
    
    if relatorio:
        # Salvar o relatório
        nome_arquivo = f"relatorio_{assunto.lower().replace(' ', '_')}.md"
        salvar_relatorio(relatorio, nome_arquivo)
    else:
        logging.error("Não foi possível gerar o relatório.")

if __name__ == "__main__":
    main() 