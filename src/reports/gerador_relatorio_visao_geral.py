#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para gerar um relatório tático de visão geral analisando múltiplos subtemas
de uma área de política pública.

Este script processará todos os registros do arquivo de resultados, classificará
por subtema e gerará um relatório abrangente com análise estatística por subtema.
"""

import os
import pandas as pd
from tqdm import tqdm
from anthropic import Anthropic
import logging
from collections import defaultdict

# Importações absolutas
from src.utils.config_utils import carregar_configuracao, carregar_variaveis_ambiente

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def classificar_por_subtema(df, subtemas, model_name, api_key):
    """
    Classifica cada relato nos subtemas configurados
    
    Args:
        df (DataFrame): DataFrame com os relatos
        subtemas (list): Lista de subtemas para classificação
        model_name (str): Nome do modelo LLM a ser utilizado
        api_key (str): Chave API da Together AI
        
    Returns:
        DataFrame: DataFrame com colunas adicionais para cada subtema
    """
    from together import Together
    cliente = Together(api_key=api_key)
    
    # Adicionar colunas para cada subtema
    for subtema in subtemas:
        df[f'SUBTEMA_{subtema.upper().replace(" ", "_")}'] = 0
    
    # Processar cada relato
    for idx, row in tqdm(df.iterrows(), total=len(df), desc="Classificando por subtemas"):
        relato = row['RELATO']
        
        # Criar prompt para classificação múltipla
        prompt = f"""Analise o seguinte relato e identifique TODOS os subtemas aos quais ele se relaciona.
        
Relato: {relato}

Subtemas disponíveis:
{chr(10).join([f"- {subtema}" for subtema in subtemas])}

Para cada subtema que o relato se relaciona, responda SIM. Para os que não se relaciona, responda NAO.
Formate sua resposta EXATAMENTE assim:
SUBTEMA - RESPOSTA

Exemplo:
Oftalmologia - SIM
Cardiologia - NAO
Neurologia - NAO

Responda para TODOS os subtemas listados."""

        try:
            resposta = cliente.completions.create(
                model=model_name,
                prompt=prompt,
                max_tokens=500,
                temperature=0
            )
            
            resposta_texto = resposta.choices[0].text.strip()
            
            # Processar resposta
            for linha in resposta_texto.split('\n'):
                if ' - ' in linha:
                    subtema_resp, classificacao = linha.split(' - ', 1)
                    subtema_resp = subtema_resp.strip()
                    classificacao = classificacao.strip().upper()
                    
                    # Encontrar o subtema correspondente
                    for subtema in subtemas:
                        if subtema.lower() in subtema_resp.lower():
                            col_name = f'SUBTEMA_{subtema.upper().replace(" ", "_")}'
                            if classificacao == 'SIM':
                                df.at[idx, col_name] = 1
                            break
                            
        except Exception as e:
            logging.error(f"Erro ao classificar registro {idx}: {e}")
    
    return df

def carregar_dados_para_visao_geral(nome_arquivo):
    """
    Carrega todos os dados do arquivo CSV para análise de visão geral
    
    Args:
        nome_arquivo (str): Caminho para o arquivo CSV com os dados
        
    Returns:
        DataFrame: DataFrame com todos os dados
    """
    try:
        df = pd.read_csv(nome_arquivo)
        return df
    except Exception as e:
        logging.error(f"Erro ao carregar arquivo CSV {nome_arquivo}: {e}")
        return None

def analisar_estatisticas_por_subtema(df, subtemas):
    """
    Analisa estatísticas para cada subtema
    
    Args:
        df (DataFrame): DataFrame com classificações por subtema
        subtemas (list): Lista de subtemas
        
    Returns:
        dict: Dicionário com estatísticas por subtema
    """
    estatisticas = {}
    total_relatos = len(df)
    
    for subtema in subtemas:
        col_name = f'SUBTEMA_{subtema.upper().replace(" ", "_")}'
        if col_name in df.columns:
            casos_positivos = df[df[col_name] == 1]
            num_casos = len(casos_positivos)
            percentual = (num_casos / total_relatos * 100) if total_relatos > 0 else 0
            
            # Coletar exemplos de relatos para este subtema
            exemplos = casos_positivos['RELATO'].head(3).tolist() if num_casos > 0 else []
            
            estatisticas[subtema] = {
                'total': num_casos,
                'percentual': percentual,
                'exemplos': exemplos
            }
    
    return estatisticas

def carregar_template_visao_geral(caminho_template='templates/template_relatorio_visao_geral.txt'):
    """
    Carrega o template do prompt para relatório de visão geral
    
    Args:
        caminho_template (str): Caminho para o arquivo de template
        
    Returns:
        str: Conteúdo do template ou None se ocorrer erro
    """
    try:
        with open(caminho_template, 'r', encoding='utf-8') as file:
            template = file.read()
        return template
    except FileNotFoundError:
        logging.error(f"Erro: Arquivo de template '{caminho_template}' não encontrado.")
        return None
    except Exception as e:
        logging.error(f"Erro ao carregar o template do prompt: {e}")
        return None

def gerar_prompt_visao_geral(df, area, subtemas, estatisticas):
    """
    Gera o prompt para enviar ao modelo Claude com análise de visão geral
    
    Args:
        df (DataFrame): DataFrame com todos os dados
        area (str): Área de política pública
        subtemas (list): Lista de subtemas
        estatisticas (dict): Estatísticas por subtema
        
    Returns:
        str: Prompt formatado para enviar ao modelo Claude
    """
    
    # Carregar o template do prompt
    template = carregar_template_visao_geral()
    if not template:
        logging.error("Erro: Não foi possível carregar o template do prompt.")
        return None
    
    # Formatar estatísticas para o prompt
    estatisticas_formatadas = ""
    for subtema, stats in estatisticas.items():
        estatisticas_formatadas += f"\n### {subtema}\n"
        estatisticas_formatadas += f"- Total de casos: {stats['total']} ({stats['percentual']:.1f}%)\n"
        if stats['exemplos']:
            estatisticas_formatadas += f"- Exemplos:\n"
            for i, exemplo in enumerate(stats['exemplos'], 1):
                estatisticas_formatadas += f"  {i}. {exemplo[:200]}...\n"
    
    # Coletar todos os relatos para análise geral
    todos_relatos = ""
    for idx, relato in enumerate(df['RELATO'].tolist(), 1):
        todos_relatos += f"### RELATO #{idx}\n{relato}\n---\n\n"
    
    # Substituir as variáveis no template
    prompt = template.format(
        area=area,
        area_maiuscula=area.upper(),
        total_relatos=len(df),
        num_subtemas=len(subtemas),
        lista_subtemas=", ".join(subtemas),
        estatisticas_formatadas=estatisticas_formatadas,
        todos_relatos=todos_relatos
    )
    
    return prompt

def gerar_relatorio_visao_geral_com_claude(prompt, model_name, api_key):
    """
    Gera um relatório de visão geral usando a API do Anthropic Claude
    
    Args:
        prompt (str): Prompt formatado para enviar ao modelo
        model_name (str): Nome do modelo Claude a ser utilizado
        api_key (str): Chave API da Anthropic
        
    Returns:
        str: Conteúdo do relatório gerado
    """
    try:
        cliente = Anthropic(api_key=api_key)
        
        # Fazer a chamada para a API
        resposta = cliente.messages.create(
            model=model_name,
            max_tokens=15000,
            temperature=0.7,
            system="Você é um analista especializado em políticas públicas com expertise em análise de dados e identificação de padrões sistêmicos. Sua tarefa é analisar conjuntos de denúncias/relatos de diferentes áreas temáticas e produzir um relatório executivo de visão geral que identifique tendências, problemas críticos e oportunidades de melhoria em cada subtema. Mantenha uma linguagem formal, objetiva e focada em insights acionáveis.",
            messages=[
                {"role": "user", "content": prompt}
            ]
        )
        
        # Extrair o conteúdo da resposta
        relatorio = resposta.content[0].text
        return relatorio
    except Exception as e:
        logging.error(f"Erro ao gerar relatório com Claude: {e}")
        return None

def main():
    """Função principal para gerar o relatório de visão geral"""
    
    # Carregar variáveis de ambiente
    env_vars = carregar_variaveis_ambiente()
    
    # Verificar variáveis de ambiente para API keys
    anthropic_api_key = env_vars.get("ANTHROPIC_API_KEY")
    together_api_key = env_vars.get("TOGETHER_API_KEY")
    
    if not anthropic_api_key:
        logging.error("ERRO: Variável de ambiente ANTHROPIC_API_KEY não encontrada no arquivo .env.")
        return
    
    if not together_api_key:
        logging.error("ERRO: Variável de ambiente TOGETHER_API_KEY não encontrada no arquivo .env.")
        return
    
    # Carregar configurações
    config = carregar_configuracao()
    if not config:
        return
    
    # Obter configurações
    area = config.get('AREA_POLITICA_PUBLICA', 'Saúde')
    report_model = config.get('REPORT_MODEL')
    llm_model = config.get('LLM_MODEL')
    
    # Obter subtemas baseado na área
    subtemas_key = f'SUBTEMAS_{area.upper()}'
    subtemas = config.get(subtemas_key, [])
    
    if not subtemas:
        logging.error(f"Erro: Nenhum subtema configurado para a área '{area}'")
        return
    
    logging.info(f"Gerando relatório de visão geral para área: {area}")
    logging.info(f"Subtemas: {', '.join(subtemas)}")
    
    # Carregar dados
    arquivo_csv = 'data/input/TS3 NF Control Page 2.csv'
    
    if not os.path.exists(arquivo_csv):
        logging.error(f"Arquivo não encontrado: {arquivo_csv}")
        return
    
    # Carregar todos os dados
    logging.info(f"Carregando dados de {arquivo_csv}...")
    df = carregar_dados_para_visao_geral(arquivo_csv)
    
    if df is None or df.empty:
        logging.error("Erro ao carregar dados ou arquivo vazio")
        return
    
    logging.info(f"Total de {len(df)} relatos carregados.")
    
    # Classificar por subtema
    logging.info("Classificando relatos por subtema...")
    df_classificado = classificar_por_subtema(df, subtemas, llm_model, together_api_key)
    
    # Salvar DataFrame classificado
    arquivo_classificado = f'data/output/relatos_classificados_visao_geral_{area.lower()}.csv'
    df_classificado.to_csv(arquivo_classificado, index=False)
    logging.info(f"Classificações salvas em {arquivo_classificado}")
    
    # Analisar estatísticas
    estatisticas = analisar_estatisticas_por_subtema(df_classificado, subtemas)
    
    # Gerar prompt para o relatório
    prompt = gerar_prompt_visao_geral(df_classificado, area, subtemas, estatisticas)
    
    if not prompt:
        logging.error("Erro ao gerar o prompt para o relatório.")
        return
    
    # Gerar relatório com Claude
    logging.info(f"Gerando relatório de visão geral com {report_model}...")
    relatorio = gerar_relatorio_visao_geral_com_claude(prompt, report_model, anthropic_api_key)
    
    if relatorio:
        # Salvar o relatório
        nome_arquivo = f"relatorio_visao_geral_{area.lower()}.md"
        arquivo_saida = os.path.join('data/output', nome_arquivo)
        
        try:
            with open(arquivo_saida, 'w', encoding='utf-8') as file:
                file.write(relatorio)
            logging.info(f"Relatório de visão geral salvo em {arquivo_saida}")
        except Exception as e:
            logging.error(f"Erro ao salvar relatório: {e}")
    else:
        logging.error("Não foi possível gerar o relatório.")

if __name__ == "__main__":
    main()