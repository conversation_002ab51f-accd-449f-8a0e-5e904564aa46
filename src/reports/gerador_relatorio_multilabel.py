#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gerador de relatórios baseado em classificação multilabel pré-processada.

Este script processa arquivos CSV com classificação multilabel já realizada,
permitindo gerar relatórios específicos por subtema ou relatórios combinados
de múltiplos subtemas de uma área.
"""

import os
import pandas as pd
from typing import List, Dict, Optional
from anthropic import Anthropic
import logging
from collections import defaultdict
import unicodedata

# Importações absolutas
from src.utils.config_utils import carregar_configuracao, carregar_variaveis_ambiente
from src.analisadores.analisador_multilabel import obter_contexto_subtema

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def carregar_dados_multilabel(arquivo_csv: str) -> Optional[pd.DataFrame]:
    """
    Carrega dados do arquivo CSV com classificação multilabel.
    
    Args:
        arquivo_csv (str): Caminho para o arquivo CSV com classificação multilabel
        
    Returns:
        DataFrame: DataFrame com os dados classificados ou None se erro
    """
    try:
        df = pd.read_csv(arquivo_csv)
        logging.info(f"Arquivo carregado: {len(df)} registros")
        return df
    except Exception as e:
        logging.error(f"Erro ao carregar arquivo CSV {arquivo_csv}: {e}")
        return None

def obter_colunas_subtemas(df: pd.DataFrame) -> List[str]:
    """
    Obtém os subtemas únicos da coluna SUBTEMAS_IDENTIFICADOS.

    Args:
        df (DataFrame): DataFrame com dados multilabel

    Returns:
        List[str]: Lista de subtemas únicos
    """
    subtemas_unicos = set()

    # Verificar se existe a coluna SUBTEMAS_IDENTIFICADOS
    if 'SUBTEMAS_IDENTIFICADOS' not in df.columns:
        logging.warning("Coluna SUBTEMAS_IDENTIFICADOS não encontrada")
        return []

    # Extrair todos os subtemas únicos da coluna
    for _, row in df.iterrows():
        subtemas_str = str(row.get('SUBTEMAS_IDENTIFICADOS', ''))
        if subtemas_str and subtemas_str != 'nan' and subtemas_str.strip():
            # Dividir por vírgula e limpar espaços
            subtemas = [s.strip() for s in subtemas_str.split(',') if s.strip()]
            subtemas_unicos.update(subtemas)

    subtemas_lista = list(subtemas_unicos)
    logging.info(f"Subtemas encontrados: {subtemas_lista}")
    return subtemas_lista

def filtrar_por_subtema(df: pd.DataFrame, subtema: str) -> pd.DataFrame:
    """
    Filtra registros que contêm um subtema específico na coluna SUBTEMAS_IDENTIFICADOS.

    Args:
        df (DataFrame): DataFrame com classificação multilabel
        subtema (str): Nome do subtema a filtrar

    Returns:
        DataFrame: DataFrame filtrado com casos que contêm o subtema
    """
    if 'SUBTEMAS_IDENTIFICADOS' not in df.columns:
        logging.error("Coluna SUBTEMAS_IDENTIFICADOS não encontrada no DataFrame")
        return pd.DataFrame()

    # Filtrar registros que contêm o subtema na coluna SUBTEMAS_IDENTIFICADOS
    casos_positivos = []

    for idx, row in df.iterrows():
        subtemas_str = str(row.get('SUBTEMAS_IDENTIFICADOS', ''))
        if subtemas_str and subtemas_str != 'nan' and subtemas_str.strip():
            # Dividir por vírgula e verificar se o subtema está presente
            subtemas = [s.strip() for s in subtemas_str.split(',') if s.strip()]
            if subtema in subtemas:
                casos_positivos.append(idx)

    df_filtrado = df.loc[casos_positivos]
    logging.info(f"Subtema '{subtema}': {len(df_filtrado)} casos positivos")
    return df_filtrado

def filtrar_por_multiplos_subtemas(df: pd.DataFrame, subtemas: List[str]) -> pd.DataFrame:
    """
    Filtra registros que foram classificados positivamente para pelo menos um dos subtemas.
    
    Args:
        df (DataFrame): DataFrame com classificação multilabel
        subtemas (List[str]): Lista de nomes das colunas dos subtemas
        
    Returns:
        DataFrame: DataFrame filtrado com casos positivos de qualquer subtema
    """
    # Criar máscara para registros que têm pelo menos um subtema positivo
    mascara = df[subtemas].sum(axis=1) > 0
    casos_positivos = df[mascara]
    logging.info(f"Múltiplos subtemas: {len(casos_positivos)} casos positivos")
    return casos_positivos

def analisar_estatisticas_subtemas(df: pd.DataFrame, subtemas_lista: List[str]) -> Dict:
    """
    Analisa estatísticas de distribuição por subtemas da coluna SUBTEMAS_IDENTIFICADOS.

    Args:
        df (DataFrame): DataFrame com classificação multilabel
        subtemas_lista (List[str]): Lista de subtemas únicos

    Returns:
        Dict: Estatísticas por subtema
    """
    estatisticas = {}
    total_registros = len(df)

    for subtema in subtemas_lista:
        # Filtrar casos que contêm este subtema
        casos_positivos = filtrar_por_subtema(df, subtema)
        num_casos = len(casos_positivos)
        percentual = (num_casos / total_registros * 100) if total_registros > 0 else 0

        estatisticas[subtema] = {
            'total': num_casos,
            'percentual': percentual,
            'casos': casos_positivos
        }

    return estatisticas

def carregar_template_multilabel(tipo_relatorio: str = 'individual') -> Optional[str]:
    """
    Carrega template de prompt baseado no tipo de relatório.
    
    Args:
        tipo_relatorio (str): Tipo do relatório ('individual', 'combinado', 'visao_geral')
        
    Returns:
        str: Conteúdo do template ou None se erro
    """
    templates = {
        'individual': 'config/template_relatorio_individual_especializado.txt',
        'combinado': 'config/template_relatorio_combinado.txt',
        'visao_geral': 'config/template_relatorio_visao_geral_especializado.txt'
    }
    
    caminho_template = templates.get(tipo_relatorio, templates['individual'])
    
    try:
        with open(caminho_template, 'r', encoding='utf-8') as file:
            template = file.read()
        return template
    except FileNotFoundError:
        logging.warning(f"Template {caminho_template} não encontrado, usando template padrão")
        # Fallback para template padrão
        try:
            with open('config/template_relatorio.txt', 'r', encoding='utf-8') as file:
                template = file.read()
            return template
        except FileNotFoundError:
            logging.error("Nenhum template encontrado")
            return None
    except Exception as e:
        logging.error(f"Erro ao carregar template: {e}")
        return None

def gerar_prompt_subtema_individual(casos_positivos: pd.DataFrame, nome_subtema: str, area: str) -> str:
    """
    Gera prompt especializado para relatório de um subtema específico.
    Inclui definições e contexto funcional do subtema.

    Args:
        casos_positivos (DataFrame): DataFrame com casos positivos do subtema
        nome_subtema (str): Nome do subtema para o relatório
        area (str): Área do subtema (SAUDE, EDUCACAO, MEIO_AMBIENTE)

    Returns:
        str: Prompt formatado
    """
    template = carregar_template_multilabel('individual')

    # Usar coluna de texto disponível
    if 'Assunto Inteiro Teor' in casos_positivos.columns:
        coluna_texto = 'Assunto Inteiro Teor'
    elif 'Assunto Resumido' in casos_positivos.columns:
        coluna_texto = 'Assunto Resumido'
    elif 'TEXTO_ANALISADO' in casos_positivos.columns:
        coluna_texto = 'TEXTO_ANALISADO'
    else:
        coluna_texto = 'Teor'  # fallback

    relatos = casos_positivos[coluna_texto].tolist()
    total_casos = len(relatos)

    # Formatar relatos seguindo o padrão do template especializado
    relatos_formatados = ""
    for i, relato in enumerate(relatos, 1):
        relatos_formatados += f"### RELATO #{i}\n{relato}\n---\n\n"

    # Carregar contexto específico do subtema
    contexto_subtema = _obter_contexto_subtema_para_relatorio(nome_subtema, area)

    # Substituir variáveis no template especializado
    prompt = template.format(
        total_casos=total_casos,
        assunto=nome_subtema,
        definicao_subtema=contexto_subtema['definicao'],
        palavras_chave_subtema=contexto_subtema['palavras_chave_formatadas'],
        foco_analise=contexto_subtema['foco_funcional'],
        denuncias_formatadas=relatos_formatados
    )
    return prompt


def _obter_contexto_subtema_para_relatorio(nome_subtema: str, area: str) -> Dict[str, str]:
    """
    Obtém contexto específico do subtema para uso em relatórios.

    Args:
        nome_subtema (str): Nome do subtema
        area (str): Área do subtema

    Returns:
        Dict[str, str]: Contexto formatado para o relatório
    """
    # Obter contexto do subtema
    contexto = obter_contexto_subtema(area, nome_subtema)

    return {
        'definicao': contexto['definicao'],
        'palavras_chave_formatadas': "",  # Não usar palavras-chave (contraditório ao uso de LLM)
        'foco_funcional': contexto['foco_funcional']
    }


def _inferir_area_do_subtema(nome_subtema: str) -> str:
    """
    Infere a área baseada no nome do subtema.

    Args:
        nome_subtema (str): Nome do subtema

    Returns:
        str: Nome da área inferida
    """
    nome_lower = nome_subtema.lower()

    if any(palavra in nome_lower for palavra in ['ambiente', 'poluicao', 'desmatamento', 'residuo', 'agua']):
        return 'MEIO_AMBIENTE'
    elif any(palavra in nome_lower for palavra in ['educacao', 'escola', 'ensino', 'professor', 'aluno', 'bullying']):
        return 'EDUCACAO'
    elif any(palavra in nome_lower for palavra in ['oncologia', 'saude', 'hospital', 'sus', 'medic', 'diagnose', 'hematologia']):
        return 'SAUDE'

    # Default para SAUDE se não conseguir determinar
    return 'SAUDE'




def gerar_prompt_multiplos_subtemas(df: pd.DataFrame, estatisticas: Dict, area: str) -> Optional[str]:
    """
    Gera prompt para relatório combinado de múltiplos subtemas.
    
    Args:
        df (DataFrame): DataFrame com todos os dados
        estatisticas (Dict): Estatísticas por subtema
        area (str): Nome da área de política pública
        
    Returns:
        str: Prompt formatado ou None se erro
    """
    template = carregar_template_multilabel('visao_geral')
    if not template:
        return None
    
    # Formatar estatísticas
    estatisticas_formatadas = ""
    lista_subtemas = []
    
    for nome_subtema, stats in estatisticas.items():
        if stats['total'] > 0:
            lista_subtemas.append(nome_subtema)
            estatisticas_formatadas += f"\n### {nome_subtema}\n"
            estatisticas_formatadas += f"- Total de casos: {stats['total']} ({stats['percentual']:.1f}%)\n"
            
            # Adicionar exemplos
            if len(stats['casos']) > 0:
                coluna_texto = 'TEXTO_ANALISADO' if 'TEXTO_ANALISADO' in stats['casos'].columns else 'Teor'
                exemplos = stats['casos'][coluna_texto].head(3).tolist()
                if exemplos:
                    estatisticas_formatadas += "- Exemplos:\n"
                    for i, exemplo in enumerate(exemplos, 1):
                        texto_exemplo = exemplo[:200] + "..." if len(exemplo) > 200 else exemplo
                        estatisticas_formatadas += f"  {i}. {texto_exemplo}\n"
    
    # Coletar todos os relatos relevantes
    subtemas_com_casos = [subtema for subtema, stats in estatisticas.items() if stats['total'] > 0]
    casos_relevantes = df[df['SUBTEMAS_IDENTIFICADOS'].str.contains('|'.join(subtemas_com_casos), na=False, regex=True)]
    
    todos_relatos = ""
    # Usar coluna de texto disponível
    if 'Assunto Inteiro Teor' in casos_relevantes.columns:
        coluna_texto = 'Assunto Inteiro Teor'
    elif 'Assunto Resumido' in casos_relevantes.columns:
        coluna_texto = 'Assunto Resumido'
    elif 'TEXTO_ANALISADO' in casos_relevantes.columns:
        coluna_texto = 'TEXTO_ANALISADO'
    else:
        coluna_texto = 'Teor'  # fallback
    
    for idx, relato in enumerate(casos_relevantes[coluna_texto].tolist(), 1):
        todos_relatos += f"### RELATO #{idx}\n{relato}\n---\n\n"
    
    # Substituir variáveis no template
    prompt = template.format(
        area=area,
        area_maiuscula=area.upper(),
        total_relatos=len(casos_relevantes),
        num_subtemas=len(lista_subtemas),
        lista_subtemas=", ".join(lista_subtemas),
        estatisticas_formatadas=estatisticas_formatadas,
        todos_relatos=todos_relatos
    )
    
    return prompt

def gerar_relatorio_com_claude(prompt: str, model_name: str, api_key: str) -> Optional[str]:
    """
    Gera relatório usando Claude.
    
    Args:
        prompt (str): Prompt formatado
        model_name (str): Nome do modelo Claude
        api_key (str): Chave API da Anthropic
        
    Returns:
        str: Relatório gerado ou None se erro
    """
    try:
        cliente = Anthropic(api_key=api_key)
        
        # Fazer a chamada para a API
        resposta = cliente.messages.create(
            model=model_name,
            max_tokens=15000,
            temperature=0.7,
            system="Você é um analista especializado em identificação de problemas sistêmicos a partir de conjuntos de denúncias. Sua tarefa é analisar rigorosamente os dados apresentados e identificar padrões, causas raiz e recomendações práticas, sempre mantendo uma linguagem formal, objetiva e analítica.",
            messages=[
                {"role": "user", "content": prompt}
            ]
        )
        
        # Extrair o conteúdo da resposta
        relatorio = resposta.content[0].text
        return relatorio
    except Exception as e:
        logging.error(f"Erro ao gerar relatório com Claude: {e}")
        return None

def salvar_relatorio(relatorio: str, nome_arquivo: str) -> bool:
    """
    Salva relatório em arquivo markdown.
    
    Args:
        relatorio (str): Conteúdo do relatório
        nome_arquivo (str): Nome do arquivo de saída
        
    Returns:
        bool: True se salvou com sucesso
    """
    try:
        if not os.path.exists('data/output'):
            os.makedirs('data/output')
            
        caminho_completo = os.path.join('data/output', nome_arquivo)
        
        with open(caminho_completo, 'w', encoding='utf-8') as file:
            file.write(relatorio)
        logging.info(f"Relatório salvo em {caminho_completo}")
        return True
    except Exception as e:
        logging.error(f"Erro ao salvar relatório: {e}")
        return False

def gerar_relatorio_subtema_especifico(arquivo_csv: str, subtema: str, api_key: str, model_name: str) -> bool:
    """
    Gera relatório para um subtema específico.
    
    Args:
        arquivo_csv (str): Caminho para arquivo CSV com classificação multilabel
        subtema (str): Nome do subtema (ex: 'ONCOLOGIA')
        api_key (str): Chave API da Anthropic
        model_name (str): Nome do modelo Claude
        
    Returns:
        bool: True se gerou com sucesso
    """
    # Carregar dados
    df = carregar_dados_multilabel(arquivo_csv)
    if df is None:
        return False
    
    # Obter lista de subtemas únicos
    subtemas_lista = obter_colunas_subtemas(df)
    if not subtemas_lista:
        return False

    # Verificar se o subtema existe
    if subtema not in subtemas_lista:
        logging.error(f"Subtema '{subtema}' não encontrado. Subtemas disponíveis: {subtemas_lista}")
        return False

    # Filtrar casos do subtema específico
    casos_positivos = filtrar_por_subtema(df, subtema)
    
    if casos_positivos.empty:
        logging.warning(f"Nenhum caso positivo encontrado para subtema {subtema}")
        return False
    
    # Gerar prompt com contexto da área
    nome_subtema = subtema.replace('_', ' ').title()
    area = _inferir_area_do_subtema(nome_subtema)
    prompt = gerar_prompt_subtema_individual(casos_positivos, nome_subtema, area)
    
    # Gerar relatório
    logging.info(f"Gerando relatório para subtema {nome_subtema}...")
    relatorio = gerar_relatorio_com_claude(prompt, model_name, api_key)
    if not relatorio:
        return False
    
    # Salvar relatório
    nome_arquivo_limpo = subtema.replace(' ', '_').replace('-', '_').lower()
    nome_arquivo = f"relatorio_{nome_arquivo_limpo}_multilabel.md"
    return salvar_relatorio(relatorio, nome_arquivo)

def gerar_relatorio_area_completa(arquivo_csv: str, area: str, api_key: str, model_name: str) -> bool:
    """
    Gera relatório de visão geral para uma área completa.
    
    Args:
        arquivo_csv (str): Caminho para arquivo CSV com classificação multilabel
        area (str): Nome da área (ex: 'SAUDE')
        api_key (str): Chave API da Anthropic
        model_name (str): Nome do modelo Claude
        
    Returns:
        bool: True se gerou com sucesso
    """
    # Carregar dados
    df = carregar_dados_multilabel(arquivo_csv)
    if df is None:
        return False
    
    # Filtrar por área
    if 'AREA_CLASSIFICACAO' in df.columns:
        # Normalizar acentos para comparação
        def normalizar_texto(texto):
            return unicodedata.normalize('NFD', texto).encode('ascii', 'ignore').decode('ascii').upper()
        
        area_normalizada = normalizar_texto(area)
        df_area = df[df['AREA_CLASSIFICACAO'].apply(normalizar_texto) == area_normalizada]
    else:
        df_area = df  # Usar todos os dados se não há coluna de área
    
    if df_area.empty:
        logging.warning(f"Nenhum registro encontrado para área {area}")
        return False
    
    # Analisar estatísticas
    colunas_subtemas = obter_colunas_subtemas(df_area)
    estatisticas = analisar_estatisticas_subtemas(df_area, colunas_subtemas)
    
    # Filtrar apenas subtemas com casos positivos
    estatisticas_positivas = {k: v for k, v in estatisticas.items() if v['total'] > 0}
    
    if not estatisticas_positivas:
        logging.warning(f"Nenhum caso positivo encontrado para área {area}")
        return False
    
    # Gerar prompt
    prompt = gerar_prompt_multiplos_subtemas(df_area, estatisticas_positivas, area)
    if not prompt:
        return False
    
    # Gerar relatório
    logging.info(f"Gerando relatório de visão geral para área {area}...")
    relatorio = gerar_relatorio_com_claude(prompt, model_name, api_key)
    if not relatorio:
        return False
    
    # Salvar relatório
    nome_arquivo = f"relatorio_visao_geral_{area.lower()}_multilabel.md"
    return salvar_relatorio(relatorio, nome_arquivo)

def main():
    """Função principal para gerar relatórios multilabel."""
    
    # Carregar variáveis de ambiente
    env_vars = carregar_variaveis_ambiente()
    api_key = env_vars.get("ANTHROPIC_API_KEY")
    
    if not api_key:
        logging.error("ERRO: Variável de ambiente ANTHROPIC_API_KEY não encontrada")
        return
    
    # Carregar configurações
    config = carregar_configuracao()
    if not config:
        return
    
    report_model = config.get('REPORT_MODEL', 'claude-3-5-sonnet-20241022')
    area = config.get('AREA_POLITICA_PUBLICA', 'SAUDE')
    
    # Arquivo de entrada
    arquivo_csv = 'data/output/relatos_classificados_multilabel.csv'
    
    if not os.path.exists(arquivo_csv):
        logging.error(f"Arquivo não encontrado: {arquivo_csv}")
        return
    
    logging.info(f"Processando arquivo: {arquivo_csv}")
    logging.info(f"Área: {area}")
    logging.info(f"Modelo: {report_model}")
    
    # Gerar relatório de visão geral da área
    sucesso = gerar_relatorio_area_completa(arquivo_csv, area, api_key, report_model)
    
    if sucesso:
        logging.info("Relatório de visão geral gerado com sucesso!")
        
        # Opcionalmente, gerar relatórios individuais por subtema
        df = carregar_dados_multilabel(arquivo_csv)
        if df is not None:
            colunas_subtemas = obter_colunas_subtemas(df)
            estatisticas = analisar_estatisticas_subtemas(df, colunas_subtemas)
            
            # Gerar relatórios individuais para subtemas com mais de 5 casos
            for nome_subtema, stats in estatisticas.items():
                if stats['total'] >= 5:  # Mínimo de casos para relatório individual
                    subtema_codigo = nome_subtema.upper().replace(' ', '_')
                    logging.info(f"Gerando relatório individual para {nome_subtema}...")
                    gerar_relatorio_subtema_especifico(arquivo_csv, subtema_codigo, api_key, report_model)
    else:
        logging.error("Falha ao gerar relatório")

if __name__ == "__main__":
    main() 