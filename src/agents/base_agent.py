"""
Base Agent class for CrewAI integration in Simple Class.

This module provides the foundation for all CrewAI agents,
ensuring consistent configuration and integration with existing tools.
"""

from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod
import os
from pathlib import Path

try:
    from crewai import Agent
    from crewai.tools import BaseTool
except ImportError:
    # Fallback for when CrewAI is not installed
    Agent = None
    BaseTool = None


class BaseAgent(ABC):
    """
    Base class for all CrewAI agents in Simple Class.
    
    Provides common functionality and integration patterns
    for working with existing Simple Class tools and services.
    """
    
    def __init__(
        self,
        role: str,
        goal: str,
        backstory: str,
        tools: Optional[List[Any]] = None,
        verbose: bool = True,
        allow_delegation: bool = False,
        **kwargs
    ):
        """
        Initialize base agent with common configuration.
        
        Args:
            role: The role of the agent
            goal: The goal the agent should achieve
            backstory: Background story for the agent
            tools: List of tools available to the agent
            verbose: Whether to enable verbose output
            allow_delegation: Whether agent can delegate tasks
            **kwargs: Additional agent configuration
        """
        self.role = role
        self.goal = goal
        self.backstory = backstory
        self.tools = tools or []
        self.verbose = verbose
        self.allow_delegation = allow_delegation
        self.config = self._load_config()
        
        # Initialize CrewAI agent if available
        if Agent is not None:
            self.agent = self._create_crew_agent(**kwargs)
        else:
            self.agent = None
            print("Warning: CrewAI not installed. Agent will run in fallback mode.")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from environment and config files."""
        config = {}
        
        # Load from environment variables
        config.update({
            'TOGETHER_API_KEY': os.getenv('TOGETHER_API_KEY'),
            'ANTHROPIC_API_KEY': os.getenv('ANTHROPIC_API_KEY'),
            'FIREWORKS_API_KEY': os.getenv('FIREWORKS_API_KEY'),
            'SUPABASE_URL': os.getenv('SUPABASE_URL'),
            'SUPABASE_KEY': os.getenv('SUPABASE_KEY'),
        })
        
        return config
    
    def _create_crew_agent(self, **kwargs) -> Optional[Agent]:
        """Create the actual CrewAI agent instance."""
        if Agent is None:
            return None
            
        return Agent(
            role=self.role,
            goal=self.goal,
            backstory=self.backstory,
            tools=self.tools,
            verbose=self.verbose,
            allow_delegation=self.allow_delegation,
            **kwargs
        )
    
    @abstractmethod
    def get_tools(self) -> List[Any]:
        """
        Get the tools specific to this agent.
        Must be implemented by subclasses.
        """
        pass
    
    @abstractmethod
    def execute_task(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task using this agent.
        Must be implemented by subclasses.
        
        Args:
            task_input: Input data for the task
            
        Returns:
            Task execution result
        """
        pass
    
    def validate_config(self) -> bool:
        """Validate that required configuration is present."""
        required_keys = self.get_required_config_keys()
        
        for key in required_keys:
            if not self.config.get(key):
                print(f"Warning: Missing required configuration: {key}")
                return False
        
        return True
    
    @abstractmethod
    def get_required_config_keys(self) -> List[str]:
        """
        Get list of required configuration keys for this agent.
        Must be implemented by subclasses.
        """
        pass
    
    def __str__(self) -> str:
        """String representation of the agent."""
        return f"{self.__class__.__name__}(role='{self.role}')"
    
    def __repr__(self) -> str:
        """Detailed representation of the agent."""
        return (
            f"{self.__class__.__name__}("
            f"role='{self.role}', "
            f"goal='{self.goal}', "
            f"tools={len(self.tools)}"
            f")"
        )
