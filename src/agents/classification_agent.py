"""
Classification Agent for CrewAI integration in Simple Class.

This agent handles multilabel text classification using the existing
AnalisadorMultilabel and classification tools from the Simple Class project.
"""

from typing import Dict, List, Any, Optional, Union
import logging
import os
import yaml
from pathlib import Path

from .base_agent import BaseAgent

# Import existing classification tools
try:
    from ..analisadores.analisador_multilabel import (
        classificar_relato_multilabel,
        carregar_definicoes_subtemas,
        carregar_configuracao_yaml,
        carregar_variaveis_ambiente
    )
except ImportError:
    # Fallback for when tools are not available
    classificar_relato_multilabel = None
    carregar_definicoes_subtemas = None
    carregar_configuracao_yaml = None
    carregar_variaveis_ambiente = None

logger = logging.getLogger(__name__)


class ClassificationAgent(BaseAgent):
    """
    CrewAI agent specialized in multilabel text classification.
    
    This agent integrates with the existing Simple Class classification tools
    to provide intelligent text classification for different areas like
    EDUCACAO (Education), SAUDE (Health), and MEIO_AMBIENTE (Environment).
    """
    
    def __init__(self, **kwargs):
        """Initialize the Classification Agent."""
        
        # Agent configuration
        role = "Text Classification Specialist"
        goal = (
            "Classify Brazilian text documents into relevant subtemas using "
            "advanced multilabel classification techniques with LLM models"
        )
        backstory = (
            "You are an expert in natural language processing and text classification "
            "specialized in Brazilian Portuguese content. Your expertise covers "
            "multiple domains including Education (EDUCACAO), Health (SAUDE), "
            "and Environment (MEIO_AMBIENTE). You use state-of-the-art LLM models "
            "like Llama and Fireworks AI to perform accurate multilabel classification, "
            "identifying up to 3 most relevant subtemas per text document."
        )
        
        # Initialize base agent
        super().__init__(
            role=role,
            goal=goal,
            backstory=backstory,
            tools=self.get_tools(),
            **kwargs
        )
        
        # Initialize classification tools and configuration
        self._setup_classification_tools()
    
    def _setup_classification_tools(self):
        """Initialize the classification tools and load configuration."""
        try:
            # Load environment variables
            if carregar_variaveis_ambiente is not None:
                self.env_vars = carregar_variaveis_ambiente()
            else:
                self.env_vars = {
                    'TOGETHER_API_KEY': os.getenv('TOGETHER_API_KEY'),
                    'FIREWORKS_API_KEY': os.getenv('FIREWORKS_API_KEY'),
                    'ANTHROPIC_API_KEY': os.getenv('ANTHROPIC_API_KEY')
                }
            
            # Load configuration
            if carregar_configuracao_yaml is not None:
                self.config = carregar_configuracao_yaml()
            else:
                self.config = {
                    'LLM_MODEL': 'meta-llama/Llama-3.3-70B-Instruct-Turbo'
                }
            
            # Available areas and their subtemas
            self.available_areas = {
                'EDUCACAO': [
                    'Educação Especial - Falta de Mediador',
                    'Educação Especial - Exclusão e Discriminação',
                    'Bullying',
                    'Infraestrutura',
                    'Transporte',
                    'Alimentação Escolar',
                    'Gestão e Casos Pontuais (Irregularidades Administrativas)',
                    'Gestão e Casos Pontuais (Violência e Maus Tratos)',
                    'Qualificação Profissional',
                    'Controle Interno',
                    'Educação de Jovens e Adultos (sistema prisional)'
                ],
                'SAUDE': [
                    'Oncologia',
                    'Hematologia (Hemorede)',
                    'Oftalmologia',
                    'Reabilitação',
                    'Saúde Auditiva',
                    'Regulação em Saúde',
                    'Diagnose',
                    'Auditoria'
                ],
                'MEIO_AMBIENTE': [
                    'Poluição Atmosférica',
                    'Poluição Hídrica',
                    'Desmatamento',
                    'Gestão de Resíduos',
                    'Licenciamento Ambiental',
                    'Áreas de Preservação',
                    'Fauna e Flora',
                    'Mudanças Climáticas',
                    'Saneamento Básico',
                    'Educação Ambiental'
                ]
            }
            
            logger.info("Classification tools initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing classification tools: {e}")
            self.env_vars = {}
            self.config = {}
            self.available_areas = {}
    
    def get_tools(self) -> List[Any]:
        """Get the tools available to this agent."""
        tools = []
        
        # Add classification function if available
        if classificar_relato_multilabel is not None:
            tools.append(classificar_relato_multilabel)
        
        return tools
    
    def get_required_config_keys(self) -> List[str]:
        """Get required configuration keys for this agent."""
        return ['TOGETHER_API_KEY']  # Primary API for classification
    
    def execute_task(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute classification task.
        
        Args:
            task_input: Dictionary containing:
                - text: Text to classify (required)
                - area: Classification area (EDUCACAO, SAUDE, MEIO_AMBIENTE)
                - model_name: LLM model to use (optional)
                - max_subtemas: Maximum number of subtemas (default: 3)
                - api_key: API key override (optional)
        
        Returns:
            Dictionary with classification results
        """
        try:
            # Validate input
            if not task_input:
                return self._error_result("No input provided")
            
            text = task_input.get('text')
            if not text or not text.strip():
                return self._error_result("No text provided for classification")
            
            area = task_input.get('area', 'EDUCACAO').upper()
            if area not in self.available_areas:
                return self._error_result(f"Invalid area: {area}. Available: {list(self.available_areas.keys())}")
            
            model_name = task_input.get('model_name') or self.config.get('LLM_MODEL', 'meta-llama/Llama-3.3-70B-Instruct-Turbo')
            max_subtemas = task_input.get('max_subtemas', 3)
            api_key = task_input.get('api_key') or self.env_vars.get('TOGETHER_API_KEY')
            
            if not api_key:
                return self._error_result("No API key available for classification")
            
            # Get subtemas for the area
            subtemas = self.available_areas[area]
            
            # Perform classification
            return self._classify_text(text, subtemas, area, model_name, api_key, max_subtemas)
            
        except Exception as e:
            logger.error(f"Error in classification task: {e}")
            return self._error_result(f"Task execution failed: {str(e)}")
    
    def _classify_text(
        self,
        text: str,
        subtemas: List[str],
        area: str,
        model_name: str,
        api_key: str,
        max_subtemas: int = 3
    ) -> Dict[str, Any]:
        """Perform text classification using the existing multilabel classifier."""
        try:
            if classificar_relato_multilabel is None:
                return self._error_result("Classification function not available")
            
            # Perform classification
            subtemas_identificados = classificar_relato_multilabel(
                relato=text,
                subtemas=subtemas,
                area=area,
                model_name=model_name,
                api_key=api_key
            )
            
            # Limit to max_subtemas
            if len(subtemas_identificados) > max_subtemas:
                subtemas_identificados = subtemas_identificados[:max_subtemas]
            
            # Load subtema definitions for additional context
            definicoes = {}
            if carregar_definicoes_subtemas is not None:
                try:
                    definicoes = carregar_definicoes_subtemas(area) or {}
                except Exception as e:
                    logger.warning(f"Could not load subtema definitions: {e}")
            
            # Prepare detailed results
            subtemas_detalhados = []
            for subtema in subtemas_identificados:
                subtema_info = {
                    'nome': subtema,
                    'area': area,
                    'definicao': definicoes.get(subtema, {}).get('definicao', ''),
                    'palavras_chave': definicoes.get(subtema, {}).get('palavras_chave', [])
                }
                subtemas_detalhados.append(subtema_info)
            
            # Create binary columns for compatibility
            colunas_binarias = {}
            for subtema in subtemas:
                coluna_nome = f"SUBTEMA_{subtema.upper().replace(' ', '_').replace('-', '_')}"
                colunas_binarias[coluna_nome] = 1 if subtema in subtemas_identificados else 0
            
            return {
                'success': True,
                'task_type': 'multilabel_classification',
                'agent': self.role,
                'area': area,
                'model_used': model_name,
                'text_length': len(text),
                'subtemas_identificados': subtemas_identificados,
                'subtemas_count': len(subtemas_identificados),
                'subtemas_detalhados': subtemas_detalhados,
                'colunas_binarias': colunas_binarias,
                'all_subtemas': subtemas,
                'max_subtemas': max_subtemas
            }
            
        except Exception as e:
            logger.error(f"Error in text classification: {e}")
            return self._error_result(f"Classification failed: {str(e)}")
    
    def _error_result(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error result."""
        return {
            'success': False,
            'error': error_message,
            'task_type': 'multilabel_classification',
            'agent': self.role,
            'subtemas_identificados': [],
            'subtemas_count': 0,
            'subtemas_detalhados': [],
            'colunas_binarias': {}
        }
    
    def get_available_areas(self) -> List[str]:
        """Get list of available classification areas."""
        return list(self.available_areas.keys())
    
    def get_subtemas_for_area(self, area: str) -> List[str]:
        """Get list of subtemas for a specific area."""
        return self.available_areas.get(area.upper(), [])
    
    def validate_input(self, task_input: Dict[str, Any]) -> bool:
        """Validate task input parameters."""
        if not task_input:
            return False
        
        text = task_input.get('text')
        if not text or not text.strip():
            return False
        
        area = task_input.get('area', 'EDUCACAO').upper()
        if area not in self.available_areas:
            return False
        
        return True
    
    def get_model_options(self) -> List[str]:
        """Get list of available LLM models for classification."""
        return [
            'meta-llama/Llama-3.3-70B-Instruct-Turbo',
            'meta-llama/Llama-3.2-3B-Instruct-Turbo',
            'accounts/fireworks/models/llama-v3p3-70b-instruct'
        ]
