"""
Anonymization Agent for CrewAI integration in Simple Class.

This agent handles text and PDF anonymization using the existing
AnonymizationTool and PDFAnonymizationTool from the Simple Class project.
"""

from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import logging

from .base_agent import BaseAgent

# Import existing tools
try:
    from ..tools.anonymization_tool import AnonymizationTool
    from ..tools.pdf_anonymization_tool import PDFAnonymizationTool
except ImportError:
    # Fallback for when tools are not available
    AnonymizationTool = None
    PDFAnonymizationTool = None

logger = logging.getLogger(__name__)


class AnonymizationAgent(BaseAgent):
    """
    CrewAI agent specialized in anonymizing Brazilian text and PDF data.
    
    This agent integrates with the existing Simple Class anonymization tools
    to provide LGPD-compliant data anonymization using Brazilian-specific
    recognizers for CPF, schools, addresses, and other PII entities.
    """
    
    def __init__(self, **kwargs):
        """Initialize the Anonymization Agent."""
        
        # Agent configuration
        role = "Data Privacy Specialist"
        goal = (
            "Anonymize Brazilian text and PDF documents to ensure LGPD compliance "
            "while preserving the semantic meaning and structure of the content"
        )
        backstory = (
            "You are an expert in Brazilian data privacy laws and LGPD compliance. "
            "Your specialty is identifying and anonymizing personally identifiable "
            "information (PII) in Portuguese text, including CPF numbers, names, "
            "addresses, phone numbers, emails, and educational institutions. "
            "You use state-of-the-art NLP tools and Brazilian-specific recognizers "
            "to ensure comprehensive and accurate anonymization."
        )
        
        # Initialize base agent
        super().__init__(
            role=role,
            goal=goal,
            backstory=backstory,
            tools=self.get_tools(),
            **kwargs
        )
        
        # Initialize anonymization tools
        self._setup_anonymization_tools()
    
    def _setup_anonymization_tools(self):
        """Initialize the anonymization tools."""
        try:
            if AnonymizationTool is not None:
                self.text_anonymizer = AnonymizationTool()
                logger.info("Text anonymization tool initialized successfully")
            else:
                self.text_anonymizer = None
                logger.warning("AnonymizationTool not available")
            
            if PDFAnonymizationTool is not None:
                self.pdf_anonymizer = PDFAnonymizationTool()
                logger.info("PDF anonymization tool initialized successfully")
            else:
                self.pdf_anonymizer = None
                logger.warning("PDFAnonymizationTool not available")
                
        except Exception as e:
            logger.error(f"Error initializing anonymization tools: {e}")
            self.text_anonymizer = None
            self.pdf_anonymizer = None
    
    def get_tools(self) -> List[Any]:
        """Get the tools available to this agent."""
        tools = []
        
        # Add anonymization tools if available
        if hasattr(self, 'text_anonymizer') and self.text_anonymizer:
            tools.append(self.text_anonymizer)
        
        if hasattr(self, 'pdf_anonymizer') and self.pdf_anonymizer:
            tools.append(self.pdf_anonymizer)
        
        return tools
    
    def get_required_config_keys(self) -> List[str]:
        """Get required configuration keys for this agent."""
        return []  # Anonymization doesn't require external API keys
    
    def execute_task(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute anonymization task.
        
        Args:
            task_input: Dictionary containing:
                - text: Text to anonymize (optional)
                - pdf_path: Path to PDF file (optional)
                - language: Language code (default: "pt")
                - entities_to_anonymize: Specific entity types (optional)
                - extract_text_only: For PDFs, only extract text (optional)
        
        Returns:
            Dictionary with anonymization results
        """
        try:
            # Validate input
            if not task_input:
                return self._error_result("No input provided")
            
            text = task_input.get('text')
            pdf_path = task_input.get('pdf_path')
            language = task_input.get('language', 'pt')
            entities_to_anonymize = task_input.get('entities_to_anonymize')
            extract_text_only = task_input.get('extract_text_only', False)
            
            # Determine task type
            if text and pdf_path:
                return self._error_result("Provide either text or pdf_path, not both")
            
            if text:
                return self._anonymize_text(text, language, entities_to_anonymize)
            elif pdf_path:
                return self._anonymize_pdf(pdf_path, language, entities_to_anonymize, extract_text_only)
            else:
                return self._error_result("No text or pdf_path provided")
                
        except Exception as e:
            logger.error(f"Error in anonymization task: {e}")
            return self._error_result(f"Task execution failed: {str(e)}")
    
    def _anonymize_text(
        self, 
        text: str, 
        language: str = "pt",
        entities_to_anonymize: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Anonymize text content."""
        if not self.text_anonymizer:
            return self._error_result("Text anonymization tool not available")
        
        try:
            # Perform anonymization
            result = self.text_anonymizer.anonymize_text(text, language)
            
            # Filter entities if specific types were requested
            if entities_to_anonymize and result.get("entities_found"):
                filtered_entities = [
                    entity for entity in result["entities_found"]
                    if entity["entity_type"] in entities_to_anonymize
                ]
                result["entities_found"] = filtered_entities
                result["entities_count"] = len(filtered_entities)
            
            # Add task metadata
            result.update({
                "task_type": "text_anonymization",
                "language": language,
                "agent": self.role
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Error in text anonymization: {e}")
            return self._error_result(f"Text anonymization failed: {str(e)}")
    
    def _anonymize_pdf(
        self,
        pdf_path: Union[str, Path],
        language: str = "pt",
        entities_to_anonymize: Optional[List[str]] = None,
        extract_text_only: bool = False
    ) -> Dict[str, Any]:
        """Anonymize PDF content."""
        if not self.pdf_anonymizer:
            return self._error_result("PDF anonymization tool not available")
        
        try:
            if extract_text_only:
                # Only extract text without anonymization
                result = self.pdf_anonymizer.extract_clean_text(pdf_path)
                result.update({
                    "task_type": "pdf_text_extraction",
                    "language": language,
                    "agent": self.role,
                    "anonymized_text": result.get("text", ""),
                    "entities_found": [],
                    "entities_count": 0
                })
            else:
                # Full anonymization
                result = self.pdf_anonymizer.anonymize_pdf(pdf_path, language)
                
                # Filter entities if specific types were requested
                if entities_to_anonymize and result.get("anonymization_metadata", {}).get("entities_found"):
                    entities = result["anonymization_metadata"]["entities_found"]
                    filtered_entities = [
                        entity for entity in entities
                        if entity["entity_type"] in entities_to_anonymize
                    ]
                    result["anonymization_metadata"]["entities_found"] = filtered_entities
                    result["anonymization_metadata"]["entities_count"] = len(filtered_entities)
                
                # Add task metadata
                result.update({
                    "task_type": "pdf_anonymization",
                    "language": language,
                    "agent": self.role
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error in PDF anonymization: {e}")
            return self._error_result(f"PDF anonymization failed: {str(e)}")
    
    def _error_result(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error result."""
        return {
            "success": False,
            "error": error_message,
            "task_type": "anonymization",
            "agent": self.role,
            "anonymized_text": "",
            "entities_found": [],
            "entities_count": 0
        }
    
    def get_supported_entities(self) -> List[str]:
        """Get list of supported entity types for anonymization."""
        if self.text_anonymizer:
            return self.text_anonymizer.get_supported_entities()
        else:
            return [
                "PERSON",           # Person names
                "EMAIL_ADDRESS",    # Email addresses
                "PHONE_NUMBER",     # Phone numbers
                "CPF",              # Brazilian CPF
                "ESCOLA",           # Educational institutions
                "ENDEREÇO"          # Addresses
            ]
    
    def validate_input(self, task_input: Dict[str, Any]) -> bool:
        """Validate task input parameters."""
        if not task_input:
            return False
        
        text = task_input.get('text')
        pdf_path = task_input.get('pdf_path')
        
        # Must have either text or pdf_path
        if not text and not pdf_path:
            return False
        
        # Cannot have both
        if text and pdf_path:
            return False
        
        # Validate PDF path if provided
        if pdf_path:
            path = Path(pdf_path)
            if not path.exists() or not path.is_file():
                return False
        
        return True
