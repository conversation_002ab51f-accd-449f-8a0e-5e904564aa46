"""
Report Agent for CrewAI integration in Simple Class.

This agent handles tactical report generation using Claude Sonnet 4
and integrates with existing report generation tools from the Simple Class project.
"""

from typing import Dict, List, Any, Optional, Union
import logging
import os
import pandas as pd
from datetime import datetime
from pathlib import Path

from .base_agent import BaseAgent

# Import existing report generation tools
try:
    from anthropic import Anthropic
    from ..reports.gerador_relatorio_multilabel import (
        gerar_relatorio_com_claude,
        gerar_prompt_multiplos_subtemas,
        gerar_prompt_subtema_individual,
        carregar_dados_multilabel,
        analisar_estatisticas_subtemas,
        obter_colunas_subtemas,
        filtrar_por_subtema,
        filtrar_por_multiplos_subtemas
    )
except ImportError:
    # Fallback for when tools are not available
    Anthropic = None
    gerar_relatorio_com_claude = None
    gerar_prompt_multiplos_subtemas = None
    gerar_prompt_subtema_individual = None
    carregar_dados_multilabel = None
    analisar_estatisticas_subtemas = None
    obter_colunas_subtemas = None
    filtrar_por_subtema = None
    filtrar_por_multiplos_subtemas = None

logger = logging.getLogger(__name__)


class ReportAgent(BaseAgent):
    """
    CrewAI agent specialized in generating tactical reports using Claude Sonnet 4.
    
    This agent integrates with the existing Simple Class report generation tools
    to create comprehensive tactical reports for different areas like Education,
    Health, and Environment based on classified complaint data.
    """
    
    def __init__(self, **kwargs):
        """Initialize the Report Agent."""
        
        # Agent configuration
        role = "Tactical Report Analyst"
        goal = (
            "Generate comprehensive tactical reports using Claude Sonnet 4 to identify "
            "systemic patterns, root causes, and practical recommendations from "
            "classified complaint data across different public policy areas"
        )
        backstory = (
            "You are an expert analyst specialized in identifying systemic problems "
            "from complaint datasets for the Public Prosecutor's Office (Ministério Público). "
            "Your expertise covers tactical analysis of complaints in Education, Health, "
            "and Environment sectors. You use advanced AI models like Claude Sonnet 4 "
            "to generate detailed reports that distinguish between evidence (reported facts) "
            "and interpretations (hypotheses for investigation), always maintaining "
            "formal, objective, and analytical language."
        )
        
        # Initialize base agent
        super().__init__(
            role=role,
            goal=goal,
            backstory=backstory,
            tools=self.get_tools(),
            **kwargs
        )
        
        # Initialize report generation tools
        self._setup_report_tools()
    
    def _setup_report_tools(self):
        """Initialize the report generation tools."""
        try:
            # Initialize Claude client if available
            if Anthropic is not None:
                api_key = self.config.get('ANTHROPIC_API_KEY')
                if api_key:
                    self.claude_client = Anthropic(api_key=api_key)
                    logger.info("Claude client initialized successfully")
                else:
                    self.claude_client = None
                    logger.warning("ANTHROPIC_API_KEY not found")
            else:
                self.claude_client = None
                logger.warning("Anthropic library not available")
            
            # Default models and configurations
            self.default_model = "claude-sonnet-4-20250514"
            self.report_types = [
                "overview",      # Visão geral de múltiplos subtemas
                "subtema",       # Relatório específico de um subtema
                "criminal",      # Análise criminal de casos
                "tactical"       # Análise tática geral
            ]
            
            logger.info("Report generation tools initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing report tools: {e}")
            self.claude_client = None
    
    def get_tools(self) -> List[Any]:
        """Get the tools available to this agent."""
        tools = []
        
        # Add report generation functions if available
        if gerar_relatorio_com_claude is not None:
            tools.append(gerar_relatorio_com_claude)
        
        if self.claude_client is not None:
            tools.append(self.claude_client)
        
        return tools
    
    def get_required_config_keys(self) -> List[str]:
        """Get required configuration keys for this agent."""
        return ['ANTHROPIC_API_KEY']  # Claude API key for report generation
    
    def execute_task(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute report generation task.
        
        Args:
            task_input: Dictionary containing:
                - data: DataFrame or CSV file path with classified data (required)
                - report_type: Type of report (overview, subtema, criminal, tactical)
                - area: Area for analysis (EDUCACAO, SAUDE, MEIO_AMBIENTE)
                - subtema: Specific subtema for subtema reports (optional)
                - model_name: Claude model to use (optional)
                - output_path: Path to save report (optional)
                - min_cases: Minimum cases for analysis (optional)
        
        Returns:
            Dictionary with report generation results
        """
        try:
            # Validate input
            if not task_input:
                return self._error_result("No input provided")
            
            data = task_input.get('data')
            if not data:
                return self._error_result("No data provided for report generation")
            
            report_type = task_input.get('report_type', 'overview')
            if report_type not in self.report_types:
                return self._error_result(f"Invalid report type: {report_type}. Available: {self.report_types}")
            
            area = task_input.get('area', 'EDUCACAO').upper()
            model_name = task_input.get('model_name', self.default_model)
            output_path = task_input.get('output_path')
            min_cases = task_input.get('min_cases', 1)
            
            # Load data if it's a file path
            if isinstance(data, str):
                df = self._load_data(data)
                if df is None:
                    return self._error_result(f"Could not load data from: {data}")
            elif isinstance(data, pd.DataFrame):
                df = data
            else:
                return self._error_result("Data must be a DataFrame or file path")
            
            # Generate report based on type
            if report_type == 'overview':
                return self._generate_overview_report(df, area, model_name, output_path, min_cases)
            elif report_type == 'subtema':
                subtema = task_input.get('subtema')
                if not subtema:
                    return self._error_result("Subtema required for subtema report")
                return self._generate_subtema_report(df, subtema, model_name, output_path, min_cases)
            elif report_type == 'criminal':
                return self._generate_criminal_report(df, area, model_name, output_path, min_cases)
            elif report_type == 'tactical':
                return self._generate_tactical_report(df, area, model_name, output_path, min_cases)
            else:
                return self._error_result(f"Report type {report_type} not implemented")
                
        except Exception as e:
            logger.error(f"Error in report generation task: {e}")
            return self._error_result(f"Task execution failed: {str(e)}")
    
    def _load_data(self, file_path: str) -> Optional[pd.DataFrame]:
        """Load data from CSV file."""
        try:
            if carregar_dados_multilabel is not None:
                return carregar_dados_multilabel(file_path)
            else:
                return pd.read_csv(file_path)
        except Exception as e:
            logger.error(f"Error loading data from {file_path}: {e}")
            return None
    
    def _generate_overview_report(
        self,
        df: pd.DataFrame,
        area: str,
        model_name: str,
        output_path: Optional[str] = None,
        min_cases: int = 1
    ) -> Dict[str, Any]:
        """Generate overview report for multiple subtemas."""
        try:
            if not self._validate_tools():
                return self._error_result("Required tools not available")
            
            # Analyze statistics
            colunas_subtemas = obter_colunas_subtemas(df)
            estatisticas = analisar_estatisticas_subtemas(df, colunas_subtemas)
            
            # Filter subtemas with minimum cases
            estatisticas_filtradas = {
                k: v for k, v in estatisticas.items() 
                if v['total'] >= min_cases
            }
            
            if not estatisticas_filtradas:
                return self._error_result(f"No subtemas found with minimum {min_cases} cases")
            
            # Generate prompt
            prompt = gerar_prompt_multiplos_subtemas(df, estatisticas_filtradas, area)
            if not prompt:
                return self._error_result("Failed to generate prompt")
            
            # Generate report with Claude
            api_key = self.config.get('ANTHROPIC_API_KEY')
            relatorio = gerar_relatorio_com_claude(prompt, model_name, api_key)
            
            if not relatorio:
                return self._error_result("Failed to generate report with Claude")
            
            # Save report if output path provided
            if output_path:
                success = self._save_report(relatorio, output_path)
                if not success:
                    logger.warning(f"Failed to save report to {output_path}")
            
            return {
                'success': True,
                'task_type': 'overview_report',
                'agent': self.role,
                'area': area,
                'model_used': model_name,
                'subtemas_analyzed': len(estatisticas_filtradas),
                'total_cases': sum(stats['total'] for stats in estatisticas_filtradas.values()),
                'report_content': relatorio,
                'output_path': output_path,
                'statistics': estatisticas_filtradas
            }
            
        except Exception as e:
            logger.error(f"Error generating overview report: {e}")
            return self._error_result(f"Overview report generation failed: {str(e)}")
    
    def _generate_subtema_report(
        self,
        df: pd.DataFrame,
        subtema: str,
        model_name: str,
        output_path: Optional[str] = None,
        min_cases: int = 1
    ) -> Dict[str, Any]:
        """Generate report for specific subtema."""
        try:
            if not self._validate_tools():
                return self._error_result("Required tools not available")
            
            # Filter cases for specific subtema
            casos_positivos = filtrar_por_subtema(df, subtema)
            
            if casos_positivos.empty or len(casos_positivos) < min_cases:
                return self._error_result(f"Insufficient cases for subtema {subtema} (minimum: {min_cases})")
            
            # Generate prompt with area context
            nome_subtema = subtema.replace('_', ' ').title()
            # Try to get area from config or infer from subtema
            area = self._get_area_for_subtema(subtema)
            prompt = gerar_prompt_subtema_individual(casos_positivos, nome_subtema, area)
            if not prompt:
                return self._error_result("Failed to generate prompt")
            
            # Generate report with Claude
            api_key = self.config.get('ANTHROPIC_API_KEY')
            relatorio = gerar_relatorio_com_claude(prompt, model_name, api_key)
            
            if not relatorio:
                return self._error_result("Failed to generate report with Claude")
            
            # Save report if output path provided
            if output_path:
                success = self._save_report(relatorio, output_path)
                if not success:
                    logger.warning(f"Failed to save report to {output_path}")
            
            return {
                'success': True,
                'task_type': 'subtema_report',
                'agent': self.role,
                'subtema': subtema,
                'model_used': model_name,
                'cases_analyzed': len(casos_positivos),
                'report_content': relatorio,
                'output_path': output_path
            }
            
        except Exception as e:
            logger.error(f"Error generating subtema report: {e}")
            return self._error_result(f"Subtema report generation failed: {str(e)}")
    
    def _generate_criminal_report(
        self,
        df: pd.DataFrame,
        area: str,
        model_name: str,
        output_path: Optional[str] = None,
        min_cases: int = 1
    ) -> Dict[str, Any]:
        """Generate criminal analysis report."""
        # This would integrate with the criminal report generation script
        # For now, return a placeholder implementation
        return self._error_result("Criminal report generation not yet implemented in agent")
    
    def _generate_tactical_report(
        self,
        df: pd.DataFrame,
        area: str,
        model_name: str,
        output_path: Optional[str] = None,
        min_cases: int = 1
    ) -> Dict[str, Any]:
        """Generate tactical analysis report."""
        # This would be similar to overview but with tactical focus
        return self._generate_overview_report(df, area, model_name, output_path, min_cases)
    
    def _get_area_for_subtema(self, subtema: str) -> str:
        """
        Get area for a given subtema.

        Args:
            subtema: Subtema name

        Returns:
            Area name
        """
        # Get from config
        for area, subtemas_list in self.available_areas.items():
            if subtema in subtemas_list or subtema.upper() in [s.upper() for s in subtemas_list]:
                return area

        # Inference based on subtema name
        subtema_lower = subtema.lower()
        if any(palavra in subtema_lower for palavra in ['oncologia', 'saude', 'hospital', 'sus', 'medic', 'diagnose', 'hematologia']):
            return 'SAUDE'
        elif any(palavra in subtema_lower for palavra in ['educacao', 'escola', 'ensino', 'professor', 'aluno', 'bullying']):
            return 'EDUCACAO'
        elif any(palavra in subtema_lower for palavra in ['ambiente', 'poluicao', 'desmatamento', 'residuo', 'agua']):
            return 'MEIO_AMBIENTE'

        # Default to SAUDE if can't determine
        return 'SAUDE'

    def _validate_tools(self) -> bool:
        """Validate that required tools are available."""
        required_functions = [
            gerar_relatorio_com_claude,
            obter_colunas_subtemas,
            analisar_estatisticas_subtemas,
            filtrar_por_subtema,
            gerar_prompt_multiplos_subtemas,
            gerar_prompt_subtema_individual
        ]

        for func in required_functions:
            if func is None:
                return False

        return True
    
    def _save_report(self, content: str, output_path: str) -> bool:
        """Save report content to file."""
        try:
            # Ensure output directory exists
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Write content to file
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"Report saved to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving report to {output_path}: {e}")
            return False
    
    def _error_result(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error result."""
        return {
            'success': False,
            'error': error_message,
            'task_type': 'report_generation',
            'agent': self.role,
            'report_content': '',
            'output_path': None
        }
    
    def get_available_report_types(self) -> List[str]:
        """Get list of available report types."""
        return self.report_types.copy()
    
    def validate_input(self, task_input: Dict[str, Any]) -> bool:
        """Validate task input parameters."""
        if not task_input:
            return False
        
        data = task_input.get('data')
        if not data:
            return False
        
        report_type = task_input.get('report_type', 'overview')
        if report_type not in self.report_types:
            return False
        
        # Validate subtema is provided for subtema reports
        if report_type == 'subtema' and not task_input.get('subtema'):
            return False
        
        return True
