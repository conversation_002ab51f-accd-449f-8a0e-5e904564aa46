"""
Processing Crew for CrewAI integration in Simple Class.

This crew orchestrates the complete text processing pipeline:
Anonymization → Classification → Report Generation
"""

from typing import Dict, List, Any, Optional, Union
import logging
import pandas as pd
from pathlib import Path

from .base_crew import BaseCrew

# Import agents
try:
    from ..agents.anonymization_agent import AnonymizationAgent
    from ..agents.classification_agent import ClassificationAgent
    from ..agents.report_agent import ReportAgent
    from crewai import Task
except ImportError:
    # Fallback for when agents or CrewAI are not available
    AnonymizationAgent = None
    ClassificationAgent = None
    ReportAgent = None
    Task = None

logger = logging.getLogger(__name__)


class ProcessingCrew(BaseCrew):
    """
    CrewAI crew for complete text processing workflow.
    
    This crew orchestrates three specialized agents:
    1. AnonymizationAgent: Anonymizes text data for LGPD compliance
    2. ClassificationAgent: Classifies text into relevant subtemas
    3. ReportAgent: Generates tactical reports using Claude Sonnet 4
    
    The workflow can process individual texts or batch process CSV files.
    """
    
    def __init__(self, **kwargs):
        """Initialize the Processing Crew."""
        
        # Initialize agents
        self.anonymization_agent = self._create_agent(AnonymizationAgent, "anonymization")
        self.classification_agent = self._create_agent(ClassificationAgent, "classification")
        self.report_agent = self._create_agent(ReportAgent, "report")
        
        # Collect available agents
        agents = []
        if self.anonymization_agent:
            agents.append(self.anonymization_agent)
        if self.classification_agent:
            agents.append(self.classification_agent)
        if self.report_agent:
            agents.append(self.report_agent)
        
        # Initialize base crew
        super().__init__(
            name="ProcessingCrew",
            description="Complete text processing pipeline with anonymization, classification, and reporting",
            agents=agents,
            **kwargs
        )
        
        # Workflow configurations
        self.supported_workflows = [
            "full_pipeline",      # Complete: anonymize → classify → report
            "anonymize_classify", # Partial: anonymize → classify
            "classify_report",    # Partial: classify → report
            "anonymize_only",     # Single: anonymize only
            "classify_only",      # Single: classify only
            "report_only"         # Single: report only
        ]
    
    def _create_agent(self, agent_class: Any, agent_type: str) -> Optional[Any]:
        """Create an agent instance with error handling."""
        if agent_class is None:
            logger.warning(f"{agent_type} agent class not available")
            return None
        
        try:
            agent = agent_class()
            if not agent.validate_config():
                logger.warning(f"{agent_type} agent configuration incomplete")
            return agent
        except Exception as e:
            logger.error(f"Error creating {agent_type} agent: {e}")
            return None
    
    def create_tasks(self, workflow_input: Dict[str, Any]) -> List[Any]:
        """Create tasks based on workflow configuration."""
        if Task is None:
            return []  # Fallback mode
        
        tasks = []
        workflow_type = workflow_input.get('workflow_type', 'full_pipeline')
        
        try:
            if workflow_type == "full_pipeline":
                tasks = self._create_full_pipeline_tasks(workflow_input)
            elif workflow_type == "anonymize_classify":
                tasks = self._create_anonymize_classify_tasks(workflow_input)
            elif workflow_type == "classify_report":
                tasks = self._create_classify_report_tasks(workflow_input)
            elif workflow_type == "anonymize_only":
                tasks = self._create_anonymize_only_tasks(workflow_input)
            elif workflow_type == "classify_only":
                tasks = self._create_classify_only_tasks(workflow_input)
            elif workflow_type == "report_only":
                tasks = self._create_report_only_tasks(workflow_input)
            else:
                logger.error(f"Unknown workflow type: {workflow_type}")
                
        except Exception as e:
            logger.error(f"Error creating tasks for {workflow_type}: {e}")
        
        return tasks
    
    def _create_full_pipeline_tasks(self, workflow_input: Dict[str, Any]) -> List[Any]:
        """Create tasks for full pipeline workflow."""
        tasks = []
        
        # Task 1: Anonymization
        if self.anonymization_agent:
            anonymize_task = Task(
                description="Anonymize input text data to ensure LGPD compliance",
                agent=self.anonymization_agent.agent if self.anonymization_agent.agent else self.anonymization_agent,
                expected_output="Anonymized text with identified PII entities"
            )
            tasks.append(anonymize_task)
        
        # Task 2: Classification
        if self.classification_agent:
            classify_task = Task(
                description="Classify anonymized text into relevant subtemas using multilabel classification",
                agent=self.classification_agent.agent if self.classification_agent.agent else self.classification_agent,
                expected_output="Classification results with identified subtemas and confidence scores"
            )
            tasks.append(classify_task)
        
        # Task 3: Report Generation
        if self.report_agent:
            report_task = Task(
                description="Generate comprehensive tactical report based on classification results",
                agent=self.report_agent.agent if self.report_agent.agent else self.report_agent,
                expected_output="Detailed tactical report with analysis and recommendations"
            )
            tasks.append(report_task)
        
        return tasks
    
    def _create_anonymize_classify_tasks(self, workflow_input: Dict[str, Any]) -> List[Any]:
        """Create tasks for anonymization + classification workflow."""
        tasks = []
        
        if self.anonymization_agent:
            tasks.append(Task(
                description="Anonymize input text data",
                agent=self.anonymization_agent.agent if self.anonymization_agent.agent else self.anonymization_agent,
                expected_output="Anonymized text data"
            ))
        
        if self.classification_agent:
            tasks.append(Task(
                description="Classify anonymized text into subtemas",
                agent=self.classification_agent.agent if self.classification_agent.agent else self.classification_agent,
                expected_output="Classification results"
            ))
        
        return tasks
    
    def _create_classify_report_tasks(self, workflow_input: Dict[str, Any]) -> List[Any]:
        """Create tasks for classification + reporting workflow."""
        tasks = []
        
        if self.classification_agent:
            tasks.append(Task(
                description="Classify input text into subtemas",
                agent=self.classification_agent.agent if self.classification_agent.agent else self.classification_agent,
                expected_output="Classification results"
            ))
        
        if self.report_agent:
            tasks.append(Task(
                description="Generate tactical report from classification results",
                agent=self.report_agent.agent if self.report_agent.agent else self.report_agent,
                expected_output="Tactical analysis report"
            ))
        
        return tasks
    
    def _create_anonymize_only_tasks(self, workflow_input: Dict[str, Any]) -> List[Any]:
        """Create tasks for anonymization only."""
        if not self.anonymization_agent:
            return []
        
        return [Task(
            description="Anonymize input text data for LGPD compliance",
            agent=self.anonymization_agent.agent if self.anonymization_agent.agent else self.anonymization_agent,
            expected_output="Anonymized text with PII entities identified"
        )]
    
    def _create_classify_only_tasks(self, workflow_input: Dict[str, Any]) -> List[Any]:
        """Create tasks for classification only."""
        if not self.classification_agent:
            return []
        
        return [Task(
            description="Classify input text into relevant subtemas",
            agent=self.classification_agent.agent if self.classification_agent.agent else self.classification_agent,
            expected_output="Multilabel classification results"
        )]
    
    def _create_report_only_tasks(self, workflow_input: Dict[str, Any]) -> List[Any]:
        """Create tasks for reporting only."""
        if not self.report_agent:
            return []
        
        return [Task(
            description="Generate tactical report from provided data",
            agent=self.report_agent.agent if self.report_agent.agent else self.report_agent,
            expected_output="Comprehensive tactical analysis report"
        )]
    
    def execute_workflow(self, workflow_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the processing workflow."""
        try:
            workflow_type = workflow_input.get('workflow_type', 'full_pipeline')
            
            # Use CrewAI if available, otherwise fallback to manual execution
            if self.crew is not None:
                return self._execute_with_crewai(workflow_input)
            else:
                return self._execute_fallback(workflow_input)
                
        except Exception as e:
            logger.error(f"Error executing workflow: {e}")
            return self._error_result(f"Workflow execution failed: {str(e)}", "unknown")
    
    def _execute_with_crewai(self, workflow_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute workflow using CrewAI."""
        try:
            result = self.crew.kickoff(inputs=workflow_input)
            
            return {
                'success': True,
                'workflow_type': workflow_input.get('workflow_type', 'full_pipeline'),
                'execution_method': 'crewai',
                'result': result,
                'tasks_completed': len(self.tasks),
                'total_tasks': len(self.tasks)
            }
            
        except Exception as e:
            logger.error(f"Error in CrewAI execution: {e}")
            return self._error_result(f"CrewAI execution failed: {str(e)}", "crewai")
    
    def _execute_fallback(self, workflow_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute workflow using fallback manual orchestration."""
        workflow_type = workflow_input.get('workflow_type', 'full_pipeline')
        results = {}
        tasks_completed = 0
        
        try:
            # Execute based on workflow type
            if workflow_type in ["full_pipeline", "anonymize_classify", "anonymize_only"]:
                if self.anonymization_agent:
                    anonymize_result = self.anonymization_agent.execute_task(workflow_input)
                    results['anonymization'] = anonymize_result
                    tasks_completed += 1
                    
                    # Use anonymized text for next step
                    if anonymize_result.get('success') and 'anonymized_text' in anonymize_result:
                        workflow_input['text'] = anonymize_result['anonymized_text']
            
            if workflow_type in ["full_pipeline", "anonymize_classify", "classify_report", "classify_only"]:
                if self.classification_agent:
                    classify_result = self.classification_agent.execute_task(workflow_input)
                    results['classification'] = classify_result
                    tasks_completed += 1
                    
                    # Prepare data for reporting
                    if classify_result.get('success'):
                        workflow_input['classification_results'] = classify_result
            
            if workflow_type in ["full_pipeline", "classify_report", "report_only"]:
                if self.report_agent:
                    # Prepare report input
                    report_input = self._prepare_report_input(workflow_input, results)
                    report_result = self.report_agent.execute_task(report_input)
                    results['report'] = report_result
                    tasks_completed += 1
            
            return {
                'success': True,
                'workflow_type': workflow_type,
                'execution_method': 'fallback',
                'results': results,
                'tasks_completed': tasks_completed,
                'total_tasks': len(self.tasks)
            }
            
        except Exception as e:
            logger.error(f"Error in fallback execution: {e}")
            return self._error_result(f"Fallback execution failed: {str(e)}", "fallback")
    
    def _prepare_report_input(self, workflow_input: Dict[str, Any], results: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare input for report generation based on previous results."""
        report_input = workflow_input.copy()
        
        # If we have classification results, use them for reporting
        if 'classification' in results:
            classification_result = results['classification']
            if classification_result.get('success'):
                # Create a simple DataFrame for reporting if we have individual text
                if 'text' in workflow_input:
                    import pandas as pd
                    df_data = {
                        'text': [workflow_input['text']],
                        'subtemas_identificados': [classification_result.get('subtemas_identificados', [])]
                    }
                    
                    # Add binary columns
                    for col, val in classification_result.get('colunas_binarias', {}).items():
                        df_data[col] = [val]
                    
                    report_input['data'] = pd.DataFrame(df_data)
        
        return report_input
    
    def validate_inputs(self, inputs: Dict[str, Any]) -> bool:
        """Validate workflow inputs."""
        if not super().validate_inputs(inputs):
            return False
        
        workflow_type = inputs.get('workflow_type', 'full_pipeline')
        if workflow_type not in self.supported_workflows:
            logger.error(f"Unsupported workflow type: {workflow_type}")
            return False
        
        # Check if required agents are available for the workflow
        if workflow_type in ["full_pipeline", "anonymize_classify", "anonymize_only"]:
            if not self.anonymization_agent:
                logger.error("Anonymization agent required but not available")
                return False
        
        if workflow_type in ["full_pipeline", "anonymize_classify", "classify_report", "classify_only"]:
            if not self.classification_agent:
                logger.error("Classification agent required but not available")
                return False
        
        if workflow_type in ["full_pipeline", "classify_report", "report_only"]:
            if not self.report_agent:
                logger.error("Report agent required but not available")
                return False
        
        return True
    
    def get_supported_workflows(self) -> List[str]:
        """Get list of supported workflow types."""
        return self.supported_workflows.copy()
    
    def get_available_agents(self) -> Dict[str, bool]:
        """Get status of available agents."""
        return {
            'anonymization': self.anonymization_agent is not None,
            'classification': self.classification_agent is not None,
            'report': self.report_agent is not None
        }
