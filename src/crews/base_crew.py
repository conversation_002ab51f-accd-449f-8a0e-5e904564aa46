"""
Base Crew class for CrewAI integration in Simple Class.

This module provides the foundation for all CrewAI crews,
ensuring consistent configuration and workflow patterns.
"""

from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod
import logging
import time
from datetime import datetime

try:
    from crewai import Crew, Task
    from crewai.process import Process
except ImportError:
    # Fallback for when CrewAI is not installed
    Crew = None
    Task = None
    Process = None

    # Create mock Process class for fallback
    class MockProcess:
        sequential = "sequential"
        hierarchical = "hierarchical"

    if Process is None:
        Process = MockProcess

logger = logging.getLogger(__name__)


class BaseCrew(ABC):
    """
    Base class for all CrewAI crews in Simple Class.
    
    Provides common functionality and patterns for orchestrating
    multi-agent workflows in the Simple Class project.
    """
    
    def __init__(
        self,
        name: str,
        description: str,
        agents: List[Any],
        verbose: bool = True,
        process: Optional[Any] = None,
        **kwargs
    ):
        """
        Initialize base crew with common configuration.
        
        Args:
            name: Name of the crew
            description: Description of the crew's purpose
            agents: List of agents in the crew
            verbose: Whether to enable verbose output
            process: Process type for the crew (sequential, hierarchical, etc.)
            **kwargs: Additional crew configuration
        """
        self.name = name
        self.description = description
        self.agents = agents or []
        self.verbose = verbose
        self.process = process or (Process.sequential if Process else None)
        self.tasks = []
        self.execution_history = []
        
        # Initialize CrewAI crew if available
        if Crew is not None:
            self.crew = self._create_crew(**kwargs)
        else:
            self.crew = None
            logger.warning("CrewAI not installed. Crew will run in fallback mode.")
    
    def _create_crew(self, **kwargs) -> Optional[Any]:
        """Create the actual CrewAI crew instance."""
        if Crew is None:
            return None
        
        try:
            return Crew(
                agents=self.agents,
                tasks=self.tasks,
                verbose=self.verbose,
                process=self.process,
                **kwargs
            )
        except Exception as e:
            logger.error(f"Error creating crew: {e}")
            return None
    
    @abstractmethod
    def create_tasks(self, workflow_input: Dict[str, Any]) -> List[Any]:
        """
        Create tasks for the crew based on input.
        Must be implemented by subclasses.
        
        Args:
            workflow_input: Input data for the workflow
            
        Returns:
            List of CrewAI tasks
        """
        pass
    
    @abstractmethod
    def execute_workflow(self, workflow_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the crew workflow.
        Must be implemented by subclasses.
        
        Args:
            workflow_input: Input data for the workflow
            
        Returns:
            Workflow execution results
        """
        pass
    
    def kickoff(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Start the crew workflow execution.
        
        Args:
            inputs: Input data for the workflow
            
        Returns:
            Workflow execution results
        """
        start_time = time.time()
        execution_id = f"{self.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        logger.info(f"Starting crew workflow: {self.name} (ID: {execution_id})")
        
        try:
            # Validate inputs
            if not self.validate_inputs(inputs):
                return self._error_result("Invalid inputs provided", execution_id)
            
            # Create tasks for this execution
            self.tasks = self.create_tasks(inputs)
            
            if not self.tasks:
                return self._error_result("No tasks created for workflow", execution_id)
            
            # Update crew with new tasks
            if self.crew is not None:
                self.crew.tasks = self.tasks
            
            # Execute workflow
            result = self.execute_workflow(inputs)
            
            # Record execution
            execution_time = time.time() - start_time
            execution_record = {
                'execution_id': execution_id,
                'start_time': start_time,
                'execution_time': execution_time,
                'inputs': inputs,
                'result': result,
                'success': result.get('success', False)
            }
            self.execution_history.append(execution_record)
            
            logger.info(f"Crew workflow completed: {self.name} in {execution_time:.2f}s")
            
            # Add execution metadata to result
            result.update({
                'execution_id': execution_id,
                'execution_time': execution_time,
                'crew_name': self.name
            })
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Error in crew workflow {self.name}: {e}")
            
            error_result = self._error_result(f"Workflow execution failed: {str(e)}", execution_id)
            error_result['execution_time'] = execution_time
            
            return error_result
    
    def validate_inputs(self, inputs: Dict[str, Any]) -> bool:
        """
        Validate workflow inputs.
        Can be overridden by subclasses for specific validation.
        
        Args:
            inputs: Input data to validate
            
        Returns:
            True if inputs are valid, False otherwise
        """
        return inputs is not None and isinstance(inputs, dict)
    
    def _error_result(self, error_message: str, execution_id: str) -> Dict[str, Any]:
        """Create standardized error result."""
        return {
            'success': False,
            'error': error_message,
            'execution_id': execution_id,
            'crew_name': self.name,
            'results': {},
            'tasks_completed': 0,
            'total_tasks': len(self.tasks)
        }
    
    def get_execution_history(self) -> List[Dict[str, Any]]:
        """Get history of crew executions."""
        return self.execution_history.copy()
    
    def get_last_execution(self) -> Optional[Dict[str, Any]]:
        """Get the last execution record."""
        return self.execution_history[-1] if self.execution_history else None
    
    def clear_history(self):
        """Clear execution history."""
        self.execution_history.clear()
    
    def get_agent_count(self) -> int:
        """Get number of agents in the crew."""
        return len(self.agents)
    
    def get_task_count(self) -> int:
        """Get number of tasks in the current workflow."""
        return len(self.tasks)
    
    def __str__(self) -> str:
        """String representation of the crew."""
        return f"{self.__class__.__name__}(name='{self.name}', agents={len(self.agents)})"
    
    def __repr__(self) -> str:
        """Detailed representation of the crew."""
        return (
            f"{self.__class__.__name__}("
            f"name='{self.name}', "
            f"agents={len(self.agents)}, "
            f"tasks={len(self.tasks)}, "
            f"executions={len(self.execution_history)}"
            f")"
        )
