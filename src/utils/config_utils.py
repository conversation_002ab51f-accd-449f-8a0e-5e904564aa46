#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Módulo para gerenciar configurações e carregar variáveis de ambiente.
"""

import os
import yaml
import logging
from typing import Dict, Any
from dotenv import load_dotenv

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def carregar_configuracao(config_path: str = "config/config.yml") -> Dict[str, Any]:
    """
    Carrega as configurações do arquivo YAML.

    Args:
        config_path (str): Caminho para o arquivo de configuração.

    Returns:
        Dict[str, Any]: Dicionário com as configurações.
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logging.info(f"Configuração carregada de {config_path}: {config}")
        if not config.get("ASSUNTO"):
            logging.error("Chave 'ASSUNTO' não encontrada no config.yml")
            raise ValueError("Chave 'ASSUNTO' não encontrada no config.yml")
        if not config.get("LLM_MODEL"):
            logging.error("Chave 'LLM_MODEL' não encontrada no config.yml")
            raise ValueError("Chave 'LLM_MODEL' não encontrada no config.yml")
        return config
    except FileNotFoundError:
        logging.error(f"Arquivo de configuração {config_path} não encontrado.")
        # Tentar localização alternativa (para retrocompatibilidade)
        alt_path = "config.yml"
        logging.info(f"Tentando localização alternativa: {alt_path}")
        try:
            with open(alt_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logging.info(f"Configuração carregada de {alt_path}: {config}")
            return config
        except FileNotFoundError:
            logging.error(f"Arquivo de configuração também não encontrado em: {alt_path}")
            raise
    except yaml.YAMLError as e:
        logging.error(f"Erro ao parsear o arquivo de configuração {config_path}: {e}")
        raise
    except ValueError as e:
        raise

def carregar_variaveis_ambiente() -> Dict[str, str]:
    """
    Carrega variáveis de ambiente do arquivo .env e verifica as chaves de API necessárias.

    Returns:
        Dict[str, str]: Dicionário com as variáveis carregadas
    """
    load_dotenv()
    
    env_vars = {
        "TOGETHER_API_KEY": os.getenv("TOGETHER_API_KEY"),
        "ANTHROPIC_API_KEY": os.getenv("ANTHROPIC_API_KEY")
    }
    
    # Verificar se a chave Together API foi carregada
    if not env_vars["TOGETHER_API_KEY"]:
        logging.warning("A variável de ambiente TOGETHER_API_KEY não foi definida.")
        
    # Verificar se a chave Anthropic API foi carregada (apenas logando)
    if not env_vars["ANTHROPIC_API_KEY"]:
        logging.info("A variável de ambiente ANTHROPIC_API_KEY não foi definida (necessária apenas para geração de relatórios).")
    
    return env_vars 