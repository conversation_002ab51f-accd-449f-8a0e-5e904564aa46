"""
FastAPI dependencies for authentication, rate limiting, and common functionality.
"""

import logging
from typing import Optional
from datetime import datetime, timedelta
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from supabase import Client

from .config import settings
from .database.client import get_supabase_client

logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer()


class User:
    """Simple User class for batch processing."""
    
    def __init__(self, user_data: dict):
        self.id = user_data.get('id')
        self.email = user_data.get('email')
        self.full_name = user_data.get('full_name', '')
        self.organization = user_data.get('organization', '')
        self.is_active = user_data.get('is_active', True)
        self.user_metadata = user_data.get('user_metadata', {})
        self.role = user_data.get('role', 'user')  # Default to 'user' role


class RateLimiter:
    """Simple in-memory rate limiter."""
    
    def __init__(self):
        self.requests = {}
    
    def is_allowed(self, key: str, limit: int = 100, window: int = 3600) -> bool:
        """Check if request is allowed based on rate limit."""
        now = datetime.now()
        
        # Clean old entries
        cutoff = now - timedelta(seconds=window)
        self.requests = {
            k: [req for req in v if req > cutoff]
            for k, v in self.requests.items()
        }
        
        # Check current requests
        if key not in self.requests:
            self.requests[key] = []
        
        if len(self.requests[key]) >= limit:
            return False
        
        self.requests[key].append(now)
        return True


# Global rate limiter instance
rate_limiter = RateLimiter()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    supabase: Client = Depends(get_supabase_client)
) -> User:
    """
    Get current authenticated user from JWT token.

    Args:
        credentials: JWT token from Authorization header
        supabase: Supabase client instance

    Returns:
        User object with user information

    Raises:
        HTTPException: If token is invalid or user not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # Decode JWT token
        payload = jwt.decode(
            credentials.credentials,
            settings.secret_key,
            algorithms=[settings.algorithm]
        )
        user_id: str = payload.get("sub")
        email: str = payload.get("email")

        if user_id is None:
            raise credentials_exception

    except JWTError as e:
        logger.warning(f"JWT decode error: {e}")
        raise credentials_exception

    # Development mode: Accept test tokens
    if user_id == "test-user-123":
        logger.info("Using test user for development")
        return User({
            "id": user_id,
            "email": email or "<EMAIL>",
            "full_name": "Test User",
            "organization": "Test Organization",
            "is_active": True,
            "user_metadata": {"test_user": True},
            "role": "admin"  # Test user has admin role
        })

    # Try to get user from Supabase Auth
    try:
        if supabase:
            # For Supabase JWT tokens, we can validate directly
            user_response = supabase.auth.get_user(credentials.credentials)
            if user_response.user:
                return User({
                    "id": user_response.user.id,
                    "email": user_response.user.email,
                    "full_name": user_response.user.user_metadata.get("full_name", ""),
                    "organization": user_response.user.user_metadata.get("organization", ""),
                    "is_active": True,
                    "user_metadata": user_response.user.user_metadata,
                    "role": user_response.user.user_metadata.get("role", "user")
                })

        # Fallback: Use JWT payload data (for custom tokens)
        logger.info(f"Using JWT payload for user {user_id}")
        return User({
            "id": user_id,
            "email": email or "<EMAIL>",
            "full_name": payload.get("full_name", ""),
            "organization": payload.get("organization", ""),
            "is_active": True,
            "user_metadata": {"jwt_user": True},
            "role": payload.get("role", "user")
        })

    except Exception as e:
        logger.error(f"Error validating user: {e}")
        raise credentials_exception


async def check_rate_limit(request: Request) -> bool:
    """
    Check rate limit for current request.
    
    Args:
        request: FastAPI request object
        
    Returns:
        True if request is allowed
        
    Raises:
        HTTPException: If rate limit exceeded
    """
    client_ip = request.client.host
    
    if not rate_limiter.is_allowed(
        client_ip,
        settings.rate_limit_requests,
        settings.rate_limit_window
    ):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded"
        )
    
    return True


async def get_supabase_dependency() -> Client:
    """
    Dependency to get Supabase client.
    
    Returns:
        Supabase client instance
        
    Raises:
        HTTPException: If Supabase client is not available
    """
    client = get_supabase_client()
    if not client:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database service unavailable"
        )
    return client


async def get_rate_limiter(request: Request) -> bool:
    """
    Rate limiter dependency for API endpoints.
    
    Args:
        request: FastAPI request object
        
    Returns:
        True if request is allowed
        
    Raises:
        HTTPException: If rate limit exceeded
    """
    return await check_rate_limit(request)
