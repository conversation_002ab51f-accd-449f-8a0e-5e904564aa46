"""
Data models for PDF batch processing functionality.

This module defines the core data structures used throughout the PDF batch
classification system, including text chunks, classification results, and
batch processing outcomes.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Protocol
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, validator


class JobStatus(str, Enum):
    """Status of a batch processing job."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ProcessingError(str, Enum):
    """Types of processing errors that can occur."""
    PDF_EXTRACTION_ERROR = "pdf_extraction_error"
    ANONYMIZATION_ERROR = "anonymization_error"
    CLASSIFICATION_ERROR = "classification_error"
    AGGREGATION_ERROR = "aggregation_error"


@dataclass
class TextChunk:
    """
    Represents a chunk of text extracted from a PDF with metadata.
    
    Attributes:
        text: The actual text content of the chunk
        chunk_id: Unique identifier for this chunk within the PDF
        page_start: Starting page number (1-indexed)
        page_end: Ending page number (1-indexed)
        char_start: Starting character position in original text
        char_end: Ending character position in original text
        pdf_path: Path to the source PDF file
        token_count: Approximate number of tokens in the chunk
    """
    text: str
    chunk_id: int
    page_start: int
    page_end: int
    char_start: int
    char_end: int
    pdf_path: str
    token_count: Optional[int] = None
    
    def __post_init__(self) -> None:
        """Validate chunk data after initialization."""
        if self.page_start > self.page_end:
            raise ValueError("page_start cannot be greater than page_end")
        if self.char_start > self.char_end:
            raise ValueError("char_start cannot be greater than char_end")
        if not self.text.strip():
            raise ValueError("text cannot be empty")


@dataclass
class ChunkResult:
    """
    Result of classifying a single text chunk.
    
    Attributes:
        chunk: The original text chunk that was classified
        subtemas: List of identified subtemas for this chunk
        confidence_scores: Confidence score for each subtema (0.0-1.0)
        evidence_texts: Text evidence for each subtema (max 200 chars)
        processing_time: Time taken to process this chunk in seconds
        error: Optional error information if processing failed
    """
    chunk: TextChunk
    subtemas: List[str] = field(default_factory=list)
    confidence_scores: Dict[str, float] = field(default_factory=dict)
    evidence_texts: Dict[str, str] = field(default_factory=dict)
    processing_time: float = 0.0
    error: Optional[str] = None
    
    def __post_init__(self) -> None:
        """Validate chunk result data."""
        # Ensure all subtemas have confidence scores
        for subtema in self.subtemas:
            if subtema not in self.confidence_scores:
                raise ValueError(f"Missing confidence score for subtema: {subtema}")
        
        # Validate confidence scores are between 0 and 1
        for subtema, score in self.confidence_scores.items():
            if not 0.0 <= score <= 1.0:
                raise ValueError(f"Confidence score for {subtema} must be between 0.0 and 1.0")
        
        # Validate evidence text length
        for subtema, evidence in self.evidence_texts.items():
            if len(evidence) > 200:
                raise ValueError(f"Evidence text for {subtema} exceeds 200 characters")


@dataclass
class PDFClassificationResult:
    """
    Final classification result for a single PDF document.
    
    Attributes:
        pdf_path: Path to the processed PDF file
        filename: Name of the PDF file
        subtemas_finais: Final list of identified subtemas (max 3)
        confidence_scores: Aggregated confidence scores for each subtema
        evidence_texts: Consolidated evidence texts for each subtema
        total_pages: Total number of pages in the PDF
        total_chunks: Number of chunks the PDF was split into
        processing_time: Total time to process this PDF in seconds
        anonymization_stats: Count of anonymized entities by type
        area: Classification area (EDUCACAO, SAUDE, MEIO_AMBIENTE)
        timestamp: When the processing was completed
        error: Optional error information if processing failed
    """
    pdf_path: str
    filename: str
    subtemas_finais: List[str] = field(default_factory=list)
    confidence_scores: Dict[str, float] = field(default_factory=dict)
    evidence_texts: Dict[str, str] = field(default_factory=dict)
    total_pages: int = 0
    total_chunks: int = 0
    processing_time: float = 0.0
    anonymization_stats: Dict[str, int] = field(default_factory=dict)
    area: str = ""
    timestamp: Optional[datetime] = None
    error: Optional[str] = None
    
    def __post_init__(self) -> None:
        """Validate PDF classification result."""
        if len(self.subtemas_finais) > 3:
            raise ValueError("Maximum 3 subtemas allowed per PDF")
        
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
        
        # Validate confidence scores
        for subtema, score in self.confidence_scores.items():
            if not 0.0 <= score <= 1.0:
                raise ValueError(f"Confidence score for {subtema} must be between 0.0 and 1.0")


@dataclass
class BatchResult:
    """
    Result of processing a batch of PDF files.
    
    Attributes:
        job_id: Unique identifier for this batch job
        folder_path: Path to the folder containing PDFs
        area: Classification area used for processing
        pdf_results: List of individual PDF classification results
        total_pdfs: Total number of PDFs found in the folder
        successful_pdfs: Number of successfully processed PDFs
        failed_pdfs: List of PDF filenames that failed processing
        total_processing_time: Total time for the entire batch in seconds
        csv_output_path: Path to the generated CSV results file
        timestamp: When the batch processing was completed
        status: Current status of the batch job
        error_summary: Summary of errors encountered during processing
    """
    job_id: str
    folder_path: str
    area: str
    pdf_results: List[PDFClassificationResult] = field(default_factory=list)
    total_pdfs: int = 0
    successful_pdfs: int = 0
    failed_pdfs: List[str] = field(default_factory=list)
    total_processing_time: float = 0.0
    csv_output_path: str = ""
    timestamp: Optional[datetime] = None
    status: JobStatus = JobStatus.PENDING
    error_summary: Dict[str, int] = field(default_factory=dict)
    
    def __post_init__(self) -> None:
        """Validate batch result data."""
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
        
        # Update counters based on results
        self.successful_pdfs = len([r for r in self.pdf_results if r.error is None])
        self.failed_pdfs = [r.filename for r in self.pdf_results if r.error is not None]
        
        if self.total_pdfs == 0 and self.pdf_results:
            self.total_pdfs = len(self.pdf_results)


# Pydantic models for API requests and responses

class BatchProcessingRequest(BaseModel):
    """Request model for starting a PDF batch processing job."""
    folder_path: str = Field(..., description="Path to folder containing PDF files")
    area: str = Field(..., description="Classification area (EDUCACAO, SAUDE, MEIO_AMBIENTE)")
    max_subtemas: int = Field(default=3, ge=1, le=5, description="Maximum subtemas per PDF")
    chunk_size: int = Field(default=1000, ge=500, le=2000, description="Token size for text chunks")
    overlap: int = Field(default=200, ge=0, le=500, description="Token overlap between chunks")
    parallel_workers: int = Field(default=4, ge=1, le=10, description="Number of parallel workers")
    
    @validator('area')
    def validate_area(cls, v: str) -> str:
        """Validate that area is one of the supported values."""
        valid_areas = ['EDUCACAO', 'SAUDE', 'MEIO_AMBIENTE']
        if v not in valid_areas:
            raise ValueError(f'Area must be one of: {", ".join(valid_areas)}')
        return v


class JobStatusResponse(BaseModel):
    """Response model for job status queries."""
    job_id: str
    status: JobStatus
    progress: float = Field(ge=0.0, le=1.0, description="Progress as decimal (0.0-1.0)")
    current_file: Optional[str] = None
    processed_files: int = 0
    total_files: int = 0
    estimated_completion: Optional[datetime] = None
    error_message: Optional[str] = None


class BatchResultSummary(BaseModel):
    """Summary response model for completed batch jobs."""
    job_id: str
    csv_download_url: str
    summary: Dict[str, Any] = Field(default_factory=dict)
    processing_stats: Dict[str, Any] = Field(default_factory=dict)


# Protocol interfaces for batch processing components

class TextExtractorProtocol(Protocol):
    """Interface for PDF text extraction components."""
    
    def extract_and_chunk(self, pdf_path: str) -> List[TextChunk]:
        """Extract text from PDF and split into chunks."""
        ...
    
    def validate_pdf(self, pdf_path: str) -> bool:
        """Validate that PDF can be processed."""
        ...


class ChunkClassifierProtocol(Protocol):
    """Interface for chunk classification components."""
    
    async def classify_chunks(self, chunks: List[TextChunk], area: str) -> List[ChunkResult]:
        """Classify a list of text chunks."""
        ...
    
    async def classify_single_chunk(self, chunk: TextChunk, area: str) -> ChunkResult:
        """Classify a single text chunk."""
        ...


class ResultAggregatorProtocol(Protocol):
    """Interface for result aggregation components."""
    
    def aggregate_pdf_results(self, chunk_results: List[ChunkResult]) -> PDFClassificationResult:
        """Aggregate chunk results into final PDF classification."""
        ...
    
    def calculate_confidence_scores(self, chunk_results: List[ChunkResult]) -> Dict[str, float]:
        """Calculate aggregated confidence scores."""
        ...


class BatchJobManagerProtocol(Protocol):
    """Interface for batch job management components."""
    
    def create_job(self, folder_path: str, area: str, user_id: str) -> str:
        """Create a new batch processing job."""
        ...
    
    def get_job_status(self, job_id: str) -> JobStatusResponse:
        """Get current status of a job."""
        ...
    
    def update_progress(self, job_id: str, progress: float, current_file: str) -> None:
        """Update job progress."""
        ...
    
    def complete_job(self, job_id: str, results: BatchResult) -> None:
        """Mark job as completed with results."""
        ...


class CSVExportProtocol(Protocol):
    """Interface for CSV export functionality."""
    
    def export_batch_results(self, batch_result: BatchResult) -> str:
        """Export batch results to CSV file."""
        ...
    
    def generate_csv_headers(self, area: str) -> List[str]:
        """Generate CSV headers for the specified area."""
        ...