"""
Pydantic models for file upload and management.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field, field_validator, model_validator
from uuid import UUID
from enum import Enum


class FileType(str, Enum):
    """Supported file types."""
    CSV = "text/csv"
    PDF = "application/pdf"
    TXT = "text/plain"
    EXCEL = "application/vnd.ms-excel"
    EXCEL_XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"


class FileStatus(str, Enum):
    """File processing status."""
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    PROCESSED = "processed"
    ERROR = "error"
    DELETED = "deleted"


class FileSource(str, Enum):
    """Source of the file."""
    UPLOAD = "upload"
    GENERATED = "generated"
    IMPORTED = "imported"


class FileMetadata(BaseModel):
    """Model for file metadata."""
    description: Optional[str] = Field(None, max_length=500, description="File description")
    tags: Optional[List[str]] = Field(None, max_items=10, description="File tags")
    source: FileSource = Field(default=FileSource.UPLOAD, description="Source of the file")
    original_filename: Optional[str] = Field(None, description="Original filename before processing")
    encoding: Optional[str] = Field(None, description="File encoding (for text files)")
    delimiter: Optional[str] = Field(None, description="CSV delimiter character")
    has_header: Optional[bool] = Field(None, description="Whether CSV has header row")
    column_count: Optional[int] = Field(None, ge=0, description="Number of columns (for CSV)")
    row_count: Optional[int] = Field(None, ge=0, description="Number of rows (for CSV)")
    page_count: Optional[int] = Field(None, ge=1, description="Number of pages (for PDF)")
    additional_info: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    @field_validator('tags')
    @classmethod
    def validate_tags(cls, v):
        if v is not None:
            # Remove duplicates and empty tags
            cleaned_tags = list(set(tag.strip() for tag in v if tag.strip()))
            return cleaned_tags
        return v


class FileUploadRequest(BaseModel):
    """Request model for file upload (used with form data)."""
    metadata: Optional[FileMetadata] = Field(None, description="File metadata")
    process_immediately: bool = Field(default=False, description="Start processing immediately after upload")
    overwrite_existing: bool = Field(default=False, description="Overwrite file if it already exists")


class FileUploadResponse(BaseModel):
    """Response model for file upload."""
    file_id: UUID = Field(..., description="Unique identifier for the uploaded file")
    filename: str = Field(..., description="Name of the uploaded file")
    file_path: str = Field(..., description="Storage path of the file")
    file_type: FileType = Field(..., description="MIME type of the file")
    file_size: int = Field(..., ge=0, description="File size in bytes")
    metadata: Optional[FileMetadata] = Field(None, description="File metadata")
    status: FileStatus = Field(default=FileStatus.UPLOADED, description="Current file status")
    upload_url: Optional[str] = Field(None, description="Direct access URL")
    download_url: Optional[str] = Field(None, description="Download URL")
    created_at: datetime = Field(..., description="Upload timestamp")
    success: bool = Field(..., description="Whether upload was successful")
    error: Optional[str] = Field(None, description="Error message if upload failed")


class FileProcessingRequest(BaseModel):
    """Request model for file processing."""
    file_id: UUID = Field(..., description="ID of the file to process")
    processing_type: str = Field(..., description="Type of processing to perform")
    options: Optional[Dict[str, Any]] = Field(None, description="Processing options")


class FileProcessingResponse(BaseModel):
    """Response model for file processing."""
    file_id: UUID = Field(..., description="ID of the processed file")
    processing_type: str = Field(..., description="Type of processing performed")
    status: FileStatus = Field(..., description="Processing status")
    result: Optional[Dict[str, Any]] = Field(None, description="Processing results")
    processing_time: Optional[float] = Field(None, ge=0, description="Processing time in seconds")
    started_at: datetime = Field(..., description="Processing start time")
    completed_at: Optional[datetime] = Field(None, description="Processing completion time")
    success: bool = Field(..., description="Whether processing was successful")
    error: Optional[str] = Field(None, description="Error message if processing failed")


class FileListItem(BaseModel):
    """Model for file list items."""
    file_id: UUID = Field(..., description="Unique identifier for the file")
    filename: str = Field(..., description="Name of the file")
    file_type: FileType = Field(..., description="MIME type of the file")
    file_size: int = Field(..., ge=0, description="File size in bytes")
    status: FileStatus = Field(..., description="Current file status")
    created_at: datetime = Field(..., description="Upload timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    metadata: Optional[FileMetadata] = Field(None, description="File metadata")
    download_url: Optional[str] = Field(None, description="Download URL")
    processing_jobs: Optional[int] = Field(None, ge=0, description="Number of associated processing jobs")


class FileListResponse(BaseModel):
    """Response model for file listing."""
    files: List[FileListItem] = Field(..., description="List of files")
    total_count: int = Field(..., ge=0, description="Total number of files")
    total_size: int = Field(..., ge=0, description="Total size of all files in bytes")
    page: int = Field(default=1, ge=1, description="Current page number")
    page_size: int = Field(default=20, ge=1, le=100, description="Number of files per page")
    total_pages: int = Field(..., ge=1, description="Total number of pages")
    filters_applied: Optional[Dict[str, Any]] = Field(None, description="Filters applied to the list")

    @model_validator(mode='after')
    def validate_pagination(self):
        expected_pages = (self.total_count + self.page_size - 1) // self.page_size
        if expected_pages == 0:
            expected_pages = 1
        if self.total_pages != expected_pages:
            raise ValueError('total_pages calculation is incorrect')
        return self


class FileDetailsResponse(BaseModel):
    """Response model for detailed file information."""
    file_id: UUID = Field(..., description="Unique identifier for the file")
    filename: str = Field(..., description="Name of the file")
    file_path: str = Field(..., description="Storage path of the file")
    file_type: FileType = Field(..., description="MIME type of the file")
    file_size: int = Field(..., ge=0, description="File size in bytes")
    status: FileStatus = Field(..., description="Current file status")
    metadata: Optional[FileMetadata] = Field(None, description="File metadata")
    created_at: datetime = Field(..., description="Upload timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    download_url: Optional[str] = Field(None, description="Download URL")
    preview_url: Optional[str] = Field(None, description="Preview URL (for supported file types)")
    processing_history: Optional[List[FileProcessingResponse]] = Field(
        None, description="History of processing jobs"
    )
    access_count: int = Field(default=0, ge=0, description="Number of times file was accessed")
    last_accessed: Optional[datetime] = Field(None, description="Last access timestamp")


class FileDeleteResponse(BaseModel):
    """Response model for file deletion."""
    file_id: UUID = Field(..., description="ID of the deleted file")
    filename: str = Field(..., description="Name of the deleted file")
    deleted_at: datetime = Field(..., description="Deletion timestamp")
    success: bool = Field(..., description="Whether deletion was successful")
    error: Optional[str] = Field(None, description="Error message if deletion failed")


class FileStatsResponse(BaseModel):
    """Response model for file statistics."""
    total_files: int = Field(..., ge=0, description="Total number of files")
    total_size: int = Field(..., ge=0, description="Total size in bytes")
    files_by_type: Dict[FileType, int] = Field(..., description="Count of files by type")
    files_by_status: Dict[FileStatus, int] = Field(..., description="Count of files by status")
    average_file_size: float = Field(..., ge=0, description="Average file size in bytes")
    largest_file_size: int = Field(..., ge=0, description="Size of largest file")
    oldest_file_date: Optional[datetime] = Field(None, description="Date of oldest file")
    newest_file_date: Optional[datetime] = Field(None, description="Date of newest file")
