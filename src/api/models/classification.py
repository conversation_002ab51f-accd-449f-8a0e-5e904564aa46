"""
Pydantic models for classification requests and responses.
"""

from typing import List, Dict, Optional
from pydantic import BaseModel, Field, field_validator, model_validator
from enum import Enum


class ClassificationArea(str, Enum):
    """Supported areas for classification."""
    EDUCACAO = "EDUCACAO"
    SAUDE = "SAUDE"
    MEIO_AMBIENTE = "MEIO_AMBIENTE"


class ClassificationType(str, Enum):
    """Types of classification."""
    MULTILABEL = "multilabel"
    UNILABEL = "unilabel"


class UnilabelDimension(str, Enum):
    """Dimensions for unilabel classification."""
    TIPO_UNIDADE = "TIPO_UNIDADE"
    CRE = "CRE"


class TipoUnidade(str, Enum):
    """Types of educational units."""
    ESTADUAL = "Estadual"
    MUNICIPAL = "Municipal"
    PRIVADA = "Privada"
    NAO_SE_APLICA = "Não se aplica"


class CRE(str, Enum):
    """Coordenadorias Regionais de Educação."""
    CRE_1 = "1ª CRE"
    CRE_2 = "2ª CRE"
    CRE_3 = "3ª CRE"
    CRE_4 = "4ª CRE"
    CRE_5 = "5ª CRE"
    CRE_6 = "6ª CRE"
    CRE_7 = "7ª CRE"
    CRE_8 = "8ª CRE"
    CRE_9 = "9ª CRE"
    CRE_10 = "10ª CRE"
    CRE_11 = "11ª CRE"
    NAO_SE_APLICA = "Não se aplica"


class ClassificationRequest(BaseModel):
    """Request model for text classification."""
    text: str = Field(..., min_length=1, max_length=10000, description="Text to classify")
    area: ClassificationArea = Field(..., description="Classification area")
    max_subtemas: int = Field(default=3, ge=1, le=5, description="Maximum number of subtemas to return")
    model_name: Optional[str] = Field(None, description="Override default model")

    @field_validator('text')
    @classmethod
    def validate_text(cls, v):
        if not v.strip():
            raise ValueError('Text cannot be empty or only whitespace')
        return v.strip()

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "summary": "Classificação de Educação",
                    "description": "Exemplo de texto sobre problemas na educação",
                    "value": {
                        "text": "A merenda escolar da Escola Municipal João da Silva está com problemas. As crianças estão recebendo comida estragada e muitas estão passando mal.",
                        "area": "EDUCACAO",
                        "max_subtemas": 3
                    }
                },
                {
                    "summary": "Classificação de Saúde",
                    "description": "Exemplo de texto sobre problemas na saúde",
                    "value": {
                        "text": "O posto de saúde da Tijuca está com demora no atendimento. Os pacientes estão esperando mais de 4 horas para serem atendidos.",
                        "area": "SAUDE",
                        "max_subtemas": 2
                    }
                }
            ]
        }


class UnilabelClassificationRequest(BaseModel):
    """Request model for unilabel classification."""
    text: str = Field(..., min_length=1, max_length=10000, description="Text to classify")
    dimension: UnilabelDimension = Field(..., description="Classification dimension")
    model_name: Optional[str] = Field(None, description="Override default model")

    @field_validator('text')
    @classmethod
    def validate_text(cls, v):
        if not v.strip():
            raise ValueError('Text cannot be empty or only whitespace')
        return v.strip()


class ClassificationResponse(BaseModel):
    """Response model for text classification."""
    text: str = Field(..., description="Original text that was classified")
    area: ClassificationArea = Field(..., description="Classification area used")
    subtemas: List[str] = Field(default=[], description="Identified subtemas")
    confidence_scores: Optional[Dict[str, float]] = Field(None, description="Confidence scores for each subtema")
    model_used: str = Field(..., description="Model used for classification")
    processing_time: Optional[float] = Field(None, ge=0, description="Processing time in seconds")
    success: bool = Field(..., description="Whether classification was successful")
    error: Optional[str] = Field(None, description="Error message if classification failed")

    @model_validator(mode='after')
    def validate_subtemas_scores(self):
        if self.confidence_scores and set(self.subtemas) != set(self.confidence_scores.keys()):
            raise ValueError('Confidence scores must match identified subtemas')
        return self

    class Config:
        json_schema_extra = {
            "example": {
                "text": "A merenda escolar da Escola Municipal João da Silva está com problemas.",
                "area": "EDUCACAO",
                "subtemas": ["ALIMENTACAO_ESCOLAR", "INFRAESTRUTURA", "CONTROLE_INTERNO"],
                "confidence_scores": {
                    "ALIMENTACAO_ESCOLAR": 0.95,
                    "INFRAESTRUTURA": 0.87,
                    "CONTROLE_INTERNO": 0.72
                },
                "model_used": "meta-llama/Llama-3.3-70B-Instruct-Turbo",
                "processing_time": 2.34,
                "success": True,
                "error": None
            }
        }


class UnilabelClassificationResponse(BaseModel):
    """Response model for unilabel classification."""
    text: str = Field(..., description="Original text that was classified")
    dimension: UnilabelDimension = Field(..., description="Classification dimension")
    classification: Optional[str] = Field(None, description="Classification result")
    confidence_score: Optional[float] = Field(None, ge=0, le=1, description="Confidence score")
    model_used: str = Field(..., description="Model used for classification")
    processing_time: Optional[float] = Field(None, ge=0, description="Processing time in seconds")
    success: bool = Field(..., description="Whether classification was successful")
    error: Optional[str] = Field(None, description="Error message if classification failed")


class BatchClassificationRequest(BaseModel):
    """Request model for batch classification."""
    texts: List[str] = Field(..., min_items=1, max_items=100, description="List of texts to classify")
    area: ClassificationArea = Field(..., description="Classification area")
    classification_type: ClassificationType = Field(
        default=ClassificationType.MULTILABEL,
        description="Type of classification"
    )
    max_subtemas: int = Field(default=3, ge=1, le=5, description="Maximum number of subtemas per text")
    model_name: Optional[str] = Field(None, description="Override default model")

    @field_validator('texts')
    @classmethod
    def validate_texts(cls, v):
        cleaned_texts = []
        for text in v:
            if not text.strip():
                raise ValueError('All texts must be non-empty')
            cleaned_texts.append(text.strip())
        return cleaned_texts


class BatchClassificationResponse(BaseModel):
    """Response model for batch classification."""
    results: List[ClassificationResponse] = Field(..., description="Classification results for each text")
    total_processed: int = Field(..., ge=0, description="Total number of texts processed")
    success_count: int = Field(..., ge=0, description="Number of successful classifications")
    error_count: int = Field(..., ge=0, description="Number of failed classifications")
    total_processing_time: Optional[float] = Field(None, ge=0, description="Total processing time in seconds")
    average_processing_time: Optional[float] = Field(None, ge=0, description="Average processing time per text")

    @model_validator(mode='after')
    def validate_counts(self):
        if self.total_processed != len(self.results):
            raise ValueError('total_processed must match the number of results')
        if self.success_count + self.error_count != self.total_processed:
            raise ValueError('success_count + error_count must equal total_processed')
        return self


class SubtemasResponse(BaseModel):
    """Response model for subtemas listing."""
    area: ClassificationArea = Field(..., description="Classification area")
    subtemas: List[str] = Field(..., description="Available subtemas for the area")
    descriptions: Optional[Dict[str, str]] = Field(None, description="Descriptions of each subtema")
    total_count: int = Field(..., ge=0, description="Total number of subtemas")

    @model_validator(mode='after')
    def validate_subtemas_count(self):
        if self.total_count != len(self.subtemas):
            raise ValueError('total_count must match the number of subtemas')
        return self
