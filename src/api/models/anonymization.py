"""
Pydantic models for anonymization requests and responses.
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field, field_validator, model_validator
from enum import Enum


class SupportedLanguage(str, Enum):
    """Supported languages for anonymization."""
    PORTUGUESE = "pt"
    ENGLISH = "en"
    SPANISH = "es"


class EntityType(str, Enum):
    """Supported entity types for anonymization."""
    PERSON = "PERSON"
    CPF = "CPF"
    EMAIL = "EMAIL"
    PHONE = "PHONE"
    ENDERECO = "ENDERECO"
    ESCOLA = "ESCOLA"
    CREDIT_CARD = "CREDIT_CARD"
    IBAN_CODE = "IBAN_CODE"
    IP_ADDRESS = "IP_ADDRESS"
    DATE_TIME = "DATE_TIME"


class AnonymizationRequest(BaseModel):
    """Request model for text anonymization."""
    text: str = Field(..., min_length=1, max_length=50000, description="Text to anonymize")
    language: SupportedLanguage = Field(default=SupportedLanguage.PORTUGUESE, description="Language code")
    entities_to_anonymize: Optional[List[EntityType]] = Field(
        default=None,
        description="Specific entity types to anonymize. If None, all supported entities will be processed"
    )

    @field_validator('text')
    @classmethod
    def validate_text(cls, v):
        if not v.strip():
            raise ValueError('Text cannot be empty or only whitespace')
        return v.strip()


class EntityFound(BaseModel):
    """Model for detected entities."""
    entity_type: EntityType = Field(..., description="Type of entity detected")
    start: int = Field(..., ge=0, description="Start position in text")
    end: int = Field(..., ge=0, description="End position in text")
    score: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    text: str = Field(..., description="Original text of the entity")
    anonymized_text: Optional[str] = Field(None, description="Anonymized replacement text")

    @model_validator(mode='after')
    def validate_end_greater_than_start(self):
        if self.end <= self.start:
            raise ValueError('End position must be greater than start position')
        return self


class AnonymizationResponse(BaseModel):
    """Response model for text anonymization."""
    anonymized_text: str = Field(..., description="Text with entities anonymized")
    original_text: str = Field(..., description="Original input text")
    entities_found: List[EntityFound] = Field(default=[], description="List of detected entities")
    entities_count: int = Field(..., ge=0, description="Total number of entities found")
    processing_time: Optional[float] = Field(None, ge=0, description="Processing time in seconds")
    language_used: SupportedLanguage = Field(..., description="Language used for processing")
    success: bool = Field(..., description="Whether anonymization was successful")
    error: Optional[str] = Field(None, description="Error message if anonymization failed")

    @model_validator(mode='after')
    def validate_entities_count(self):
        if self.entities_count != len(self.entities_found):
            raise ValueError('entities_count must match the length of entities_found list')
        return self


class PDFAnonymizationRequest(BaseModel):
    """Request model for PDF anonymization."""
    file_path: str = Field(..., description="Path to PDF file or file ID")
    language: SupportedLanguage = Field(default=SupportedLanguage.PORTUGUESE, description="Language code")
    entities_to_anonymize: Optional[List[EntityType]] = Field(
        default=None,
        description="Specific entity types to anonymize"
    )
    extract_text_only: bool = Field(default=False, description="Only extract text without anonymization")

    @field_validator('file_path')
    @classmethod
    def validate_file_path(cls, v):
        if not v.strip():
            raise ValueError('File path cannot be empty')
        # Basic validation - could be enhanced with actual file existence check
        if not (v.endswith('.pdf') or v.startswith('file_')):
            raise ValueError('File path must be a PDF file or valid file ID')
        return v.strip()


class PDFAnonymizationResponse(BaseModel):
    """Response model for PDF anonymization."""
    anonymized_text: str = Field(..., description="Anonymized text extracted from PDF")
    original_text: str = Field(..., description="Original text extracted from PDF")
    entities_found: List[EntityFound] = Field(default=[], description="List of detected entities")
    entities_count: int = Field(..., ge=0, description="Total number of entities found")
    page_count: Optional[int] = Field(None, ge=1, description="Number of pages in PDF")
    processing_time: Optional[float] = Field(None, ge=0, description="Processing time in seconds")
    language_used: SupportedLanguage = Field(..., description="Language used for processing")
    success: bool = Field(..., description="Whether PDF anonymization was successful")
    error: Optional[str] = Field(None, description="Error message if processing failed")

    @model_validator(mode='after')
    def validate_entities_count(self):
        if self.entities_count != len(self.entities_found):
            raise ValueError('entities_count must match the length of entities_found list')
        return self


class SupportedEntitiesResponse(BaseModel):
    """Response model for supported entities endpoint."""
    entities: List[EntityType] = Field(..., description="List of supported entity types")
    descriptions: Dict[EntityType, str] = Field(..., description="Description of each entity type")

    class Config:
        schema_extra = {
            "example": {
                "entities": ["PERSON", "CPF", "EMAIL"],
                "descriptions": {
                    "PERSON": "Names of people",
                    "CPF": "Brazilian CPF numbers",
                    "EMAIL": "Email addresses"
                }
            }
        }
