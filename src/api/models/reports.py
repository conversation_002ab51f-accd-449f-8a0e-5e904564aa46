"""
Pydantic models for report generation requests and responses.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field, field_validator, model_validator
from uuid import UUID
from enum import Enum

from .classification import ClassificationArea


class ReportType(str, Enum):
    """Types of reports that can be generated."""
    INDIVIDUAL = "individual"
    OVERVIEW = "overview"
    SPECIALIZED = "specialized"
    EXECUTIVE = "executive"


class ReportTemplate(str, Enum):
    """Available report templates."""
    STANDARD = "standard"
    DETAILED = "detailed"
    SUMMARY = "summary"
    TECHNICAL = "technical"


class ReportFormat(str, Enum):
    """Output formats for reports."""
    MARKDOWN = "markdown"
    HTML = "html"
    PDF = "pdf"
    TEXT = "text"


class DataSourceType(str, Enum):
    """Types of data sources for reports."""
    FILE_ID = "file_id"
    JOB_ID = "job_id"
    CSV_PATH = "csv_path"


class ReportRequest(BaseModel):
    """Request model for report generation."""
    report_type: ReportType = Field(..., description="Type of report to generate")
    subtema: Optional[str] = Field(None, description="Specific subtema for individual reports")
    area: Optional[ClassificationArea] = Field(None, description="Area for overview reports")
    data_source: str = Field(..., description="Source of classified data (file_id, job_id, or path)")
    data_source_type: DataSourceType = Field(default=DataSourceType.FILE_ID, description="Type of data source")
    template: ReportTemplate = Field(default=ReportTemplate.STANDARD, description="Report template to use")
    output_format: ReportFormat = Field(default=ReportFormat.MARKDOWN, description="Output format")
    include_statistics: bool = Field(default=True, description="Include statistical analysis")
    include_examples: bool = Field(default=True, description="Include example cases")
    max_examples: int = Field(default=5, ge=1, le=20, description="Maximum number of examples to include")
    language: str = Field(default="pt", description="Report language")

    @model_validator(mode='after')
    def validate_report_requirements(self):
        if self.report_type == ReportType.INDIVIDUAL and not self.subtema:
            raise ValueError('Individual reports require a subtema')
        if self.report_type == ReportType.OVERVIEW and not self.area:
            raise ValueError('Overview reports require an area')
        return self

    @field_validator('data_source')
    @classmethod
    def validate_data_source(cls, v):
        if not v.strip():
            raise ValueError('Data source cannot be empty')
        return v.strip()


class ReportMetadata(BaseModel):
    """Metadata for generated reports."""
    total_records: int = Field(..., ge=0, description="Total number of records analyzed")
    records_with_subtema: int = Field(..., ge=0, description="Records matching the subtema/area")
    processing_time: float = Field(..., ge=0, description="Time taken to generate report")
    model_used: str = Field(..., description="Model used for analysis")
    template_used: ReportTemplate = Field(..., description="Template used for generation")
    statistics: Optional[Dict[str, Any]] = Field(None, description="Statistical analysis results")

    @model_validator(mode='after')
    def validate_record_counts(self):
        if self.records_with_subtema > self.total_records:
            raise ValueError('records_with_subtema cannot exceed total_records')
        return self


class ReportResponse(BaseModel):
    """Response model for report generation."""
    report_id: UUID = Field(..., description="Unique identifier for the generated report")
    report_type: ReportType = Field(..., description="Type of report generated")
    subtema: Optional[str] = Field(None, description="Subtema analyzed (for individual reports)")
    area: Optional[ClassificationArea] = Field(None, description="Area analyzed (for overview reports)")
    content: str = Field(..., description="Generated report content")
    metadata: ReportMetadata = Field(..., description="Report metadata and statistics")
    output_format: ReportFormat = Field(..., description="Format of the generated report")
    created_at: datetime = Field(..., description="Timestamp when report was created")
    file_path: Optional[str] = Field(None, description="Path to saved report file")
    download_url: Optional[str] = Field(None, description="URL to download the report")
    success: bool = Field(..., description="Whether report generation was successful")
    error: Optional[str] = Field(None, description="Error message if generation failed")


class ReportListItem(BaseModel):
    """Model for report list items."""
    report_id: UUID = Field(..., description="Unique identifier for the report")
    report_type: ReportType = Field(..., description="Type of report")
    subtema: Optional[str] = Field(None, description="Subtema (for individual reports)")
    area: Optional[ClassificationArea] = Field(None, description="Area (for overview reports)")
    output_format: ReportFormat = Field(..., description="Report format")
    created_at: datetime = Field(..., description="Creation timestamp")
    file_size: Optional[int] = Field(None, ge=0, description="File size in bytes")
    download_url: Optional[str] = Field(None, description="Download URL")
    total_records: int = Field(..., ge=0, description="Number of records analyzed")


class ReportListResponse(BaseModel):
    """Response model for report listing."""
    reports: List[ReportListItem] = Field(..., description="List of reports")
    total_count: int = Field(..., ge=0, description="Total number of reports")
    page: int = Field(default=1, ge=1, description="Current page number")
    page_size: int = Field(default=20, ge=1, le=100, description="Number of reports per page")
    total_pages: int = Field(..., ge=1, description="Total number of pages")

    @model_validator(mode='after')
    def validate_pagination(self):
        if self.total_count != len(self.reports):
            # Allow for pagination - reports list might be a subset
            pass
        expected_pages = (self.total_count + self.page_size - 1) // self.page_size
        if expected_pages == 0:
            expected_pages = 1
        if self.total_pages != expected_pages:
            raise ValueError('total_pages calculation is incorrect')
        return self


class ReportTemplatesResponse(BaseModel):
    """Response model for available report templates."""
    templates: List[ReportTemplate] = Field(..., description="Available report templates")
    descriptions: Dict[ReportTemplate, str] = Field(..., description="Description of each template")
    supported_formats: Dict[ReportTemplate, List[ReportFormat]] = Field(
        ..., description="Supported output formats for each template"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "templates": ["standard", "detailed", "summary"],
                "descriptions": {
                    "standard": "Standard report with basic analysis",
                    "detailed": "Comprehensive report with detailed analysis",
                    "summary": "Brief summary report"
                },
                "supported_formats": {
                    "standard": ["markdown", "html", "pdf"],
                    "detailed": ["markdown", "html", "pdf"],
                    "summary": ["markdown", "text"]
                }
            }
        }
