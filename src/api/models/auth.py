"""
Pydantic models for authentication and user management.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, Field, validator
import re


class UserRegister(BaseModel):
    """User registration request model."""
    
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=8, max_length=128, description="User password")
    full_name: str = Field(..., min_length=2, max_length=100, description="User full name")
    organization: Optional[str] = Field(None, max_length=100, description="User organization")
    
    @validator('password')
    def validate_password(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        return v
    
    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "SecurePassword123",
                "full_name": "João Silva",
                "organization": "MPRJ"
            }
        }


class UserLogin(BaseModel):
    """
    User login request model.

    **Credenciais de Teste:**
    - Email: `<EMAIL>`
    - Senha: `test123`

    Essas credenciais podem ser usadas para testar a API em modo de desenvolvimento.
    """

    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., description="User password")

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "summary": "Credenciais de teste",
                    "description": "Use estas credenciais para testar a API",
                    "value": {
                        "email": "<EMAIL>",
                        "password": "test123"
                    }
                },
                {
                    "summary": "Exemplo de produção",
                    "description": "Formato para credenciais reais",
                    "value": {
                        "email": "<EMAIL>",
                        "password": "SecurePassword123"
                    }
                }
            ]
        }


class User(BaseModel):
    """User model for authentication and authorization."""
    
    id: str = Field(..., description="User unique identifier")
    email: str = Field(..., description="User email address")
    full_name: str = Field(..., description="User full name")
    organization: Optional[str] = Field(None, description="User organization")
    role: str = Field(default="user", description="User role")
    is_active: bool = Field(default=True, description="Whether user account is active")
    email_verified: bool = Field(default=False, description="Whether email is verified")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Account creation timestamp")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "550e8400-e29b-41d4-a716-************",
                "email": "<EMAIL>",
                "full_name": "João Silva",
                "organization": "MPRJ",
                "role": "user",
                "is_active": True,
                "email_verified": True,
                "created_at": "2024-01-15T10:30:00Z",
                "last_login": "2024-01-20T14:45:00Z"
            }
        }


class UserResponse(BaseModel):
    """User information response model."""
    
    id: str = Field(..., description="User unique identifier")
    email: str = Field(..., description="User email address")
    full_name: str = Field(..., description="User full name")
    organization: Optional[str] = Field(None, description="User organization")
    is_active: bool = Field(..., description="Whether user account is active")
    email_verified: bool = Field(..., description="Whether email is verified")
    created_at: datetime = Field(..., description="Account creation timestamp")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "550e8400-e29b-41d4-a716-************",
                "email": "<EMAIL>",
                "full_name": "João Silva",
                "organization": "MPRJ",
                "is_active": True,
                "email_verified": True,
                "created_at": "2024-01-15T10:30:00Z",
                "last_login": "2024-01-20T14:45:00Z"
            }
        }


class TokenResponse(BaseModel):
    """Authentication token response model."""
    
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: UserResponse = Field(..., description="User information")
    
    class Config:
        json_schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 3600,
                "user": {
                    "id": "550e8400-e29b-41d4-a716-************",
                    "email": "<EMAIL>",
                    "full_name": "João Silva",
                    "organization": "MPRJ",
                    "is_active": True,
                    "email_verified": True,
                    "created_at": "2024-01-15T10:30:00Z"
                }
            }
        }


class TokenRefresh(BaseModel):
    """Token refresh request model."""
    
    refresh_token: str = Field(..., description="JWT refresh token")
    
    class Config:
        json_schema_extra = {
            "example": {
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            }
        }


class PasswordReset(BaseModel):
    """Password reset request model."""
    
    email: EmailStr = Field(..., description="User email address")
    
    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>"
            }
        }


class PasswordChange(BaseModel):
    """Password change request model."""
    
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, max_length=128, description="New password")
    
    @validator('new_password')
    def validate_new_password(cls, v):
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        return v
    
    class Config:
        json_schema_extra = {
            "example": {
                "current_password": "OldPassword123",
                "new_password": "NewSecurePassword456"
            }
        }


class EmailVerification(BaseModel):
    """Email verification request model."""
    
    token: str = Field(..., description="Email verification token")
    
    class Config:
        json_schema_extra = {
            "example": {
                "token": "verification-token-here"
            }
        }


class UserProfile(BaseModel):
    """User profile update model."""
    
    full_name: Optional[str] = Field(None, min_length=2, max_length=100, description="User full name")
    organization: Optional[str] = Field(None, max_length=100, description="User organization")
    
    class Config:
        json_schema_extra = {
            "example": {
                "full_name": "João Silva Santos",
                "organization": "MPRJ - Promotoria de Justiça"
            }
        }


class AuthStatus(BaseModel):
    """Authentication status response model."""
    
    authenticated: bool = Field(..., description="Whether user is authenticated")
    user: Optional[UserResponse] = Field(None, description="User information if authenticated")
    permissions: list[str] = Field(default_factory=list, description="User permissions")
    
    class Config:
        json_schema_extra = {
            "example": {
                "authenticated": True,
                "user": {
                    "id": "550e8400-e29b-41d4-a716-************",
                    "email": "<EMAIL>",
                    "full_name": "João Silva",
                    "organization": "MPRJ",
                    "is_active": True,
                    "email_verified": True,
                    "created_at": "2024-01-15T10:30:00Z"
                },
                "permissions": ["read:files", "write:files", "generate:reports"]
            }
        }


class SecurityEvent(BaseModel):
    """Security event logging model."""
    
    event_type: str = Field(..., description="Type of security event")
    user_id: Optional[str] = Field(None, description="User ID if applicable")
    ip_address: str = Field(..., description="Client IP address")
    user_agent: Optional[str] = Field(None, description="Client user agent")
    details: dict = Field(default_factory=dict, description="Additional event details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Event timestamp")
    
    class Config:
        json_schema_extra = {
            "example": {
                "event_type": "login_attempt",
                "user_id": "550e8400-e29b-41d4-a716-************",
                "ip_address": "*************",
                "user_agent": "Mozilla/5.0...",
                "details": {"success": True, "method": "password"},
                "timestamp": "2024-01-20T14:45:00Z"
            }
        }
