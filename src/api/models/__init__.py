"""
Pydantic models for API requests and responses.
"""

from .anonymization import *
from .classification import *
from .reports import *
from .files import *
from .batch_processing import *

__all__ = [
    # Anonymization models
    "SupportedLanguage",
    "EntityType",
    "AnonymizationRequest",
    "AnonymizationResponse",
    "PDFAnonymizationRequest",
    "PDFAnonymizationResponse",
    "EntityFound",
    "SupportedEntitiesResponse",

    # Classification models
    "ClassificationArea",
    "ClassificationType",
    "UnilabelDimension",
    "TipoUnidade",
    "CRE",
    "ClassificationRequest",
    "UnilabelClassificationRequest",
    "ClassificationResponse",
    "UnilabelClassificationResponse",
    "BatchClassificationRequest",
    "BatchClassificationResponse",
    "SubtemasResponse",

    # Reports models
    "ReportType",
    "ReportTemplate",
    "ReportFormat",
    "DataSourceType",
    "ReportRequest",
    "ReportMetadata",
    "ReportResponse",
    "ReportListItem",
    "ReportListResponse",
    "ReportTemplatesResponse",

    # Files models
    "FileType",
    "FileStatus",
    "FileSource",
    "FileMetadata",
    "FileUploadRequest",
    "FileUploadResponse",
    "FileProcessingRequest",
    "FileProcessingResponse",
    "FileListItem",
    "FileListResponse",
    "FileDetailsResponse",
    "FileDeleteResponse",
    "FileStatsResponse",

    # Batch processing models
    "JobStatus",
    "ProcessingError",
    "TextChunk",
    "ChunkResult",
    "PDFClassificationResult",
    "BatchResult",
    "BatchProcessingRequest",
    "JobStatusResponse",
    "BatchResultSummary",
    "TextExtractorProtocol",
    "ChunkClassifierProtocol",
    "ResultAggregatorProtocol",
    "BatchJobManagerProtocol",
    "CSVExportProtocol",
]
