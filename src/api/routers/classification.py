"""
Classification endpoints for multilabel and unilabel processing.
"""

import logging
import time
from fastapi import APIRouter, Depends, HTTPException, status

from ..dependencies import get_current_user, check_rate_limit
from ..models.classification import (
    ClassificationRequest,
    ClassificationResponse,
    UnilabelClassificationRequest,
    UnilabelClassificationResponse,
    BatchClassificationRequest,
    BatchClassificationResponse,
    SubtemasResponse,
    ClassificationArea
)
from ..services.classification_service import ClassificationService

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize classification service
classification_service = ClassificationService()


@router.post("/multilabel", response_model=ClassificationResponse)
async def classify_multilabel(
    request: ClassificationRequest,
    current_user: dict = Depends(get_current_user),
    _: bool = Depends(check_rate_limit)
):
    """
    Classify text into multiple subtemas using LLM models.

    This endpoint processes text to identify relevant subtemas within a specific area
    using advanced language models. It supports Brazilian Portuguese and can identify
    up to 3 subtemas per text.

    **Supported Areas:**
    - EDUCACAO: Educational issues and policies
    - SAUDE: Health-related topics
    - MEIO_AMBIENTE: Environmental concerns

    **Features:**
    - Multi-label classification (up to 3 subtemas)
    - Confidence scores for each classification
    - Support for custom models
    - Processing time metrics

    **Example:**
    ```json
    {
        "text": "Problema na merenda escolar da escola municipal",
        "area": "EDUCACAO",
        "max_subtemas": 3,
        "model_name": "meta-llama/Llama-3.3-70B-Instruct-Turbo"
    }
    ```
    """
    try:
        logger.info(f"Processing multilabel classification for user {current_user['id']}, area: {request.area}")
        start_time = time.time()

        # Call classification service
        result = await classification_service.classify_multilabel(
            text=request.text,
            area=request.area.value,
            model_name=request.model_name,
            max_subtemas=request.max_subtemas
        )

        processing_time = time.time() - start_time

        # Create response
        response = ClassificationResponse(
            text=request.text,
            area=request.area,
            subtemas=result.get("subtemas", []),
            confidence_scores=result.get("confidence_scores"),
            model_used=result.get("model_used", request.model_name or "default"),
            processing_time=processing_time,
            success=result["success"],
            error=result.get("error")
        )

        logger.info(f"Multilabel classification completed in {processing_time:.2f}s, found {len(response.subtemas)} subtemas")
        return response

    except Exception as e:
        logger.error(f"Error in multilabel classification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Classification failed: {str(e)}"
        )


@router.post("/unilabel", response_model=UnilabelClassificationResponse)
async def classify_unilabel(
    request: UnilabelClassificationRequest,
    current_user: dict = Depends(get_current_user),
    _: bool = Depends(check_rate_limit)
):
    """
    Classify text for specific unilabel dimensions (TIPO_UNIDADE, CRE).

    This endpoint performs single-label classification for specific dimensions
    related to educational administration in Rio de Janeiro.

    **Supported Dimensions:**
    - TIPO_UNIDADE: Type of educational unit (Estadual, Municipal, Privada)
    - CRE: Regional Education Coordination (1ª CRE to 11ª CRE)

    **Features:**
    - Single classification per dimension
    - Confidence scores
    - Support for "Não se aplica" when not applicable

    **Example:**
    ```json
    {
        "text": "Problema na escola municipal da Tijuca",
        "dimension": "TIPO_UNIDADE"
    }
    ```
    """
    try:
        logger.info(f"Processing unilabel classification for user {current_user['id']}, dimension: {request.dimension}")
        start_time = time.time()

        # Call classification service
        result = await classification_service.classify_unilabel(
            text=request.text,
            dimension=request.dimension.value,
            model_name=request.model_name
        )

        processing_time = time.time() - start_time

        # Create response
        response = UnilabelClassificationResponse(
            text=request.text,
            dimension=request.dimension,
            classification=result.get("classification"),
            confidence_score=result.get("confidence_score"),
            model_used=result.get("model_used", request.model_name or "default"),
            processing_time=processing_time,
            success=result["success"],
            error=result.get("error")
        )

        logger.info(f"Unilabel classification completed in {processing_time:.2f}s, result: {response.classification}")
        return response

    except Exception as e:
        logger.error(f"Error in unilabel classification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unilabel classification failed: {str(e)}"
        )


@router.post("/batch", response_model=BatchClassificationResponse)
async def classify_batch(
    request: BatchClassificationRequest,
    current_user: dict = Depends(get_current_user),
    _: bool = Depends(check_rate_limit)
):
    """
    Process multiple texts in batch for efficient classification.

    This endpoint allows processing multiple texts at once, which is more efficient
    than individual requests when dealing with large datasets.

    **Features:**
    - Process up to 100 texts per request
    - Parallel processing for better performance
    - Detailed statistics and error reporting
    - Support for both multilabel and unilabel classification

    **Limitations:**
    - Maximum 100 texts per batch
    - All texts must use the same area/dimension
    - Timeout protection for large batches

    **Example:**
    ```json
    {
        "texts": [
            "Problema na merenda escolar",
            "Falta de professores na escola",
            "Infraestrutura precária"
        ],
        "area": "EDUCACAO",
        "classification_type": "multilabel",
        "max_subtemas": 3
    }
    ```
    """
    try:
        logger.info(f"Processing batch classification for user {current_user['id']}, {len(request.texts)} texts")
        start_time = time.time()

        # Call classification service for batch processing
        result = await classification_service.classify_batch(
            texts=request.texts,
            area=request.area.value,
            classification_type=request.classification_type.value,
            max_subtemas=request.max_subtemas,
            model_name=request.model_name
        )

        total_processing_time = time.time() - start_time

        # Create individual classification responses
        classification_results = []
        success_count = 0
        error_count = 0

        for i, text_result in enumerate(result.get("results", [])):
            individual_response = ClassificationResponse(
                text=request.texts[i],
                area=request.area,
                subtemas=text_result.get("subtemas", []),
                confidence_scores=text_result.get("confidence_scores"),
                model_used=text_result.get("model_used", request.model_name or "default"),
                processing_time=text_result.get("processing_time"),
                success=text_result.get("success", True),
                error=text_result.get("error")
            )
            classification_results.append(individual_response)

            if individual_response.success:
                success_count += 1
            else:
                error_count += 1

        # Create batch response
        response = BatchClassificationResponse(
            results=classification_results,
            total_processed=len(request.texts),
            success_count=success_count,
            error_count=error_count,
            total_processing_time=total_processing_time,
            average_processing_time=total_processing_time / len(request.texts) if request.texts else 0
        )

        logger.info(f"Batch classification completed in {total_processing_time:.2f}s, {success_count}/{len(request.texts)} successful")
        return response

    except Exception as e:
        logger.error(f"Error in batch classification: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Batch classification failed: {str(e)}"
        )


@router.get("/subtemas/{area}", response_model=SubtemasResponse)
async def get_subtemas(
    area: ClassificationArea,
    current_user: dict = Depends(get_current_user)
):
    """
    Get available subtemas for a specific classification area.

    Returns all available subtemas that can be used for classification within
    the specified area, along with their descriptions when available.

    **Supported Areas:**
    - EDUCACAO: Educational subtemas (special education, school feeding, etc.)
    - SAUDE: Health-related subtemas (basic care, emergency, medications, etc.)
    - MEIO_AMBIENTE: Environmental subtemas (pollution, deforestation, etc.)

    **Response includes:**
    - List of all subtemas for the area
    - Descriptions of each subtema
    - Total count of available subtemas

    **Example:**
    ```
    GET /api/v1/classification/subtemas/EDUCACAO
    ```
    """
    try:
        logger.info(f"Fetching subtemas for area {area} for user {current_user['id']}")

        # Get subtemas from service
        result = classification_service.get_subtemas_for_area(area.value)

        # Get descriptions if available
        descriptions = classification_service.get_subtemas_descriptions(area.value)

        response = SubtemasResponse(
            area=area,
            subtemas=result,
            descriptions=descriptions,
            total_count=len(result)
        )

        logger.info(f"Returned {len(result)} subtemas for area {area}")
        return response

    except Exception as e:
        logger.error(f"Error fetching subtemas for area {area}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch subtemas: {str(e)}"
        )
