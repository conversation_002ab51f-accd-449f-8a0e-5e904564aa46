"""
Authentication endpoints for user registration, login, and token management.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from jose import JWTError, jwt

from ..dependencies import check_rate_limit, get_current_user
from ..models.auth import (
    UserLogin,
    UserResponse,
    TokenResponse,
    AuthStatus
)
from ..config import settings

logger = logging.getLogger(__name__)
router = APIRouter()


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire, "iat": datetime.utcnow()})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


@router.post("/login", response_model=TokenResponse)
async def login_user(
    user_credentials: UserLogin,
    request: Request,
    _: bool = Depends(check_rate_limit)
):
    """
    Authenticate user and return access tokens.
    
    **Test Credentials:**
    - Email: <EMAIL>
    - Password: test123
    
    **Example:**
    ```json
    {
        "email": "<EMAIL>",
        "password": "test123"
    }
    ```
    """
    try:
        logger.info(f"Login attempt for user: {user_credentials.email}")
        
        # Special handling for test user
        if user_credentials.email == "<EMAIL>" and user_credentials.password == "test123":
            # Create test user tokens
            access_token = create_access_token(
                data={
                    "sub": "test-user-123",
                    "email": "<EMAIL>",
                    "full_name": "Test User",
                    "organization": "Test Organization"
                }
            )
            
            response = TokenResponse(
                access_token=access_token,
                refresh_token="mock-refresh-token",
                token_type="bearer",
                expires_in=settings.access_token_expire_minutes * 60,
                user=UserResponse(
                    id="test-user-123",
                    email="<EMAIL>",
                    full_name="Test User",
                    organization="Test Organization",
                    is_active=True,
                    email_verified=True,
                    created_at=datetime.utcnow()
                )
            )
            
            logger.info("Test user logged in successfully")
            return response
        
        # If we reach here, authentication failed
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials. Use <EMAIL> / test123 for testing."
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: dict = Depends(get_current_user)
):
    """
    Get current user information.
    
    Returns detailed information about the currently authenticated user.
    """
    try:
        return UserResponse(
            id=current_user["id"],
            email=current_user["email"],
            full_name=current_user.get("full_name", ""),
            organization=current_user.get("organization", ""),
            is_active=current_user.get("is_active", True),
            email_verified=True,
            created_at=datetime.utcnow()
        )
    except Exception as e:
        logger.error(f"Error getting user info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user information"
        )


@router.get("/status", response_model=AuthStatus)
async def get_auth_status(
    current_user: dict = Depends(get_current_user)
):
    """
    Get authentication status and user permissions.
    
    Returns the current authentication status and user permissions.
    """
    try:
        user_response = UserResponse(
            id=current_user["id"],
            email=current_user["email"],
            full_name=current_user.get("full_name", ""),
            organization=current_user.get("organization", ""),
            is_active=current_user.get("is_active", True),
            email_verified=True,
            created_at=datetime.utcnow()
        )
        
        # Basic permissions for all authenticated users
        permissions = [
            "read:files",
            "write:files",
            "read:reports",
            "write:reports",
            "use:anonymization",
            "use:classification"
        ]
        
        return AuthStatus(
            authenticated=True,
            user=user_response,
            permissions=permissions
        )
    except Exception as e:
        logger.error(f"Error getting auth status: {e}")
        return AuthStatus(
            authenticated=False,
            user=None,
            permissions=[]
        )
