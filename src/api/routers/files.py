"""
File management endpoints for upload, download and storage with Supabase.
"""

import logging
import json
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Query
from fastapi.responses import StreamingResponse

from ..dependencies import get_current_user, check_rate_limit
from ..models.files import (
    FileUploadResponse,
    FileListResponse,
    FileDetailsResponse,
    FileDeleteResponse,
    FileStatsResponse,
    FileMetadata,
    FileType,
    FileStatus
)
from ..services.file_service import FileService

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize file service
file_service = FileService()


@router.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    metadata: Optional[str] = Form(None),
    process_immediately: bool = Form(False),
    current_user: dict = Depends(get_current_user),
    _: bool = Depends(check_rate_limit)
):
    """
    Upload file to Supabase Storage with metadata and processing options.

    This endpoint handles secure file uploads to Supabase Storage with comprehensive
    validation, metadata management, and optional immediate processing.

    **Supported File Types:**
    - CSV files (text/csv)
    - PDF documents (application/pdf)
    - Text files (text/plain)
    - Excel files (.xls, .xlsx)

    **Features:**
    - Automatic file type validation
    - Size limit enforcement (50MB max)
    - Metadata attachment and indexing
    - Optional immediate processing trigger
    - Secure user-isolated storage

    **File Organization:**
    Files are stored in user-specific folders: `{user_id}/{file_id}.{extension}`

    **Example:**
    ```bash
    curl -X POST "http://localhost:8000/api/v1/files/upload" \
      -H "Authorization: Bearer $TOKEN" \
      -F "file=@data.csv" \
      -F "metadata={\"description\": \"Ouvidoria data\", \"tags\": [\"education\"]}" \
      -F "process_immediately=true"
    ```
    """
    try:
        logger.info(f"Processing file upload for user {current_user['id']}: {file.filename}")

        # Parse metadata if provided
        file_metadata = None
        if metadata:
            try:
                metadata_dict = json.loads(metadata)
                file_metadata = FileMetadata(**metadata_dict)
            except (json.JSONDecodeError, ValueError) as e:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid metadata format: {str(e)}"
                )

        # Call file service
        result = await file_service.upload_file(
            file=file,
            user_id=current_user["id"],
            metadata=file_metadata.model_dump() if file_metadata else None,
            process_immediately=process_immediately
        )

        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )

        # Create response
        response = FileUploadResponse(
            file_id=result["file_id"],
            filename=result["filename"],
            file_path=result["file_path"],
            file_type=FileType(result["file_type"]),
            file_size=result["file_size"],
            metadata=file_metadata,
            status=FileStatus.UPLOADED,
            upload_url=result.get("upload_url"),
            download_url=result.get("download_url"),
            created_at=result.get("created_at"),
            success=True
        )

        logger.info(f"File uploaded successfully: {result['file_id']}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"File upload failed: {str(e)}"
        )


@router.get("/", response_model=FileListResponse)
async def list_files(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    file_type: Optional[FileType] = Query(None, description="Filter by file type"),
    status: Optional[FileStatus] = Query(None, description="Filter by status"),
    current_user: dict = Depends(get_current_user)
):
    """
    List files for the current user with pagination and filtering.

    Returns a paginated list of files uploaded by the authenticated user,
    with optional filtering by file type and processing status.

    **Query Parameters:**
    - **page**: Page number (default: 1)
    - **page_size**: Items per page (default: 20, max: 100)
    - **file_type**: Filter by file type (CSV, PDF, etc.)
    - **status**: Filter by processing status (uploaded, processing, etc.)

    **Response includes:**
    - Paginated list of files with metadata
    - Total count and pagination info
    - Download URLs for each file
    - Processing status and job information

    **Example:**
    ```
    GET /api/v1/files/?page=1&page_size=10&file_type=text/csv&status=processed
    ```
    """
    try:
        logger.info(f"Listing files for user {current_user['id']}, page {page}")

        # Call file service
        result = await file_service.list_files(
            user_id=current_user["id"],
            page=page,
            page_size=page_size,
            file_type=file_type.value if file_type else None,
            status=status.value if status else None
        )

        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result["error"]
            )

        # Create response
        response = FileListResponse(
            files=result["files"],
            total_count=result["total_count"],
            total_size=result["total_size"],
            page=page,
            page_size=page_size,
            total_pages=result["total_pages"],
            filters_applied={
                "file_type": file_type.value if file_type else None,
                "status": status.value if status else None
            }
        )

        logger.info(f"Listed {len(result['files'])} files for user {current_user['id']}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing files: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list files: {str(e)}"
        )


@router.get("/{file_id}", response_model=FileDetailsResponse)
async def get_file_details(
    file_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Get detailed information about a specific file.

    Returns comprehensive information about a file including metadata,
    processing history, access statistics, and download URLs.

    **Response includes:**
    - Complete file metadata and properties
    - Processing job history and status
    - Access count and last accessed time
    - Download and preview URLs
    - File statistics and analytics

    **Security:**
    - Users can only access their own files
    - File ownership is validated
    - Deleted files return 404

    **Example:**
    ```
    GET /api/v1/files/550e8400-e29b-41d4-a716-446655440000
    ```
    """
    try:
        logger.info(f"Getting file details {file_id} for user {current_user['id']}")

        # Call file service
        result = await file_service.get_file_details(
            file_id=file_id,
            user_id=current_user["id"]
        )

        if not result["success"]:
            if "not found" in result.get("error", "").lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="File not found or access denied"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result["error"]
                )

        # Create response
        response = FileDetailsResponse(**result["file_details"])

        logger.info(f"Retrieved file details for {file_id}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting file details {file_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get file details: {str(e)}"
        )


@router.get("/{file_id}/download")
async def download_file(
    file_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Download a file from Supabase Storage.

    Returns the file content as a streaming response with appropriate
    headers for download. Supports all uploaded file types.

    **Features:**
    - Streaming download for large files
    - Proper content-type headers
    - Access logging and analytics
    - Secure user validation

    **Security:**
    - Users can only download their own files
    - File access is logged for audit
    - Temporary URLs for secure access

    **Example:**
    ```
    GET /api/v1/files/550e8400-e29b-41d4-a716-446655440000/download
    ```
    """
    try:
        logger.info(f"Downloading file {file_id} for user {current_user['id']}")

        # Call file service
        result = await file_service.download_file(
            file_id=file_id,
            user_id=current_user["id"]
        )

        if not result["success"]:
            if "not found" in result.get("error", "").lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="File not found or access denied"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result["error"]
                )

        # Return streaming response
        def generate():
            yield result["content"]

        return StreamingResponse(
            generate(),
            media_type=result["content_type"],
            headers={
                "Content-Disposition": f"attachment; filename={result['filename']}"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading file {file_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download file: {str(e)}"
        )


@router.delete("/{file_id}", response_model=FileDeleteResponse)
async def delete_file(
    file_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a file from Supabase Storage and database.

    Permanently removes a file from both storage and database,
    including all associated metadata and processing jobs.

    **Warning:** This action cannot be undone!

    **What gets deleted:**
    - File content from Supabase Storage
    - File record from database
    - Associated processing jobs
    - Generated reports (optional)

    **Security:**
    - Users can only delete their own files
    - Confirmation required for important files
    - Audit trail maintained

    **Example:**
    ```
    DELETE /api/v1/files/550e8400-e29b-41d4-a716-446655440000
    ```
    """
    try:
        logger.info(f"Deleting file {file_id} for user {current_user['id']}")

        # Call file service
        result = await file_service.delete_file(
            file_id=file_id,
            user_id=current_user["id"]
        )

        if not result["success"]:
            if "not found" in result.get("error", "").lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="File not found or access denied"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result["error"]
                )

        # Create response
        response = FileDeleteResponse(
            file_id=result["file_id"],
            filename=result["filename"],
            deleted_at=result["deleted_at"],
            success=True
        )

        logger.info(f"File {file_id} deleted successfully")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting file {file_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete file: {str(e)}"
        )


@router.get("/stats/summary", response_model=FileStatsResponse)
async def get_file_statistics(
    current_user: dict = Depends(get_current_user)
):
    """
    Get file usage statistics for the current user.

    Returns comprehensive statistics about the user's file usage,
    including counts, sizes, types, and trends.

    **Statistics include:**
    - Total files and storage usage
    - Breakdown by file type and status
    - Average and largest file sizes
    - Upload trends and patterns
    - Processing job statistics

    **Use cases:**
    - Dashboard analytics
    - Storage quota monitoring
    - Usage pattern analysis
    - Performance optimization

    **Example:**
    ```
    GET /api/v1/files/stats/summary
    ```
    """
    try:
        logger.info(f"Getting file statistics for user {current_user['id']}")

        # Call file service
        result = await file_service.get_file_statistics(
            user_id=current_user["id"]
        )

        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result["error"]
            )

        # Create response
        response = FileStatsResponse(**result["statistics"])

        logger.info(f"Retrieved file statistics for user {current_user['id']}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting file statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get file statistics: {str(e)}"
        )
