"""
Batch Processing API Router.

This module provides REST API endpoints for PDF batch processing with:
- Job creation and management
- Real-time progress tracking
- Result retrieval and CSV download
- Authentication and rate limiting
- Comprehensive error handling
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, status
from fastapi.responses import FileResponse, StreamingResponse
from fastapi.security import HTTPBearer

from ..models.batch_processing import (
    BatchProcessingRequest,
    JobStatusResponse,
    BatchResultSummary,
    JobStatus
)
from ..services.optimized_batch_processing_service import OptimizedBatchProcessingService
from ..services.token_budget_manager import PriorityLevel
from ..dependencies import get_current_user, get_rate_limiter, User

# Initialize router
router = APIRouter(
    prefix="/api/v1/batch",
    tags=["batch"],
    dependencies=[Depends(get_rate_limiter)],
)

# Initialize services
batch_service = OptimizedBatchProcessingService()

# Security
security = HTTPBearer()
logger = logging.getLogger(__name__)


@router.post("/process", response_model=Dict[str, str])
async def start_batch_processing(
    request: BatchProcessingRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """
    Start a new PDF batch processing job.
    
    This endpoint creates a new batch processing job and starts processing
    PDFs in the specified folder asynchronously.
    
    Args:
        request: Batch processing parameters
        background_tasks: FastAPI background tasks
        current_user: Authenticated user
        
    Returns:
        Job ID for tracking progress
        
    Raises:
        HTTPException: If folder doesn't exist or processing fails
    """
    try:
        # Validate folder path
        folder_path = Path(request.folder_path)
        if not folder_path.exists():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Folder does not exist: {request.folder_path}"
            )
        
        if not folder_path.is_dir():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Path is not a directory: {request.folder_path}"
            )
        
        # Check if user has permission to access the folder
        if not _has_folder_permission(current_user, folder_path):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to access folder"
            )
        
        # Start batch processing
        job_id = await batch_service.start_batch_processing(
            request=request,
            user_id=current_user.id,
            progress_callback=_create_progress_callback(current_user.id)
        )
        
        logger.info(f"Started batch processing job {job_id} for user {current_user.id}")
        
        return {
            "job_id": job_id,
            "message": "Batch processing started successfully",
            "status": "processing"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting batch processing: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start batch processing: {str(e)}"
        )


@router.get("/status/{job_id}", response_model=JobStatusResponse)
async def get_job_status(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Get the current status of a batch processing job.
    
    Args:
        job_id: Unique job identifier
        current_user: Authenticated user
        
    Returns:
        Current job status with progress information
        
    Raises:
        HTTPException: If job not found or access denied
    """
    try:
        # Get job status
        job_status = batch_service.get_job_status(job_id)
        
        if not job_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job not found: {job_id}"
            )
        
        # Verify user has access to this job
        if not _has_job_permission(current_user, job_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to access job"
            )
        
        return job_status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get job status: {str(e)}"
        )


@router.get("/results/{job_id}", response_model=BatchResultSummary)
async def get_job_results(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Get the results of a completed batch processing job.
    
    Args:
        job_id: Unique job identifier
        current_user: Authenticated user
        
    Returns:
        Batch processing results summary
        
    Raises:
        HTTPException: If job not found, not completed, or access denied
    """
    try:
        # Check job status first
        job_status = batch_service.get_job_status(job_id)
        
        if not job_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job not found: {job_id}"
            )
        
        # Verify user has access to this job
        if not _has_job_permission(current_user, job_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to access job"
            )
        
        # Check if job is completed
        if job_status.status != JobStatus.COMPLETED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Job is not completed. Current status: {job_status.status}"
            )
        
        # Get job results
        batch_result = batch_service.get_job_results(job_id)
        
        if not batch_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Results not found for job: {job_id}"
            )
        
        # Create summary response
        summary = BatchResultSummary(
            job_id=job_id,
            csv_download_url=f"/api/v1/batch/download/{job_id}",
            summary={
                "total_pdfs": batch_result.total_pdfs,
                "successful_pdfs": batch_result.successful_pdfs,
                "failed_pdfs": len(batch_result.failed_pdfs),
                "area": batch_result.area,
                "timestamp": batch_result.timestamp.isoformat() if batch_result.timestamp else None
            },
            processing_stats={
                "total_processing_time": batch_result.total_processing_time,
                "average_time_per_pdf": batch_result.total_processing_time / max(batch_result.total_pdfs, 1),
                "success_rate": batch_result.successful_pdfs / max(batch_result.total_pdfs, 1) * 100,
                "error_summary": batch_result.error_summary
            }
        )
        
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job results {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get job results: {str(e)}"
        )


@router.get("/download/{job_id}")
async def download_job_results(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Download the CSV results of a completed batch processing job.
    
    Args:
        job_id: Unique job identifier
        current_user: Authenticated user
        
    Returns:
        CSV file download response
        
    Raises:
        HTTPException: If job not found, not completed, or access denied
    """
    try:
        # Check job status first
        job_status = batch_service.get_job_status(job_id)
        
        if not job_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job not found: {job_id}"
            )
        
        # Verify user has access to this job
        if not _has_job_permission(current_user, job_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to access job"
            )
        
        # Check if job is completed
        if job_status.status != JobStatus.COMPLETED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Job is not completed. Current status: {job_status.status}"
            )
        
        # Get job results
        batch_result = batch_service.get_job_results(job_id)
        
        if not batch_result or not batch_result.csv_output_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"CSV file not found for job: {job_id}"
            )
        
        # Check if CSV file exists
        csv_path = Path(batch_result.csv_output_path)
        if not csv_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"CSV file not found: {csv_path}"
            )
        
        # Return file response
        return FileResponse(
            path=str(csv_path),
            filename=f"batch_results_{job_id}.csv",
            media_type='text/csv',
            headers={
                "Content-Disposition": f"attachment; filename=batch_results_{job_id}.csv"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading job results {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download job results: {str(e)}"
        )


@router.delete("/{job_id}")
async def cancel_job(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Cancel a running batch processing job.
    
    Args:
        job_id: Unique job identifier
        current_user: Authenticated user
        
    Returns:
        Cancellation confirmation
        
    Raises:
        HTTPException: If job not found or cannot be cancelled
    """
    try:
        # Check job status first
        job_status = batch_service.get_job_status(job_id)
        
        if not job_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job not found: {job_id}"
            )
        
        # Verify user has access to this job
        if not _has_job_permission(current_user, job_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to cancel job"
            )
        
        # Check if job can be cancelled
        if job_status.status not in [JobStatus.PENDING, JobStatus.PROCESSING]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Job cannot be cancelled. Current status: {job_status.status}"
            )
        
        # Cancel the job
        success = batch_service.cancel_job(job_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to cancel job: {job_id}"
            )
        
        logger.info(f"Job {job_id} cancelled by user {current_user.id}")
        
        return {
            "job_id": job_id,
            "message": "Job cancelled successfully",
            "status": "cancelled"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling job {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel job: {str(e)}"
        )


@router.get("/jobs")
async def list_user_jobs(
    current_user: User = Depends(get_current_user),
    limit: int = 10,
    offset: int = 0
):
    """
    List batch processing jobs for the current user.
    
    Args:
        current_user: Authenticated user
        limit: Maximum number of jobs to return
        offset: Number of jobs to skip
        
    Returns:
        List of user's batch processing jobs
    """
    try:
        # Get user's jobs
        user_jobs = []
        
        for job_id, job_data in batch_service.job_manager.jobs.items():
            if job_data.get('user_id') == current_user.id:
                user_jobs.append({
                    "job_id": job_id,
                    "area": job_data.get('area'),
                    "status": job_data.get('status'),
                    "created_at": job_data.get('created_at'),
                    "updated_at": job_data.get('updated_at'),
                    "progress": job_data.get('progress', 0.0),
                    "total_files": job_data.get('total_files', 0),
                    "processed_files": job_data.get('processed_files', 0)
                })
        
        # Sort by creation date (newest first)
        user_jobs.sort(key=lambda x: x['created_at'], reverse=True)
        
        # Apply pagination
        paginated_jobs = user_jobs[offset:offset + limit]
        
        return {
            "jobs": paginated_jobs,
            "total": len(user_jobs),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"Error listing user jobs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list jobs: {str(e)}"
        )


@router.post("/cleanup")
async def cleanup_old_jobs(
    days_old: int = 7,
    current_user: User = Depends(get_current_user)
):
    """
    Clean up old completed jobs (admin only).
    
    Args:
        days_old: Number of days old for cleanup
        current_user: Authenticated user
        
    Returns:
        Cleanup summary
    """
    try:
        # Check if user is admin
        if not _is_admin(current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )
        
        # Cleanup old jobs
        cleaned_count = batch_service.cleanup_old_jobs(days_old)
        
        logger.info(f"Cleaned up {cleaned_count} old jobs by admin {current_user.id}")
        
        return {
            "cleaned_jobs": cleaned_count,
            "days_old": days_old,
            "message": f"Successfully cleaned up {cleaned_count} old jobs"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cleaning up jobs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cleanup jobs: {str(e)}"
        )


@router.get("/optimization/stats")
async def get_optimization_stats(
    current_user: User = Depends(get_current_user)
):
    """
    Get optimization statistics and metrics.
    
    Args:
        current_user: Authenticated user
        
    Returns:
        Optimization statistics
    """
    try:
        stats = batch_service.get_optimization_stats()
        
        return {
            "optimization_stats": stats,
            "user_id": current_user.id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting optimization stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get optimization stats: {str(e)}"
        )


@router.get("/optimization/suggestions")
async def get_optimization_suggestions(
    current_user: User = Depends(get_current_user)
):
    """
    Get optimization suggestions for token usage.
    
    Args:
        current_user: Authenticated user
        
    Returns:
        Optimization suggestions
    """
    try:
        suggestions = batch_service.get_optimization_suggestions()
        
        return {
            "suggestions": suggestions,
            "user_id": current_user.id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting optimization suggestions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get optimization suggestions: {str(e)}"
        )


@router.post("/optimization/config")
async def update_optimization_config(
    config_updates: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """
    Update optimization configuration (admin only).
    
    Args:
        config_updates: Configuration updates
        current_user: Authenticated user
        
    Returns:
        Update confirmation
    """
    try:
        # Check if user is admin
        if not _is_admin(current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )
        
        # Update configuration
        batch_service.update_optimization_config(**config_updates)
        
        logger.info(f"Updated optimization config by admin {current_user.id}: {config_updates}")
        
        return {
            "message": "Optimization configuration updated successfully",
            "updated_config": config_updates,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating optimization config: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update optimization config: {str(e)}"
        )


# Helper functions

def _has_folder_permission(user: User, folder_path: Path) -> bool:
    """
    Check if user has permission to access the folder.
    
    Args:
        user: User object
        folder_path: Path to folder
        
    Returns:
        True if user has permission, False otherwise
    """
    # Basic permission check - in production, implement proper access control
    try:
        # Check if folder is readable
        return folder_path.exists() and folder_path.is_dir()
    except Exception:
        return False


def _has_job_permission(user: User, job_id: str) -> bool:
    """
    Check if user has permission to access the job.
    
    Args:
        user: User object
        job_id: Job identifier
        
    Returns:
        True if user has permission, False otherwise
    """
    # Check if job belongs to user
    job_data = batch_service.job_manager.jobs.get(job_id)
    if not job_data:
        return False
    
    return job_data.get('user_id') == user.id or _is_admin(user)


def _is_admin(user: User) -> bool:
    """
    Check if user is admin.
    
    Args:
        user: User object
        
    Returns:
        True if user is admin, False otherwise
    """
    # Check if user has admin role
    return hasattr(user, 'role') and user.role == 'admin'


def _create_progress_callback(user_id: str) -> Optional[callable]:
    """
    Create a progress callback function for real-time updates.
    
    Args:
        user_id: User identifier
        
    Returns:
        Progress callback function or None
    """
    # In a production system, this would implement WebSocket or SSE
    # for real-time progress updates
    async def progress_callback(job_id: str, progress: float, current_file: str,
                              processed_files: int, total_files: int, status: JobStatus):
        """Progress callback for real-time updates."""
        logger.info(f"Job {job_id} progress: {progress:.1%} - {current_file}")
        # TODO: Implement WebSocket or SSE for real-time updates
        pass
    
    return progress_callback