"""
Reports endpoints for generating individual and overview reports.
"""

import logging
import time
from uuid import uuid4
from fastapi import APIRouter, Depends, HTTPException, status, Response
from fastapi.responses import FileResponse

from ..dependencies import get_current_user, check_rate_limit
from ..models.reports import (
    ReportRequest,
    ReportResponse,
    ReportListResponse,
    ReportTemplatesResponse,
    ReportType,
    ReportTemplate,
    ReportFormat,
    ReportMetadata
)
from ..services.reports_service import ReportsService

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize reports service
reports_service = ReportsService()


@router.post("/individual", response_model=ReportResponse)
async def generate_individual_report(
    request: ReportRequest,
    current_user: dict = Depends(get_current_user),
    _: bool = Depends(check_rate_limit)
):
    """
    Generate detailed report for a specific subtema.

    This endpoint creates comprehensive reports focused on a single subtema,
    providing in-depth analysis, statistics, and examples from the classified data.

    **Features:**
    - Deep analysis of specific subtema
    - Statistical breakdowns and trends
    - Representative examples and cases
    - Multiple output formats (Markdown, HTML, PDF)
    - Customizable templates and detail levels

    **Report Types:**
    - **Individual**: Focused analysis of one subtema
    - **Specialized**: Technical deep-dive with advanced metrics
    - **Executive**: High-level summary for decision makers

    **Example:**
    ```json
    {
        "report_type": "individual",
        "subtema": "ALIMENTACAO_ESCOLAR",
        "area": "EDUCACAO",
        "data_source": "file_12345",
        "template": "detailed",
        "output_format": "markdown",
        "include_statistics": true,
        "include_examples": true,
        "max_examples": 10
    }
    ```
    """
    try:
        logger.info(f"Generating individual report for user {current_user['id']}, subtema: {request.subtema}")
        start_time = time.time()

        # Validate that this is an individual report
        if request.report_type not in [ReportType.INDIVIDUAL, ReportType.SPECIALIZED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="This endpoint only supports individual and specialized report types"
            )

        # Call reports service
        result = await reports_service.generate_individual_report(
            subtema=request.subtema,
            area=request.area.value if request.area else None,
            data_source=request.data_source,
            data_source_type=request.data_source_type.value,
            template=request.template.value,
            output_format=request.output_format.value,
            include_statistics=request.include_statistics,
            include_examples=request.include_examples,
            max_examples=request.max_examples,
            language=request.language
        )

        processing_time = time.time() - start_time

        # Create metadata
        metadata = ReportMetadata(
            total_records=result.get("total_records", 0),
            records_with_subtema=result.get("records_with_subtema", 0),
            processing_time=processing_time,
            model_used=result.get("model_used", "unknown"),
            template_used=request.template,
            statistics=result.get("statistics")
        )

        # Create response
        response = ReportResponse(
            report_id=uuid4(),
            report_type=request.report_type,
            subtema=request.subtema,
            area=request.area,
            content=result["content"],
            metadata=metadata,
            output_format=request.output_format,
            created_at=result.get("created_at"),
            file_path=result.get("file_path"),
            download_url=result.get("download_url"),
            success=result["success"],
            error=result.get("error")
        )

        logger.info(f"Individual report generated in {processing_time:.2f}s, {metadata.records_with_subtema} records analyzed")
        return response

    except Exception as e:
        logger.error(f"Error generating individual report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Report generation failed: {str(e)}"
        )


@router.post("/overview", response_model=ReportResponse)
async def generate_overview_report(
    request: ReportRequest,
    current_user: dict = Depends(get_current_user),
    _: bool = Depends(check_rate_limit)
):
    """
    Generate comprehensive overview report for an entire area.

    This endpoint creates broad analytical reports covering all subtemas within
    a specific area, providing comparative analysis and general insights.

    **Features:**
    - Cross-subtema comparative analysis
    - Area-wide statistics and trends
    - Distribution analysis across subtemas
    - Executive summaries and key findings
    - Visual data representations (when supported)

    **Report Sections:**
    - Executive Summary
    - Statistical Overview
    - Subtema Distribution
    - Key Findings and Trends
    - Recommendations

    **Example:**
    ```json
    {
        "report_type": "overview",
        "area": "EDUCACAO",
        "data_source": "file_12345",
        "template": "standard",
        "output_format": "html",
        "include_statistics": true,
        "include_examples": false
    }
    ```
    """
    try:
        logger.info(f"Generating overview report for user {current_user['id']}, area: {request.area}")
        start_time = time.time()

        # Validate that this is an overview report
        if request.report_type not in [ReportType.OVERVIEW, ReportType.EXECUTIVE]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="This endpoint only supports overview and executive report types"
            )

        # Call reports service
        result = await reports_service.generate_overview_report(
            area=request.area.value,
            data_source=request.data_source,
            data_source_type=request.data_source_type.value,
            template=request.template.value,
            output_format=request.output_format.value,
            include_statistics=request.include_statistics,
            include_examples=request.include_examples,
            max_examples=request.max_examples,
            language=request.language
        )

        processing_time = time.time() - start_time

        # Create metadata
        metadata = ReportMetadata(
            total_records=result.get("total_records", 0),
            records_with_subtema=result.get("records_analyzed", 0),
            processing_time=processing_time,
            model_used=result.get("model_used", "unknown"),
            template_used=request.template,
            statistics=result.get("statistics")
        )

        # Create response
        response = ReportResponse(
            report_id=uuid4(),
            report_type=request.report_type,
            subtema=None,
            area=request.area,
            content=result["content"],
            metadata=metadata,
            output_format=request.output_format,
            created_at=result.get("created_at"),
            file_path=result.get("file_path"),
            download_url=result.get("download_url"),
            success=result["success"],
            error=result.get("error")
        )

        logger.info(f"Overview report generated in {processing_time:.2f}s, {metadata.total_records} records analyzed")
        return response

    except Exception as e:
        logger.error(f"Error generating overview report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Overview report generation failed: {str(e)}"
        )


@router.get("/templates", response_model=ReportTemplatesResponse)
async def get_report_templates(
    current_user: dict = Depends(get_current_user)
):
    """
    Get available report templates and their configurations.

    Returns all available report templates with their descriptions,
    supported output formats, and configuration options.

    **Template Types:**
    - **Standard**: Balanced report with essential information
    - **Detailed**: Comprehensive analysis with extended sections
    - **Summary**: Concise overview focusing on key points
    - **Technical**: In-depth technical analysis with advanced metrics

    **Output Formats:**
    - **Markdown**: Plain text with formatting for easy editing
    - **HTML**: Web-ready format with styling and interactivity
    - **PDF**: Print-ready document format
    - **Text**: Simple plain text format

    **Response includes:**
    - List of all available templates
    - Detailed descriptions of each template
    - Supported output formats per template
    - Configuration options and recommendations
    """
    try:
        logger.info(f"Fetching report templates for user {current_user['id']}")

        # Get templates from service
        templates_info = reports_service.get_available_templates()

        response = ReportTemplatesResponse(
            templates=templates_info["templates"],
            descriptions=templates_info["descriptions"],
            supported_formats=templates_info["supported_formats"]
        )

        logger.info(f"Returned {len(templates_info['templates'])} report templates")
        return response

    except Exception as e:
        logger.error(f"Error fetching report templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch report templates: {str(e)}"
        )


@router.get("/", response_model=ReportListResponse)
async def list_reports(
    page: int = 1,
    page_size: int = 20,
    report_type: ReportType = None,
    area: str = None,
    current_user: dict = Depends(get_current_user)
):
    """
    List all reports generated by the current user.

    Returns a paginated list of all reports created by the authenticated user,
    with optional filtering by report type and area.

    **Query Parameters:**
    - **page**: Page number (default: 1)
    - **page_size**: Items per page (default: 20, max: 100)
    - **report_type**: Filter by report type (individual, overview, etc.)
    - **area**: Filter by classification area (EDUCACAO, SAUDE, etc.)

    **Response includes:**
    - Paginated list of reports
    - Report metadata and creation dates
    - Download URLs for completed reports
    - Total count and pagination info

    **Example:**
    ```
    GET /api/v1/reports/?page=1&page_size=10&report_type=individual&area=EDUCACAO
    ```
    """
    try:
        logger.info(f"Listing reports for user {current_user['id']}, page {page}")

        # Validate pagination parameters
        if page < 1:
            raise HTTPException(status_code=400, detail="Page must be >= 1")
        if page_size < 1 or page_size > 100:
            raise HTTPException(status_code=400, detail="Page size must be between 1 and 100")

        # Get reports from service
        result = await reports_service.list_user_reports(
            user_id=current_user["id"],
            page=page,
            page_size=page_size,
            report_type=report_type.value if report_type else None,
            area=area
        )

        response = ReportListResponse(
            reports=result["reports"],
            total_count=result["total_count"],
            page=page,
            page_size=page_size,
            total_pages=result["total_pages"]
        )

        logger.info(f"Listed {len(result['reports'])} reports for user {current_user['id']}")
        return response

    except Exception as e:
        logger.error(f"Error listing reports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list reports: {str(e)}"
        )


@router.get("/{report_id}")
async def download_report(
    report_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Download a specific report by ID.

    Returns the content of a previously generated report. The response format
    depends on the original output format chosen during report generation.

    **Supported Formats:**
    - **Markdown**: Returns as text/markdown
    - **HTML**: Returns as text/html
    - **PDF**: Returns as application/pdf
    - **Text**: Returns as text/plain

    **Security:**
    - Users can only download their own reports
    - Report access is validated against user ownership
    - Expired or deleted reports return 404

    **Example:**
    ```
    GET /api/v1/reports/550e8400-e29b-41d4-a716-446655440000
    ```
    """
    try:
        logger.info(f"Downloading report {report_id} for user {current_user['id']}")

        # Get report from service
        result = await reports_service.get_report(
            report_id=report_id,
            user_id=current_user["id"]
        )

        if not result["success"]:
            if "not found" in result.get("error", "").lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Report not found or access denied"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result.get("error", "Failed to retrieve report")
                )

        # Determine content type based on format
        content_type_map = {
            "markdown": "text/markdown",
            "html": "text/html",
            "pdf": "application/pdf",
            "text": "text/plain"
        }

        output_format = result.get("output_format", "text")
        content_type = content_type_map.get(output_format, "text/plain")

        # Return file if it exists, otherwise return content directly
        if result.get("file_path"):
            return FileResponse(
                path=result["file_path"],
                media_type=content_type,
                filename=f"report_{report_id}.{output_format}"
            )
        else:
            return Response(
                content=result["content"],
                media_type=content_type,
                headers={
                    "Content-Disposition": f"attachment; filename=report_{report_id}.{output_format}"
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading report {report_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download report: {str(e)}"
        )
