"""
Anonymization endpoints for text and PDF processing.
"""

import logging
import time
from fastapi import APIRouter, Depends, HTTPException, status


from ..dependencies import get_current_user, check_rate_limit
from ..models.anonymization import (
    AnonymizationRequest,
    AnonymizationResponse,
    PDFAnonymizationRequest,
    PDFAnonymizationResponse,
    SupportedEntitiesResponse,
    EntityType,
    EntityFound
)
from ..services.anonymization_service import AnonymizationService

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize anonymization service
anonymization_service = AnonymizationService()


@router.post("/text", response_model=AnonymizationResponse)
async def anonymize_text(
    request: AnonymizationRequest,
    current_user: dict = Depends(get_current_user),
    _: bool = Depends(check_rate_limit)
):
    """
    Anonymize text using Brazilian-specific recognizers.

    This endpoint processes text to identify and anonymize personally identifiable information (PII)
    using specialized recognizers for Brazilian entities like CPF, addresses, schools, etc.

    **Supported Entity Types:**
    - PERSON: Names of people
    - CPF: Brazilian CPF numbers
    - EMAIL: Email addresses
    - PHONE: Phone numbers
    - ENDERECO: Brazilian addresses
    - ESCOLA: Educational institutions
    - And more...

    **Example:**
    ```json
    {
        "text": "João Silva mora na Rua das Flores, 123 e seu CPF é 123.456.789-00",
        "language": "pt",
        "entities_to_anonymize": ["PERSON", "CPF", "ENDERECO"]
    }
    ```
    """
    try:
        logger.info(f"Processing text anonymization request for user {current_user['id']}")
        start_time = time.time()

        # Call anonymization service
        result = await anonymization_service.anonymize_text(
            text=request.text,
            language=request.language.value,
            entities_to_anonymize=[e.value for e in request.entities_to_anonymize] if request.entities_to_anonymize else None
        )

        processing_time = time.time() - start_time

        # Convert result to response model
        entities_found = [
            EntityFound(
                entity_type=EntityType(entity["entity_type"]),
                start=entity["start"],
                end=entity["end"],
                score=entity["score"],
                text=entity["text"],
                anonymized_text=entity.get("anonymized_text")
            )
            for entity in result.get("entities_found", [])
        ]

        response = AnonymizationResponse(
            anonymized_text=result["anonymized_text"],
            original_text=request.text,
            entities_found=entities_found,
            entities_count=len(entities_found),
            processing_time=processing_time,
            language_used=request.language,
            success=result["success"],
            error=result.get("error")
        )

        logger.info(f"Text anonymization completed in {processing_time:.2f}s, found {len(entities_found)} entities")
        return response

    except Exception as e:
        logger.error(f"Error in text anonymization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Anonymization failed: {str(e)}"
        )


@router.post("/pdf", response_model=PDFAnonymizationResponse)
async def anonymize_pdf(
    request: PDFAnonymizationRequest,
    current_user: dict = Depends(get_current_user),
    _: bool = Depends(check_rate_limit)
):
    """
    Anonymize PDF and extract clean text.

    This endpoint processes PDF files to extract text and anonymize any personally identifiable
    information found within the document.

    **Features:**
    - Text extraction from PDF
    - PII detection and anonymization
    - Page count and metadata
    - Support for multi-page documents

    **Example:**
    ```json
    {
        "file_path": "/path/to/document.pdf",
        "language": "pt",
        "extract_text_only": false
    }
    ```
    """
    try:
        logger.info(f"Processing PDF anonymization request for user {current_user['id']}")
        start_time = time.time()

        # Call PDF anonymization service
        result = await anonymization_service.anonymize_pdf(
            file_path=request.file_path,
            language=request.language.value,
            entities_to_anonymize=[e.value for e in request.entities_to_anonymize] if request.entities_to_anonymize else None,
            extract_text_only=request.extract_text_only
        )

        processing_time = time.time() - start_time

        # Convert result to response model
        entities_found = [
            EntityFound(
                entity_type=EntityType(entity["entity_type"]),
                start=entity["start"],
                end=entity["end"],
                score=entity["score"],
                text=entity["text"],
                anonymized_text=entity.get("anonymized_text")
            )
            for entity in result.get("entities_found", [])
        ]

        response = PDFAnonymizationResponse(
            anonymized_text=result["anonymized_text"],
            original_text=result["original_text"],
            entities_found=entities_found,
            entities_count=len(entities_found),
            page_count=result.get("page_count"),
            processing_time=processing_time,
            language_used=request.language,
            success=result["success"],
            error=result.get("error")
        )

        logger.info(f"PDF anonymization completed in {processing_time:.2f}s, found {len(entities_found)} entities")
        return response

    except Exception as e:
        logger.error(f"Error in PDF anonymization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"PDF anonymization failed: {str(e)}"
        )


@router.get("/entities", response_model=SupportedEntitiesResponse)
async def get_supported_entities(
    current_user: dict = Depends(get_current_user)
):
    """
    Get list of supported entity types for anonymization.

    Returns all entity types that can be detected and anonymized by the system,
    along with descriptions of what each entity type represents.

    **Entity Types Include:**
    - Brazilian-specific entities (CPF, addresses, schools)
    - International entities (emails, phones, credit cards)
    - Personal information (names, dates)
    """
    try:
        logger.info(f"Fetching supported entities for user {current_user['id']}")

        # Get supported entities from service
        entities = anonymization_service.get_supported_entities()

        # Create descriptions for each entity type
        descriptions = {
            EntityType.PERSON: "Names of people and individuals",
            EntityType.CPF: "Brazilian CPF (Cadastro de Pessoas Físicas) numbers",
            EntityType.EMAIL: "Email addresses",
            EntityType.PHONE: "Phone numbers in various formats",
            EntityType.ENDERECO: "Brazilian addresses and locations",
            EntityType.ESCOLA: "Educational institutions and schools",
            EntityType.CREDIT_CARD: "Credit card numbers",
            EntityType.IBAN_CODE: "International Bank Account Numbers",
            EntityType.IP_ADDRESS: "IP addresses (IPv4 and IPv6)",
            EntityType.DATE_TIME: "Dates and timestamps"
        }

        # Filter descriptions to only include supported entities
        supported_entity_types = [EntityType(entity) for entity in entities]
        filtered_descriptions = {
            entity_type: descriptions.get(entity_type, "Entity description not available")
            for entity_type in supported_entity_types
        }

        response = SupportedEntitiesResponse(
            entities=supported_entity_types,
            descriptions=filtered_descriptions
        )

        logger.info(f"Returned {len(supported_entity_types)} supported entity types")
        return response

    except Exception as e:
        logger.error(f"Error fetching supported entities: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch supported entities: {str(e)}"
        )
