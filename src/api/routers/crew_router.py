"""
CrewAI Router for FastAPI integration in Simple Class.

This router exposes CrewAI workflows through REST API endpoints,
providing orchestrated multi-agent processing capabilities.
"""

from typing import Dict, List, Any, Optional, Union
import logging
from fastapi import APIRouter, HTTPException, BackgroundTasks, UploadFile, File
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import pandas as pd
import io
import uuid
from datetime import datetime

# Import CrewAI components
try:
    from ...crews.processing_crew import ProcessingCrew
except ImportError:
    ProcessingCrew = None

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/crew", tags=["CrewAI Workflows"])

# Global crew instance (will be initialized on first use)
_processing_crew: Optional[ProcessingCrew] = None

# In-memory storage for async job tracking
_job_status: Dict[str, Dict[str, Any]] = {}


# Pydantic models for request/response
class WorkflowRequest(BaseModel):
    """Request model for workflow execution."""
    workflow_type: str = Field(
        default="full_pipeline",
        description="Type of workflow to execute",
        enum=["full_pipeline", "anonymize_classify", "classify_report", 
              "anonymize_only", "classify_only", "report_only"]
    )
    text: Optional[str] = Field(None, description="Text to process (for single text workflows)")
    area: str = Field(default="EDUCACAO", description="Classification area")
    language: str = Field(default="pt", description="Language for processing")
    model_name: Optional[str] = Field(None, description="LLM model to use")
    report_type: str = Field(default="overview", description="Type of report to generate")
    max_subtemas: int = Field(default=3, description="Maximum number of subtemas for classification")
    min_cases: int = Field(default=1, description="Minimum cases for report generation")
    output_path: Optional[str] = Field(None, description="Path to save outputs")


class WorkflowResponse(BaseModel):
    """Response model for workflow execution."""
    success: bool
    execution_id: str
    workflow_type: str
    execution_time: Optional[float] = None
    results: Dict[str, Any] = {}
    tasks_completed: int = 0
    total_tasks: int = 0
    error: Optional[str] = None


class JobStatusResponse(BaseModel):
    """Response model for job status."""
    job_id: str
    status: str  # "pending", "running", "completed", "failed"
    progress: float  # 0.0 to 1.0
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: datetime
    updated_at: datetime


def get_processing_crew() -> ProcessingCrew:
    """Get or create the global processing crew instance."""
    global _processing_crew
    
    if _processing_crew is None:
        if ProcessingCrew is None:
            raise HTTPException(
                status_code=503,
                detail="CrewAI not available. Please install crewai package."
            )
        
        try:
            _processing_crew = ProcessingCrew()
            logger.info("ProcessingCrew initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing ProcessingCrew: {e}")
            raise HTTPException(
                status_code=503,
                detail=f"Failed to initialize CrewAI: {str(e)}"
            )
    
    return _processing_crew


@router.get("/health", summary="Health check for CrewAI services")
async def health_check():
    """Check the health of CrewAI services."""
    try:
        crew = get_processing_crew()
        available_agents = crew.get_available_agents()
        
        return {
            "status": "healthy",
            "crewai_available": ProcessingCrew is not None,
            "agents": available_agents,
            "supported_workflows": crew.get_supported_workflows()
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "crewai_available": ProcessingCrew is not None
            }
        )


@router.get("/workflows", summary="Get available workflow types")
async def get_workflows():
    """Get list of available workflow types and their descriptions."""
    workflows = {
        "full_pipeline": {
            "description": "Complete pipeline: anonymization → classification → reporting",
            "agents": ["anonymization", "classification", "report"],
            "input_required": ["text or csv_file"],
            "output": "Anonymized text, classification results, and tactical report"
        },
        "anonymize_classify": {
            "description": "Partial pipeline: anonymization → classification",
            "agents": ["anonymization", "classification"],
            "input_required": ["text or csv_file"],
            "output": "Anonymized text and classification results"
        },
        "classify_report": {
            "description": "Partial pipeline: classification → reporting",
            "agents": ["classification", "report"],
            "input_required": ["text or csv_file"],
            "output": "Classification results and tactical report"
        },
        "anonymize_only": {
            "description": "Single agent: anonymization only",
            "agents": ["anonymization"],
            "input_required": ["text"],
            "output": "Anonymized text with PII entities identified"
        },
        "classify_only": {
            "description": "Single agent: classification only",
            "agents": ["classification"],
            "input_required": ["text"],
            "output": "Multilabel classification results"
        },
        "report_only": {
            "description": "Single agent: reporting only",
            "agents": ["report"],
            "input_required": ["csv_file with classification results"],
            "output": "Tactical analysis report"
        }
    }
    
    return {"workflows": workflows}


@router.post("/process", response_model=WorkflowResponse, summary="Execute CrewAI workflow")
async def process_workflow(request: WorkflowRequest):
    """
    Execute a CrewAI workflow with the specified configuration.
    
    This endpoint processes text through the selected workflow pipeline,
    which can include anonymization, classification, and report generation.
    """
    try:
        # Get processing crew
        crew = get_processing_crew()
        
        # Validate workflow type
        if request.workflow_type not in crew.get_supported_workflows():
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported workflow type: {request.workflow_type}"
            )
        
        # Prepare workflow input
        workflow_input = {
            "workflow_type": request.workflow_type,
            "text": request.text,
            "area": request.area.upper(),
            "language": request.language,
            "model_name": request.model_name,
            "report_type": request.report_type,
            "max_subtemas": request.max_subtemas,
            "min_cases": request.min_cases,
            "output_path": request.output_path
        }
        
        # Validate input based on workflow type
        if request.workflow_type in ["anonymize_only", "classify_only", "anonymize_classify"]:
            if not request.text:
                raise HTTPException(
                    status_code=400,
                    detail="Text input required for this workflow type"
                )
        
        # Execute workflow
        result = crew.kickoff(workflow_input)
        
        # Convert to response model
        response = WorkflowResponse(
            success=result.get("success", False),
            execution_id=result.get("execution_id", "unknown"),
            workflow_type=result.get("workflow_type", request.workflow_type),
            execution_time=result.get("execution_time"),
            results=result.get("results", {}),
            tasks_completed=result.get("tasks_completed", 0),
            total_tasks=result.get("total_tasks", 0),
            error=result.get("error")
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing workflow: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Workflow processing failed: {str(e)}"
        )


@router.post("/process-file", summary="Process CSV file through CrewAI workflow")
async def process_file(
    file: UploadFile = File(...),
    workflow_type: str = "full_pipeline",
    area: str = "EDUCACAO",
    text_column: str = "Teor",
    background_tasks: BackgroundTasks = None
):
    """
    Process a CSV file through CrewAI workflow.
    
    This endpoint accepts a CSV file and processes it through the specified
    workflow pipeline. For large files, processing is done in the background.
    """
    try:
        # Validate file type
        if not file.filename.endswith('.csv'):
            raise HTTPException(
                status_code=400,
                detail="Only CSV files are supported"
            )
        
        # Read CSV file
        content = await file.read()
        df = pd.read_csv(io.StringIO(content.decode('utf-8')))
        
        # Validate text column exists
        if text_column not in df.columns:
            raise HTTPException(
                status_code=400,
                detail=f"Column '{text_column}' not found in CSV. Available columns: {list(df.columns)}"
            )
        
        # Create job ID for tracking
        job_id = str(uuid.uuid4())
        
        # Store initial job status
        _job_status[job_id] = {
            "job_id": job_id,
            "status": "pending",
            "progress": 0.0,
            "result": None,
            "error": None,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "workflow_type": workflow_type,
            "total_rows": len(df)
        }
        
        # For small files, process synchronously
        if len(df) <= 10:
            return await _process_file_sync(df, workflow_type, area, text_column, job_id)
        
        # For large files, process in background
        if background_tasks:
            background_tasks.add_task(
                _process_file_async, df, workflow_type, area, text_column, job_id
            )
        
        return {
            "job_id": job_id,
            "status": "accepted",
            "message": f"File processing started. Use /crew/jobs/{job_id} to check status.",
            "total_rows": len(df)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing file: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"File processing failed: {str(e)}"
        )


async def _process_file_sync(
    df: pd.DataFrame,
    workflow_type: str,
    area: str,
    text_column: str,
    job_id: str
) -> Dict[str, Any]:
    """Process file synchronously for small files."""
    try:
        crew = get_processing_crew()
        
        # Update job status
        _job_status[job_id]["status"] = "running"
        _job_status[job_id]["updated_at"] = datetime.now()
        
        # Prepare workflow input
        workflow_input = {
            "workflow_type": workflow_type,
            "data": df,
            "area": area.upper(),
            "text_column": text_column
        }
        
        # Execute workflow
        result = crew.kickoff(workflow_input)
        
        # Update job status
        _job_status[job_id]["status"] = "completed" if result.get("success") else "failed"
        _job_status[job_id]["progress"] = 1.0
        _job_status[job_id]["result"] = result
        _job_status[job_id]["error"] = result.get("error")
        _job_status[job_id]["updated_at"] = datetime.now()
        
        return {
            "job_id": job_id,
            "status": _job_status[job_id]["status"],
            "result": result
        }
        
    except Exception as e:
        _job_status[job_id]["status"] = "failed"
        _job_status[job_id]["error"] = str(e)
        _job_status[job_id]["updated_at"] = datetime.now()
        raise


async def _process_file_async(
    df: pd.DataFrame,
    workflow_type: str,
    area: str,
    text_column: str,
    job_id: str
):
    """Process file asynchronously for large files."""
    try:
        crew = get_processing_crew()
        
        # Update job status
        _job_status[job_id]["status"] = "running"
        _job_status[job_id]["updated_at"] = datetime.now()
        
        # Process in batches for progress tracking
        batch_size = 50
        total_rows = len(df)
        results = []
        
        for i in range(0, total_rows, batch_size):
            batch_df = df.iloc[i:i+batch_size]
            
            # Prepare workflow input for batch
            workflow_input = {
                "workflow_type": workflow_type,
                "data": batch_df,
                "area": area.upper(),
                "text_column": text_column
            }
            
            # Execute workflow for batch
            batch_result = crew.kickoff(workflow_input)
            results.append(batch_result)
            
            # Update progress
            progress = min((i + batch_size) / total_rows, 1.0)
            _job_status[job_id]["progress"] = progress
            _job_status[job_id]["updated_at"] = datetime.now()
        
        # Combine results
        final_result = {
            "success": all(r.get("success", False) for r in results),
            "batch_results": results,
            "total_batches": len(results),
            "total_rows": total_rows
        }
        
        # Update final job status
        _job_status[job_id]["status"] = "completed"
        _job_status[job_id]["progress"] = 1.0
        _job_status[job_id]["result"] = final_result
        _job_status[job_id]["updated_at"] = datetime.now()
        
    except Exception as e:
        _job_status[job_id]["status"] = "failed"
        _job_status[job_id]["error"] = str(e)
        _job_status[job_id]["updated_at"] = datetime.now()
        logger.error(f"Error in async file processing: {e}")


@router.get("/jobs/{job_id}", response_model=JobStatusResponse, summary="Get job status")
async def get_job_status(job_id: str):
    """Get the status of a background processing job."""
    if job_id not in _job_status:
        raise HTTPException(
            status_code=404,
            detail=f"Job {job_id} not found"
        )
    
    job_info = _job_status[job_id]
    
    return JobStatusResponse(
        job_id=job_info["job_id"],
        status=job_info["status"],
        progress=job_info["progress"],
        result=job_info["result"],
        error=job_info["error"],
        created_at=job_info["created_at"],
        updated_at=job_info["updated_at"]
    )


@router.get("/jobs", summary="List all jobs")
async def list_jobs(limit: int = 50):
    """List recent processing jobs."""
    jobs = list(_job_status.values())
    
    # Sort by creation time (newest first)
    jobs.sort(key=lambda x: x["created_at"], reverse=True)
    
    # Limit results
    jobs = jobs[:limit]
    
    return {"jobs": jobs, "total": len(_job_status)}


@router.delete("/jobs/{job_id}", summary="Delete job")
async def delete_job(job_id: str):
    """Delete a job and its results."""
    if job_id not in _job_status:
        raise HTTPException(
            status_code=404,
            detail=f"Job {job_id} not found"
        )
    
    del _job_status[job_id]
    
    return {"message": f"Job {job_id} deleted successfully"}
