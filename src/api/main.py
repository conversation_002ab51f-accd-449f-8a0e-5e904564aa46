"""
FastAPI main application for Simple Class API.

This API provides endpoints for:
- Text and PDF anonymization using Presidio
- Multilabel and unilabel classification using LLMs
- Report generation with Claude Sonnet 4
- File upload and management with Supabase Storage
"""

import logging
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse

from .config import settings
from .middleware import setup_cors_middleware, setup_custom_middleware
from .docs_config import tags_metadata, customize_openapi_schema
from .routers import (
    anonymization_router,
    classification_router,
    reports_router,
    files_router,
    auth_router,
    crew_router,
    batch_router
)
from backend.src.api.v1.rag import router as rag_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Create FastAPI application with enhanced documentation
app = FastAPI(
    title=settings.api_title,
    description="""
## Simple Class API

Uma API completa para processamento de texto e documentos com foco em políticas públicas.

### Funcionalidades Principais

🔐 **Autenticação**
- Sistema de login com JWT tokens
- Rate limiting e segurança

📁 **Gerenciamento de Arquivos**
- Upload de arquivos CSV e PDF
- Integração com Supabase Storage
- Validação e metadados

🏷️ **Classificação de Texto**
- Classificação multilabel (até 3 subtemas)
- Classificação unilabel (TIPO_UNIDADE, CRE)
- Suporte para múltiplas áreas (Educação, Saúde, Meio Ambiente)
- Modelos LLM avançados (Llama, Claude)

🔒 **Anonimização**
- Anonimização de texto e PDF
- Detecção de PII (CPF, telefone, email, etc.)
- Suporte para português e inglês

📊 **Relatórios**
- Relatórios individuais por subtema
- Relatórios de visão geral
- Geração com Claude Sonnet 4

### Tecnologias

- **Backend**: FastAPI, Python 3.11+
- **Banco de Dados**: Supabase (PostgreSQL)
- **Storage**: Supabase Storage
- **LLMs**: Together AI, Fireworks AI, Anthropic Claude
- **Anonimização**: Microsoft Presidio
- **Autenticação**: JWT tokens

### Ambientes

- **Desenvolvimento**: Dados de teste e mocks
- **Produção**: Integração completa com serviços externos
    """,
    version=settings.api_version,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    openapi_tags=tags_metadata,
    contact={
        "name": "Simple Class API",
        "url": "https://github.com/daniribeiroBR/simple_class",
        "email": "<EMAIL>"
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT"
    },
    servers=[
        {
            "url": "http://localhost:8000",
            "description": "Desenvolvimento"
        },
        {
            "url": "https://api.simpleclass.com",
            "description": "Produção"
        }
    ]
)

# Setup middleware
setup_cors_middleware(app)
setup_custom_middleware(app)

# Include routers with enhanced documentation
app.include_router(
    auth_router,
    prefix=f"{settings.api_prefix}/auth",
    tags=["🔐 Autenticação"],
    responses={
        401: {"description": "Token inválido ou expirado"},
        403: {"description": "Acesso negado"},
        429: {"description": "Rate limit excedido"}
    }
)

app.include_router(
    files_router,
    prefix=f"{settings.api_prefix}/files",
    tags=["📁 Gerenciamento de Arquivos"],
    responses={
        400: {"description": "Arquivo inválido ou formato não suportado"},
        413: {"description": "Arquivo muito grande"},
        422: {"description": "Dados de entrada inválidos"}
    }
)

app.include_router(
    classification_router,
    prefix=f"{settings.api_prefix}/classification",
    tags=["🏷️ Classificação de Texto"],
    responses={
        400: {"description": "Texto vazio ou parâmetros inválidos"},
        500: {"description": "Erro na classificação com LLM"}
    }
)

app.include_router(
    anonymization_router,
    prefix=f"{settings.api_prefix}/anonymization",
    tags=["🔒 Anonimização"],
    responses={
        400: {"description": "Texto vazio ou idioma não suportado"},
        500: {"description": "Erro no processamento de anonimização"}
    }
)

app.include_router(
    reports_router,
    prefix=f"{settings.api_prefix}/reports",
    tags=["📊 Relatórios"],
    responses={
        404: {"description": "Arquivo ou relatório não encontrado"},
        500: {"description": "Erro na geração do relatório"}
    }
)

app.include_router(
    crew_router,
    prefix=f"{settings.api_prefix}/crew",
    tags=["🤖 CrewAI Workflows"],
    responses={
        400: {"description": "Parâmetros de workflow inválidos"},
        503: {"description": "CrewAI não disponível"},
        500: {"description": "Erro na execução do workflow"}
    }
)

app.include_router(
    batch_router,
    prefix="",  # Already includes prefix in router
    tags=["📦 Batch Processing"],
    responses={
        400: {"description": "Parâmetros de batch inválidos"},
        404: {"description": "Job não encontrado"},
        403: {"description": "Acesso negado ao job"},
        429: {"description": "Rate limit excedido"},
        500: {"description": "Erro no processamento em lote"}
    }
)

app.include_router(
    rag_router,
    prefix=f"{settings.api_prefix}/rag",
    tags=["🧠 RAG - Consulta Inteligente"],
    responses={
        400: {"description": "Query inválida ou parâmetros incorretos"},
        404: {"description": "Documento não encontrado ou não indexado"},
        500: {"description": "Erro no sistema RAG"}
    }
)


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Simple Class API",
        "version": settings.api_version,
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": "2025-01-23T00:00:00Z",
        "version": settings.api_version
    }


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Internal server error",
            "type": "internal_error"
        }
    )


# Customize OpenAPI schema
app.openapi = lambda: customize_openapi_schema(app)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
