"""
ChunkClassifier component for processing individual text chunks.

This module provides chunk-level classification functionality, adapting the existing
multilabel classification system to work with PDF text chunks while maintaining
evidence text extraction and confidence scoring.
"""

import logging
import time
import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from ..models.batch_processing import Text<PERSON>hun<PERSON>, Chunk<PERSON><PERSON>ult
from .classification_service import ClassificationService
from ...analisadores.analisador_multilabel import classificar_relato_multilabel
from ...utils.config_utils import carregar_configuracao, carregar_variaveis_ambiente

logger = logging.getLogger(__name__)


@dataclass
class ChunkClassificationConfig:
    """Configuration for chunk classification."""
    max_subtemas: int = 3
    confidence_threshold: float = 0.3
    evidence_max_length: int = 200
    model_name: Optional[str] = None
    api_key: Optional[str] = None


class ChunkClassifier:
    """
    Classifier for individual text chunks with evidence extraction.
    
    Adapts the existing multilabel classification system to work with PDF chunks,
    providing confidence scoring and evidence text extraction for each subtema.
    """
    
    def __init__(self, classification_service: Optional[ClassificationService] = None):
        """
        Initialize the chunk classifier.
        
        Args:
            classification_service: Optional existing classification service instance
        """
        self.classification_service = classification_service or ClassificationService()
        self.config = carregar_configuracao()
        self.env_vars = carregar_variaveis_ambiente()
        
        # Setup default configuration
        self.default_config = ChunkClassificationConfig(
            model_name=self.config.get('LLM_MODEL', 'meta-llama/Llama-3.3-70B-Instruct-Turbo'),
            api_key=self.env_vars.get("FIREWORKS_API_KEY")
        )
        
        if not self.default_config.api_key:
            logger.warning("FIREWORKS_API_KEY not found in environment variables")
    
    async def classify_chunks(
        self, 
        chunks: List[TextChunk], 
        area: str,
        config: Optional[ChunkClassificationConfig] = None
    ) -> List[ChunkResult]:
        """
        Classify a list of text chunks.
        
        Args:
            chunks: List of text chunks to classify
            area: Classification area (EDUCACAO, SAUDE, MEIO_AMBIENTE)
            config: Optional configuration override
            
        Returns:
            List of chunk classification results
        """
        if not chunks:
            return []
        
        config = config or self.default_config
        results = []
        
        logger.info(f"Starting classification of {len(chunks)} chunks for area {area}")
        
        for i, chunk in enumerate(chunks):
            try:
                result = await self.classify_single_chunk(chunk, area, config)
                results.append(result)
                
                # Log progress every 10 chunks
                if (i + 1) % 10 == 0:
                    logger.info(f"Processed {i + 1}/{len(chunks)} chunks")
                    
            except Exception as e:
                logger.error(f"Error classifying chunk {chunk.chunk_id}: {e}")
                # Create error result
                error_result = ChunkResult(
                    chunk=chunk,
                    subtemas=[],
                    confidence_scores={},
                    evidence_texts={},
                    processing_time=0.0,
                    error=str(e)
                )
                results.append(error_result)
        
        logger.info(f"Completed classification of {len(chunks)} chunks")
        return results
    
    async def classify_single_chunk(
        self, 
        chunk: TextChunk, 
        area: str,
        config: Optional[ChunkClassificationConfig] = None
    ) -> ChunkResult:
        """
        Classify a single text chunk.
        
        Args:
            chunk: Text chunk to classify
            area: Classification area
            config: Optional configuration override
            
        Returns:
            Chunk classification result with evidence and confidence scores
        """
        config = config or self.default_config
        start_time = time.time()
        
        try:
            # Get subtemas for the area
            subtemas = self.classification_service.get_subtemas_for_area(area)
            if not subtemas:
                raise ValueError(f"No subtemas found for area: {area}")
            
            # Perform classification using existing multilabel function
            identified_subtemas = classificar_relato_multilabel(
                relato=chunk.text,
                subtemas=subtemas,
                area=area,
                model_name=config.model_name,
                api_key=config.api_key,
                idx=chunk.chunk_id
            )
            
            # Limit to max_subtemas
            if len(identified_subtemas) > config.max_subtemas:
                identified_subtemas = identified_subtemas[:config.max_subtemas]
            
            # Generate confidence scores (simplified approach)
            confidence_scores = self._generate_confidence_scores(
                identified_subtemas, config.confidence_threshold
            )
            
            # Extract evidence texts
            evidence_texts = self._extract_evidence_texts(
                chunk, identified_subtemas, config.evidence_max_length
            )
            
            processing_time = time.time() - start_time
            
            return ChunkResult(
                chunk=chunk,
                subtemas=identified_subtemas,
                confidence_scores=confidence_scores,
                evidence_texts=evidence_texts,
                processing_time=processing_time,
                error=None
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error classifying chunk {chunk.chunk_id}: {e}")
            
            return ChunkResult(
                chunk=chunk,
                subtemas=[],
                confidence_scores={},
                evidence_texts={},
                processing_time=processing_time,
                error=str(e)
            )
    
    def _generate_confidence_scores(
        self, 
        subtemas: List[str], 
        base_confidence: float = 0.7
    ) -> Dict[str, float]:
        """
        Generate confidence scores for identified subtemas.
        
        This is a simplified approach. In a production system, you might want
        to extract actual confidence scores from the LLM response.
        
        Args:
            subtemas: List of identified subtemas
            base_confidence: Base confidence score to assign
            
        Returns:
            Dictionary mapping subtemas to confidence scores
        """
        confidence_scores = {}
        
        # Assign decreasing confidence scores based on order
        for i, subtema in enumerate(subtemas):
            # First subtema gets highest confidence, subsequent ones get slightly lower
            confidence = max(base_confidence - (i * 0.1), 0.3)
            confidence_scores[subtema] = round(confidence, 2)
        
        return confidence_scores
    
    def _extract_evidence_texts(
        self, 
        chunk: TextChunk, 
        subtemas: List[str], 
        max_length: int = 200
    ) -> Dict[str, str]:
        """
        Extract evidence text for each identified subtema.
        
        This method attempts to find the most relevant text segments that
        support each subtema classification.
        
        Args:
            chunk: The text chunk being classified
            subtemas: List of identified subtemas
            max_length: Maximum length of evidence text
            
        Returns:
            Dictionary mapping subtemas to evidence text
        """
        evidence_texts = {}
        text = chunk.text.lower()
        
        # Load subtema definitions for keyword matching
        try:
            from ...analisadores.analisador_multilabel import carregar_definicoes_completas_subtemas
            definicoes = carregar_definicoes_completas_subtemas(chunk.pdf_path.split('/')[-1].split('_')[0] if '_' in chunk.pdf_path else 'EDUCACAO')
        except Exception:
            definicoes = {}
        
        for subtema in subtemas:
            evidence = self._find_evidence_for_subtema(
                text, subtema, definicoes.get(subtema, {}), max_length
            )
            if evidence:
                evidence_texts[subtema] = evidence
        
        return evidence_texts
    
    def _find_evidence_for_subtema(
        self, 
        text: str, 
        subtema: str, 
        subtema_info: Dict[str, Any], 
        max_length: int
    ) -> str:
        """
        Find the best evidence text for a specific subtema.
        
        Args:
            text: The chunk text (lowercased)
            subtema: The subtema name
            subtema_info: Information about the subtema (keywords, definition)
            max_length: Maximum length of evidence text
            
        Returns:
            Evidence text or empty string if none found
        """
        # Get keywords for this subtema
        keywords = subtema_info.get('palavras_chave', [])
        if not keywords:
            # Fallback to subtema name components
            keywords = [word.lower() for word in subtema.replace('_', ' ').split()]
        
        # Find sentences containing keywords
        sentences = re.split(r'[.!?]+', text)
        scored_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) < 20:  # Skip very short sentences
                continue
                
            score = 0
            for keyword in keywords:
                if isinstance(keyword, str) and keyword.lower() in sentence:
                    score += 1
            
            if score > 0:
                scored_sentences.append((score, sentence))
        
        if not scored_sentences:
            # Fallback: return first meaningful sentence
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) >= 30:
                    return self._truncate_evidence(sentence, max_length)
            return ""
        
        # Sort by score and get the best sentence
        scored_sentences.sort(key=lambda x: x[0], reverse=True)
        best_sentence = scored_sentences[0][1]
        
        return self._truncate_evidence(best_sentence, max_length)
    
    def _truncate_evidence(self, text: str, max_length: int) -> str:
        """
        Truncate evidence text to maximum length while preserving word boundaries.
        
        Args:
            text: Text to truncate
            max_length: Maximum allowed length
            
        Returns:
            Truncated text with "..." if truncated
        """
        if len(text) <= max_length:
            return text.strip()
        
        # Find the last space before max_length
        truncated = text[:max_length]
        last_space = truncated.rfind(' ')
        
        if last_space > max_length * 0.8:  # If we can preserve most of the text
            return truncated[:last_space].strip() + "..."
        else:
            return truncated.strip() + "..."
    
    def get_supported_areas(self) -> List[str]:
        """
        Get list of supported classification areas.
        
        Returns:
            List of supported area names
        """
        return ["EDUCACAO", "SAUDE", "MEIO_AMBIENTE"]
    
    def validate_area(self, area: str) -> bool:
        """
        Validate that an area is supported.
        
        Args:
            area: Area name to validate
            
        Returns:
            True if area is supported, False otherwise
        """
        return area in self.get_supported_areas()