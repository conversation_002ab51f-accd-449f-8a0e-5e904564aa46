"""
Service for file management operations with Supabase Storage.
"""

import logging
import os
from typing import Dict, Any, List, Optional
from uuid import uuid4
from fastapi import UploadFile
from supabase import Client

from ..database.client import get_supabase_client, get_supabase_admin_client
from ..database.models import FileRecord
from ..config import settings

logger = logging.getLogger(__name__)


class FileService:
    """Service for handling file operations with Supabase Storage."""
    
    def __init__(self):
        """Initialize file service."""
        self.client = get_supabase_client()
        self.admin_client = get_supabase_admin_client()
        self.bucket_name = "simple-class-files"
    
    async def upload_file(
        self,
        file: UploadFile,
        user_id: str,
        metadata: Optional[Dict[str, Any]] = None,
        process_immediately: bool = False
    ) -> Dict[str, Any]:
        """
        Upload file to Supabase Storage.

        Args:
            file: File to upload
            user_id: ID of the user uploading the file
            metadata: Optional file metadata
            process_immediately: Whether to start processing immediately

        Returns:
            Upload result dictionary
        """
        try:
            # Validate file type
            if file.content_type not in settings.allowed_file_types:
                raise ValueError(f"File type {file.content_type} not allowed")

            # Read file content
            file_content = await file.read()

            # Validate file size
            if len(file_content) > settings.max_file_size:
                raise ValueError(f"File size exceeds maximum allowed size ({settings.max_file_size} bytes)")

            # Generate unique file path
            file_id = str(uuid4())
            file_extension = os.path.splitext(file.filename)[1]
            file_path = f"{user_id}/{file_id}{file_extension}"

            # Check if Supabase client is available
            if not self.client:
                raise Exception("Supabase client not available")

            # Upload to Supabase Storage
            try:
                storage_result = self.client.storage.from_(self.bucket_name).upload(
                    file_path, file_content
                )

                # Check for errors in storage result
                if hasattr(storage_result, 'error') and storage_result.error:
                    raise Exception(f"Storage upload failed: {storage_result.error}")

            except Exception as storage_error:
                logger.warning(f"Supabase storage upload failed: {storage_error}")
                # For now, continue without storage (development mode)
                logger.info("Continuing in development mode without Supabase storage")

            # Prepare file record
            from datetime import datetime
            current_time = datetime.now().isoformat()

            file_record_data = {
                "id": file_id,
                "user_id": user_id,
                "filename": file.filename,
                "file_path": file_path,
                "file_type": file.content_type,
                "file_size": len(file_content),
                "metadata": metadata or {},
                "status": "uploaded",
                "created_at": current_time,
                "updated_at": current_time
            }

            # Try to save to database
            try:
                db_result = self.client.table("files").insert(file_record_data).execute()

                if not db_result.data:
                    logger.warning("Database insert returned no data")

            except Exception as db_error:
                logger.warning(f"Database insert failed: {db_error}")
                # For now, continue without database (development mode)
                logger.info("Continuing in development mode without database")

            # Generate download URL (mock for development)
            download_url = f"/api/v1/files/{file_id}/download"

            return {
                "file_id": file_id,
                "filename": file.filename,
                "file_path": file_path,
                "file_type": file.content_type,
                "file_size": len(file_content),
                "metadata": metadata,
                "download_url": download_url,
                "created_at": current_time,
                "success": True
            }

        except Exception as e:
            logger.error(f"Error in file upload: {e}")
            return {
                "file_id": None,
                "filename": file.filename if file else None,
                "success": False,
                "error": str(e)
            }
    
    async def download_file(self, file_id: str, user_id: str) -> Dict[str, Any]:
        """
        Download file from Supabase Storage.
        
        Args:
            file_id: ID of the file to download
            user_id: ID of the user requesting the file
            
        Returns:
            Download result dictionary
        """
        try:
            # Get file record from database
            file_result = self.client.table("files").select("*").eq("id", file_id).eq("user_id", user_id).execute()
            
            if not file_result.data:
                raise Exception("File not found or access denied")
            
            file_record = file_result.data[0]
            
            # Download from Supabase Storage
            storage_result = self.client.storage.from_(self.bucket_name).download(file_record["file_path"])
            
            return {
                "file_id": file_id,
                "filename": file_record["filename"],
                "content": storage_result,
                "content_type": file_record["file_type"],
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error in file download: {e}")
            return {
                "file_id": file_id,
                "success": False,
                "error": str(e)
            }
    
    async def delete_file(self, file_id: str, user_id: str) -> Dict[str, Any]:
        """
        Delete file from Supabase Storage and database.
        
        Args:
            file_id: ID of the file to delete
            user_id: ID of the user requesting deletion
            
        Returns:
            Deletion result dictionary
        """
        try:
            # Get file record from database
            file_result = self.client.table("files").select("*").eq("id", file_id).eq("user_id", user_id).execute()
            
            if not file_result.data:
                raise Exception("File not found or access denied")
            
            file_record = file_result.data[0]
            
            # Delete from Supabase Storage
            storage_result = self.client.storage.from_(self.bucket_name).remove([file_record["file_path"]])
            
            # Delete from database
            db_result = self.client.table("files").delete().eq("id", file_id).execute()
            
            return {
                "file_id": file_id,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error in file deletion: {e}")
            return {
                "file_id": file_id,
                "success": False,
                "error": str(e)
            }
    
    async def list_files(
        self,
        user_id: str,
        page: int = 1,
        page_size: int = 20,
        file_type: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        List files for a user with pagination and filtering.

        Args:
            user_id: ID of the user
            page: Page number
            page_size: Items per page
            file_type: Optional filter by file type
            status: Optional filter by status

        Returns:
            File list result dictionary
        """
        try:
            if not self.client:
                # Return mock data for development
                return self._get_mock_file_list(user_id, page, page_size)

            # Build query
            query = self.client.table("files").select("*").eq("user_id", user_id)

            # Apply filters
            if file_type:
                query = query.eq("file_type", file_type)
            if status:
                query = query.eq("status", status)

            # Get total count
            count_result = query.execute()
            total_count = len(count_result.data) if count_result.data else 0

            # Apply pagination
            offset = (page - 1) * page_size
            files_result = query.range(offset, offset + page_size - 1).execute()

            files = files_result.data or []
            total_size = sum(file.get("file_size", 0) for file in files)
            total_pages = (total_count + page_size - 1) // page_size

            return {
                "files": files,
                "total_count": total_count,
                "total_size": total_size,
                "total_pages": total_pages,
                "success": True
            }

        except Exception as e:
            logger.error(f"Error in file listing: {e}")
            return {
                "files": [],
                "total_count": 0,
                "total_size": 0,
                "total_pages": 1,
                "success": False,
                "error": str(e)
            }

    async def get_file_details(
        self,
        file_id: str,
        user_id: str
    ) -> Dict[str, Any]:
        """
        Get detailed information about a file.

        Args:
            file_id: ID of the file
            user_id: ID of the user (for access validation)

        Returns:
            File details result dictionary
        """
        try:
            if not self.client:
                # Return mock data for development
                return self._get_mock_file_details(file_id, user_id)

            # Get file from database
            file_result = self.client.table("files").select("*").eq("id", file_id).eq("user_id", user_id).execute()

            if not file_result.data:
                return {
                    "success": False,
                    "error": "File not found or access denied"
                }

            file_data = file_result.data[0]

            # Get processing jobs for this file
            jobs_result = self.client.table("processing_jobs").select("*").eq("file_id", file_id).execute()
            processing_history = jobs_result.data or []

            # Build file details
            file_details = {
                "file_id": file_data["id"],
                "filename": file_data["filename"],
                "file_path": file_data["file_path"],
                "file_type": file_data["file_type"],
                "file_size": file_data["file_size"],
                "status": file_data.get("status", "uploaded"),
                "metadata": file_data.get("metadata", {}),
                "created_at": file_data["created_at"],
                "updated_at": file_data.get("updated_at"),
                "download_url": f"/api/v1/files/{file_id}/download",
                "processing_history": processing_history,
                "access_count": 0,  # TODO: Implement access tracking
                "last_accessed": None
            }

            return {
                "file_details": file_details,
                "success": True
            }

        except Exception as e:
            logger.error(f"Error getting file details: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_file_statistics(
        self,
        user_id: str
    ) -> Dict[str, Any]:
        """
        Get file usage statistics for a user.

        Args:
            user_id: ID of the user

        Returns:
            Statistics result dictionary
        """
        try:
            if not self.client:
                # Return mock data for development
                return self._get_mock_file_statistics(user_id)

            # Get all files for user
            files_result = self.client.table("files").select("*").eq("user_id", user_id).execute()
            files = files_result.data or []

            # Calculate statistics
            total_files = len(files)
            total_size = sum(file.get("file_size", 0) for file in files)

            # Group by file type
            files_by_type = {}
            for file in files:
                file_type = file.get("file_type", "unknown")
                files_by_type[file_type] = files_by_type.get(file_type, 0) + 1

            # Group by status
            files_by_status = {}
            for file in files:
                status = file.get("status", "unknown")
                files_by_status[status] = files_by_status.get(status, 0) + 1

            # Calculate averages
            average_file_size = total_size / total_files if total_files > 0 else 0
            largest_file_size = max((file.get("file_size", 0) for file in files), default=0)

            # Get date ranges
            oldest_file_date = None
            newest_file_date = None
            if files:
                dates = [file.get("created_at") for file in files if file.get("created_at")]
                if dates:
                    oldest_file_date = min(dates)
                    newest_file_date = max(dates)

            statistics = {
                "total_files": total_files,
                "total_size": total_size,
                "files_by_type": files_by_type,
                "files_by_status": files_by_status,
                "average_file_size": average_file_size,
                "largest_file_size": largest_file_size,
                "oldest_file_date": oldest_file_date,
                "newest_file_date": newest_file_date
            }

            return {
                "statistics": statistics,
                "success": True
            }

        except Exception as e:
            logger.error(f"Error getting file statistics: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _get_mock_file_list(self, user_id: str, page: int, page_size: int) -> Dict[str, Any]:
        """Generate mock file list for development."""
        from datetime import datetime

        mock_files = [
            {
                "id": "mock-file-1",
                "filename": "ouvidorias_educacao.csv",
                "file_type": "text/csv",
                "file_size": 1024000,
                "status": "processed",
                "created_at": datetime.now().isoformat(),
                "metadata": {"description": "Dados de ouvidoria - Educação"}
            },
            {
                "id": "mock-file-2",
                "filename": "relatorio_saude.pdf",
                "file_type": "application/pdf",
                "file_size": 2048000,
                "status": "uploaded",
                "created_at": datetime.now().isoformat(),
                "metadata": {"description": "Relatório de saúde"}
            }
        ]

        return {
            "files": mock_files,
            "total_count": len(mock_files),
            "total_size": sum(f["file_size"] for f in mock_files),
            "total_pages": 1,
            "success": True
        }

    def _get_mock_file_details(self, file_id: str, user_id: str) -> Dict[str, Any]:
        """Generate mock file details for development."""
        from datetime import datetime

        file_details = {
            "file_id": file_id,
            "filename": "mock_file.csv",
            "file_path": f"{user_id}/{file_id}.csv",
            "file_type": "text/csv",
            "file_size": 1024000,
            "status": "processed",
            "metadata": {"description": "Mock file for development"},
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "download_url": f"/api/v1/files/{file_id}/download",
            "processing_history": [],
            "access_count": 5,
            "last_accessed": datetime.now().isoformat()
        }

        return {
            "file_details": file_details,
            "success": True
        }

    def _get_mock_file_statistics(self, user_id: str) -> Dict[str, Any]:
        """Generate mock file statistics for development."""
        from datetime import datetime

        statistics = {
            "total_files": 5,
            "total_size": 10240000,
            "files_by_type": {
                "text/csv": 3,
                "application/pdf": 2
            },
            "files_by_status": {
                "uploaded": 2,
                "processed": 3
            },
            "average_file_size": 2048000,
            "largest_file_size": 5120000,
            "oldest_file_date": datetime.now().isoformat(),
            "newest_file_date": datetime.now().isoformat()
        }

        return {
            "statistics": statistics,
            "success": True
        }
