"""
Service for classification operations using existing analyzers.
"""

import asyncio
import logging
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass

# Import existing classification tools
from ...analisadores.analisador_multilabel import classificar_relato_multilabel
from ...utils.config_utils import carregar_configuracao, carregar_variaveis_ambiente
from ..models.batch_processing import TextChunk, ChunkResult

logger = logging.getLogger(__name__)


@dataclass
class BatchClassificationConfig:
    """Configuration for batch classification operations."""
    max_workers: int = 4
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout_per_chunk: float = 30.0
    batch_size: int = 10
    rate_limit_delay: float = 0.1


class ClassificationService:
    """Service for handling classification operations."""
    
    def __init__(self):
        """Initialize classification service."""
        self.config = carregar_configuracao()
        self.env_vars = carregar_variaveis_ambiente()
    
    async def classify_multilabel(
        self,
        text: str,
        area: str,
        model_name: Optional[str] = None,
        max_subtemas: int = 3
    ) -> Dict[str, Any]:
        """
        Classify text into multiple subtemas.

        Args:
            text: Text to classify
            area: Classification area (EDUCACAO, SAUDE, MEIO_AMBIENTE)
            model_name: Optional model name override
            max_subtemas: Maximum number of subtemas to return

        Returns:
            Classification result dictionary
        """
        try:
            # Get subtemas for area
            subtemas = self.get_subtemas_for_area(area)

            # Use configured model or default
            if not model_name:
                model_name = self.config.get('LLM_MODEL', 'meta-llama/Llama-3.3-70B-Instruct-Turbo')

            # Get API key
            api_key = self.env_vars.get("TOGETHER_API_KEY")
            if not api_key:
                raise ValueError("TOGETHER_API_KEY not configured")

            # Perform classification
            result_subtemas = classificar_relato_multilabel(
                text, subtemas, area, model_name, api_key
            )

            # Limit to max_subtemas
            if len(result_subtemas) > max_subtemas:
                result_subtemas = result_subtemas[:max_subtemas]

            return {
                "subtemas": result_subtemas,
                "model_used": model_name,
                "success": True
            }

        except Exception as e:
            logger.error(f"Error in multilabel classification: {e}")
            return {
                "subtemas": [],
                "model_used": model_name or "unknown",
                "success": False,
                "error": str(e)
            }
    
    async def classify_unilabel(
        self,
        text: str,
        dimension: str,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Classify text for unilabel dimensions (TIPO_UNIDADE, CRE).

        Args:
            text: Text to classify
            dimension: Classification dimension
            model_name: Optional model name override

        Returns:
            Classification result dictionary
        """
        try:
            # Use configured model or default
            if not model_name:
                model_name = self.config.get('LLM_MODEL', 'meta-llama/Llama-3.3-70B-Instruct-Turbo')

            # Get API key
            api_key = self.env_vars.get("TOGETHER_API_KEY")
            if not api_key:
                raise ValueError("TOGETHER_API_KEY not configured")

            # Get possible values for dimension
            possible_values = self._get_dimension_values(dimension)

            # Create prompt for unilabel classification
            prompt = self._create_unilabel_prompt(text, dimension, possible_values)

            # Call LLM for classification (simplified implementation)
            # TODO: Implement actual unilabel classification with existing tools
            classification_result = self._call_llm_for_unilabel(prompt, model_name, api_key)

            return {
                "classification": classification_result.get("classification"),
                "confidence_score": classification_result.get("confidence_score"),
                "model_used": model_name,
                "success": True
            }

        except Exception as e:
            logger.error(f"Error in unilabel classification: {e}")
            return {
                "classification": None,
                "confidence_score": None,
                "model_used": model_name or "unknown",
                "success": False,
                "error": str(e)
            }
    
    async def classify_batch(
        self,
        texts: List[str],
        area: str,
        classification_type: str = "multilabel",
        max_subtemas: int = 3,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Classify multiple texts in batch.

        Args:
            texts: List of texts to classify
            area: Classification area
            classification_type: Type of classification (multilabel or unilabel)
            max_subtemas: Maximum subtemas per text
            model_name: Optional model name override

        Returns:
            Batch classification result dictionary
        """
        try:
            results = []

            for text in texts:
                if classification_type == "multilabel":
                    result = await self.classify_multilabel(
                        text=text,
                        area=area,
                        model_name=model_name,
                        max_subtemas=max_subtemas
                    )
                else:
                    # For unilabel batch, we'd need to specify dimension
                    # For now, default to multilabel
                    result = await self.classify_multilabel(
                        text=text,
                        area=area,
                        model_name=model_name,
                        max_subtemas=max_subtemas
                    )

                results.append(result)

            return {
                "results": results,
                "success": True
            }

        except Exception as e:
            logger.error(f"Error in batch classification: {e}")
            return {
                "results": [],
                "success": False,
                "error": str(e)
            }

    async def classify_chunks_batch(
        self,
        chunks: List[TextChunk],
        area: str,
        config: Optional[BatchClassificationConfig] = None,
        model_name: Optional[str] = None,
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> List[ChunkResult]:
        """
        Classify multiple text chunks in parallel with error handling and retry logic.

        Args:
            chunks: List of text chunks to classify
            area: Classification area (EDUCACAO, SAUDE, MEIO_AMBIENTE)
            config: Optional batch configuration
            model_name: Optional model name override
            progress_callback: Optional callback for progress updates

        Returns:
            List of chunk classification results
        """
        if not chunks:
            return []

        config = config or BatchClassificationConfig()
        results = []
        
        logger.info(f"Starting batch classification of {len(chunks)} chunks for area {area}")
        
        # Process chunks in batches to manage memory and API limits
        for batch_start in range(0, len(chunks), config.batch_size):
            batch_end = min(batch_start + config.batch_size, len(chunks))
            batch_chunks = chunks[batch_start:batch_end]
            
            logger.info(f"Processing batch {batch_start//config.batch_size + 1}/{(len(chunks) + config.batch_size - 1)//config.batch_size}")
            
            # Process batch with parallel workers
            batch_results = await self._process_chunk_batch_parallel(
                batch_chunks, area, config, model_name
            )
            
            results.extend(batch_results)
            
            # Update progress
            if progress_callback:
                progress_callback(len(results), len(chunks))
            
            # Rate limiting between batches
            if batch_end < len(chunks):
                await asyncio.sleep(config.rate_limit_delay)
        
        logger.info(f"Completed batch classification of {len(chunks)} chunks")
        return results

    async def _process_chunk_batch_parallel(
        self,
        chunks: List[TextChunk],
        area: str,
        config: BatchClassificationConfig,
        model_name: Optional[str] = None
    ) -> List[ChunkResult]:
        """
        Process a batch of chunks in parallel using ThreadPoolExecutor.

        Args:
            chunks: Batch of chunks to process
            area: Classification area
            config: Batch configuration
            model_name: Optional model name override

        Returns:
            List of chunk results
        """
        results = []
        
        # Create tasks for parallel processing
        tasks = []
        for chunk in chunks:
            task = self._classify_chunk_with_retry(chunk, area, config, model_name)
            tasks.append(task)
        
        # Execute tasks with limited concurrency
        semaphore = asyncio.Semaphore(config.max_workers)
        
        async def bounded_task(task):
            async with semaphore:
                return await task
        
        # Wait for all tasks to complete
        bounded_tasks = [bounded_task(task) for task in tasks]
        results = await asyncio.gather(*bounded_tasks, return_exceptions=True)
        
        # Handle exceptions and convert to ChunkResult objects
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Error processing chunk {chunks[i].chunk_id}: {result}")
                error_result = ChunkResult(
                    chunk=chunks[i],
                    subtemas=[],
                    confidence_scores={},
                    evidence_texts={},
                    processing_time=0.0,
                    error=str(result)
                )
                final_results.append(error_result)
            else:
                final_results.append(result)
        
        return final_results

    async def _classify_chunk_with_retry(
        self,
        chunk: TextChunk,
        area: str,
        config: BatchClassificationConfig,
        model_name: Optional[str] = None
    ) -> ChunkResult:
        """
        Classify a single chunk with retry logic for API failures.

        Args:
            chunk: Text chunk to classify
            area: Classification area
            config: Batch configuration
            model_name: Optional model name override

        Returns:
            Chunk classification result
        """
        last_error = None
        
        for attempt in range(config.max_retries + 1):
            try:
                # Add timeout for individual chunk processing
                result = await asyncio.wait_for(
                    self._classify_single_chunk_internal(chunk, area, model_name),
                    timeout=config.timeout_per_chunk
                )
                return result
                
            except asyncio.TimeoutError as e:
                last_error = f"Timeout after {config.timeout_per_chunk}s"
                logger.warning(f"Timeout classifying chunk {chunk.chunk_id}, attempt {attempt + 1}")
                
            except Exception as e:
                last_error = str(e)
                logger.warning(f"Error classifying chunk {chunk.chunk_id}, attempt {attempt + 1}: {e}")
            
            # Wait before retry (exponential backoff)
            if attempt < config.max_retries:
                wait_time = config.retry_delay * (2 ** attempt)
                await asyncio.sleep(wait_time)
        
        # All retries failed, return error result
        logger.error(f"Failed to classify chunk {chunk.chunk_id} after {config.max_retries + 1} attempts")
        return ChunkResult(
            chunk=chunk,
            subtemas=[],
            confidence_scores={},
            evidence_texts={},
            processing_time=0.0,
            error=f"Failed after {config.max_retries + 1} attempts: {last_error}"
        )

    async def _classify_single_chunk_internal(
        self,
        chunk: TextChunk,
        area: str,
        model_name: Optional[str] = None
    ) -> ChunkResult:
        """
        Internal method to classify a single chunk.

        Args:
            chunk: Text chunk to classify
            area: Classification area
            model_name: Optional model name override

        Returns:
            Chunk classification result
        """
        start_time = time.time()
        
        try:
            # Get subtemas for the area
            subtemas = self.get_subtemas_for_area(area)
            if not subtemas:
                raise ValueError(f"No subtemas found for area: {area}")
            
            # Use configured model or default
            if not model_name:
                model_name = self.config.get('LLM_MODEL', 'meta-llama/Llama-3.3-70B-Instruct-Turbo')
            
            # Get API key
            api_key = self.env_vars.get("FIREWORKS_API_KEY")
            if not api_key:
                raise ValueError("FIREWORKS_API_KEY not configured")
            
            # Perform classification using existing multilabel function
            identified_subtemas = classificar_relato_multilabel(
                relato=chunk.text,
                subtemas=subtemas,
                area=area,
                model_name=model_name,
                api_key=api_key,
                idx=chunk.chunk_id
            )
            
            # Limit to max 3 subtemas
            if len(identified_subtemas) > 3:
                identified_subtemas = identified_subtemas[:3]
            
            # Generate confidence scores (simplified approach)
            confidence_scores = self._generate_confidence_scores(identified_subtemas)
            
            # Extract evidence texts
            evidence_texts = self._extract_evidence_texts(chunk, identified_subtemas)
            
            processing_time = time.time() - start_time
            
            return ChunkResult(
                chunk=chunk,
                subtemas=identified_subtemas,
                confidence_scores=confidence_scores,
                evidence_texts=evidence_texts,
                processing_time=processing_time,
                error=None
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            raise e

    def _generate_confidence_scores(self, subtemas: List[str], base_confidence: float = 0.7) -> Dict[str, float]:
        """
        Generate confidence scores for identified subtemas.

        Args:
            subtemas: List of identified subtemas
            base_confidence: Base confidence score to assign

        Returns:
            Dictionary mapping subtemas to confidence scores
        """
        confidence_scores = {}
        
        # Assign decreasing confidence scores based on order
        for i, subtema in enumerate(subtemas):
            # First subtema gets highest confidence, subsequent ones get slightly lower
            confidence = max(base_confidence - (i * 0.1), 0.3)
            confidence_scores[subtema] = round(confidence, 2)
        
        return confidence_scores

    def _extract_evidence_texts(self, chunk: TextChunk, subtemas: List[str]) -> Dict[str, str]:
        """
        Extract evidence text for each identified subtema.

        Args:
            chunk: The text chunk being classified
            subtemas: List of identified subtemas

        Returns:
            Dictionary mapping subtemas to evidence text
        """
        evidence_texts = {}
        text = chunk.text.lower()
        
        for subtema in subtemas:
            evidence = self._find_evidence_for_subtema(text, subtema)
            if evidence:
                evidence_texts[subtema] = evidence
        
        return evidence_texts

    def _find_evidence_for_subtema(self, text: str, subtema: str, max_length: int = 200) -> str:
        """
        Find the best evidence text for a specific subtema.

        Args:
            text: The chunk text (lowercased)
            subtema: The subtema name
            max_length: Maximum length of evidence text

        Returns:
            Evidence text or empty string if none found
        """
        import re
        
        # Get keywords for this subtema (simplified approach)
        keywords = [word.lower() for word in subtema.replace('_', ' ').split()]
        
        # Find sentences containing keywords
        sentences = re.split(r'[.!?]+', text)
        scored_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) < 20:  # Skip very short sentences
                continue
                
            score = 0
            for keyword in keywords:
                if keyword in sentence:
                    score += 1
            
            if score > 0:
                scored_sentences.append((score, sentence))
        
        if not scored_sentences:
            # Fallback: return first meaningful sentence
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) >= 30:
                    return self._truncate_evidence(sentence, max_length)
            return ""
        
        # Sort by score and get the best sentence
        scored_sentences.sort(key=lambda x: x[0], reverse=True)
        best_sentence = scored_sentences[0][1]
        
        return self._truncate_evidence(best_sentence, max_length)

    def _truncate_evidence(self, text: str, max_length: int) -> str:
        """
        Truncate evidence text to maximum length while preserving word boundaries.

        Args:
            text: Text to truncate
            max_length: Maximum allowed length

        Returns:
            Truncated text with "..." if truncated
        """
        if len(text) <= max_length:
            return text.strip()
        
        # Find the last space before max_length
        truncated = text[:max_length]
        last_space = truncated.rfind(' ')
        
        if last_space > max_length * 0.8:  # If we can preserve most of the text
            return truncated[:last_space].strip() + "..."
        else:
            return truncated.strip() + "..."

    def get_subtemas_for_area(self, area: str) -> List[str]:
        """
        Get subtemas for a specific area.

        Args:
            area: Classification area

        Returns:
            List of subtemas for the area
        """
        # Load from actual configuration or use default mapping
        subtemas_map = {
            "EDUCACAO": [
                "EDUCACAO_ESPECIAL",
                "ALIMENTACAO_ESCOLAR",
                "QUALIFICACAO_PROFISSIONAL",
                "BULLYING",
                "CONTROLE_INTERNO",
                "TRANSPORTE",
                "INFRAESTRUTURA",
                "EDUCACAO_ADULTOS_PRISIONAL"
            ],
            "SAUDE": [
                "ATENDIMENTO_BASICO",
                "EMERGENCIA",
                "MEDICAMENTOS",
                "ESPECIALIDADES"
            ],
            "MEIO_AMBIENTE": [
                "POLUICAO",
                "DESMATAMENTO",
                "RESIDUOS",
                "AGUA"
            ]
        }

        return subtemas_map.get(area, [])

    def get_subtemas_descriptions(self, area: str) -> Optional[Dict[str, str]]:
        """
        Get descriptions for subtemas in a specific area.

        Args:
            area: Classification area

        Returns:
            Dictionary mapping subtemas to descriptions
        """
        descriptions_map = {
            "EDUCACAO": {
                "EDUCACAO_ESPECIAL": "Questões relacionadas à educação especial e inclusiva",
                "ALIMENTACAO_ESCOLAR": "Problemas com merenda e alimentação escolar",
                "QUALIFICACAO_PROFISSIONAL": "Formação e qualificação de profissionais da educação",
                "BULLYING": "Casos de bullying e violência escolar",
                "CONTROLE_INTERNO": "Questões de gestão e controle interno",
                "TRANSPORTE": "Transporte escolar e mobilidade",
                "INFRAESTRUTURA": "Infraestrutura física das escolas",
                "EDUCACAO_ADULTOS_PRISIONAL": "Educação de adultos e sistema prisional"
            },
            "SAUDE": {
                "ATENDIMENTO_BASICO": "Atendimento básico de saúde",
                "EMERGENCIA": "Serviços de emergência e urgência",
                "MEDICAMENTOS": "Disponibilidade e acesso a medicamentos",
                "ESPECIALIDADES": "Consultas e tratamentos especializados"
            },
            "MEIO_AMBIENTE": {
                "POLUICAO": "Problemas de poluição ambiental",
                "DESMATAMENTO": "Questões de desmatamento e preservação",
                "RESIDUOS": "Gestão de resíduos e lixo",
                "AGUA": "Qualidade e acesso à água"
            }
        }

        return descriptions_map.get(area)

    def _get_dimension_values(self, dimension: str) -> List[str]:
        """
        Get possible values for a unilabel dimension.

        Args:
            dimension: Classification dimension

        Returns:
            List of possible values
        """
        dimension_values = {
            "TIPO_UNIDADE": ["Estadual", "Municipal", "Privada", "Não se aplica"],
            "CRE": [
                "1ª CRE", "2ª CRE", "3ª CRE", "4ª CRE", "5ª CRE", "6ª CRE",
                "7ª CRE", "8ª CRE", "9ª CRE", "10ª CRE", "11ª CRE", "Não se aplica"
            ]
        }

        return dimension_values.get(dimension, [])

    def _create_unilabel_prompt(self, text: str, dimension: str, possible_values: List[str]) -> str:
        """
        Create prompt for unilabel classification.

        Args:
            text: Text to classify
            dimension: Classification dimension
            possible_values: Possible classification values

        Returns:
            Formatted prompt string
        """
        values_str = ", ".join(possible_values)

        return f"""
        Classifique o seguinte texto na dimensão {dimension}.

        Texto: "{text}"

        Valores possíveis: {values_str}

        Responda apenas com um dos valores possíveis, sem explicações adicionais.
        """

    def _call_llm_for_unilabel(self, prompt: str, model_name: str, api_key: str) -> Dict[str, Any]:
        """
        Call LLM for unilabel classification.

        Args:
            prompt: Classification prompt
            model_name: Model to use
            api_key: API key

        Returns:
            Classification result
        """
        # TODO: Implement actual LLM call for unilabel classification
        # For now, return a placeholder
        return {
            "classification": "Não se aplica",
            "confidence_score": 0.5
        }
