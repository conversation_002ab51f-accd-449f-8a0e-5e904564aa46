"""
HierarchicalClassifier for multi-stage PDF batch processing.

This module provides a hierarchical classification system that reduces token usage
by implementing a multi-stage pipeline with progressive refinement and early stopping.
"""

import logging
import time
from typing import List, Dict, Tuple, Optional, Any, Set
from dataclasses import dataclass
from enum import Enum
import asyncio
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor

from ..models.batch_processing import <PERSON><PERSON>hun<PERSON>, ChunkResult
from .chunk_relevance_filter import ChunkRelevanceFilter, FilteringMetrics
from .semantic_summarizer import SemanticSummarizer, SummarizedChunk, SummarizationMetrics
from .chunk_classifier import ChunkClassifier, ChunkClassificationConfig
from ...utils.config_utils import carregar_configuracao

logger = logging.getLogger(__name__)


class ClassificationStage(Enum):
    """Stages of hierarchical classification."""
    AREA_DETECTION = "area_detection"
    RELEVANCE_FILTERING = "relevance_filtering"
    SEMANTIC_SUMMARIZATION = "semantic_summarization"
    SUBTEMA_CLASSIFICATION = "subtema_classification"
    RESULT_AGGREGATION = "result_aggregation"


@dataclass
class StageMetrics:
    """Metrics for a single classification stage."""
    stage: ClassificationStage
    input_chunks: int
    output_chunks: int
    processing_time: float
    tokens_processed: int
    tokens_saved: int
    accuracy_score: Optional[float] = None
    confidence_score: Optional[float] = None
    

@dataclass
class HierarchicalMetrics:
    """Comprehensive metrics for hierarchical classification."""
    total_stages: int
    stage_metrics: List[StageMetrics]
    total_processing_time: float
    total_tokens_saved: int
    final_reduction_ratio: float
    pipeline_efficiency: float
    early_stopping_count: int
    

@dataclass
class ClassificationPipeline:
    """Configuration for classification pipeline."""
    enable_area_detection: bool = True
    enable_relevance_filtering: bool = True
    enable_semantic_summarization: bool = True
    relevance_threshold: float = 0.3
    summarization_threshold: float = 0.75
    max_chunks_per_stage: int = 100
    early_stopping_enabled: bool = True
    early_stopping_threshold: float = 0.1
    

class HierarchicalClassifier:
    """
    Multi-stage hierarchical classifier for optimized token usage.
    
    Pipeline stages:
    1. Area Detection - Quick area classification using keywords
    2. Relevance Filtering - Filter chunks by relevance to area
    3. Semantic Summarization - Combine similar chunks
    4. Subtema Classification - Detailed classification with LLM
    5. Result Aggregation - Combine results from all stages
    """
    
    def __init__(self, 
                 pipeline_config: Optional[ClassificationPipeline] = None,
                 chunk_classifier: Optional[ChunkClassifier] = None,
                 relevance_filter: Optional[ChunkRelevanceFilter] = None,
                 semantic_summarizer: Optional[SemanticSummarizer] = None):
        """
        Initialize the hierarchical classifier.
        
        Args:
            pipeline_config: Configuration for classification pipeline
            chunk_classifier: Optional chunk classifier instance
            relevance_filter: Optional relevance filter instance
            semantic_summarizer: Optional semantic summarizer instance
        """
        self.pipeline_config = pipeline_config or ClassificationPipeline()
        
        # Initialize components
        self.chunk_classifier = chunk_classifier or ChunkClassifier()
        self.relevance_filter = relevance_filter or ChunkRelevanceFilter()
        self.semantic_summarizer = semantic_summarizer or SemanticSummarizer()
        
        # Configuration
        self.config = carregar_configuracao()
        
        # Thread pool for parallel processing
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
        # Cache for area detection
        self.area_keywords_cache = {}
        self._initialize_area_keywords()
        
        logger.info("Initialized HierarchicalClassifier with multi-stage pipeline")
    
    def _initialize_area_keywords(self) -> None:
        """Initialize area-specific keywords for quick area detection."""
        try:
            subtemas_config = self.config.get('subtemas', {})
            
            for area, subtemas in subtemas_config.items():
                keywords = set()
                
                # Extract keywords from subtemas
                for subtema, info in subtemas.items():
                    if isinstance(info, dict):
                        # Add subtema name
                        keywords.add(subtema.lower().replace('_', ' '))
                        
                        # Add keywords from configuration
                        if 'palavras_chave' in info:
                            keywords.update([k.lower() for k in info['palavras_chave']])
                
                self.area_keywords_cache[area] = list(keywords)
                
        except Exception as e:
            logger.error(f"Error initializing area keywords: {e}")
            # Fallback keywords
            self.area_keywords_cache = {
                'EDUCACAO': ['educacao', 'escola', 'ensino', 'aluno', 'professor'],
                'SAUDE': ['saude', 'hospital', 'medico', 'paciente', 'tratamento'],
                'MEIO_AMBIENTE': ['ambiente', 'poluicao', 'lixo', 'agua', 'sustentavel']
            }
    
    async def classify_chunks_hierarchical(self, 
                                         chunks: List[TextChunk], 
                                         area: str) -> Tuple[List[ChunkResult], HierarchicalMetrics]:
        """
        Classify chunks using hierarchical multi-stage pipeline.
        
        Args:
            chunks: List of text chunks to classify
            area: Target classification area
            
        Returns:
            Tuple of (classification_results, hierarchical_metrics)
        """
        if not chunks:
            return [], self._create_empty_metrics()
        
        start_time = time.time()
        stage_metrics = []
        current_chunks = chunks.copy()
        
        logger.info(f"Starting hierarchical classification of {len(chunks)} chunks for area {area}")
        
        # Stage 1: Area Detection
        if self.pipeline_config.enable_area_detection:
            current_chunks, metrics = await self._stage_area_detection(current_chunks, area)
            stage_metrics.append(metrics)
            
            # Early stopping if no relevant chunks
            if self._should_early_stop(current_chunks, area):
                logger.info("Early stopping: No relevant chunks found")
                return self._create_early_stop_results(chunks, stage_metrics, start_time)
        
        # Stage 2: Relevance Filtering
        if self.pipeline_config.enable_relevance_filtering and current_chunks:
            current_chunks, metrics = await self._stage_relevance_filtering(current_chunks, area)
            stage_metrics.append(metrics)
            
            # Early stopping if too few relevant chunks
            if self._should_early_stop(current_chunks, area):
                logger.info("Early stopping: Too few relevant chunks")
                return self._create_early_stop_results(chunks, stage_metrics, start_time)
        
        # Stage 3: Semantic Summarization
        summarized_chunks = []
        if self.pipeline_config.enable_semantic_summarization and current_chunks:
            summarized_chunks, metrics = await self._stage_semantic_summarization(current_chunks, area)
            stage_metrics.append(metrics)
            
            # Convert summarized chunks back to text chunks for classification
            current_chunks = self._convert_summarized_to_chunks(summarized_chunks)
        
        # Stage 4: Subtema Classification
        if current_chunks:
            classification_results, metrics = await self._stage_subtema_classification(current_chunks, area)
            stage_metrics.append(metrics)
        else:
            classification_results = []
            
        # Stage 5: Result Aggregation
        final_results, metrics = await self._stage_result_aggregation(
            classification_results, summarized_chunks, chunks, area
        )
        stage_metrics.append(metrics)
        
        # Calculate overall metrics
        total_time = time.time() - start_time
        hierarchical_metrics = self._calculate_hierarchical_metrics(
            stage_metrics, total_time, len(chunks), len(final_results)
        )
        
        logger.info(f"Hierarchical classification completed: {len(chunks)} → {len(final_results)} chunks "
                   f"({hierarchical_metrics.final_reduction_ratio:.2f} reduction, "
                   f"{hierarchical_metrics.total_tokens_saved} tokens saved)")
        
        return final_results, hierarchical_metrics
    
    async def _stage_area_detection(self, 
                                   chunks: List[TextChunk], 
                                   area: str) -> Tuple[List[TextChunk], StageMetrics]:
        """Stage 1: Quick area detection using keywords."""
        start_time = time.time()
        
        logger.debug(f"Stage 1: Area detection for {len(chunks)} chunks")
        
        # Get area keywords
        area_keywords = self.area_keywords_cache.get(area, [])
        
        # Filter chunks by area keywords
        relevant_chunks = []
        for chunk in chunks:
            if self._has_area_keywords(chunk.text, area_keywords):
                relevant_chunks.append(chunk)
        
        # Calculate metrics
        processing_time = time.time() - start_time
        tokens_saved = sum(
            chunk.token_count or self._estimate_tokens(chunk.text)
            for chunk in chunks[len(relevant_chunks):]
        )
        
        metrics = StageMetrics(
            stage=ClassificationStage.AREA_DETECTION,
            input_chunks=len(chunks),
            output_chunks=len(relevant_chunks),
            processing_time=processing_time,
            tokens_processed=sum(
                chunk.token_count or self._estimate_tokens(chunk.text)
                for chunk in relevant_chunks
            ),
            tokens_saved=tokens_saved,
            accuracy_score=None,  # Not applicable for keyword filtering
            confidence_score=len(relevant_chunks) / max(len(chunks), 1)
        )
        
        logger.debug(f"Stage 1 completed: {len(chunks)} → {len(relevant_chunks)} chunks")
        return relevant_chunks, metrics
    
    async def _stage_relevance_filtering(self, 
                                       chunks: List[TextChunk], 
                                       area: str) -> Tuple[List[TextChunk], StageMetrics]:
        """Stage 2: Relevance filtering using embeddings."""
        start_time = time.time()
        
        logger.debug(f"Stage 2: Relevance filtering for {len(chunks)} chunks")
        
        # Apply relevance filtering
        filtered_chunks, filtering_metrics = await self.relevance_filter.filter_chunks(chunks, area)
        
        # Convert to stage metrics
        metrics = StageMetrics(
            stage=ClassificationStage.RELEVANCE_FILTERING,
            input_chunks=filtering_metrics.total_chunks,
            output_chunks=filtering_metrics.filtered_chunks,
            processing_time=filtering_metrics.processing_time,
            tokens_processed=sum(
                chunk.token_count or self._estimate_tokens(chunk.text)
                for chunk in filtered_chunks
            ),
            tokens_saved=filtering_metrics.tokens_saved,
            accuracy_score=None,
            confidence_score=filtering_metrics.avg_relevance_score
        )
        
        logger.debug(f"Stage 2 completed: {len(chunks)} → {len(filtered_chunks)} chunks")
        return filtered_chunks, metrics
    
    async def _stage_semantic_summarization(self, 
                                          chunks: List[TextChunk], 
                                          area: str) -> Tuple[List[SummarizedChunk], StageMetrics]:
        """Stage 3: Semantic summarization of similar chunks."""
        start_time = time.time()
        
        logger.debug(f"Stage 3: Semantic summarization for {len(chunks)} chunks")
        
        # Apply semantic summarization
        summarized_chunks, summarization_metrics = await self.semantic_summarizer.summarize_chunks(chunks, area)
        
        # Convert to stage metrics
        metrics = StageMetrics(
            stage=ClassificationStage.SEMANTIC_SUMMARIZATION,
            input_chunks=summarization_metrics.original_chunks,
            output_chunks=summarization_metrics.summarized_chunks,
            processing_time=summarization_metrics.processing_time,
            tokens_processed=sum(
                self._estimate_tokens(chunk.summarized_text)
                for chunk in summarized_chunks
            ),
            tokens_saved=summarization_metrics.tokens_saved,
            accuracy_score=None,
            confidence_score=sum(chunk.confidence_score for chunk in summarized_chunks) / max(len(summarized_chunks), 1)
        )
        
        logger.debug(f"Stage 3 completed: {len(chunks)} → {len(summarized_chunks)} chunks")
        return summarized_chunks, metrics
    
    async def _stage_subtema_classification(self, 
                                          chunks: List[TextChunk], 
                                          area: str) -> Tuple[List[ChunkResult], StageMetrics]:
        """Stage 4: Detailed subtema classification with LLM."""
        start_time = time.time()
        
        logger.debug(f"Stage 4: Subtema classification for {len(chunks)} chunks")
        
        # Apply chunk classification
        classification_results = await self.chunk_classifier.classify_chunks(chunks, area)
        
        # Calculate metrics
        processing_time = time.time() - start_time
        tokens_processed = sum(
            chunk.token_count or self._estimate_tokens(chunk.text)
            for chunk in chunks
        )
        
        # Calculate average confidence
        avg_confidence = 0.0
        if classification_results:
            total_confidence = 0.0
            confidence_count = 0
            
            for result in classification_results:
                if result.confidence_scores:
                    total_confidence += sum(result.confidence_scores.values())
                    confidence_count += len(result.confidence_scores)
            
            avg_confidence = total_confidence / max(confidence_count, 1)
        
        metrics = StageMetrics(
            stage=ClassificationStage.SUBTEMA_CLASSIFICATION,
            input_chunks=len(chunks),
            output_chunks=len(classification_results),
            processing_time=processing_time,
            tokens_processed=tokens_processed,
            tokens_saved=0,  # No tokens saved in this stage
            accuracy_score=None,  # Would require ground truth
            confidence_score=avg_confidence
        )
        
        logger.debug(f"Stage 4 completed: {len(chunks)} → {len(classification_results)} results")
        return classification_results, metrics
    
    async def _stage_result_aggregation(self, 
                                      classification_results: List[ChunkResult],
                                      summarized_chunks: List[SummarizedChunk],
                                      original_chunks: List[TextChunk],
                                      area: str) -> Tuple[List[ChunkResult], StageMetrics]:
        """Stage 5: Aggregate results from all stages."""
        start_time = time.time()
        
        logger.debug(f"Stage 5: Result aggregation for {len(classification_results)} results")
        
        # Create mapping from summarized chunks to original chunks
        chunk_mapping = {}
        if summarized_chunks:
            for i, summarized_chunk in enumerate(summarized_chunks):
                for original_id in summarized_chunk.original_chunk_ids:
                    chunk_mapping[original_id] = i
        
        # Aggregate results
        final_results = []
        
        for result in classification_results:
            # Check if this result came from a summarized chunk
            if result.chunk.chunk_id in chunk_mapping:
                summarized_idx = chunk_mapping[result.chunk.chunk_id]
                summarized_chunk = summarized_chunks[summarized_idx]
                
                # Update evidence texts with summarized information
                if summarized_chunk.evidence_snippets:
                    for subtema in result.subtemas:
                        if subtema in result.evidence_texts:
                            # Combine original evidence with summarized evidence
                            original_evidence = result.evidence_texts[subtema]
                            summarized_evidence = "...".join(summarized_chunk.evidence_snippets)
                            result.evidence_texts[subtema] = f"{original_evidence}...{summarized_evidence}"[:200]
                
                # Update confidence scores based on summarization confidence
                for subtema in result.subtemas:
                    if subtema in result.confidence_scores:
                        # Adjust confidence based on summarization confidence
                        original_confidence = result.confidence_scores[subtema]
                        summarization_confidence = summarized_chunk.confidence_score
                        adjusted_confidence = original_confidence * (0.7 + 0.3 * summarization_confidence)
                        result.confidence_scores[subtema] = adjusted_confidence
            
            final_results.append(result)
        
        # Calculate metrics
        processing_time = time.time() - start_time
        
        metrics = StageMetrics(
            stage=ClassificationStage.RESULT_AGGREGATION,
            input_chunks=len(classification_results),
            output_chunks=len(final_results),
            processing_time=processing_time,
            tokens_processed=0,  # No token processing in aggregation
            tokens_saved=0,
            accuracy_score=None,
            confidence_score=None
        )
        
        logger.debug(f"Stage 5 completed: {len(classification_results)} → {len(final_results)} results")
        return final_results, metrics
    
    def _has_area_keywords(self, text: str, keywords: List[str]) -> bool:
        """Check if text contains area-specific keywords."""
        text_lower = text.lower()
        
        # Check if at least one keyword is present
        for keyword in keywords:
            if keyword in text_lower:
                return True
        
        return False
    
    def _should_early_stop(self, chunks: List[TextChunk], area: str) -> bool:
        """Determine if early stopping should be applied."""
        if not self.pipeline_config.early_stopping_enabled:
            return False
        
        # Early stop if too few chunks remain
        if len(chunks) < self.pipeline_config.max_chunks_per_stage * self.pipeline_config.early_stopping_threshold:
            return True
        
        return False
    
    def _convert_summarized_to_chunks(self, summarized_chunks: List[SummarizedChunk]) -> List[TextChunk]:
        """Convert summarized chunks back to text chunks for classification."""
        text_chunks = []
        
        for i, summarized_chunk in enumerate(summarized_chunks):
            # Create a new text chunk from summarized content
            new_chunk = TextChunk(
                text=summarized_chunk.summarized_text,
                chunk_id=i,  # New chunk ID
                page_start=summarized_chunk.representative_chunk.page_start,
                page_end=summarized_chunk.representative_chunk.page_end,
                char_start=summarized_chunk.representative_chunk.char_start,
                char_end=summarized_chunk.representative_chunk.char_end,
                pdf_path=summarized_chunk.representative_chunk.pdf_path,
                token_count=self._estimate_tokens(summarized_chunk.summarized_text)
            )
            text_chunks.append(new_chunk)
        
        return text_chunks
    
    def _create_early_stop_results(self, 
                                 original_chunks: List[TextChunk],
                                 stage_metrics: List[StageMetrics],
                                 start_time: float) -> Tuple[List[ChunkResult], HierarchicalMetrics]:
        """Create results for early stopping scenario."""
        # Create empty results
        empty_results = []
        
        # Calculate metrics
        total_time = time.time() - start_time
        hierarchical_metrics = self._calculate_hierarchical_metrics(
            stage_metrics, total_time, len(original_chunks), 0
        )
        hierarchical_metrics.early_stopping_count = 1
        
        return empty_results, hierarchical_metrics
    
    def _calculate_hierarchical_metrics(self, 
                                      stage_metrics: List[StageMetrics],
                                      total_time: float,
                                      original_chunks: int,
                                      final_chunks: int) -> HierarchicalMetrics:
        """Calculate comprehensive hierarchical metrics."""
        total_tokens_saved = sum(metrics.tokens_saved for metrics in stage_metrics)
        
        # Calculate final reduction ratio
        final_reduction_ratio = 1.0 - (final_chunks / max(original_chunks, 1))
        
        # Calculate pipeline efficiency (tokens saved per second)
        pipeline_efficiency = total_tokens_saved / max(total_time, 1)
        
        return HierarchicalMetrics(
            total_stages=len(stage_metrics),
            stage_metrics=stage_metrics,
            total_processing_time=total_time,
            total_tokens_saved=total_tokens_saved,
            final_reduction_ratio=final_reduction_ratio,
            pipeline_efficiency=pipeline_efficiency,
            early_stopping_count=0
        )
    
    def _create_empty_metrics(self) -> HierarchicalMetrics:
        """Create empty metrics for edge cases."""
        return HierarchicalMetrics(
            total_stages=0,
            stage_metrics=[],
            total_processing_time=0.0,
            total_tokens_saved=0,
            final_reduction_ratio=0.0,
            pipeline_efficiency=0.0,
            early_stopping_count=0
        )
    
    def _estimate_tokens(self, text: str) -> int:
        """Estimate number of tokens in text."""
        return max(1, len(text) // 4)
    
    def get_pipeline_config(self) -> Dict[str, Any]:
        """Get current pipeline configuration."""
        return {
            'enable_area_detection': self.pipeline_config.enable_area_detection,
            'enable_relevance_filtering': self.pipeline_config.enable_relevance_filtering,
            'enable_semantic_summarization': self.pipeline_config.enable_semantic_summarization,
            'relevance_threshold': self.pipeline_config.relevance_threshold,
            'summarization_threshold': self.pipeline_config.summarization_threshold,
            'max_chunks_per_stage': self.pipeline_config.max_chunks_per_stage,
            'early_stopping_enabled': self.pipeline_config.early_stopping_enabled,
            'early_stopping_threshold': self.pipeline_config.early_stopping_threshold,
            'area_keywords_loaded': list(self.area_keywords_cache.keys())
        }
    
    def update_pipeline_config(self, **kwargs) -> None:
        """Update pipeline configuration."""
        for key, value in kwargs.items():
            if hasattr(self.pipeline_config, key):
                setattr(self.pipeline_config, key, value)
                logger.info(f"Updated pipeline config: {key} = {value}")
    
    def get_stage_performance(self, metrics: HierarchicalMetrics) -> Dict[str, Any]:
        """Get detailed performance analysis for each stage."""
        stage_performance = {}
        
        for stage_metric in metrics.stage_metrics:
            reduction_ratio = 1.0 - (stage_metric.output_chunks / max(stage_metric.input_chunks, 1))
            
            stage_performance[stage_metric.stage.value] = {
                'input_chunks': stage_metric.input_chunks,
                'output_chunks': stage_metric.output_chunks,
                'reduction_ratio': reduction_ratio,
                'processing_time': stage_metric.processing_time,
                'tokens_saved': stage_metric.tokens_saved,
                'confidence_score': stage_metric.confidence_score,
                'efficiency': stage_metric.tokens_saved / max(stage_metric.processing_time, 1)
            }
        
        return stage_performance
    
    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=True)


# Factory function for easy instantiation
def create_hierarchical_classifier(
    enable_all_stages: bool = True,
    relevance_threshold: float = 0.3,
    summarization_threshold: float = 0.75
) -> HierarchicalClassifier:
    """
    Create a configured hierarchical classifier.
    
    Args:
        enable_all_stages: Whether to enable all pipeline stages
        relevance_threshold: Relevance filtering threshold
        summarization_threshold: Summarization clustering threshold
        
    Returns:
        Configured HierarchicalClassifier instance
    """
    pipeline_config = ClassificationPipeline(
        enable_area_detection=enable_all_stages,
        enable_relevance_filtering=enable_all_stages,
        enable_semantic_summarization=enable_all_stages,
        relevance_threshold=relevance_threshold,
        summarization_threshold=summarization_threshold
    )
    
    return HierarchicalClassifier(pipeline_config=pipeline_config)