"""
SemanticSummarizer for intelligent chunk condensation in PDF batch processing.

This module provides semantic summarization capabilities to reduce token usage
by combining similar chunks while preserving essential information and context.
"""

import logging
import numpy as np
from typing import List, Dict, Tuple, Optional, Any, Set
from dataclasses import dataclass, field
from collections import defaultdict
import asyncio
from concurrent.futures import ThreadPoolExecutor
import re
from itertools import combinations

try:
    from sklearn.cluster import KMeans
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

from ..models.batch_processing import TextChunk
from .chunk_relevance_filter import ChunkRelevanceFilter

logger = logging.getLogger(__name__)


@dataclass
class SummarizedChunk:
    """A summarized chunk combining multiple original chunks."""
    original_chunk_ids: List[int]
    summarized_text: str
    representative_chunk: TextChunk
    similarity_score: float
    compression_ratio: float
    preserved_keywords: List[str]
    evidence_snippets: List[str]
    confidence_score: float
    

@dataclass
class SummarizationMetrics:
    """Metrics for summarization performance."""
    original_chunks: int
    summarized_chunks: int
    compression_ratio: float
    tokens_saved: int
    processing_time: float
    similarity_threshold: float
    clusters_formed: int
    avg_cluster_size: float
    

class SemanticSummarizer:
    """
    Intelligent semantic summarization for text chunks.
    
    This summarizer reduces token usage by:
    1. Clustering semantically similar chunks
    2. Creating extractive summaries from clusters
    3. Preserving key information and evidence
    4. Maintaining context and readability
    """
    
    def __init__(self, 
                 embedding_model_name: str = "all-MiniLM-L6-v2",
                 similarity_threshold: float = 0.75,
                 max_cluster_size: int = 5,
                 min_cluster_size: int = 2,
                 target_compression_ratio: float = 0.6,
                 preserve_evidence: bool = True,
                 max_summary_length: int = 500):
        """
        Initialize the semantic summarizer.
        
        Args:
            embedding_model_name: Name of the sentence transformer model
            similarity_threshold: Minimum similarity for clustering
            max_cluster_size: Maximum chunks per cluster
            min_cluster_size: Minimum chunks to form a cluster
            target_compression_ratio: Target compression ratio (0.0-1.0)
            preserve_evidence: Whether to preserve evidence snippets
            max_summary_length: Maximum length of summarized text
        """
        self.embedding_model_name = embedding_model_name
        self.similarity_threshold = similarity_threshold
        self.max_cluster_size = max_cluster_size
        self.min_cluster_size = min_cluster_size
        self.target_compression_ratio = target_compression_ratio
        self.preserve_evidence = preserve_evidence
        self.max_summary_length = max_summary_length
        
        # Initialize components
        self.embedding_model = None
        self.thread_pool = ThreadPoolExecutor(max_workers=2)
        
        # Initialize embedding model
        self._initialize_embedding_model()
        
        logger.info(f"Initialized SemanticSummarizer with similarity_threshold={similarity_threshold}")
    
    def _initialize_embedding_model(self) -> None:
        """Initialize the sentence transformer model."""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.warning("sentence-transformers not available, summarization disabled")
            return
        
        try:
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            logger.info(f"Loaded embedding model: {self.embedding_model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
            self.embedding_model = None
    
    async def summarize_chunks(self, 
                             chunks: List[TextChunk],
                             area: str) -> Tuple[List[SummarizedChunk], SummarizationMetrics]:
        """
        Summarize chunks by clustering and extractive summarization.
        
        Args:
            chunks: List of text chunks to summarize
            area: Classification area for context
            
        Returns:
            Tuple of (summarized_chunks, summarization_metrics)
        """
        if not chunks or not self.embedding_model:
            return self._create_fallback_summary(chunks)
        
        import time
        start_time = time.time()
        
        logger.info(f"Summarizing {len(chunks)} chunks for area {area}")
        
        try:
            # Compute embeddings for all chunks
            embeddings = await self._compute_chunk_embeddings(chunks)
            
            # Cluster similar chunks
            clusters = self._cluster_chunks(chunks, embeddings)
            
            # Create summaries from clusters
            summarized_chunks = []
            
            for cluster_chunks in clusters:
                if len(cluster_chunks) >= self.min_cluster_size:
                    # Create summary from cluster
                    summary = await self._create_cluster_summary(cluster_chunks, area)
                    summarized_chunks.append(summary)
                else:
                    # Keep individual chunks if cluster is too small
                    for chunk in cluster_chunks:
                        summary = self._create_individual_summary(chunk)
                        summarized_chunks.append(summary)
            
            # Calculate metrics
            processing_time = time.time() - start_time
            metrics = self._calculate_metrics(
                chunks, summarized_chunks, processing_time, len(clusters)
            )
            
            logger.info(f"Summarized {len(chunks)} → {len(summarized_chunks)} chunks "
                       f"({metrics.compression_ratio:.2f} compression, {metrics.tokens_saved} tokens saved)")
            
            return summarized_chunks, metrics
            
        except Exception as e:
            logger.error(f"Error during summarization: {e}")
            return self._create_fallback_summary(chunks)
    
    async def _compute_chunk_embeddings(self, chunks: List[TextChunk]) -> np.ndarray:
        """Compute embeddings for all chunks."""
        texts = [chunk.text for chunk in chunks]
        
        # Compute embeddings in thread pool
        loop = asyncio.get_event_loop()
        embeddings = await loop.run_in_executor(
            self.thread_pool,
            self.embedding_model.encode,
            texts
        )
        
        return embeddings
    
    def _cluster_chunks(self, 
                       chunks: List[TextChunk], 
                       embeddings: np.ndarray) -> List[List[TextChunk]]:
        """Cluster chunks based on semantic similarity."""
        if not SKLEARN_AVAILABLE:
            logger.warning("scikit-learn not available, using similarity-based clustering")
            return self._similarity_based_clustering(chunks, embeddings)
        
        try:
            # Calculate similarity matrix
            similarity_matrix = cosine_similarity(embeddings)
            
            # Use agglomerative clustering approach
            clusters = []
            used_indices = set()
            
            for i in range(len(chunks)):
                if i in used_indices:
                    continue
                
                # Find similar chunks
                cluster_indices = [i]
                used_indices.add(i)
                
                for j in range(i + 1, len(chunks)):
                    if j in used_indices:
                        continue
                    
                    if (similarity_matrix[i, j] >= self.similarity_threshold and 
                        len(cluster_indices) < self.max_cluster_size):
                        cluster_indices.append(j)
                        used_indices.add(j)
                
                # Create cluster
                cluster_chunks = [chunks[idx] for idx in cluster_indices]
                clusters.append(cluster_chunks)
            
            return clusters
            
        except Exception as e:
            logger.error(f"Error in clustering: {e}")
            return self._similarity_based_clustering(chunks, embeddings)
    
    def _similarity_based_clustering(self, 
                                   chunks: List[TextChunk], 
                                   embeddings: np.ndarray) -> List[List[TextChunk]]:
        """Simple similarity-based clustering fallback."""
        clusters = []
        used_indices = set()
        
        for i in range(len(chunks)):
            if i in used_indices:
                continue
            
            cluster = [chunks[i]]
            used_indices.add(i)
            
            # Find similar chunks
            for j in range(i + 1, len(chunks)):
                if j in used_indices or len(cluster) >= self.max_cluster_size:
                    continue
                
                # Calculate cosine similarity
                similarity = np.dot(embeddings[i], embeddings[j]) / (
                    np.linalg.norm(embeddings[i]) * np.linalg.norm(embeddings[j])
                )
                
                if similarity >= self.similarity_threshold:
                    cluster.append(chunks[j])
                    used_indices.add(j)
            
            clusters.append(cluster)
        
        return clusters
    
    async def _create_cluster_summary(self, 
                                    cluster_chunks: List[TextChunk], 
                                    area: str) -> SummarizedChunk:
        """Create an extractive summary from a cluster of chunks."""
        try:
            # Extract key information
            all_text = " ".join([chunk.text for chunk in cluster_chunks])
            keywords = self._extract_keywords(all_text, area)
            
            # Create extractive summary
            summary_text = self._create_extractive_summary(cluster_chunks, keywords)
            
            # Find representative chunk (most central)
            representative_chunk = self._find_representative_chunk(cluster_chunks)
            
            # Extract evidence snippets
            evidence_snippets = []
            if self.preserve_evidence:
                evidence_snippets = self._extract_evidence_snippets(cluster_chunks, keywords)
            
            # Calculate metrics
            original_length = sum(len(chunk.text) for chunk in cluster_chunks)
            compression_ratio = len(summary_text) / max(original_length, 1)
            
            # Calculate similarity score (average pairwise similarity)
            similarity_score = self._calculate_cluster_similarity(cluster_chunks)
            
            return SummarizedChunk(
                original_chunk_ids=[chunk.chunk_id for chunk in cluster_chunks],
                summarized_text=summary_text,
                representative_chunk=representative_chunk,
                similarity_score=similarity_score,
                compression_ratio=compression_ratio,
                preserved_keywords=keywords,
                evidence_snippets=evidence_snippets,
                confidence_score=self._calculate_confidence_score(cluster_chunks, similarity_score)
            )
            
        except Exception as e:
            logger.error(f"Error creating cluster summary: {e}")
            # Fallback to first chunk
            return self._create_individual_summary(cluster_chunks[0])
    
    def _extract_keywords(self, text: str, area: str) -> List[str]:
        """Extract relevant keywords from text."""
        # Simple keyword extraction based on frequency and length
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Filter words by length and frequency
        word_freq = defaultdict(int)
        for word in words:
            if len(word) >= 3:  # Minimum word length
                word_freq[word] += 1
        
        # Sort by frequency and take top keywords
        keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        
        # Return top 10 keywords
        return [word for word, freq in keywords[:10] if freq >= 2]
    
    def _create_extractive_summary(self, 
                                 cluster_chunks: List[TextChunk], 
                                 keywords: List[str]) -> str:
        """Create extractive summary by selecting important sentences."""
        # Combine all text
        all_sentences = []
        for chunk in cluster_chunks:
            sentences = self._split_into_sentences(chunk.text)
            all_sentences.extend(sentences)
        
        # Score sentences based on keyword presence
        scored_sentences = []
        for sentence in all_sentences:
            score = self._score_sentence(sentence, keywords)
            scored_sentences.append((sentence, score))
        
        # Sort by score and select top sentences
        scored_sentences.sort(key=lambda x: x[1], reverse=True)
        
        # Build summary within length limit
        summary_parts = []
        current_length = 0
        
        for sentence, score in scored_sentences:
            if current_length + len(sentence) <= self.max_summary_length:
                summary_parts.append(sentence)
                current_length += len(sentence)
            else:
                break
        
        # Ensure we have at least one sentence
        if not summary_parts and scored_sentences:
            summary_parts = [scored_sentences[0][0][:self.max_summary_length]]
        
        return " ".join(summary_parts)
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences."""
        # Simple sentence splitting
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]
    
    def _score_sentence(self, sentence: str, keywords: List[str]) -> float:
        """Score a sentence based on keyword presence and other factors."""
        sentence_lower = sentence.lower()
        
        # Keyword score
        keyword_score = sum(1 for keyword in keywords if keyword in sentence_lower)
        
        # Length score (prefer medium-length sentences)
        length_score = 1.0 if 20 <= len(sentence) <= 200 else 0.5
        
        # Position score (prefer sentences from beginning)
        position_score = 1.0  # Could be enhanced with position information
        
        return keyword_score * 0.6 + length_score * 0.2 + position_score * 0.2
    
    def _find_representative_chunk(self, cluster_chunks: List[TextChunk]) -> TextChunk:
        """Find the most representative chunk in the cluster."""
        if len(cluster_chunks) == 1:
            return cluster_chunks[0]
        
        # For now, select the chunk with median length
        # In a more sophisticated implementation, we could use centrality measures
        chunks_by_length = sorted(cluster_chunks, key=lambda x: len(x.text))
        median_index = len(chunks_by_length) // 2
        
        return chunks_by_length[median_index]
    
    def _extract_evidence_snippets(self, 
                                 cluster_chunks: List[TextChunk], 
                                 keywords: List[str]) -> List[str]:
        """Extract evidence snippets from cluster chunks."""
        evidence_snippets = []
        
        for chunk in cluster_chunks:
            # Find sentences containing keywords
            sentences = self._split_into_sentences(chunk.text)
            
            for sentence in sentences:
                sentence_lower = sentence.lower()
                
                # Check if sentence contains important keywords
                keyword_count = sum(1 for keyword in keywords if keyword in sentence_lower)
                
                if keyword_count >= 2:  # Sentence must contain at least 2 keywords
                    # Truncate if too long
                    if len(sentence) > 150:
                        sentence = sentence[:147] + "..."
                    evidence_snippets.append(sentence)
                    
                    # Limit number of evidence snippets
                    if len(evidence_snippets) >= 3:
                        break
            
            if len(evidence_snippets) >= 3:
                break
        
        return evidence_snippets
    
    def _calculate_cluster_similarity(self, cluster_chunks: List[TextChunk]) -> float:
        """Calculate average pairwise similarity within cluster."""
        if len(cluster_chunks) < 2:
            return 1.0
        
        # Simple text-based similarity calculation
        total_similarity = 0.0
        pair_count = 0
        
        for i in range(len(cluster_chunks)):
            for j in range(i + 1, len(cluster_chunks)):
                # Calculate Jaccard similarity of words
                words_i = set(cluster_chunks[i].text.lower().split())
                words_j = set(cluster_chunks[j].text.lower().split())
                
                intersection = len(words_i & words_j)
                union = len(words_i | words_j)
                
                if union > 0:
                    similarity = intersection / union
                    total_similarity += similarity
                    pair_count += 1
        
        return total_similarity / max(pair_count, 1)
    
    def _calculate_confidence_score(self, 
                                  cluster_chunks: List[TextChunk], 
                                  similarity_score: float) -> float:
        """Calculate confidence score for the summary."""
        # Base confidence on cluster size and similarity
        size_score = min(len(cluster_chunks) / self.max_cluster_size, 1.0)
        
        # Combine factors
        confidence = (similarity_score * 0.7 + size_score * 0.3)
        
        return min(confidence, 1.0)
    
    def _create_individual_summary(self, chunk: TextChunk) -> SummarizedChunk:
        """Create a summary for an individual chunk (no clustering)."""
        return SummarizedChunk(
            original_chunk_ids=[chunk.chunk_id],
            summarized_text=chunk.text,
            representative_chunk=chunk,
            similarity_score=1.0,
            compression_ratio=1.0,
            preserved_keywords=[],
            evidence_snippets=[],
            confidence_score=0.5  # Lower confidence for individual chunks
        )
    
    def _calculate_metrics(self, 
                         original_chunks: List[TextChunk],
                         summarized_chunks: List[SummarizedChunk],
                         processing_time: float,
                         clusters_formed: int) -> SummarizationMetrics:
        """Calculate summarization metrics."""
        original_tokens = sum(
            chunk.token_count or self._estimate_tokens(chunk.text)
            for chunk in original_chunks
        )
        
        summarized_tokens = sum(
            self._estimate_tokens(summary.summarized_text)
            for summary in summarized_chunks
        )
        
        compression_ratio = summarized_tokens / max(original_tokens, 1)
        tokens_saved = original_tokens - summarized_tokens
        
        avg_cluster_size = len(original_chunks) / max(clusters_formed, 1)
        
        return SummarizationMetrics(
            original_chunks=len(original_chunks),
            summarized_chunks=len(summarized_chunks),
            compression_ratio=compression_ratio,
            tokens_saved=tokens_saved,
            processing_time=processing_time,
            similarity_threshold=self.similarity_threshold,
            clusters_formed=clusters_formed,
            avg_cluster_size=avg_cluster_size
        )
    
    def _create_fallback_summary(self, chunks: List[TextChunk]) -> Tuple[List[SummarizedChunk], SummarizationMetrics]:
        """Create fallback summary when embeddings are not available."""
        summaries = [self._create_individual_summary(chunk) for chunk in chunks]
        
        metrics = SummarizationMetrics(
            original_chunks=len(chunks),
            summarized_chunks=len(summaries),
            compression_ratio=1.0,
            tokens_saved=0,
            processing_time=0.0,
            similarity_threshold=self.similarity_threshold,
            clusters_formed=len(chunks),
            avg_cluster_size=1.0
        )
        
        return summaries, metrics
    
    def _estimate_tokens(self, text: str) -> int:
        """Estimate number of tokens in text."""
        # Simple estimation: ~4 characters per token for Portuguese
        return max(1, len(text) // 4)
    
    def get_summarization_config(self) -> Dict[str, Any]:
        """Get current summarization configuration."""
        return {
            'embedding_model_name': self.embedding_model_name,
            'similarity_threshold': self.similarity_threshold,
            'max_cluster_size': self.max_cluster_size,
            'min_cluster_size': self.min_cluster_size,
            'target_compression_ratio': self.target_compression_ratio,
            'preserve_evidence': self.preserve_evidence,
            'max_summary_length': self.max_summary_length,
            'embedding_model_available': self.embedding_model is not None,
            'sklearn_available': SKLEARN_AVAILABLE
        }
    
    def update_similarity_threshold(self, new_threshold: float) -> None:
        """Update similarity threshold."""
        self.similarity_threshold = new_threshold
        logger.info(f"Updated similarity threshold to {new_threshold}")
    
    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=True)


# Factory function for easy instantiation
def create_semantic_summarizer(
    similarity_threshold: float = 0.75,
    target_compression_ratio: float = 0.6,
    max_cluster_size: int = 5
) -> SemanticSummarizer:
    """
    Create a configured semantic summarizer.
    
    Args:
        similarity_threshold: Minimum similarity for clustering
        target_compression_ratio: Target compression ratio
        max_cluster_size: Maximum chunks per cluster
        
    Returns:
        Configured SemanticSummarizer instance
    """
    return SemanticSummarizer(
        similarity_threshold=similarity_threshold,
        target_compression_ratio=target_compression_ratio,
        max_cluster_size=max_cluster_size
    )