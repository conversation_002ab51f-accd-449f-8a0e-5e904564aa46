"""
OptimizedBatchProcessingService with token optimization and hierarchical classification.

This service integrates all optimization components to provide efficient PDF batch processing
with significant token reduction while maintaining classification accuracy.
"""

import asyncio
import logging
import os
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any, Tuple
from dataclasses import asdict
from concurrent.futures import ThreadPoolExecutor

from ..models.batch_processing import (
    BatchResult,
    JobStatus,
    JobStatusResponse,
    PDFClassificationResult,
    TextChunk,
    ChunkResult,
    BatchProcessingRequest,
    BatchResultSummary,
    ProcessingError
)
from .hierarchical_classifier import HierarchicalClassifier, HierarchicalMetrics
from .token_budget_manager import TokenBudgetManager, PriorityLevel
from .chunk_relevance_filter import ChunkRelevanceFilter
from .semantic_summarizer import SemanticSummarizer
from .chunk_classifier import ChunkClassifier
from .cascade_classifier import CascadeClassifier
from .batch_processing_service import BatchJobManager, CSVExporter, JobProgressCallback
from ...tools.pdf_text_extractor import PDFTextExtractor
from ...utils.config_utils import carregar_configuracao

logger = logging.getLogger(__name__)


class OptimizedBatchProcessingService:
    """
    Optimized batch processing service with hierarchical classification and token optimization.
    
    Features:
    - Token usage reduction of 80-95%
    - Hierarchical multi-stage classification
    - Intelligent chunk filtering and summarization
    - Real-time budget management
    - Comprehensive performance metrics
    """
    
    def __init__(self, 
                 max_workers: int = 4,
                 output_dir: str = "data/output",
                 enable_token_optimization: bool = True,
                 budget_config: Optional[Dict[str, Any]] = None,
                 use_cascade_pipeline: bool = False):
        """
        Initialize the optimized batch processing service.
        
        Args:
            max_workers: Maximum number of parallel workers
            output_dir: Directory for output files
            enable_token_optimization: Whether to enable token optimization
            budget_config: Token budget configuration
            use_cascade_pipeline: Whether to use intelligent cascade pipeline
        """
        self.max_workers = max_workers
        self.output_dir = Path(output_dir)
        self.enable_token_optimization = enable_token_optimization
        self.use_cascade_pipeline = use_cascade_pipeline
        
        # Initialize components
        self.pdf_extractor = PDFTextExtractor()
        self.csv_exporter = CSVExporter(output_dir)
        self.job_manager = BatchJobManager()
        
        # Initialize classification components based on mode
        if use_cascade_pipeline:
            # Use intelligent cascade pipeline
            self.cascade_classifier = CascadeClassifier(
                enable_cache=True,
                confidence_threshold=0.8,
                complexity_threshold=0.6
            )
            self.token_budget_manager = TokenBudgetManager(budget_config=budget_config)
            # Keep other components for compatibility
            self.relevance_filter = None
            self.semantic_summarizer = None
            self.hierarchical_classifier = None
            self.chunk_classifier = None
        elif enable_token_optimization:
            # Use traditional optimization pipeline
            self.relevance_filter = ChunkRelevanceFilter()
            self.semantic_summarizer = SemanticSummarizer()
            self.hierarchical_classifier = HierarchicalClassifier()
            self.token_budget_manager = TokenBudgetManager(budget_config=budget_config)
            self.cascade_classifier = None
        else:
            # Fallback to basic classification
            self.chunk_classifier = ChunkClassifier()
            self.relevance_filter = None
            self.semantic_summarizer = None
            self.hierarchical_classifier = None
            self.token_budget_manager = None
            self.cascade_classifier = None
        
        # Thread pool for CPU-intensive operations
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        
        # Configuration
        self.config = carregar_configuracao()
        
        # Performance metrics
        self.processing_metrics = {}
        
        mode = "cascade" if use_cascade_pipeline else ("optimized" if enable_token_optimization else "basic")
        logger.info(f"Initialized OptimizedBatchProcessingService with mode={mode}")
    
    async def start_batch_processing(self, 
                                   request: BatchProcessingRequest,
                                   user_id: str,
                                   priority: PriorityLevel = PriorityLevel.MEDIUM,
                                   progress_callback: Optional[Callable] = None) -> str:
        """
        Start a new optimized batch processing job.
        
        Args:
            request: Batch processing request parameters
            user_id: User ID for the job
            priority: Priority level for token allocation
            progress_callback: Optional callback for progress updates
            
        Returns:
            Job ID for tracking progress
        """
        try:
            # Validate folder path
            folder_path = Path(request.folder_path)
            if not folder_path.exists():
                raise ValueError(f"Folder does not exist: {folder_path}")
            
            # Create job
            job_id = self.job_manager.create_job(
                folder_path=str(folder_path),
                area=request.area,
                user_id=user_id,
                config={**request.dict(), "priority": priority.value}
            )
            
            # Start processing in background
            asyncio.create_task(self._process_batch_optimized(
                job_id, request, user_id, priority, progress_callback
            ))
            
            return job_id
            
        except Exception as e:
            logger.error(f"Error starting optimized batch processing: {e}")
            raise
    
    async def _process_batch_optimized(self, 
                                     job_id: str, 
                                     request: BatchProcessingRequest,
                                     user_id: str,
                                     priority: PriorityLevel,
                                     progress_callback: Optional[Callable] = None) -> None:
        """Process batch with optimization pipeline."""
        try:
            # Setup progress callback
            callback = JobProgressCallback(job_id, progress_callback)
            
            # Find PDF files
            pdf_files = self._find_pdf_files(request.folder_path)
            if not pdf_files:
                await self.job_manager.update_progress(
                    job_id, 0.0, "No PDF files found", 0, 0, JobStatus.FAILED
                )
                self.job_manager.fail_job(job_id, "No PDF files found in folder")
                return
            
            # Estimate total tokens and request budget allocation
            estimated_tokens = await self._estimate_total_tokens(pdf_files, request)
            
            if self.enable_token_optimization and self.token_budget_manager:
                # Request token allocation
                approved, reason, allocated_tokens = await self.token_budget_manager.request_token_allocation(
                    estimated_tokens, job_id, user_id, request.area, priority
                )
                
                if not approved:
                    await self.job_manager.update_progress(
                        job_id, 0.0, f"Token allocation denied: {reason}", 0, 0, JobStatus.FAILED
                    )
                    self.job_manager.fail_job(job_id, f"Token allocation denied: {reason}")
                    return
                
                logger.info(f"Token allocation approved: {allocated_tokens}/{estimated_tokens} tokens")
            
            # Update job status
            await self.job_manager.update_progress(
                job_id, 0.0, "Starting optimized processing", 0, len(pdf_files), JobStatus.PROCESSING
            )
            
            # Process PDFs with optimization
            pdf_results = []
            total_metrics = {}
            processing_start_time = time.time()
            
            for i, pdf_file in enumerate(pdf_files):
                try:
                    # Update progress
                    progress = i / len(pdf_files)
                    await callback.update_progress(
                        progress, 
                        f"Processing {pdf_file.name}",
                        i,
                        len(pdf_files)
                    )
                    
                    # Process single PDF with optimization
                    pdf_result, pdf_metrics = await self._process_single_pdf_optimized(
                        pdf_file, request, user_id, job_id
                    )
                    
                    pdf_results.append(pdf_result)
                    
                    # Aggregate metrics
                    for key, value in pdf_metrics.items():
                        if key not in total_metrics:
                            total_metrics[key] = 0
                        total_metrics[key] += value
                    
                    # Check if we should continue (budget constraints)
                    if self.enable_token_optimization and self.token_budget_manager:
                        current_budget = self.token_budget_manager.get_current_metrics()
                        if current_budget.status.value == "exceeded":
                            logger.warning(f"Budget exceeded, stopping processing at {i+1}/{len(pdf_files)}")
                            break
                    
                except Exception as e:
                    logger.error(f"Error processing PDF {pdf_file}: {e}")
                    # Create error result
                    error_result = PDFClassificationResult(
                        pdf_path=str(pdf_file),
                        filename=pdf_file.name,
                        error=str(e)
                    )
                    pdf_results.append(error_result)
            
            # Calculate total processing time
            total_processing_time = time.time() - processing_start_time
            
            # Create batch result
            batch_result = BatchResult(
                job_id=job_id,
                folder_path=request.folder_path,
                area=request.area,
                pdf_results=pdf_results,
                total_pdfs=len(pdf_files),
                total_processing_time=total_processing_time,
                status=JobStatus.COMPLETED
            )
            
            # Export to CSV
            csv_path = self.csv_exporter.export_batch_results(batch_result)
            batch_result.csv_output_path = csv_path
            
            # Complete job
            self.job_manager.complete_job(job_id, batch_result)
            
            # Log optimization metrics
            await self._log_optimization_metrics(job_id, total_metrics, batch_result)
            
        except Exception as e:
            logger.error(f"Error in optimized batch processing {job_id}: {e}")
            self.job_manager.fail_job(job_id, str(e))
    
    async def _process_single_pdf_optimized(self, 
                                          pdf_file: Path, 
                                          request: BatchProcessingRequest,
                                          user_id: str,
                                          job_id: str) -> Tuple[PDFClassificationResult, Dict[str, Any]]:
        """Process a single PDF with optimization pipeline."""
        operation_start_time = time.time()
        operation_id = f"{job_id}_{pdf_file.stem}"
        
        try:
            # Configure extractors
            self.pdf_extractor.chunk_size = request.chunk_size
            self.pdf_extractor.overlap = request.overlap
            
            # Extract and chunk PDF
            chunks = await asyncio.get_event_loop().run_in_executor(
                self.thread_pool,
                self.pdf_extractor.extract_and_chunk,
                pdf_file
            )
            
            if not chunks:
                return PDFClassificationResult(
                    pdf_path=str(pdf_file),
                    filename=pdf_file.name,
                    error="Failed to extract text from PDF"
                ), {}
            
            # Initialize metrics
            metrics = {
                'original_chunks': len(chunks),
                'original_tokens': sum(chunk.token_count or self._estimate_tokens(chunk.text) for chunk in chunks),
                'processing_time': 0.0,
                'tokens_saved': 0,
                'reduction_ratio': 0.0
            }
            
            # Apply classification pipeline based on mode
            if self.use_cascade_pipeline and self.cascade_classifier:
                # Use intelligent cascade pipeline
                cascade_result = await self.cascade_classifier.classify_chunks(chunks, request.area)
                classification_results = cascade_result.chunks_results
                
                # Update metrics with cascade data
                cascade_metrics = cascade_result.metrics
                metrics.update({
                    'final_chunks': len(classification_results),
                    'tokens_saved': cascade_metrics.total_tokens_saved,
                    'reduction_ratio': cascade_metrics.cost_reduction_ratio,
                    'cache_hits': cascade_metrics.cache_hits,
                    'fast_model_usage': cascade_metrics.fast_model_usage,
                    'accurate_model_usage': cascade_metrics.accurate_model_usage,
                    'cascade_processing_time': cascade_result.total_processing_time
                })
            elif self.enable_token_optimization and self.hierarchical_classifier:
                # Use traditional hierarchical classification
                classification_results, hierarchical_metrics = await self.hierarchical_classifier.classify_chunks_hierarchical(
                    chunks, request.area
                )
                
                # Update metrics
                metrics.update({
                    'final_chunks': len(classification_results),
                    'tokens_saved': hierarchical_metrics.total_tokens_saved,
                    'reduction_ratio': hierarchical_metrics.final_reduction_ratio,
                    'pipeline_efficiency': hierarchical_metrics.pipeline_efficiency,
                    'stages_completed': hierarchical_metrics.total_stages
                })
            else:
                # Use basic classification
                classification_results = await self.chunk_classifier.classify_chunks(chunks, request.area)
                metrics['final_chunks'] = len(classification_results)
            
            # Aggregate results into PDF classification
            pdf_result = await self._aggregate_pdf_results(
                classification_results, str(pdf_file), chunks
            )
            
            # Record token usage
            operation_time = time.time() - operation_start_time
            metrics['processing_time'] = operation_time
            
            if self.enable_token_optimization and self.token_budget_manager:
                tokens_used = metrics['original_tokens'] - metrics['tokens_saved']
                await self.token_budget_manager.record_token_usage(
                    operation_id=operation_id,
                    operation_type="pdf_classification",
                    tokens_used=tokens_used,
                    processing_time=operation_time,
                    chunks_processed=len(chunks),
                    area=request.area,
                    user_id=user_id
                )
            
            return pdf_result, metrics
            
        except Exception as e:
            logger.error(f"Error processing PDF {pdf_file}: {e}")
            return PDFClassificationResult(
                pdf_path=str(pdf_file),
                filename=pdf_file.name,
                error=str(e)
            ), {}
    
    async def _aggregate_pdf_results(self, 
                                   classification_results: List[ChunkResult],
                                   pdf_path: str,
                                   original_chunks: List[TextChunk]) -> PDFClassificationResult:
        """Aggregate chunk results into PDF classification result."""
        if not classification_results:
            return PDFClassificationResult(
                pdf_path=pdf_path,
                filename=Path(pdf_path).name,
                error="No classification results"
            )
        
        # Aggregate subtemas from all chunks
        subtema_votes = {}
        subtema_evidences = {}
        total_processing_time = 0
        successful_chunks = []
        
        for chunk_result in classification_results:
            if chunk_result.error:
                continue
                
            successful_chunks.append(chunk_result)
            total_processing_time += chunk_result.processing_time
            
            # Aggregate votes with confidence weighting
            for subtema in chunk_result.subtemas:
                confidence = chunk_result.confidence_scores.get(subtema, 0.5)
                if confidence >= 0.3:  # Minimum confidence threshold
                    if subtema not in subtema_votes:
                        subtema_votes[subtema] = []
                    subtema_votes[subtema].append(confidence)
                    
                    # Collect evidence texts
                    evidence = chunk_result.evidence_texts.get(subtema, "")
                    if evidence and subtema not in subtema_evidences:
                        subtema_evidences[subtema] = []
                    if evidence:
                        subtema_evidences[subtema].append(evidence)
        
        # Calculate final confidence scores
        final_confidence_scores = {}
        for subtema, votes in subtema_votes.items():
            final_confidence_scores[subtema] = sum(votes) / len(votes)
        
        # Select top subtemas based on confidence
        sorted_subtemas = sorted(
            final_confidence_scores.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        # Limit to max_subtemas
        max_subtemas = 3
        selected_subtemas = sorted_subtemas[:max_subtemas]
        final_subtemas = [subtema for subtema, _ in selected_subtemas]
        final_confidences = {subtema: conf for subtema, conf in selected_subtemas}
        
        # Consolidate evidence texts
        final_evidence_texts = {}
        for subtema in final_subtemas:
            if subtema in subtema_evidences:
                unique_evidences = list(set(subtema_evidences[subtema]))
                combined_evidence = "...".join(unique_evidences[:3])
                final_evidence_texts[subtema] = combined_evidence[:200]
        
        # Calculate metadata
        total_pages = max(
            (chunk.chunk.page_end for chunk in successful_chunks),
            default=0
        )
        total_chunks = len(classification_results)
        
        return PDFClassificationResult(
            pdf_path=pdf_path,
            filename=Path(pdf_path).name,
            subtemas_finais=final_subtemas,
            confidence_scores=final_confidences,
            evidence_texts=final_evidence_texts,
            total_pages=total_pages,
            total_chunks=total_chunks,
            processing_time=total_processing_time,
            area=self._determine_area(pdf_path),
            timestamp=datetime.utcnow(),
            error=None
        )
    
    async def _estimate_total_tokens(self, 
                                   pdf_files: List[Path], 
                                   request: BatchProcessingRequest) -> int:
        """Estimate total tokens needed for batch processing."""
        # Sample a few PDFs to estimate average
        sample_size = min(3, len(pdf_files))
        sample_files = pdf_files[:sample_size]
        
        total_sample_tokens = 0
        for pdf_file in sample_files:
            try:
                # Quick text extraction for estimation
                chunks = await asyncio.get_event_loop().run_in_executor(
                    self.thread_pool,
                    self.pdf_extractor.extract_and_chunk,
                    pdf_file
                )
                
                file_tokens = sum(
                    chunk.token_count or self._estimate_tokens(chunk.text) 
                    for chunk in chunks
                )
                total_sample_tokens += file_tokens
                
            except Exception as e:
                logger.warning(f"Error sampling PDF {pdf_file}: {e}")
                # Fallback estimation
                total_sample_tokens += 10000  # Conservative estimate
        
        # Calculate average and project
        avg_tokens_per_pdf = total_sample_tokens / sample_size
        total_estimated = int(avg_tokens_per_pdf * len(pdf_files))
        
        logger.info(f"Estimated total tokens: {total_estimated} for {len(pdf_files)} PDFs")
        return total_estimated
    
    async def _log_optimization_metrics(self, 
                                      job_id: str, 
                                      total_metrics: Dict[str, Any],
                                      batch_result: BatchResult) -> None:
        """Log comprehensive optimization metrics."""
        try:
            # Calculate overall optimization metrics
            if 'original_tokens' in total_metrics and 'tokens_saved' in total_metrics:
                optimization_ratio = total_metrics['tokens_saved'] / max(total_metrics['original_tokens'], 1)
                
                logger.info(f"Optimization Metrics for Job {job_id}:")
                logger.info(f"  - Original tokens: {total_metrics.get('original_tokens', 0):,}")
                logger.info(f"  - Tokens saved: {total_metrics.get('tokens_saved', 0):,}")
                logger.info(f"  - Optimization ratio: {optimization_ratio:.2%}")
                logger.info(f"  - Processing time: {total_metrics.get('processing_time', 0):.2f}s")
                logger.info(f"  - Pipeline efficiency: {total_metrics.get('pipeline_efficiency', 0):.2f} tokens/s")
                logger.info(f"  - Success rate: {batch_result.successful_pdfs}/{batch_result.total_pdfs} ({batch_result.successful_pdfs/batch_result.total_pdfs:.1%})")
            
            # Get budget metrics if available
            if self.token_budget_manager:
                budget_metrics = self.token_budget_manager.get_current_metrics()
                logger.info(f"  - Current budget usage: {budget_metrics.usage_percentage:.1f}%")
                logger.info(f"  - Estimated cost: ${budget_metrics.cost_estimate:.4f}")
        
        except Exception as e:
            logger.error(f"Error logging optimization metrics: {e}")
    
    def _find_pdf_files(self, folder_path: str) -> List[Path]:
        """Find all PDF files in the folder."""
        folder = Path(folder_path)
        pdf_files = []
        
        for file_path in folder.rglob("*.pdf"):
            if file_path.is_file():
                pdf_files.append(file_path)
        
        return sorted(pdf_files)
    
    def _determine_area(self, pdf_path: str) -> str:
        """Determine area from PDF path or filename."""
        filename = Path(pdf_path).name.upper()
        
        if any(term in filename for term in ['EDUCACAO', 'ESCOLA', 'ENSINO']):
            return 'EDUCACAO'
        elif any(term in filename for term in ['SAUDE', 'HOSPITAL', 'MEDICO']):
            return 'SAUDE'
        elif any(term in filename for term in ['AMBIENTE', 'AMBIENTAL', 'MEIO']):
            return 'MEIO_AMBIENTE'
        
        return 'EDUCACAO'  # Default
    
    def _estimate_tokens(self, text: str) -> int:
        """Estimate number of tokens in text."""
        return max(1, len(text) // 4)
    
    # Delegate methods to existing services
    def get_job_status(self, job_id: str) -> Optional[JobStatusResponse]:
        """Get current status of a job."""
        return self.job_manager.get_job_status(job_id)
    
    def get_job_results(self, job_id: str) -> Optional[BatchResult]:
        """Get results of a completed job."""
        job_data = self.job_manager.jobs.get(job_id)
        if not job_data or not job_data.get('results'):
            return None
        
        result_data = job_data['results']
        return BatchResult(**result_data)
    
    def cancel_job(self, job_id: str) -> bool:
        """Cancel a running job."""
        return self.job_manager.cancel_job(job_id)
    
    def cleanup_old_jobs(self, days_old: int = 7) -> int:
        """Clean up old completed jobs."""
        return self.job_manager.cleanup_old_jobs(days_old)
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get optimization statistics."""
        if not self.enable_token_optimization:
            return {"optimization_enabled": False}
        
        stats = {"optimization_enabled": True}
        
        # Get budget manager stats
        if self.token_budget_manager:
            stats["budget_metrics"] = self.token_budget_manager.get_current_metrics()
            stats["budget_config"] = self.token_budget_manager.get_budget_config()
            stats["usage_analytics"] = self.token_budget_manager.get_usage_analytics()
        
        # Get filter stats
        if self.relevance_filter:
            stats["filter_config"] = self.relevance_filter.get_filtering_config()
            stats["filter_cache"] = self.relevance_filter.get_cache_stats()
        
        # Get summarizer stats
        if self.semantic_summarizer:
            stats["summarizer_config"] = self.semantic_summarizer.get_summarization_config()
        
        # Get hierarchical classifier stats
        if self.hierarchical_classifier:
            stats["pipeline_config"] = self.hierarchical_classifier.get_pipeline_config()
        
        return stats
    
    def get_optimization_suggestions(self) -> List[Any]:
        """Get optimization suggestions."""
        if not self.enable_token_optimization or not self.token_budget_manager:
            return []
        
        return self.token_budget_manager.get_optimization_suggestions()
    
    def update_optimization_config(self, **kwargs) -> None:
        """Update optimization configuration."""
        if not self.enable_token_optimization:
            logger.warning("Optimization not enabled, cannot update config")
            return
        
        # Update budget manager
        budget_keys = ['daily_limit', 'hourly_limit', 'per_job_limit', 'cost_per_token']
        budget_updates = {k: v for k, v in kwargs.items() if k in budget_keys}
        if budget_updates and self.token_budget_manager:
            self.token_budget_manager.update_budget_config(**budget_updates)
        
        # Update relevance filter
        filter_keys = ['relevance_threshold', 'max_chunks_per_pdf']
        filter_updates = {k: v for k, v in kwargs.items() if k in filter_keys}
        if filter_updates and self.relevance_filter:
            if 'relevance_threshold' in filter_updates:
                self.relevance_filter.update_threshold(filter_updates['relevance_threshold'])
        
        # Update semantic summarizer
        summarizer_keys = ['similarity_threshold']
        summarizer_updates = {k: v for k, v in kwargs.items() if k in summarizer_keys}
        if summarizer_updates and self.semantic_summarizer:
            if 'similarity_threshold' in summarizer_updates:
                self.semantic_summarizer.update_similarity_threshold(summarizer_updates['similarity_threshold'])
        
        # Update hierarchical classifier
        pipeline_keys = ['enable_relevance_filtering', 'enable_semantic_summarization', 'early_stopping_enabled']
        pipeline_updates = {k: v for k, v in kwargs.items() if k in pipeline_keys}
        if pipeline_updates and self.hierarchical_classifier:
            self.hierarchical_classifier.update_pipeline_config(**pipeline_updates)
    
    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=True)


# Factory function for easy instantiation
def create_optimized_batch_processing_service(
    enable_token_optimization: bool = True,
    daily_token_limit: int = 1000000,
    max_workers: int = 4
) -> OptimizedBatchProcessingService:
    """
    Create a configured optimized batch processing service.
    
    Args:
        enable_token_optimization: Whether to enable token optimization
        daily_token_limit: Daily token limit
        max_workers: Maximum number of parallel workers
        
    Returns:
        Configured OptimizedBatchProcessingService instance
    """
    budget_config = {
        'daily_limit': daily_token_limit,
        'hourly_limit': daily_token_limit // 24,
        'per_job_limit': daily_token_limit // 20,
        'per_user_limit': daily_token_limit // 5
    }
    
    return OptimizedBatchProcessingService(
        max_workers=max_workers,
        enable_token_optimization=enable_token_optimization,
        budget_config=budget_config
    )