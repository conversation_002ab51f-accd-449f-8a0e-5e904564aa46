"""
TokenBudgetManager for intelligent token budget control in PDF batch processing.

This module provides comprehensive token budget management to prevent cost overruns
and optimize resource allocation across different classification tasks.
"""

import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import json
from pathlib import Path
import asyncio

from ..models.batch_processing import TextChunk, ChunkResult, JobStatus

logger = logging.getLogger(__name__)


class BudgetStatus(Enum):
    """Token budget status."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    EXCEEDED = "exceeded"


class PriorityLevel(Enum):
    """Priority levels for token allocation."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class TokenUsage:
    """Token usage tracking for a specific operation."""
    operation_id: str
    operation_type: str
    tokens_used: int
    timestamp: datetime
    cost_estimate: float
    processing_time: float
    chunks_processed: int
    area: str
    user_id: str


@dataclass
class BudgetAllocation:
    """Budget allocation for different operations."""
    daily_limit: int
    hourly_limit: int
    per_job_limit: int
    per_user_limit: int
    reserved_tokens: int
    emergency_tokens: int


@dataclass
class BudgetMetrics:
    """Comprehensive budget metrics."""
    total_allocated: int
    total_used: int
    total_remaining: int
    usage_percentage: float
    cost_estimate: float
    daily_usage: int
    hourly_usage: int
    projected_daily_usage: int
    time_to_limit: Optional[float]
    status: BudgetStatus
    

@dataclass
class TokenOptimizationSuggestion:
    """Optimization suggestions for token usage."""
    suggestion_type: str
    description: str
    potential_savings: int
    confidence: float
    implementation_effort: str
    

class TokenBudgetManager:
    """
    Intelligent token budget management system.
    
    Features:
    - Real-time token usage tracking
    - Budget allocation and limits
    - Cost estimation and projection
    - Optimization suggestions
    - Priority-based allocation
    - Emergency budget management
    """
    
    def __init__(self, 
                 budget_config: Optional[Dict[str, Any]] = None,
                 cost_per_token: float = 0.0001,
                 storage_path: str = "data/token_budget",
                 enable_cost_tracking: bool = True):
        """
        Initialize the token budget manager.
        
        Args:
            budget_config: Configuration for budget limits
            cost_per_token: Estimated cost per token in USD
            storage_path: Path to store budget data
            enable_cost_tracking: Whether to track costs
        """
        self.cost_per_token = cost_per_token
        self.storage_path = Path(storage_path)
        self.enable_cost_tracking = enable_cost_tracking
        
        # Create storage directory
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # Default budget configuration
        default_budget = {
            'daily_limit': 1000000,      # 1M tokens per day
            'hourly_limit': 100000,      # 100K tokens per hour
            'per_job_limit': 50000,      # 50K tokens per job
            'per_user_limit': 200000,    # 200K tokens per user per day
            'reserved_tokens': 100000,   # Reserved for high-priority tasks
            'emergency_tokens': 50000    # Emergency buffer
        }
        
        # Update with provided config
        if budget_config:
            default_budget.update(budget_config)
        
        self.budget_allocation = BudgetAllocation(**default_budget)
        
        # Usage tracking
        self.usage_history: List[TokenUsage] = []
        self.daily_usage: Dict[str, int] = {}  # date -> usage
        self.hourly_usage: Dict[str, int] = {}  # hour -> usage
        self.user_usage: Dict[str, int] = {}  # user_id -> usage
        self.job_usage: Dict[str, int] = {}  # job_id -> usage
        
        # Priority queues
        self.priority_allocations: Dict[PriorityLevel, int] = {
            PriorityLevel.LOW: int(0.3 * self.budget_allocation.daily_limit),
            PriorityLevel.MEDIUM: int(0.4 * self.budget_allocation.daily_limit),
            PriorityLevel.HIGH: int(0.2 * self.budget_allocation.daily_limit),
            PriorityLevel.CRITICAL: int(0.1 * self.budget_allocation.daily_limit)
        }
        
        # Load existing data
        self._load_usage_data()
        
        logger.info(f"Initialized TokenBudgetManager with daily limit: {self.budget_allocation.daily_limit}")
    
    async def request_token_allocation(self, 
                                     estimated_tokens: int,
                                     job_id: str,
                                     user_id: str,
                                     area: str,
                                     priority: PriorityLevel = PriorityLevel.MEDIUM) -> Tuple[bool, str, int]:
        """
        Request token allocation for a specific operation.
        
        Args:
            estimated_tokens: Estimated token requirement
            job_id: Job identifier
            user_id: User identifier
            area: Classification area
            priority: Priority level for allocation
            
        Returns:
            Tuple of (approved, reason, allocated_tokens)
        """
        logger.info(f"Token allocation request: {estimated_tokens} tokens for job {job_id}")
        
        # Check current budget status
        current_metrics = self.get_current_metrics()
        
        # Check various limits
        checks = [
            self._check_daily_limit(estimated_tokens),
            self._check_hourly_limit(estimated_tokens),
            self._check_job_limit(job_id, estimated_tokens),
            self._check_user_limit(user_id, estimated_tokens),
            self._check_priority_allocation(priority, estimated_tokens)
        ]
        
        # Find the most restrictive limit
        approved_tokens = estimated_tokens
        rejection_reason = None
        
        for check_passed, reason, max_tokens in checks:
            if not check_passed:
                return False, reason, 0
            approved_tokens = min(approved_tokens, max_tokens)
        
        # Apply optimization if budget is tight
        if current_metrics.status in [BudgetStatus.WARNING, BudgetStatus.CRITICAL]:
            approved_tokens = int(approved_tokens * 0.8)  # 20% reduction
            logger.warning(f"Budget tight, reducing allocation to {approved_tokens} tokens")
        
        # Reserve tokens
        if approved_tokens > 0:
            await self._reserve_tokens(approved_tokens, job_id, user_id, area, priority)
        
        return True, "Allocation approved", approved_tokens
    
    async def record_token_usage(self, 
                               operation_id: str,
                               operation_type: str,
                               tokens_used: int,
                               processing_time: float,
                               chunks_processed: int,
                               area: str,
                               user_id: str) -> None:
        """
        Record actual token usage for an operation.
        
        Args:
            operation_id: Unique operation identifier
            operation_type: Type of operation (classification, summarization, etc.)
            tokens_used: Actual tokens consumed
            processing_time: Time taken for processing
            chunks_processed: Number of chunks processed
            area: Classification area
            user_id: User identifier
        """
        # Create usage record
        usage = TokenUsage(
            operation_id=operation_id,
            operation_type=operation_type,
            tokens_used=tokens_used,
            timestamp=datetime.utcnow(),
            cost_estimate=tokens_used * self.cost_per_token if self.enable_cost_tracking else 0.0,
            processing_time=processing_time,
            chunks_processed=chunks_processed,
            area=area,
            user_id=user_id
        )
        
        # Add to history
        self.usage_history.append(usage)
        
        # Update tracking dictionaries
        today = datetime.utcnow().date().isoformat()
        current_hour = datetime.utcnow().strftime("%Y-%m-%d-%H")
        
        self.daily_usage[today] = self.daily_usage.get(today, 0) + tokens_used
        self.hourly_usage[current_hour] = self.hourly_usage.get(current_hour, 0) + tokens_used
        self.user_usage[user_id] = self.user_usage.get(user_id, 0) + tokens_used
        self.job_usage[operation_id] = self.job_usage.get(operation_id, 0) + tokens_used
        
        # Save data
        await self._save_usage_data()
        
        logger.info(f"Recorded token usage: {tokens_used} tokens for operation {operation_id}")
    
    def get_current_metrics(self) -> BudgetMetrics:
        """Get current budget metrics."""
        today = datetime.utcnow().date().isoformat()
        current_hour = datetime.utcnow().strftime("%Y-%m-%d-%H")
        
        # Calculate current usage
        daily_usage = self.daily_usage.get(today, 0)
        hourly_usage = self.hourly_usage.get(current_hour, 0)
        
        # Calculate remaining budget
        daily_remaining = max(0, self.budget_allocation.daily_limit - daily_usage)
        hourly_remaining = max(0, self.budget_allocation.hourly_limit - hourly_usage)
        
        # Overall remaining is the minimum of daily and hourly
        total_remaining = min(daily_remaining, hourly_remaining)
        
        # Calculate usage percentage
        usage_percentage = (daily_usage / self.budget_allocation.daily_limit) * 100
        
        # Determine status
        if usage_percentage >= 100:
            status = BudgetStatus.EXCEEDED
        elif usage_percentage >= 90:
            status = BudgetStatus.CRITICAL
        elif usage_percentage >= 75:
            status = BudgetStatus.WARNING
        else:
            status = BudgetStatus.HEALTHY
        
        # Calculate cost estimate
        cost_estimate = daily_usage * self.cost_per_token if self.enable_cost_tracking else 0.0
        
        # Project daily usage
        current_hour_of_day = datetime.utcnow().hour
        if current_hour_of_day > 0:
            projected_daily_usage = int(daily_usage * (24 / current_hour_of_day))
        else:
            projected_daily_usage = daily_usage
        
        # Calculate time to limit
        time_to_limit = None
        if hourly_usage > 0:
            # Estimate time to reach daily limit based on current hourly rate
            remaining_hours = (self.budget_allocation.daily_limit - daily_usage) / hourly_usage
            time_to_limit = remaining_hours * 3600  # Convert to seconds
        
        return BudgetMetrics(
            total_allocated=self.budget_allocation.daily_limit,
            total_used=daily_usage,
            total_remaining=total_remaining,
            usage_percentage=usage_percentage,
            cost_estimate=cost_estimate,
            daily_usage=daily_usage,
            hourly_usage=hourly_usage,
            projected_daily_usage=projected_daily_usage,
            time_to_limit=time_to_limit,
            status=status
        )
    
    def get_optimization_suggestions(self) -> List[TokenOptimizationSuggestion]:
        """Get optimization suggestions based on usage patterns."""
        suggestions = []
        current_metrics = self.get_current_metrics()
        
        # Analyze usage patterns
        if len(self.usage_history) >= 10:
            # Calculate average tokens per chunk
            total_tokens = sum(usage.tokens_used for usage in self.usage_history[-10:])
            total_chunks = sum(usage.chunks_processed for usage in self.usage_history[-10:])
            avg_tokens_per_chunk = total_tokens / max(total_chunks, 1)
            
            # Suggestion 1: Chunk size optimization
            if avg_tokens_per_chunk > 300:
                suggestions.append(TokenOptimizationSuggestion(
                    suggestion_type="chunk_size_optimization",
                    description="Consider reducing chunk size to decrease tokens per classification",
                    potential_savings=int(total_tokens * 0.2),
                    confidence=0.8,
                    implementation_effort="low"
                ))
            
            # Suggestion 2: Relevance filtering
            if current_metrics.usage_percentage > 75:
                suggestions.append(TokenOptimizationSuggestion(
                    suggestion_type="relevance_filtering",
                    description="Enable or tighten relevance filtering to reduce irrelevant chunks",
                    potential_savings=int(total_tokens * 0.4),
                    confidence=0.9,
                    implementation_effort="medium"
                ))
            
            # Suggestion 3: Semantic summarization
            if current_metrics.usage_percentage > 60:
                suggestions.append(TokenOptimizationSuggestion(
                    suggestion_type="semantic_summarization",
                    description="Enable semantic summarization to combine similar chunks",
                    potential_savings=int(total_tokens * 0.3),
                    confidence=0.7,
                    implementation_effort="medium"
                ))
        
        # Suggestion 4: Priority optimization
        if current_metrics.status == BudgetStatus.CRITICAL:
            suggestions.append(TokenOptimizationSuggestion(
                suggestion_type="priority_optimization",
                description="Process only high-priority jobs to conserve budget",
                potential_savings=int(current_metrics.total_used * 0.5),
                confidence=0.9,
                implementation_effort="low"
            ))
        
        return suggestions
    
    def get_usage_analytics(self, days: int = 7) -> Dict[str, Any]:
        """Get usage analytics for the specified number of days."""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Filter recent usage
        recent_usage = [
            usage for usage in self.usage_history
            if usage.timestamp >= cutoff_date
        ]
        
        if not recent_usage:
            return {"error": "No usage data available"}
        
        # Calculate analytics
        total_tokens = sum(usage.tokens_used for usage in recent_usage)
        total_cost = sum(usage.cost_estimate for usage in recent_usage)
        total_chunks = sum(usage.chunks_processed for usage in recent_usage)
        
        # Usage by area
        area_usage = {}
        for usage in recent_usage:
            area_usage[usage.area] = area_usage.get(usage.area, 0) + usage.tokens_used
        
        # Usage by operation type
        operation_usage = {}
        for usage in recent_usage:
            operation_usage[usage.operation_type] = operation_usage.get(usage.operation_type, 0) + usage.tokens_used
        
        # Daily usage trend
        daily_trend = {}
        for usage in recent_usage:
            day = usage.timestamp.date().isoformat()
            daily_trend[day] = daily_trend.get(day, 0) + usage.tokens_used
        
        return {
            "period_days": days,
            "total_tokens": total_tokens,
            "total_cost": total_cost,
            "total_chunks": total_chunks,
            "avg_tokens_per_chunk": total_tokens / max(total_chunks, 1),
            "area_usage": area_usage,
            "operation_usage": operation_usage,
            "daily_trend": daily_trend,
            "total_operations": len(recent_usage)
        }
    
    def _check_daily_limit(self, tokens: int) -> Tuple[bool, str, int]:
        """Check daily token limit."""
        today = datetime.utcnow().date().isoformat()
        current_usage = self.daily_usage.get(today, 0)
        available = self.budget_allocation.daily_limit - current_usage
        
        if tokens > available:
            return False, f"Daily limit exceeded. Available: {available}, Requested: {tokens}", 0
        
        return True, "Daily limit OK", available
    
    def _check_hourly_limit(self, tokens: int) -> Tuple[bool, str, int]:
        """Check hourly token limit."""
        current_hour = datetime.utcnow().strftime("%Y-%m-%d-%H")
        current_usage = self.hourly_usage.get(current_hour, 0)
        available = self.budget_allocation.hourly_limit - current_usage
        
        if tokens > available:
            return False, f"Hourly limit exceeded. Available: {available}, Requested: {tokens}", 0
        
        return True, "Hourly limit OK", available
    
    def _check_job_limit(self, job_id: str, tokens: int) -> Tuple[bool, str, int]:
        """Check per-job token limit."""
        current_usage = self.job_usage.get(job_id, 0)
        available = self.budget_allocation.per_job_limit - current_usage
        
        if tokens > available:
            return False, f"Job limit exceeded. Available: {available}, Requested: {tokens}", 0
        
        return True, "Job limit OK", available
    
    def _check_user_limit(self, user_id: str, tokens: int) -> Tuple[bool, str, int]:
        """Check per-user token limit."""
        current_usage = self.user_usage.get(user_id, 0)
        available = self.budget_allocation.per_user_limit - current_usage
        
        if tokens > available:
            return False, f"User limit exceeded. Available: {available}, Requested: {tokens}", 0
        
        return True, "User limit OK", available
    
    def _check_priority_allocation(self, priority: PriorityLevel, tokens: int) -> Tuple[bool, str, int]:
        """Check priority-based allocation."""
        allocated = self.priority_allocations.get(priority, 0)
        
        if tokens > allocated:
            return False, f"Priority allocation exceeded. Available: {allocated}, Requested: {tokens}", 0
        
        return True, "Priority allocation OK", allocated
    
    async def _reserve_tokens(self, tokens: int, job_id: str, user_id: str, area: str, priority: PriorityLevel) -> None:
        """Reserve tokens for a job."""
        # Update usage tracking (pre-allocation)
        today = datetime.utcnow().date().isoformat()
        current_hour = datetime.utcnow().strftime("%Y-%m-%d-%H")
        
        self.daily_usage[today] = self.daily_usage.get(today, 0) + tokens
        self.hourly_usage[current_hour] = self.hourly_usage.get(current_hour, 0) + tokens
        self.user_usage[user_id] = self.user_usage.get(user_id, 0) + tokens
        self.job_usage[job_id] = self.job_usage.get(job_id, 0) + tokens
        
        # Reduce priority allocation
        self.priority_allocations[priority] = max(0, self.priority_allocations[priority] - tokens)
        
        logger.info(f"Reserved {tokens} tokens for job {job_id} with priority {priority.value}")
    
    def _load_usage_data(self) -> None:
        """Load usage data from storage."""
        try:
            usage_file = self.storage_path / "usage_data.json"
            if usage_file.exists():
                with open(usage_file, 'r') as f:
                    data = json.load(f)
                
                # Load usage tracking dictionaries
                self.daily_usage = data.get('daily_usage', {})
                self.hourly_usage = data.get('hourly_usage', {})
                self.user_usage = data.get('user_usage', {})
                self.job_usage = data.get('job_usage', {})
                
                # Load usage history
                history_data = data.get('usage_history', [])
                self.usage_history = []
                for item in history_data:
                    item['timestamp'] = datetime.fromisoformat(item['timestamp'])
                    self.usage_history.append(TokenUsage(**item))
                
                logger.info(f"Loaded {len(self.usage_history)} usage records")
        
        except Exception as e:
            logger.error(f"Error loading usage data: {e}")
    
    async def _save_usage_data(self) -> None:
        """Save usage data to storage."""
        try:
            # Prepare data for serialization
            history_data = []
            for usage in self.usage_history:
                item = {
                    'operation_id': usage.operation_id,
                    'operation_type': usage.operation_type,
                    'tokens_used': usage.tokens_used,
                    'timestamp': usage.timestamp.isoformat(),
                    'cost_estimate': usage.cost_estimate,
                    'processing_time': usage.processing_time,
                    'chunks_processed': usage.chunks_processed,
                    'area': usage.area,
                    'user_id': usage.user_id
                }
                history_data.append(item)
            
            data = {
                'daily_usage': self.daily_usage,
                'hourly_usage': self.hourly_usage,
                'user_usage': self.user_usage,
                'job_usage': self.job_usage,
                'usage_history': history_data
            }
            
            usage_file = self.storage_path / "usage_data.json"
            with open(usage_file, 'w') as f:
                json.dump(data, f, indent=2)
        
        except Exception as e:
            logger.error(f"Error saving usage data: {e}")
    
    def reset_daily_usage(self) -> None:
        """Reset daily usage counters (typically called at midnight)."""
        today = datetime.utcnow().date().isoformat()
        self.daily_usage = {today: 0}
        self.user_usage = {}
        
        # Reset priority allocations
        self.priority_allocations = {
            PriorityLevel.LOW: int(0.3 * self.budget_allocation.daily_limit),
            PriorityLevel.MEDIUM: int(0.4 * self.budget_allocation.daily_limit),
            PriorityLevel.HIGH: int(0.2 * self.budget_allocation.daily_limit),
            PriorityLevel.CRITICAL: int(0.1 * self.budget_allocation.daily_limit)
        }
        
        logger.info("Daily usage counters reset")
    
    def get_budget_config(self) -> Dict[str, Any]:
        """Get current budget configuration."""
        return {
            'daily_limit': self.budget_allocation.daily_limit,
            'hourly_limit': self.budget_allocation.hourly_limit,
            'per_job_limit': self.budget_allocation.per_job_limit,
            'per_user_limit': self.budget_allocation.per_user_limit,
            'reserved_tokens': self.budget_allocation.reserved_tokens,
            'emergency_tokens': self.budget_allocation.emergency_tokens,
            'cost_per_token': self.cost_per_token,
            'enable_cost_tracking': self.enable_cost_tracking
        }
    
    def update_budget_config(self, **kwargs) -> None:
        """Update budget configuration."""
        for key, value in kwargs.items():
            if hasattr(self.budget_allocation, key):
                setattr(self.budget_allocation, key, value)
                logger.info(f"Updated budget config: {key} = {value}")
            elif key in ['cost_per_token', 'enable_cost_tracking']:
                setattr(self, key, value)
                logger.info(f"Updated budget config: {key} = {value}")


# Factory function for easy instantiation
def create_token_budget_manager(
    daily_limit: int = 1000000,
    cost_per_token: float = 0.0001,
    enable_cost_tracking: bool = True
) -> TokenBudgetManager:
    """
    Create a configured token budget manager.
    
    Args:
        daily_limit: Daily token limit
        cost_per_token: Cost per token in USD
        enable_cost_tracking: Whether to track costs
        
    Returns:
        Configured TokenBudgetManager instance
    """
    budget_config = {
        'daily_limit': daily_limit,
        'hourly_limit': daily_limit // 24,
        'per_job_limit': daily_limit // 20,
        'per_user_limit': daily_limit // 5
    }
    
    return TokenBudgetManager(
        budget_config=budget_config,
        cost_per_token=cost_per_token,
        enable_cost_tracking=enable_cost_tracking
    )