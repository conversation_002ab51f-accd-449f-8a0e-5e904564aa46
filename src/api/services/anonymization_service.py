"""
Service for anonymization operations using existing tools.
"""

import logging
from typing import Dict, Any, Optional, List

# Import existing anonymization tools
from ...tools.anonymization_tool import AnonymizationTool
from ...tools.pdf_anonymization_tool import PDFAnonymizationTool

logger = logging.getLogger(__name__)


class AnonymizationService:
    """Service for handling anonymization operations."""
    
    def __init__(self):
        """Initialize anonymization tools."""
        self.text_anonymizer = AnonymizationTool()
        self.pdf_anonymizer = PDFAnonymizationTool()
    
    async def anonymize_text(
        self,
        text: str,
        language: str = "pt",
        entities_to_anonymize: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Anonymize text using Brazilian-specific recognizers.

        Args:
            text: Text to anonymize
            language: Language code
            entities_to_anonymize: Specific entity types to anonymize

        Returns:
            Anonymization result dictionary
        """
        try:
            # Use existing anonymization tool
            result = self.text_anonymizer.anonymize_text(text, language)

            # Filter entities if specific types were requested
            if entities_to_anonymize and result.get("entities_found"):
                filtered_entities = [
                    entity for entity in result["entities_found"]
                    if entity["entity_type"] in entities_to_anonymize
                ]
                result["entities_found"] = filtered_entities
                result["entities_count"] = len(filtered_entities)

            # Add anonymized_text field to each entity for consistency
            for entity in result.get("entities_found", []):
                if "anonymized_text" not in entity:
                    entity["anonymized_text"] = f"<{entity['entity_type']}>"

            return result

        except Exception as e:
            logger.error(f"Error in text anonymization: {e}")
            return {
                "anonymized_text": text,
                "entities_found": [],
                "entities_count": 0,
                "success": False,
                "error": str(e)
            }
    
    async def anonymize_pdf(
        self,
        file_path: str,
        language: str = "pt",
        entities_to_anonymize: Optional[List[str]] = None,
        extract_text_only: bool = False
    ) -> Dict[str, Any]:
        """
        Anonymize PDF and extract text.

        Args:
            file_path: Path to PDF file
            language: Language code
            entities_to_anonymize: Specific entity types to anonymize
            extract_text_only: Only extract text without anonymization

        Returns:
            PDF anonymization result dictionary
        """
        try:
            if extract_text_only:
                # Only extract text without anonymization
                extraction_result = self.pdf_anonymizer.extract_clean_text(file_path)
                return {
                    "anonymized_text": extraction_result.get("text", ""),
                    "original_text": extraction_result.get("text", ""),
                    "entities_found": [],
                    "entities_count": 0,
                    "page_count": extraction_result.get("page_count"),
                    "success": extraction_result.get("success", True),
                    "error": extraction_result.get("error")
                }
            else:
                # Full anonymization
                result = self.pdf_anonymizer.anonymize_pdf(file_path, language)

                # Filter entities if specific types were requested
                if entities_to_anonymize and result.get("entities_found"):
                    filtered_entities = [
                        entity for entity in result["entities_found"]
                        if entity["entity_type"] in entities_to_anonymize
                    ]
                    result["entities_found"] = filtered_entities
                    result["entities_count"] = len(filtered_entities)

                # Add anonymized_text field to each entity for consistency
                for entity in result.get("entities_found", []):
                    if "anonymized_text" not in entity:
                        entity["anonymized_text"] = f"<{entity['entity_type']}>"

                return result

        except Exception as e:
            logger.error(f"Error in PDF anonymization: {e}")
            return {
                "anonymized_text": "",
                "original_text": "",
                "entities_found": [],
                "entities_count": 0,
                "success": False,
                "error": str(e)
            }
    
    def get_supported_entities(self) -> List[str]:
        """
        Get list of supported entity types.

        Returns:
            List of supported entity types
        """
        return [
            "PERSON",
            "CPF",
            "EMAIL",
            "PHONE",
            "ENDERECO",
            "ESCOLA",
            "CREDIT_CARD",
            "IBAN_CODE",
            "IP_ADDRESS",
            "DATE_TIME"
        ]
