"""
IntelligentRouter for LLM model selection and routing.

This module implements intelligent routing between fast/cheap and accurate/expensive
models based on content analysis and confidence scoring.
"""

import asyncio
import logging
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum

import numpy as np

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

from .semantic_cache import SemanticCache
from .chunk_classifier import ChunkClassifier
from ..models.batch_processing import TextChunk, ChunkResult
from ...utils.config_utils import carregar_configuracao

logger = logging.getLogger(__name__)


class ModelTier(Enum):
    """Model tier classification."""
    FAST = "fast"        # Small, fast, cheap model
    ACCURATE = "accurate"  # Large, accurate, expensive model


@dataclass
class RouterDecision:
    """Router decision result."""
    model_tier: ModelTier
    confidence: float
    reasoning: str
    estimated_cost: float
    estimated_time: float
    cache_hit: bool = False


@dataclass
class RoutingMetrics:
    """Routing performance metrics."""
    total_requests: int = 0
    fast_model_usage: int = 0
    accurate_model_usage: int = 0
    cache_hits: int = 0
    avg_confidence: float = 0.0
    cost_savings_ratio: float = 0.0
    processing_time: float = 0.0


class IntelligentRouter:
    """
    Intelligent router for LLM model selection.
    
    Uses multiple signals to decide between fast/cheap and accurate/expensive models:
    - Content complexity analysis
    - Semantic similarity to training data
    - Confidence scoring from quick assessments
    - Historical performance patterns
    """
    
    def __init__(self,
                 fast_model_config: Optional[Dict[str, Any]] = None,
                 accurate_model_config: Optional[Dict[str, Any]] = None,
                 confidence_threshold: float = 0.8,
                 complexity_threshold: float = 0.6,
                 semantic_cache: Optional[SemanticCache] = None,
                 enable_cache: bool = True):
        """
        Initialize intelligent router.
        
        Args:
            fast_model_config: Configuration for fast model
            accurate_model_config: Configuration for accurate model
            confidence_threshold: Threshold for using fast model result
            complexity_threshold: Threshold for routing to accurate model
            semantic_cache: Semantic cache instance
            enable_cache: Whether to use semantic caching
        """
        self.confidence_threshold = confidence_threshold
        self.complexity_threshold = complexity_threshold
        self.enable_cache = enable_cache
        
        # Model configurations
        self.fast_model_config = fast_model_config or {
            "model_name": "gpt-3.5-turbo",
            "cost_per_token": 0.0000005,
            "avg_response_time": 0.5
        }
        self.accurate_model_config = accurate_model_config or {
            "model_name": "gpt-4",
            "cost_per_token": 0.00003,
            "avg_response_time": 2.0
        }
        
        # Initialize components
        self.semantic_cache = semantic_cache or SemanticCache() if enable_cache else None
        self.embedding_model = None
        self.config = carregar_configuracao()
        
        # Metrics
        self.metrics = RoutingMetrics()
        
        # Initialize embedding model for complexity analysis
        self._initialize_embedding_model()
        
        # Create model instances
        self.fast_classifier = ChunkClassifier()
        self.accurate_classifier = ChunkClassifier()  # Could be different config
        
        logger.info("Initialized IntelligentRouter with fast/accurate model cascade")
    
    def _initialize_embedding_model(self) -> None:
        """Initialize embedding model for complexity analysis."""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.warning("sentence-transformers not available, complexity analysis disabled")
            return
        
        try:
            self.embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
            logger.info("Loaded embedding model for router complexity analysis")
        except Exception as e:
            logger.error(f"Failed to initialize router embedding model: {e}")
            self.embedding_model = None
    
    def _analyze_content_complexity(self, text: str) -> float:
        """
        Analyze content complexity to help routing decisions.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Complexity score (0.0 = simple, 1.0 = complex)
        """
        complexity_score = 0.0
        
        # Length-based complexity
        text_length = len(text)
        length_score = min(text_length / 2000, 1.0)  # Normalize to 0-1
        complexity_score += length_score * 0.3
        
        # Vocabulary complexity (unique words ratio)
        words = text.lower().split()
        if words:
            unique_ratio = len(set(words)) / len(words)
            complexity_score += unique_ratio * 0.2
        
        # Sentence complexity (average sentence length)
        sentences = text.split('.')
        if sentences:
            avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences)
            sentence_complexity = min(avg_sentence_length / 20, 1.0)
            complexity_score += sentence_complexity * 0.2
        
        # Technical terms detection
        technical_indicators = [
            'regulamentação', 'legislação', 'procedimento', 'protocolo',
            'diretrizes', 'normas', 'especificação', 'critério',
            'análise', 'avaliação', 'diagnóstico', 'investigação'
        ]
        
        technical_count = sum(1 for term in technical_indicators if term in text.lower())
        technical_score = min(technical_count / len(technical_indicators), 1.0)
        complexity_score += technical_score * 0.3
        
        return min(complexity_score, 1.0)
    
    def _estimate_token_count(self, text: str) -> int:
        """Estimate token count for cost calculation."""
        # Simple estimation: ~4 characters per token for Portuguese
        return max(1, len(text) // 4)
    
    def _calculate_cost_estimate(self, text: str, model_tier: ModelTier) -> float:
        """Calculate estimated cost for processing text with given model."""
        token_count = self._estimate_token_count(text)
        
        if model_tier == ModelTier.FAST:
            cost_per_token = self.fast_model_config["cost_per_token"]
        else:
            cost_per_token = self.accurate_model_config["cost_per_token"]
        
        return token_count * cost_per_token
    
    async def route_chunk(self, chunk: TextChunk, area: str) -> RouterDecision:
        """
        Route chunk to appropriate model tier.
        
        Args:
            chunk: Text chunk to route
            area: Classification area
            
        Returns:
            Routing decision with model tier and reasoning
        """
        start_time = time.time()
        self.metrics.total_requests += 1
        
        # Check semantic cache first
        if self.semantic_cache:
            cached_result = await self.semantic_cache.get_similar(chunk.text, area)
            if cached_result:
                self.metrics.cache_hits += 1
                return RouterDecision(
                    model_tier=ModelTier.FAST,  # Cache hit is always "fast"
                    confidence=1.0,
                    reasoning="Cache hit - using cached result",
                    estimated_cost=0.0,
                    estimated_time=0.0,
                    cache_hit=True
                )
        
        # Analyze content complexity
        complexity_score = self._analyze_content_complexity(chunk.text)
        
        # If content is highly complex, route to accurate model
        if complexity_score >= self.complexity_threshold:
            decision = RouterDecision(
                model_tier=ModelTier.ACCURATE,
                confidence=complexity_score,
                reasoning=f"High complexity ({complexity_score:.3f}) - routing to accurate model",
                estimated_cost=self._calculate_cost_estimate(chunk.text, ModelTier.ACCURATE),
                estimated_time=self.accurate_model_config["avg_response_time"]
            )
            self.metrics.accurate_model_usage += 1
            return decision
        
        # Try fast model first for simple content
        try:
            # Quick assessment with fast model
            quick_result = await self._quick_assessment(chunk, area)
            
            if quick_result and quick_result.confidence >= self.confidence_threshold:
                # High confidence from fast model - use its result
                decision = RouterDecision(
                    model_tier=ModelTier.FAST,
                    confidence=quick_result.confidence,
                    reasoning=f"High confidence from fast model ({quick_result.confidence:.3f})",
                    estimated_cost=self._calculate_cost_estimate(chunk.text, ModelTier.FAST),
                    estimated_time=self.fast_model_config["avg_response_time"]
                )
                self.metrics.fast_model_usage += 1
                
                # Cache the result
                if self.semantic_cache:
                    await self.semantic_cache.store(
                        chunk.text, quick_result, area, quick_result.confidence
                    )
                
                return decision
            else:
                # Low confidence from fast model - escalate to accurate model
                decision = RouterDecision(
                    model_tier=ModelTier.ACCURATE,
                    confidence=quick_result.confidence if quick_result else 0.0,
                    reasoning=f"Low confidence from fast model ({quick_result.confidence if quick_result else 0.0:.3f}) - escalating",
                    estimated_cost=self._calculate_cost_estimate(chunk.text, ModelTier.ACCURATE),
                    estimated_time=self.accurate_model_config["avg_response_time"]
                )
                self.metrics.accurate_model_usage += 1
                return decision
                
        except Exception as e:
            logger.error(f"Error in fast model assessment: {e}")
            # Fallback to accurate model on error
            decision = RouterDecision(
                model_tier=ModelTier.ACCURATE,
                confidence=0.0,
                reasoning=f"Fast model error - fallback to accurate model: {str(e)}",
                estimated_cost=self._calculate_cost_estimate(chunk.text, ModelTier.ACCURATE),
                estimated_time=self.accurate_model_config["avg_response_time"]
            )
            self.metrics.accurate_model_usage += 1
            return decision
        
        finally:
            self.metrics.processing_time += time.time() - start_time
    
    async def _quick_assessment(self, chunk: TextChunk, area: str) -> Optional[ChunkResult]:
        """
        Perform quick assessment with fast model.
        
        Args:
            chunk: Text chunk to assess
            area: Classification area
            
        Returns:
            Quick assessment result with confidence score
        """
        try:
            # Use fast classifier for quick assessment
            results = await self.fast_classifier.classify_chunks([chunk], area)
            if results:
                return results[0]
        except Exception as e:
            logger.error(f"Error in quick assessment: {e}")
        
        return None
    
    async def process_chunk_with_routing(self, chunk: TextChunk, area: str) -> ChunkResult:
        """
        Process chunk with intelligent routing.
        
        Args:
            chunk: Text chunk to process
            area: Classification area
            
        Returns:
            Final classification result
        """
        # Get routing decision
        decision = await self.route_chunk(chunk, area)
        
        # If cache hit, return cached result
        if decision.cache_hit and self.semantic_cache:
            cached_result = await self.semantic_cache.get_similar(chunk.text, area)
            if cached_result:
                return cached_result
        
        # Process with selected model tier
        if decision.model_tier == ModelTier.FAST:
            # Use fast model (might already have result from quick assessment)
            quick_result = await self._quick_assessment(chunk, area)
            if quick_result:
                # Cache the result
                if self.semantic_cache:
                    await self.semantic_cache.store(
                        chunk.text, quick_result, area, quick_result.confidence
                    )
                return quick_result
        
        # Use accurate model
        results = await self.accurate_classifier.classify_chunks([chunk], area)
        result = results[0] if results else ChunkResult(
            chunk_id=chunk.chunk_id,
            subtemas=[],
            confidence=0.0,
            error="No classification result"
        )
        
        # Cache the result
        if self.semantic_cache and result.confidence > 0.5:
            await self.semantic_cache.store(
                chunk.text, result, area, result.confidence
            )
        
        return result
    
    def get_routing_metrics(self) -> RoutingMetrics:
        """Get routing performance metrics."""
        if self.metrics.total_requests > 0:
            self.metrics.avg_confidence = (
                self.metrics.fast_model_usage + self.metrics.accurate_model_usage
            ) / self.metrics.total_requests
            
            # Calculate cost savings ratio
            fast_cost = self.fast_model_config["cost_per_token"]
            accurate_cost = self.accurate_model_config["cost_per_token"]
            
            if self.metrics.total_requests > 0:
                fast_ratio = self.metrics.fast_model_usage / self.metrics.total_requests
                savings = fast_ratio * (1 - fast_cost / accurate_cost)
                self.metrics.cost_savings_ratio = savings * 100
        
        return self.metrics
    
    def update_thresholds(self, 
                         confidence_threshold: Optional[float] = None,
                         complexity_threshold: Optional[float] = None) -> None:
        """Update routing thresholds."""
        if confidence_threshold is not None:
            self.confidence_threshold = confidence_threshold
            logger.info(f"Updated confidence threshold to {confidence_threshold}")
        
        if complexity_threshold is not None:
            self.complexity_threshold = complexity_threshold
            logger.info(f"Updated complexity threshold to {complexity_threshold}")
    
    def get_router_config(self) -> Dict[str, Any]:
        """Get current router configuration."""
        return {
            "confidence_threshold": self.confidence_threshold,
            "complexity_threshold": self.complexity_threshold,
            "fast_model": self.fast_model_config,
            "accurate_model": self.accurate_model_config,
            "cache_enabled": self.enable_cache,
            "embedding_model_available": self.embedding_model is not None,
            "metrics": self.get_routing_metrics()
        }
    
    def reset_metrics(self) -> None:
        """Reset routing metrics."""
        self.metrics = RoutingMetrics()
        logger.info("Reset routing metrics")


# Factory function for easy instantiation
def create_intelligent_router(
    confidence_threshold: float = 0.8,
    complexity_threshold: float = 0.6,
    enable_cache: bool = True
) -> IntelligentRouter:
    """
    Create a configured intelligent router.
    
    Args:
        confidence_threshold: Threshold for using fast model result
        complexity_threshold: Threshold for routing to accurate model
        enable_cache: Whether to enable semantic caching
        
    Returns:
        Configured IntelligentRouter instance
    """
    semantic_cache = SemanticCache() if enable_cache else None
    
    return IntelligentRouter(
        confidence_threshold=confidence_threshold,
        complexity_threshold=complexity_threshold,
        semantic_cache=semantic_cache,
        enable_cache=enable_cache
    )