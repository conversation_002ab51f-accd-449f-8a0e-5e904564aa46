"""
SemanticCache for LLM result caching and reuse.

This module provides intelligent caching based on semantic similarity,
allowing reuse of previous classification results for similar content.
"""

import hashlib
import json
import logging
import pickle
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta

import numpy as np

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Entry in the semantic cache."""
    key: str
    content_hash: str
    embedding: np.ndarray
    result: Any
    timestamp: datetime
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    area: str = ""
    confidence: float = 0.0
    
    def __post_init__(self):
        if self.last_accessed is None:
            self.last_accessed = self.timestamp


@dataclass
class CacheStats:
    """Cache performance statistics."""
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    entries_count: int = 0
    cache_size_mb: float = 0.0
    hit_rate: float = 0.0
    avg_similarity_threshold: float = 0.0


class SemanticCache:
    """
    Intelligent semantic cache for LLM results.
    
    Features:
    - Semantic similarity matching using embeddings
    - Configurable similarity thresholds
    - LRU-style eviction with access tracking
    - Persistence to disk
    - Performance metrics tracking
    """
    
    def __init__(self,
                 cache_dir: str = "data/cache/semantic",
                 embedding_model_name: str = "all-MiniLM-L6-v2",
                 similarity_threshold: float = 0.85,
                 max_entries: int = 10000,
                 max_age_days: int = 30,
                 persist_to_disk: bool = True):
        """
        Initialize semantic cache.
        
        Args:
            cache_dir: Directory for cache persistence
            embedding_model_name: Sentence transformer model name
            similarity_threshold: Minimum similarity for cache hit
            max_entries: Maximum cache entries
            max_age_days: Maximum age for cache entries
            persist_to_disk: Whether to persist cache to disk
        """
        self.cache_dir = Path(cache_dir)
        self.embedding_model_name = embedding_model_name
        self.similarity_threshold = similarity_threshold
        self.max_entries = max_entries
        self.max_age_days = max_age_days
        self.persist_to_disk = persist_to_disk
        
        # Cache storage
        self.entries: Dict[str, CacheEntry] = {}
        self.embedding_model = None
        
        # Statistics
        self.stats = CacheStats()
        
        # Initialize
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self._initialize_embedding_model()
        
        if self.persist_to_disk:
            self._load_cache()
        
        logger.info(f"Initialized SemanticCache with {len(self.entries)} entries")
    
    def _initialize_embedding_model(self) -> None:
        """Initialize sentence transformer model."""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.warning("sentence-transformers not available, semantic cache disabled")
            return
        
        try:
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            logger.info(f"Loaded embedding model: {self.embedding_model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
            self.embedding_model = None
    
    def _compute_embedding(self, text: str) -> Optional[np.ndarray]:
        """Compute embedding for text."""
        if not self.embedding_model:
            return None
        
        try:
            return self.embedding_model.encode(text)
        except Exception as e:
            logger.error(f"Error computing embedding: {e}")
            return None
    
    def _compute_similarity(self, emb1: np.ndarray, emb2: np.ndarray) -> float:
        """Compute cosine similarity between embeddings."""
        try:
            similarity = np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))
            return float(similarity)
        except Exception as e:
            logger.error(f"Error computing similarity: {e}")
            return 0.0
    
    def _generate_content_hash(self, content: str, area: str = "") -> str:
        """Generate hash for content."""
        combined = f"{content}_{area}"
        return hashlib.md5(combined.encode()).hexdigest()
    
    def _cleanup_expired_entries(self) -> None:
        """Remove expired cache entries."""
        cutoff_date = datetime.now() - timedelta(days=self.max_age_days)
        expired_keys = [
            key for key, entry in self.entries.items()
            if entry.timestamp < cutoff_date
        ]
        
        for key in expired_keys:
            del self.entries[key]
        
        if expired_keys:
            logger.info(f"Removed {len(expired_keys)} expired cache entries")
    
    def _evict_least_used_entries(self) -> None:
        """Evict least recently used entries if cache is full."""
        if len(self.entries) <= self.max_entries:
            return
        
        # Sort by access count and last accessed time
        sorted_entries = sorted(
            self.entries.items(),
            key=lambda x: (x[1].access_count, x[1].last_accessed)
        )
        
        # Remove oldest entries
        entries_to_remove = len(self.entries) - self.max_entries
        for i in range(entries_to_remove):
            key = sorted_entries[i][0]
            del self.entries[key]
        
        logger.info(f"Evicted {entries_to_remove} least used cache entries")
    
    async def get_similar(self, content: str, area: str = "") -> Optional[Any]:
        """
        Get cached result for similar content.
        
        Args:
            content: Text content to search for
            area: Classification area
            
        Returns:
            Cached result if similar content found, None otherwise
        """
        self.stats.total_requests += 1
        
        if not self.embedding_model:
            self.stats.cache_misses += 1
            return None
        
        # Compute embedding for query
        query_embedding = self._compute_embedding(content)
        if query_embedding is None:
            self.stats.cache_misses += 1
            return None
        
        # Find most similar entry
        best_similarity = 0.0
        best_entry = None
        
        for entry in self.entries.values():
            if area and entry.area != area:
                continue  # Skip entries from different areas
            
            similarity = self._compute_similarity(query_embedding, entry.embedding)
            if similarity > best_similarity:
                best_similarity = similarity
                best_entry = entry
        
        # Check if similarity meets threshold
        if best_similarity >= self.similarity_threshold and best_entry:
            # Update access statistics
            best_entry.access_count += 1
            best_entry.last_accessed = datetime.now()
            
            self.stats.cache_hits += 1
            
            logger.debug(f"Cache hit with similarity {best_similarity:.3f} for area {area}")
            return best_entry.result
        
        self.stats.cache_misses += 1
        return None
    
    async def store(self, content: str, result: Any, area: str = "", confidence: float = 0.0) -> None:
        """
        Store result in semantic cache.
        
        Args:
            content: Text content
            result: Classification result to cache
            area: Classification area
            confidence: Confidence score of the result
        """
        if not self.embedding_model:
            return
        
        # Compute embedding
        embedding = self._compute_embedding(content)
        if embedding is None:
            return
        
        # Generate cache key
        content_hash = self._generate_content_hash(content, area)
        cache_key = f"{area}_{content_hash}"
        
        # Create cache entry
        entry = CacheEntry(
            key=cache_key,
            content_hash=content_hash,
            embedding=embedding,
            result=result,
            timestamp=datetime.now(),
            area=area,
            confidence=confidence
        )
        
        # Store entry
        self.entries[cache_key] = entry
        
        # Cleanup if needed
        self._cleanup_expired_entries()
        self._evict_least_used_entries()
        
        logger.debug(f"Stored cache entry for area {area} with confidence {confidence:.3f}")
        
        # Persist to disk if enabled
        if self.persist_to_disk:
            await self._save_cache_async()
    
    def get_stats(self) -> CacheStats:
        """Get cache performance statistics."""
        self.stats.entries_count = len(self.entries)
        self.stats.hit_rate = (
            self.stats.cache_hits / max(self.stats.total_requests, 1) * 100
        )
        
        # Estimate cache size
        try:
            cache_size_bytes = sum(
                entry.embedding.nbytes + len(str(entry.result))
                for entry in self.entries.values()
            )
            self.stats.cache_size_mb = cache_size_bytes / (1024 * 1024)
        except Exception:
            self.stats.cache_size_mb = 0.0
        
        return self.stats
    
    def clear_cache(self) -> None:
        """Clear all cache entries."""
        self.entries.clear()
        self.stats = CacheStats()
        
        if self.persist_to_disk:
            cache_file = self.cache_dir / "semantic_cache.pkl"
            if cache_file.exists():
                cache_file.unlink()
        
        logger.info("Cleared semantic cache")
    
    def _load_cache(self) -> None:
        """Load cache from disk."""
        cache_file = self.cache_dir / "semantic_cache.pkl"
        
        if not cache_file.exists():
            return
        
        try:
            with open(cache_file, 'rb') as f:
                self.entries = pickle.load(f)
            
            # Cleanup expired entries after loading
            self._cleanup_expired_entries()
            
            logger.info(f"Loaded {len(self.entries)} cache entries from disk")
        except Exception as e:
            logger.error(f"Error loading cache from disk: {e}")
            self.entries = {}
    
    def _save_cache(self) -> None:
        """Save cache to disk."""
        if not self.persist_to_disk:
            return
        
        cache_file = self.cache_dir / "semantic_cache.pkl"
        
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(self.entries, f)
            
            logger.debug(f"Saved {len(self.entries)} cache entries to disk")
        except Exception as e:
            logger.error(f"Error saving cache to disk: {e}")
    
    async def _save_cache_async(self) -> None:
        """Save cache to disk asynchronously."""
        try:
            # Run in thread pool to avoid blocking
            import asyncio
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._save_cache)
        except Exception as e:
            logger.error(f"Error saving cache asynchronously: {e}")
    
    def update_similarity_threshold(self, threshold: float) -> None:
        """Update similarity threshold."""
        self.similarity_threshold = threshold
        logger.info(f"Updated similarity threshold to {threshold}")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get detailed cache information."""
        return {
            "total_entries": len(self.entries),
            "similarity_threshold": self.similarity_threshold,
            "max_entries": self.max_entries,
            "max_age_days": self.max_age_days,
            "embedding_model": self.embedding_model_name,
            "model_available": self.embedding_model is not None,
            "cache_dir": str(self.cache_dir),
            "persist_to_disk": self.persist_to_disk,
            "stats": self.get_stats()
        }
    
    def __del__(self):
        """Cleanup when cache is destroyed."""
        if hasattr(self, 'persist_to_disk') and self.persist_to_disk:
            self._save_cache()


# Factory function for easy instantiation
def create_semantic_cache(
    similarity_threshold: float = 0.85,
    max_entries: int = 10000
) -> SemanticCache:
    """
    Create a configured semantic cache.
    
    Args:
        similarity_threshold: Minimum similarity for cache hit
        max_entries: Maximum cache entries
        
    Returns:
        Configured SemanticCache instance
    """
    return SemanticCache(
        similarity_threshold=similarity_threshold,
        max_entries=max_entries
    )