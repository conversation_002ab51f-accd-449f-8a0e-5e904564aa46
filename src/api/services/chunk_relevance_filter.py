"""
ChunkRelevanceFilter for token optimization in PDF batch processing.

This module provides intelligent chunk filtering using embeddings to reduce
the number of tokens sent to LLM classification while maintaining accuracy.
"""

import logging
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from collections import defaultdict
import json
from pathlib import Path
import hashlib
import asyncio
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

from ..models.batch_processing import TextChunk, ChunkResult
from ...utils.config_utils import carregar_configuracao

logger = logging.getLogger(__name__)


@dataclass
class RelevanceScore:
    """Relevance score for a text chunk."""
    chunk_id: int
    score: float
    reasoning: str
    area_match: bool
    keywords_found: List[str]
    

@dataclass
class FilteringMetrics:
    """Metrics for chunk filtering performance."""
    total_chunks: int
    filtered_chunks: int
    reduction_percentage: float
    avg_relevance_score: float
    processing_time: float
    tokens_saved: int
    

class ChunkRelevanceFilter:
    """
    Intelligent chunk filtering using embeddings and keyword matching.
    
    This filter reduces the number of chunks sent to LLM classification by:
    1. Using embeddings to identify semantically relevant chunks
    2. Keyword matching based on area definitions
    3. Combining multiple signals for relevance scoring
    4. Caching embeddings for performance
    """
    
    def __init__(self, 
                 embedding_model_name: str = "all-MiniLM-L6-v2",
                 relevance_threshold: float = 0.3,
                 max_chunks_per_pdf: int = 50,
                 cache_dir: str = "data/cache/embeddings",
                 use_keywords: bool = True,
                 keyword_weight: float = 0.3,
                 embedding_weight: float = 0.7):
        """
        Initialize the chunk relevance filter.
        
        Args:
            embedding_model_name: Name of the sentence transformer model
            relevance_threshold: Minimum relevance score for chunk inclusion
            max_chunks_per_pdf: Maximum number of chunks to keep per PDF
            cache_dir: Directory for embedding cache
            use_keywords: Whether to use keyword matching
            keyword_weight: Weight for keyword matching score
            embedding_weight: Weight for embedding similarity score
        """
        self.embedding_model_name = embedding_model_name
        self.relevance_threshold = relevance_threshold
        self.max_chunks_per_pdf = max_chunks_per_pdf
        self.cache_dir = Path(cache_dir)
        self.use_keywords = use_keywords
        self.keyword_weight = keyword_weight
        self.embedding_weight = embedding_weight
        
        # Initialize components
        self.embedding_model = None
        self.area_embeddings = {}
        self.area_keywords = {}
        self.config = carregar_configuracao()
        
        # Create cache directory
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Thread pool for embedding computation
        self.thread_pool = ThreadPoolExecutor(max_workers=2)
        
        # Load area definitions
        self._load_area_definitions()
        
        # Initialize embedding model
        self._initialize_embedding_model()
        
        logger.info(f"Initialized ChunkRelevanceFilter with threshold={relevance_threshold}")
    
    def _initialize_embedding_model(self) -> None:
        """Initialize the sentence transformer model."""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.warning("sentence-transformers not available, using keyword-only filtering")
            return
        
        try:
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            logger.info(f"Loaded embedding model: {self.embedding_model_name}")
            
            # Pre-compute area embeddings
            self._compute_area_embeddings()
            
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
            self.embedding_model = None
    
    def _load_area_definitions(self) -> None:
        """Load area definitions and keywords from configuration."""
        try:
            # Load subtemas from current config structure
            area_mappings = {
                'EDUCACAO': 'SUBTEMAS_EDUCACAO',
                'SAUDE': 'SUBTEMAS_SAUDE', 
                'MEIO_AMBIENTE': 'SUBTEMAS_MEIO_AMBIENTE'
            }
            
            for area, config_key in area_mappings.items():
                keywords = set()
                
                # Get subtemas list from config
                subtemas_list = self.config.get(config_key, [])
                
                # Extract keywords from subtemas
                for subtema in subtemas_list:
                    if isinstance(subtema, str):
                        # Add subtema name as keywords
                        clean_subtema = subtema.lower().replace('_', ' ')
                        keywords.add(clean_subtema)
                        
                        # Add individual words from subtema
                        words = clean_subtema.split()
                        keywords.update(words)
                        
                        # Add stemmed versions and common variations
                        if 'educacao' in clean_subtema or 'educacional' in clean_subtema:
                            keywords.update(['escola', 'ensino', 'aluno', 'professor', 'pedagogico'])
                        elif 'saude' in clean_subtema or 'medico' in clean_subtema:
                            keywords.update(['hospital', 'medico', 'paciente', 'tratamento', 'clinica'])
                        elif 'ambiente' in clean_subtema or 'ambiental' in clean_subtema:
                            keywords.update(['poluicao', 'lixo', 'agua', 'ar', 'sustentavel'])
                
                # Add area-specific base keywords
                if area == 'EDUCACAO':
                    keywords.update(['educacao', 'escola', 'ensino', 'aluno', 'professor', 'pedagogico', 'escolar'])
                elif area == 'SAUDE':
                    keywords.update(['saude', 'hospital', 'medico', 'paciente', 'tratamento', 'clinica', 'sus'])
                elif area == 'MEIO_AMBIENTE':
                    keywords.update(['ambiente', 'ambiental', 'poluicao', 'lixo', 'agua', 'ar', 'sustentavel'])
                
                self.area_keywords[area] = list(keywords)
                logger.debug(f"Loaded {len(keywords)} keywords for area {area}")
                
        except Exception as e:
            logger.error(f"Error loading area definitions: {e}")
            # Fallback keywords
            self.area_keywords = {
                'EDUCACAO': ['educacao', 'escola', 'ensino', 'aluno', 'professor', 'pedagogico'],
                'SAUDE': ['saude', 'hospital', 'medico', 'paciente', 'tratamento', 'clinica'],
                'MEIO_AMBIENTE': ['ambiente', 'poluicao', 'lixo', 'agua', 'ar', 'sustentavel']
            }
    
    def _compute_area_embeddings(self) -> None:
        """Pre-compute embeddings for each area."""
        if not self.embedding_model:
            return
        
        try:
            for area, keywords in self.area_keywords.items():
                # Create area description from keywords
                area_text = f"Documentos sobre {area.lower()}: " + " ".join(keywords[:20])
                
                # Compute embedding
                embedding = self.embedding_model.encode(area_text)
                self.area_embeddings[area] = embedding
                
                logger.debug(f"Computed embedding for area {area}")
                
        except Exception as e:
            logger.error(f"Error computing area embeddings: {e}")
    
    async def filter_chunks(self, 
                          chunks: List[TextChunk], 
                          area: str) -> Tuple[List[TextChunk], FilteringMetrics]:
        """
        Filter chunks based on relevance to the specified area.
        
        Args:
            chunks: List of text chunks to filter
            area: Classification area
            
        Returns:
            Tuple of (filtered_chunks, filtering_metrics)
        """
        if not chunks:
            return [], FilteringMetrics(0, 0, 0.0, 0.0, 0.0, 0)
        
        import time
        start_time = time.time()
        
        logger.info(f"Filtering {len(chunks)} chunks for area {area}")
        
        # Compute relevance scores
        relevance_scores = await self._compute_relevance_scores(chunks, area)
        
        # Apply filtering
        filtered_chunks = []
        total_relevance = 0.0
        
        # Sort by relevance score
        scored_chunks = list(zip(chunks, relevance_scores))
        scored_chunks.sort(key=lambda x: x[1].score, reverse=True)
        
        # Apply threshold and max chunks limit
        for chunk, score in scored_chunks:
            if (score.score >= self.relevance_threshold and 
                len(filtered_chunks) < self.max_chunks_per_pdf):
                filtered_chunks.append(chunk)
                total_relevance += score.score
        
        # Calculate metrics
        processing_time = time.time() - start_time
        reduction_percentage = (len(chunks) - len(filtered_chunks)) / len(chunks) * 100
        avg_relevance_score = total_relevance / max(len(filtered_chunks), 1)
        
        # Estimate tokens saved (rough calculation)
        tokens_saved = sum(
            chunk.token_count or self._estimate_tokens(chunk.text) 
            for chunk in chunks[len(filtered_chunks):]
        )
        
        metrics = FilteringMetrics(
            total_chunks=len(chunks),
            filtered_chunks=len(filtered_chunks),
            reduction_percentage=reduction_percentage,
            avg_relevance_score=avg_relevance_score,
            processing_time=processing_time,
            tokens_saved=tokens_saved
        )
        
        logger.info(f"Filtered {len(chunks)} → {len(filtered_chunks)} chunks "
                   f"({reduction_percentage:.1f}% reduction, {tokens_saved} tokens saved)")
        
        return filtered_chunks, metrics
    
    async def _compute_relevance_scores(self, 
                                      chunks: List[TextChunk], 
                                      area: str) -> List[RelevanceScore]:
        """Compute relevance scores for all chunks."""
        tasks = []
        
        for chunk in chunks:
            task = asyncio.create_task(
                self._compute_single_relevance_score(chunk, area)
            )
            tasks.append(task)
        
        # Process in batches to avoid overwhelming the system
        batch_size = 10
        results = []
        
        for i in range(0, len(tasks), batch_size):
            batch = tasks[i:i + batch_size]
            batch_results = await asyncio.gather(*batch)
            results.extend(batch_results)
        
        return results
    
    async def _compute_single_relevance_score(self, 
                                            chunk: TextChunk, 
                                            area: str) -> RelevanceScore:
        """Compute relevance score for a single chunk."""
        try:
            # Initialize components
            embedding_score = 0.0
            keyword_score = 0.0
            keywords_found = []
            
            # Compute keyword score
            if self.use_keywords and area in self.area_keywords:
                keyword_score, keywords_found = self._compute_keyword_score(
                    chunk.text, area
                )
            
            # Compute embedding score
            if self.embedding_model and area in self.area_embeddings:
                embedding_score = await self._compute_embedding_score(
                    chunk.text, area
                )
            
            # Combine scores
            if self.embedding_model and self.use_keywords:
                final_score = (
                    self.embedding_weight * embedding_score + 
                    self.keyword_weight * keyword_score
                )
            elif self.embedding_model:
                final_score = embedding_score
            else:
                final_score = keyword_score
            
            # Determine area match
            area_match = final_score >= self.relevance_threshold
            
            # Create reasoning
            reasoning = self._create_reasoning(
                embedding_score, keyword_score, keywords_found, area_match
            )
            
            return RelevanceScore(
                chunk_id=chunk.chunk_id,
                score=final_score,
                reasoning=reasoning,
                area_match=area_match,
                keywords_found=keywords_found
            )
            
        except Exception as e:
            logger.error(f"Error computing relevance score for chunk {chunk.chunk_id}: {e}")
            return RelevanceScore(
                chunk_id=chunk.chunk_id,
                score=0.0,
                reasoning=f"Error: {str(e)}",
                area_match=False,
                keywords_found=[]
            )
    
    def _compute_keyword_score(self, text: str, area: str) -> Tuple[float, List[str]]:
        """Compute keyword-based relevance score."""
        if area not in self.area_keywords:
            return 0.0, []
        
        text_lower = text.lower()
        keywords = self.area_keywords[area]
        keywords_found = []
        
        # Count keyword matches
        for keyword in keywords:
            if keyword in text_lower:
                keywords_found.append(keyword)
        
        # Score based on keyword density
        if keywords_found:
            score = min(len(keywords_found) / len(keywords), 1.0)
            # Boost score for multiple matches
            score = score * (1 + 0.1 * (len(keywords_found) - 1))
        else:
            score = 0.0
        
        return score, keywords_found
    
    async def _compute_embedding_score(self, text: str, area: str) -> float:
        """Compute embedding-based relevance score."""
        if not self.embedding_model or area not in self.area_embeddings:
            return 0.0
        
        try:
            # Get cached embedding or compute new one
            text_embedding = await self._get_cached_embedding(text)
            area_embedding = self.area_embeddings[area]
            
            # Compute cosine similarity
            similarity = np.dot(text_embedding, area_embedding) / (
                np.linalg.norm(text_embedding) * np.linalg.norm(area_embedding)
            )
            
            # Normalize to 0-1 range
            score = max(0.0, (similarity + 1) / 2)
            
            return score
            
        except Exception as e:
            logger.error(f"Error computing embedding score: {e}")
            return 0.0
    
    async def _get_cached_embedding(self, text: str) -> np.ndarray:
        """Get embedding from cache or compute and cache it."""
        # Create hash for text
        text_hash = hashlib.md5(text.encode()).hexdigest()
        cache_file = self.cache_dir / f"{text_hash}.npy"
        
        # Try to load from cache
        if cache_file.exists():
            try:
                return np.load(cache_file)
            except Exception as e:
                logger.warning(f"Failed to load cached embedding: {e}")
        
        # Compute embedding in thread pool
        loop = asyncio.get_event_loop()
        embedding = await loop.run_in_executor(
            self.thread_pool, 
            self.embedding_model.encode, 
            text
        )
        
        # Cache the embedding
        try:
            np.save(cache_file, embedding)
        except Exception as e:
            logger.warning(f"Failed to cache embedding: {e}")
        
        return embedding
    
    def _create_reasoning(self, 
                         embedding_score: float, 
                         keyword_score: float, 
                         keywords_found: List[str], 
                         area_match: bool) -> str:
        """Create human-readable reasoning for the score."""
        reasoning_parts = []
        
        if self.embedding_model:
            reasoning_parts.append(f"Embedding similarity: {embedding_score:.3f}")
        
        if self.use_keywords:
            reasoning_parts.append(f"Keyword score: {keyword_score:.3f}")
            if keywords_found:
                reasoning_parts.append(f"Keywords found: {', '.join(keywords_found)}")
        
        reasoning_parts.append(f"Area match: {'Yes' if area_match else 'No'}")
        
        return " | ".join(reasoning_parts)
    
    def _estimate_tokens(self, text: str) -> int:
        """Estimate number of tokens in text."""
        # Simple estimation: ~4 characters per token for Portuguese
        return max(1, len(text) // 4)
    
    def get_filtering_config(self) -> Dict[str, Any]:
        """Get current filtering configuration."""
        return {
            'embedding_model_name': self.embedding_model_name,
            'relevance_threshold': self.relevance_threshold,
            'max_chunks_per_pdf': self.max_chunks_per_pdf,
            'use_keywords': self.use_keywords,
            'keyword_weight': self.keyword_weight,
            'embedding_weight': self.embedding_weight,
            'embedding_model_available': self.embedding_model is not None,
            'areas_loaded': list(self.area_keywords.keys())
        }
    
    def update_threshold(self, new_threshold: float) -> None:
        """Update relevance threshold."""
        self.relevance_threshold = new_threshold
        logger.info(f"Updated relevance threshold to {new_threshold}")
    
    def clear_cache(self) -> None:
        """Clear embedding cache."""
        try:
            import shutil
            if self.cache_dir.exists():
                shutil.rmtree(self.cache_dir)
                self.cache_dir.mkdir(parents=True, exist_ok=True)
                logger.info("Cleared embedding cache")
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            if not self.cache_dir.exists():
                return {"cache_size": 0, "cache_files": 0}
            
            cache_files = list(self.cache_dir.glob("*.npy"))
            total_size = sum(f.stat().st_size for f in cache_files)
            
            return {
                "cache_size": total_size,
                "cache_files": len(cache_files),
                "cache_dir": str(self.cache_dir)
            }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {"error": str(e)}
    
    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=True)


# Factory function for easy instantiation
def create_chunk_relevance_filter(
    area: str,
    relevance_threshold: float = 0.3,
    max_chunks_per_pdf: int = 50
) -> ChunkRelevanceFilter:
    """
    Create a configured chunk relevance filter.
    
    Args:
        area: Classification area
        relevance_threshold: Minimum relevance score
        max_chunks_per_pdf: Maximum chunks per PDF
        
    Returns:
        Configured ChunkRelevanceFilter instance
    """
    return ChunkRelevanceFilter(
        relevance_threshold=relevance_threshold,
        max_chunks_per_pdf=max_chunks_per_pdf
    )