"""
Database models for Supabase tables.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, UUID4


class FileRecord(BaseModel):
    """Model for files table."""
    id: Optional[UUID4] = None
    user_id: UUID4
    filename: str
    file_path: str
    file_type: str
    file_size: int
    metadata: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class ProcessingJob(BaseModel):
    """Model for processing_jobs table."""
    id: Optional[UUID4] = None
    user_id: UUID4
    file_id: Optional[UUID4] = None
    job_type: str  # 'anonymization', 'classification', 'report'
    status: str = "pending"  # 'pending', 'processing', 'completed', 'failed'
    input_data: Optional[Dict[str, Any]] = None
    output_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class Report(BaseModel):
    """Model for reports table."""
    id: Optional[UUID4] = None
    user_id: UUID4
    job_id: UUID4
    report_type: str
    content: str
    metadata: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
