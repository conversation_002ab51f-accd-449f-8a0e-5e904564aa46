"""
Supabase client configuration and initialization.
"""

import logging
from typing import Optional
from supabase import Client, create_client
from ..config import settings

logger = logging.getLogger(__name__)


def get_supabase_client() -> Optional[Client]:
    """
    Get Supabase client with anon key for user operations.
    
    Returns:
        Supabase client instance or None if configuration is missing
    """
    if not settings.supabase_url or not settings.supabase_key:
        logger.error("Supabase URL or key not configured")
        return None
    
    try:
        client = create_client(settings.supabase_url, settings.supabase_key)
        return client
    except Exception as e:
        logger.error(f"Failed to create Supabase client: {e}")
        return None


def get_supabase_admin_client() -> Optional[Client]:
    """
    Get Supabase client with service key for admin operations.
    
    Returns:
        Supabase admin client instance or None if configuration is missing
    """
    if not settings.supabase_url or not settings.supabase_service_key:
        logger.error("Supabase URL or service key not configured")
        return None
    
    try:
        client = create_client(settings.supabase_url, settings.supabase_service_key)
        return client
    except Exception as e:
        logger.error(f"Failed to create Supabase admin client: {e}")
        return None


# Global client instances
supabase_client = get_supabase_client()
supabase_admin_client = get_supabase_admin_client()
