"""
OpenAPI documentation configuration for Simple Class API.
"""

# Tags metadata for better organization in Swagger UI
tags_metadata = [
    {
        "name": "🔐 Autenticação",
        "description": """
        **Sistema de autenticação e autorização**
        
        Endpoints para login, gerenciamento de tokens e verificação de status de autenticação.
        
        **Funcionalidades:**
        - Login com email e senha
        - Geração de tokens JWT
        - Verificação de status de autenticação
        - Rate limiting para segurança
        
        **Credenciais de Teste:**
        - Email: `<EMAIL>`
        - Senha: `test123`
        """,
        "externalDocs": {
            "description": "Documentação sobre JWT",
            "url": "https://jwt.io/introduction/"
        }
    },
    {
        "name": "📁 Gerenciamento de Arquivos",
        "description": """
        **Upload e gerenciamento de arquivos**
        
        Endpoints para upload, listagem e gerenciamento de arquivos CSV e PDF.
        
        **Funcionalidades:**
        - Upload de arquivos CSV e PDF
        - Validação de formato e tamanho
        - Metadados e organização
        - Integração com Supabase Storage
        
        **Formatos Suportados:**
        - CSV (text/csv)
        - PDF (application/pdf)
        - Excel (xlsx, xls)
        
        **Limites:**
        - Tamanho máximo: 50MB
        - Rate limit: 100 requests/hora
        """,
        "externalDocs": {
            "description": "Supabase Storage",
            "url": "https://supabase.com/docs/guides/storage"
        }
    },
    {
        "name": "🏷️ Classificação de Texto",
        "description": """
        **Classificação automática de texto usando LLMs**
        
        Endpoints para classificação multilabel e unilabel de textos em diferentes áreas de política pública.
        
        **Tipos de Classificação:**
        - **Multilabel**: Identifica até 3 subtemas por texto
        - **Unilabel**: Classifica TIPO_UNIDADE e CRE
        - **Batch**: Processa múltiplos textos simultaneamente
        
        **Áreas Suportadas:**
        - **EDUCACAO**: Alimentação escolar, infraestrutura, transporte, etc.
        - **SAUDE**: Atenção básica, emergência, medicamentos, etc.
        - **MEIO_AMBIENTE**: Poluição, desmatamento, saneamento, etc.
        
        **Modelos LLM:**
        - Llama 3.3 Instruct (padrão)
        - Llama 4 Scout
        - Claude Sonnet 4
        
        **Performance:**
        - Tempo médio: 2-5 segundos por texto
        - Precisão: >90% para textos bem estruturados
        """,
        "externalDocs": {
            "description": "Together AI Models",
            "url": "https://docs.together.ai/docs/inference-models"
        }
    },
    {
        "name": "🔒 Anonimização",
        "description": """
        **Anonimização de dados pessoais em textos e PDFs**
        
        Endpoints para detecção e anonimização de informações pessoais identificáveis (PII).
        
        **Entidades Detectadas:**
        - **PERSON**: Nomes de pessoas
        - **CPF**: Números de CPF
        - **PHONE**: Números de telefone
        - **EMAIL**: Endereços de email
        - **ENDEREÇO**: Endereços completos
        - **RG**: Números de RG
        - **CNPJ**: Números de CNPJ
        
        **Idiomas Suportados:**
        - Português (pt) - padrão
        - Inglês (en)
        
        **Formatos:**
        - Texto simples
        - Arquivos PDF
        
        **Tecnologia:**
        - Microsoft Presidio para detecção
        - Modelos customizados para português
        """,
        "externalDocs": {
            "description": "Microsoft Presidio",
            "url": "https://microsoft.github.io/presidio/"
        }
    },
    {
        "name": "📊 Relatórios",
        "description": """
        **Geração de relatórios analíticos**
        
        Endpoints para geração de relatórios detalhados sobre dados classificados.
        
        **Tipos de Relatório:**
        - **Individual**: Análise detalhada de um subtema específico
        - **Visão Geral**: Análise abrangente de toda a área
        
        **Características:**
        - Geração com Claude Sonnet 4
        - Análise estatística e qualitativa
        - Insights e recomendações
        - Formato Markdown
        
        **Conteúdo dos Relatórios:**
        - Estatísticas descritivas
        - Análise de tendências
        - Identificação de padrões
        - Recomendações estratégicas
        - Evidências e interpretações
        
        **Performance:**
        - Tempo de geração: 10-30 segundos
        - Qualidade: Análise profissional detalhada
        """,
        "externalDocs": {
            "description": "Claude AI",
            "url": "https://www.anthropic.com/claude"
        }
    }
]

# Custom OpenAPI schema modifications
def customize_openapi_schema(app):
    """Customize the OpenAPI schema with additional information."""
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = app.openapi()
    
    # Add custom info
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }
    
    # Add security schemes
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "Token JWT obtido através do endpoint de login"
        }
    }
    
    # Add global security requirement
    openapi_schema["security"] = [{"BearerAuth": []}]
    
    # Add custom examples for common responses
    openapi_schema["components"]["examples"] = {
        "UnauthorizedError": {
            "summary": "Token inválido",
            "value": {
                "detail": "Could not validate credentials",
                "type": "authentication_error"
            }
        },
        "ValidationError": {
            "summary": "Erro de validação",
            "value": {
                "detail": [
                    {
                        "loc": ["body", "text"],
                        "msg": "field required",
                        "type": "value_error.missing"
                    }
                ]
            }
        },
        "RateLimitError": {
            "summary": "Rate limit excedido",
            "value": {
                "detail": "Rate limit exceeded. Try again later.",
                "type": "rate_limit_error"
            }
        }
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


# Response examples for common scenarios
RESPONSE_EXAMPLES = {
    "success_login": {
        "summary": "Login bem-sucedido",
        "value": {
            "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            "token_type": "bearer",
            "expires_in": 3600,
            "user": {
                "id": "user-123",
                "email": "<EMAIL>",
                "full_name": "Test User",
                "organization": "Test Organization"
            }
        }
    },
    "classification_result": {
        "summary": "Resultado da classificação",
        "value": {
            "text": "Problema na merenda escolar da escola municipal",
            "area": "EDUCACAO",
            "subtemas": ["ALIMENTACAO_ESCOLAR", "INFRAESTRUTURA"],
            "confidence_scores": [0.95, 0.87],
            "model_used": "meta-llama/Llama-3.3-70B-Instruct-Turbo",
            "processing_time": 2.34,
            "success": True,
            "error": None
        }
    },
    "file_upload_result": {
        "summary": "Upload de arquivo bem-sucedido",
        "value": {
            "file_id": "file-uuid-123",
            "filename": "dados.csv",
            "file_type": "text/csv",
            "file_size": 1024,
            "created_at": "2025-01-23T10:00:00Z",
            "status": "uploaded",
            "metadata": {
                "description": "Dados de ouvidoria",
                "tags": ["educacao", "ouvidoria"]
            }
        }
    },
    "anonymization_result": {
        "summary": "Resultado da anonimização",
        "value": {
            "original_text": "João Silva, CPF 123.456.789-00, telefone (21) 99999-9999",
            "anonymized_text": "[PERSON], CPF [CPF], telefone [PHONE]",
            "entities_found": [
                {
                    "entity_type": "PERSON",
                    "text": "João Silva",
                    "start": 0,
                    "end": 10,
                    "confidence": 0.95
                }
            ],
            "language": "pt",
            "processing_time": 0.45,
            "success": True
        }
    }
}
