#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para executar comparação entre diferentes métodos de análise de relatos.
"""

import os
import argparse
import logging
import pandas as pd
from datetime import datetime

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def executar_comparacao(coluna_texto, diretorio_saida="data/output", tamanho_lote=5, limiar=0.2):
    """
    Executa a comparação entre os três métodos de análise.
    
    Args:
        coluna_texto (str): Nome da coluna que contém o texto a ser classificado
        diretorio_saida (str): Diretório para salvar os resultados
        tamanho_lote (int): Tamanho do lote para o método em lotes
        limiar (float): Limiar de similaridade para o método de embeddings
    """
    
    # Arquivo de entrada fixo
    arquivo_entrada = "data/input/ouvidorias.csv"
    
    if not os.path.exists(arquivo_entrada):
        logging.error(f"Arquivo de entrada não encontrado: {arquivo_entrada}")
        print(f"Erro: Arquivo '{arquivo_entrada}' não encontrado.")
        return
    
    # Verificar se a coluna existe no arquivo
    try:
        df_test = pd.read_csv(arquivo_entrada)
        if coluna_texto not in df_test.columns:
            logging.error(f"Coluna '{coluna_texto}' não encontrada no arquivo.")
            print(f"Colunas disponíveis: {', '.join(df_test.columns)}")
            return
    except Exception as e:
        logging.error(f"Erro ao verificar arquivo: {e}")
        return
    
    # Importar os analisadores
    try:
        from src.analisadores.analisador_relatos import main as analisador_individual
        from src.analisadores.analisador_lote import main as analisador_lote
        from src.analisadores.analisador_embeddings import main as analisador_embeddings
    except ImportError as e:
        logging.error(f"Erro ao importar analisadores: {e}")
        return
    
    # Criar diretório de saída se não existir
    os.makedirs(diretorio_saida, exist_ok=True)
    
    # Definir arquivos de saída para cada método
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    arquivo_individual = os.path.join(diretorio_saida, f"relatos_classificados_individual_{timestamp}.csv")
    arquivo_lote = os.path.join(diretorio_saida, f"relatos_classificados_lote_{timestamp}.csv")
    arquivo_embeddings = os.path.join(diretorio_saida, f"relatos_classificados_embeddings_{timestamp}.csv")
    
    print("="*60)
    print("INICIANDO COMPARAÇÃO DE MÉTODOS DE ANÁLISE")
    print("="*60)
    print(f"Arquivo de entrada: {arquivo_entrada}")
    print(f"Coluna a ser analisada: {coluna_texto}")
    print(f"Diretório de saída: {diretorio_saida}")
    print()
    
    # Executar método individual
    print("1. Executando método INDIVIDUAL...")
    try:
        analisador_individual(coluna_texto, arquivo_individual)
        print(f"   ✓ Concluído: {arquivo_individual}")
        df_individual = pd.read_csv(arquivo_individual) if os.path.exists(arquivo_individual) else None
    except Exception as e:
        logging.error(f"Erro no método individual: {e}")
        df_individual = None
        print(f"   ✗ Erro: {e}")
    
    print()
    
    # Executar método em lotes
    print("2. Executando método EM LOTES...")
    try:
        analisador_lote(coluna_texto, arquivo_lote, tamanho_lote)
        print(f"   ✓ Concluído: {arquivo_lote}")
        df_lote = pd.read_csv(arquivo_lote) if os.path.exists(arquivo_lote) else None
    except Exception as e:
        logging.error(f"Erro no método em lotes: {e}")
        df_lote = None
        print(f"   ✗ Erro: {e}")
    
    print()
    
    # Executar método de embeddings
    print("3. Executando método EMBEDDINGS...")
    try:
        analisador_embeddings(coluna_texto, arquivo_embeddings, limiar)
        print(f"   ✓ Concluído: {arquivo_embeddings}")
        df_embeddings = pd.read_csv(arquivo_embeddings) if os.path.exists(arquivo_embeddings) else None
    except Exception as e:
        logging.error(f"Erro no método embeddings: {e}")
        df_embeddings = None
        print(f"   ✗ Erro: {e}")
    
    print()
    print("="*60)
    print("GERANDO RELATÓRIO DE COMPARAÇÃO")
    print("="*60)
    
    # Gerar relatório de comparação
    arquivo_comparacao = os.path.join(diretorio_saida, f"comparacao_metodos_{timestamp}.csv")
    
    resultados_comparacao = []
    
    # Analisar resultados de cada método
    metodos = [
        ("Individual", df_individual),
        ("Lote", df_lote),
        ("Embeddings", df_embeddings)
    ]
    
    for nome_metodo, df in metodos:
        if df is not None and 'REFERE_ASSUNTO' in df.columns:
            total_registros = len(df)
            casos_positivos = df['REFERE_ASSUNTO'].sum()
            percentual = (casos_positivos / total_registros * 100) if total_registros > 0 else 0
            
            resultados_comparacao.append({
                'Método': nome_metodo,
                'Total_Registros': total_registros,
                'Casos_Positivos': casos_positivos,
                'Percentual_Positivos': round(percentual, 2),
                'Arquivo_Resultado': arquivo_individual if nome_metodo == "Individual" else 
                                   arquivo_lote if nome_metodo == "Lote" else arquivo_embeddings
            })
            
            print(f"{nome_metodo:12}: {casos_positivos:4d}/{total_registros:4d} casos positivos ({percentual:.1f}%)")
        else:
            print(f"{nome_metodo:12}: Falhou na execução")
            resultados_comparacao.append({
                'Método': nome_metodo,
                'Total_Registros': 0,
                'Casos_Positivos': 0,
                'Percentual_Positivos': 0,
                'Arquivo_Resultado': 'ERRO'
            })
    
    # Salvar comparação
    if resultados_comparacao:
        df_comparacao = pd.DataFrame(resultados_comparacao)
        df_comparacao.to_csv(arquivo_comparacao, index=False)
        print(f"\nRelatório de comparação salvo em: {arquivo_comparacao}")
    
    # Se tivermos resultados para todos os métodos, verificar concordância
    # A tabela deve conter três linhas (uma para cada método)
    if len(df_comparacao) == 3:
        print("\n" + "="*60)
        print("ANÁLISE DE CONCORDÂNCIA ENTRE MÉTODOS")
        print("="*60)
        
        # Verificar se todos os métodos processaram o mesmo número de registros
        totais = df_comparacao['Total_Registros'].tolist()
        if len(set(totais)) == 1 and totais[0] > 0:
            print(f"✓ Todos os métodos processaram {totais[0]} registros")
            
            # Analisar diferenças nos percentuais
            percentuais = df_comparacao['Percentual_Positivos'].tolist()
            max_perc = max(percentuais)
            min_perc = min(percentuais)
            diferenca = max_perc - min_perc
            
            print(f"Variação nos resultados: {min_perc:.1f}% - {max_perc:.1f}% (diferença: {diferenca:.1f}pp)")
            
            if diferenca <= 5:
                print("✓ Métodos apresentam resultados consistentes (diferença ≤ 5pp)")
            elif diferenca <= 10:
                print("⚠ Métodos apresentam alguma variação (diferença 5-10pp)")
            else:
                print("⚠ Métodos apresentam resultados divergentes (diferença > 10pp)")
        else:
            print("⚠ Métodos processaram números diferentes de registros")
    
    print(f"\n{'='*60}")
    print("COMPARAÇÃO CONCLUÍDA")
    print(f"{'='*60}")
    print(f"Resultados salvos em: {diretorio_saida}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Compara diferentes métodos de análise de relatos usando o arquivo ouvidorias.csv.")
    parser.add_argument("coluna_texto", help="Nome da coluna que contém o texto a ser classificado")
    parser.add_argument("--diretorio_saida", default="data/output", help="Diretório para salvar os resultados")
    parser.add_argument("--tamanho_lote", type=int, default=5, help="Tamanho do lote para o método em lotes")
    parser.add_argument("--limiar", type=float, default=0.2, help="Limiar de similaridade para o método de embeddings")
    
    args = parser.parse_args()
    
    executar_comparacao(args.coluna_texto, args.diretorio_saida, args.tamanho_lote, args.limiar) 