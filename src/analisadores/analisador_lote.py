import pandas as pd
import yaml
import os
from dotenv import load_dotenv
import together
from together.error import AuthenticationError, TogetherException
import logging
from typing import Dict, Any, List
import time
import json

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def carregar_configuracao(config_path: str = "config/config.yml") -> Dict[str, Any]:
    """
    Carrega as configurações do arquivo YAML.

    Args:
        config_path (str): Caminho para o arquivo de configuração.

    Returns:
        Dict[str, Any]: Dicionário com as configurações.
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logging.info(f"Configuração carregada de {config_path}: {config}")
        if not config.get("ASSUNTO"):
            logging.error("Chave 'ASSUNTO' não encontrada no arquivo de configuração")
            raise ValueError("Chave 'ASSUNTO' não encontrada no arquivo de configuração")
        if not config.get("LLM_MODEL"):
            logging.error("Chave 'LLM_MODEL' não encontrada no arquivo de configuração")
            raise ValueError("Chave 'LLM_MODEL' não encontrada no arquivo de configuração")
        return config
    except FileNotFoundError:
        logging.error(f"Arquivo de configuração {config_path} não encontrado.")
        # Tentar localização alternativa (para retrocompatibilidade)
        alt_path = "config.yml"
        logging.info(f"Tentando localização alternativa: {alt_path}")
        try:
            with open(alt_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logging.info(f"Configuração carregada de {alt_path}: {config}")
            return config
        except FileNotFoundError:
            logging.error(f"Arquivo de configuração também não encontrado em: {alt_path}")
            raise
    except yaml.YAMLError as e:
        logging.error(f"Erro ao parsear o arquivo de configuração {config_path}: {e}")
        raise
    except ValueError as e:
        raise

def processar_lote(relatos: List[str], assunto: str, modelo_llm: str, tamanho_lote: int = 5) -> List[int]:
    """
    Processa um lote de relatos de uma vez com o LLM.

    Args:
        relatos (List[str]): Lista de relatos para processar.
        assunto (str): Assunto para verificar nos relatos.
        modelo_llm (str): Modelo LLM para usar.
        tamanho_lote (int): Tamanho máximo do lote para cada chamada.

    Returns:
        List[int]: Lista de resultados (1 = referente ao assunto, 0 = não referente).
    """
    client = together.Together()
    resultados = []
    max_tentativas = 3
    
    # Dividir a lista em lotes menores
    for i in range(0, len(relatos), tamanho_lote):
        lote_atual = relatos[i:i+tamanho_lote]
        indices_lote = list(range(i+1, i+len(lote_atual)+1))  # Números dos relatos (1-indexed)
        
        # Construir o prompt para o lote
        instrucao = f"Classifique se cada um dos seguintes relatos está relacionado a '{assunto}'."
        texto_relatos = ""
        for idx, relato in zip(indices_lote, lote_atual):
            texto_relatos += f"Relato {idx}: \"{relato}\"\n\n"
            
        instrucao_final = f"{instrucao}\n\n{texto_relatos}Para cada relato, responda apenas com o número do relato seguido de SIM ou NAO.\nExemplo:\n1: SIM\n2: NAO\n"
        
        logging.info(f"Processando lote de {len(lote_atual)} relatos ({i+1}-{i+len(lote_atual)} de {len(relatos)})")
        
        for tentativa in range(max_tentativas):
            try:
                response = client.completions.create(
                    prompt=instrucao_final,
                    model=modelo_llm,
                    max_tokens=150,  # Aumentar tokens para acomodar respostas para vários relatos
                    temperature=0.2,
                )
                resposta_llm = response.choices[0].text.strip()
                logging.debug(f"Resposta do LLM para o lote: {resposta_llm}")
                
                # Processar a resposta
                resultados_lote = [0] * len(lote_atual)  # Default: não referente
                
                # Analisar a resposta linha por linha
                linhas = resposta_llm.split('\n')
                for linha in linhas:
                    linha = linha.strip()
                    # Procurar padrões como "1: SIM" ou "2: NAO"
                    if ':' in linha:
                        partes = linha.split(':')
                        if len(partes) == 2:
                            try:
                                num_relato = int(partes[0].strip())
                                resposta = partes[1].strip().upper()
                                # Converter para índice relativo ao lote (0-indexed)
                                idx_lote = num_relato - indices_lote[0]
                                if 0 <= idx_lote < len(lote_atual):
                                    if "SIM" in resposta:
                                        resultados_lote[idx_lote] = 1
                            except (ValueError, IndexError):
                                logging.warning(f"Formato inválido na resposta: {linha}")
                                continue
                
                resultados.extend(resultados_lote)
                break  # Sair do loop de tentativas se bem-sucedido
                
            except (AuthenticationError, TogetherException) as e:
                logging.error(f"Erro na API (tentativa {tentativa+1}/{max_tentativas}): {e}")
                if "rate limit" in str(e).lower() and tentativa < max_tentativas - 1:
                    delay = 60 * (2 ** tentativa)  # Backoff exponencial
                    logging.warning(f"Rate limit. Aguardando {delay} segundos antes de tentar novamente...")
                    time.sleep(delay)
                else:
                    # Se for a última tentativa, preenche com zeros
                    resultados.extend([0] * len(lote_atual))
                    break
        
        # Pequena pausa entre lotes
        time.sleep(2)
    
    return resultados

def processar_csv(arquivo_entrada: str, arquivo_saida: str, config: Dict[str, Any], tamanho_lote: int = 5) -> None:
    """
    Lê um arquivo CSV, analisa a coluna 'RELATO' com um LLM em lotes e salva o resultado.

    Args:
        arquivo_entrada (str): Caminho do arquivo CSV de entrada.
        arquivo_saida (str): Caminho para salvar o CSV processado.
        config (Dict[str, Any]): Dicionário com as configurações (ASSUNTO, LLM_MODEL).
        tamanho_lote (int): Tamanho do lote para processamento.
    """
    assunto = config["ASSUNTO"]
    modelo_llm = config["LLM_MODEL"]

    try:
        logging.info(f"Lendo o arquivo CSV de entrada: {arquivo_entrada}")
        df = pd.read_csv(arquivo_entrada, header=None, names=['RELATO'], quotechar='"', escapechar='\\', on_bad_lines='warn')
    except FileNotFoundError:
        logging.error(f"Arquivo CSV de entrada '{arquivo_entrada}' não encontrado.")
        raise
    except Exception as e:
        logging.error(f"Erro ao ler o arquivo CSV '{arquivo_entrada}': {e}")
        raise

    if "RELATO" not in df.columns:
        logging.error(f"A coluna 'RELATO' não foi encontrada no arquivo '{arquivo_entrada}'.")
        raise ValueError(f"Coluna 'RELATO' não encontrada em {arquivo_entrada}.")

    logging.info(f"Iniciando processamento em lotes de {len(df)} relatos.")
    
    # Converter a coluna RELATO para uma lista
    relatos = df["RELATO"].astype(str).tolist()
    
    # Processar os relatos em lotes
    resultados = processar_lote(relatos, assunto, modelo_llm, tamanho_lote)
    
    # Garantir que temos o mesmo número de resultados que de relatos
    if len(resultados) < len(relatos):
        logging.warning(f"Número de resultados ({len(resultados)}) menor que o número de relatos ({len(relatos)}). Preenchendo com zeros.")
        resultados.extend([0] * (len(relatos) - len(resultados)))
    elif len(resultados) > len(relatos):
        logging.warning(f"Número de resultados ({len(resultados)}) maior que o número de relatos ({len(relatos)}). Truncando.")
        resultados = resultados[:len(relatos)]
    
    df["REFERE_ASSUNTO"] = resultados
    logging.info("Processamento dos relatos em lote concluído.")
    
    # Calcular estatísticas
    total_positivos = sum(resultados)
    total_registros = len(resultados)
    porcentagem = (total_positivos / total_registros) * 100 if total_registros > 0 else 0
    
    logging.info(f"Estatísticas: {total_positivos} de {total_registros} relatos ({porcentagem:.2f}%) referem-se a '{assunto}'.")

    try:
        df.to_csv(arquivo_saida, index=False, encoding='utf-8')
        logging.info(f"Arquivo CSV processado salvo em: {arquivo_saida}")
    except Exception as e:
        logging.error(f"Erro ao salvar o arquivo CSV em '{arquivo_saida}': {e}")
        raise

def main(csv_input_path: str, csv_output_path: str, tamanho_lote: int = 5) -> None:
    """
    Função principal para executar o pipeline de análise de relatos em lotes.

    Args:
        csv_input_path (str): Caminho para o arquivo CSV de entrada.
        csv_output_path (str): Caminho para salvar o arquivo CSV de saída.
        tamanho_lote (int): Tamanho do lote para processamento.
    """
    logging.info(f"Iniciando o processo de análise de relatos em lotes de tamanho {tamanho_lote}.")
    load_dotenv()
    api_key = os.getenv("TOGETHER_API_KEY")

    if not api_key:
        logging.error("A variável de ambiente TOGETHER_API_KEY não foi definida.")
        print("Erro: A variável de ambiente TOGETHER_API_KEY não está configurada.")
        print("Por favor, crie um arquivo .env no diretório raiz do projeto com a seguinte linha:")
        print("TOGETHER_API_KEY='sua_chave_api_aqui'")
        return

    try:
        config = carregar_configuracao()
        processar_csv(csv_input_path, csv_output_path, config, tamanho_lote)
        logging.info("Pipeline de análise de relatos em lote concluído com sucesso.")
    except Exception as e:
        logging.error(f"Ocorreu um erro no pipeline: {e}")

if __name__ == "__main__":
    pass 