#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Analisador multilabel para classificação de relatos em múltiplos subtemas.
Classifica cada relato individualmente usando LLM, podendo atribuir até 3 subtemas por relato.
"""

import os
import time
import pandas as pd
import yaml
from tqdm import tqdm
import logging
import json
import re
from typing import List, Dict, Any, Optional
from datetime import datetime

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Criar logger específico para análise de performance
perf_logger = logging.getLogger('performance')
perf_handler = logging.FileHandler('data/output/analisador_multilabel_performance.log')
perf_handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
perf_logger.addHandler(perf_handler)
perf_logger.setLevel(logging.INFO)


def carregar_configuracao_yaml(caminho_config: str = 'config/config.yml') -> Optional[Dict[str, Any]]:
    """
    Carrega configurações do arquivo YAML.
    
    Args:
        caminho_config (str): Caminho para o arquivo de configuração
        
    Returns:
        dict: Dicionário com as configurações ou None se ocorrer erro
    """
    try:
        with open(caminho_config, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        return config
    except FileNotFoundError:
        logging.error(f"Erro: Arquivo de configuração '{caminho_config}' não encontrado.")
        return None
    except yaml.YAMLError as e:
        logging.error(f"Erro ao carregar configuração YAML: {e}")
        return None


def carregar_variaveis_ambiente() -> Dict[str, str]:
    """
    Carrega variáveis de ambiente do arquivo .env.
    
    Returns:
        dict: Dicionário com as variáveis de ambiente
    """
    env_vars = {}
    try:
        with open('.env', 'r', encoding='utf-8') as file:
            for linha in file:
                linha = linha.strip()
                if linha and not linha.startswith('#') and '=' in linha:
                    chave, valor = linha.split('=', 1)
                    env_vars[chave] = valor.strip("'\"")
    except FileNotFoundError:
        logging.error("Erro: Arquivo .env não encontrado.")
    return env_vars


def obter_subtemas_por_area(config: Dict[str, Any], area: str) -> List[str]:
    """
    Obtém a lista de subtemas para uma área específica.
    
    Args:
        config (dict): Configurações carregadas do YAML
        area (str): Nome da área (ex: "SAUDE", "EDUCACAO", "MEIO_AMBIENTE")
        
    Returns:
        List[str]: Lista de subtemas da área
    """
    chave_subtemas = f"SUBTEMAS_{area.upper()}"
    return config.get(chave_subtemas, [])


def carregar_definicoes_subtemas(area: str, caminho_definicoes: str = 'config/definicoes_subtemas.yml') -> Dict[str, str]:
    """
    Carrega as definições específicas dos subtemas para uma área.

    Args:
        area (str): Nome da área (SAUDE, EDUCACAO, MEIO_AMBIENTE)
        caminho_definicoes (str): Caminho para o arquivo de definições

    Returns:
        Dict[str, str]: Dicionário com subtema -> definição
    """
    try:
        with open(caminho_definicoes, 'r', encoding='utf-8') as file:
            definicoes_config = yaml.safe_load(file)

        area_definicoes = definicoes_config.get(area, {})

        # Extrair apenas as definições (não as palavras-chave)
        definicoes_subtemas = {}
        for subtema, dados in area_definicoes.items():
            if isinstance(dados, dict) and 'definicao' in dados:
                definicoes_subtemas[subtema] = dados['definicao']
            else:
                definicoes_subtemas[subtema] = f"Casos relacionados a {subtema.lower()}"

        return definicoes_subtemas

    except FileNotFoundError:
        logging.warning(f"Arquivo de definições não encontrado: {caminho_definicoes}")
        return {}
    except Exception as e:
        logging.error(f"Erro ao carregar definições dos subtemas: {e}")
        return {}


def carregar_definicoes_completas_subtemas(area: str, caminho_definicoes: str = 'config/definicoes_subtemas.yml') -> Dict[str, Dict]:
    """
    Carrega definições completas dos subtemas (definição + palavras-chave + contexto).

    Args:
        area (str): Nome da área (SAUDE, EDUCACAO, MEIO_AMBIENTE)
        caminho_definicoes (str): Caminho para o arquivo de definições

    Returns:
        Dict[str, Dict]: Dicionário com subtema -> {definicao, palavras_chave, foco_funcional}
    """
    with open(caminho_definicoes, 'r', encoding='utf-8') as file:
        definicoes_config = yaml.safe_load(file)

    area_definicoes = definicoes_config[area]

    # Extrair definições completas
    definicoes_completas = {}
    for subtema, dados in area_definicoes.items():
        definicao = dados['definicao']
        palavras_chave = dados['palavras_chave']

        # Gerar foco funcional baseado na definição
        foco_funcional = f"Análise especializada em {definicao.lower()}"

        definicoes_completas[subtema] = {
            'definicao': definicao,
            'palavras_chave': palavras_chave,
            'foco_funcional': foco_funcional
        }

    return definicoes_completas


def obter_contexto_subtema(area: str, subtema: str) -> Dict[str, str]:
    """
    Obtém contexto do subtema.

    Args:
        area (str): Nome da área
        subtema (str): Nome do subtema

    Returns:
        Dict[str, str]: Contexto do subtema
    """
    definicoes = carregar_definicoes_completas_subtemas(area)

    # Tentar busca exata primeiro
    if subtema in definicoes:
        return definicoes[subtema]

    # Tentar busca case-insensitive
    for key in definicoes.keys():
        if key.lower() == subtema.lower():
            return definicoes[key]

    # Tentar busca parcial (para casos como "Controle Interno" vs "Controle Interno, Gestão...")
    for key in definicoes.keys():
        if subtema.lower() in key.lower() or key.lower() in subtema.lower():
            return definicoes[key]

    # Fallback para definição básica
    return {
        'definicao': f"Casos relacionados a {subtema.lower()}",
        'palavras_chave': [],
        'foco_funcional': f"Análise de {subtema.lower()}"
    }


def classificar_relato_multilabel(
    relato: str, 
    subtemas: List[str], 
    area: str,
    model_name: str = 'meta-llama/Llama-3.2-3B-Instruct-Turbo', 
    api_key: str = None,
    idx: int = None
) -> List[str]:
    """
    Classifica um relato em múltiplos subtemas usando LLM (Fireworks Llama 4).
    Args:
        relato (str): Texto do relato a ser classificado
        subtemas (List[str]): Lista de subtemas disponíveis
        area (str): Área de classificação (SAUDE, EDUCACAO, MEIO_AMBIENTE, EDUCACAO2)
        model_name (str): Nome do modelo LLM a ser usado
        api_key (str): Chave da API Fireworks
        idx (int): Índice do registro para logging
    Returns:
        List[str]: Lista de subtemas identificados (máximo 3)
    """
    inicio_classificacao = time.time()
    try:
        import requests

        perf_logger.info(f"Iniciando classificação do registro {idx} - Tamanho do texto: {len(relato)} caracteres")

        # Carregar definições dos subtemas do arquivo YAML
        definicoes_subtemas = carregar_definicoes_subtemas(area)
        if not definicoes_subtemas:
            definicoes_subtemas = {subtema: f"Casos relacionados a {subtema.lower()}" for subtema in subtemas}

        subtemas_com_definicoes = []
        for i, subtema in enumerate(subtemas):
            definicao = definicoes_subtemas.get(subtema, f"Casos relacionados a {subtema.lower()}")
            subtemas_com_definicoes.append(f"{i+1}. {subtema}: {definicao}")
        subtemas_formatados = "\n".join(subtemas_com_definicoes)

        prompt = f"""Classifique o relato abaixo nos subtemas de {area}.

SUBTEMAS DISPONÍVEIS:
{subtemas_formatados}

RELATO A CLASSIFICAR:
{relato}

IMPORTANTE: Responda APENAS com os números dos subtemas (separados por vírgula) ou "NENHUM".
NÃO ESCREVA NENHUMA EXPLICAÇÃO, APENAS OS NÚMEROS.

Exemplos de resposta válida:
- 1,3,5
- 2
- NENHUM

RESPOSTA (apenas números ou NENHUM):"""

        perf_logger.info(f"Registro {idx} - Prompt criado com {len(prompt)} caracteres")
        inicio_api = time.time()

        # Fireworks API (OpenAI-like)
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        data = {
            "model": model_name,
            "max_tokens": 50,
            "temperature": 0.1,
            "messages": [{"role": "user", "content": prompt}]
        }
        response = requests.post(
            "https://api.fireworks.ai/inference/v1/chat/completions",
            headers=headers,
            json=data
        )
        tempo_api = time.time() - inicio_api
        perf_logger.info(f"Registro {idx} - Tempo de resposta da API: {tempo_api:.2f}s")

        if response.status_code == 200:
            resposta = response.json()
        else:
            raise Exception(f"Erro na API: {response.status_code} - {response.text}")

        # Extrair texto da resposta (OpenAI/Fireworks)
        try:
            resposta_texto = resposta["choices"][0]["message"]["content"].strip().upper()
            # Log da resposta para diagnóstico
            logging.info(f"Registro {idx} - Resposta do modelo: '{resposta_texto}'")
        except (KeyError, IndexError) as e:
            logging.warning(f"Erro ao extrair texto da resposta: {e}, resposta: {resposta}")
            return []

        subtemas_identificados = []
        if resposta_texto == "NENHUM":
            return subtemas_identificados
        try:
            numeros = [int(num.strip()) for num in resposta_texto.split(',') if num.strip().isdigit()]
            for num in numeros[:3]:
                if 1 <= num <= len(subtemas):
                    subtemas_identificados.append(subtemas[num - 1])
        except (ValueError, IndexError) as e:
            logging.warning(f"Erro ao interpretar resposta '{resposta_texto}': {e}")
        tempo_total = time.time() - inicio_classificacao
        perf_logger.info(f"Registro {idx} - Classificação concluída em {tempo_total:.2f}s - Subtemas: {subtemas_identificados}")
        return subtemas_identificados
    except Exception as e:
        tempo_total = time.time() - inicio_classificacao
        logging.error(f"Erro ao classificar relato {idx}: {e}")
        perf_logger.error(f"Registro {idx} - ERRO após {tempo_total:.2f}s: {str(e)}")
        return []


def extrair_conteudo_principal(texto: str) -> str:
    """
    Extrai o conteúdo principal do relato, focando na denúncia/reclamação real.
    
    Args:
        texto (str): Texto bruto com metadados e conteúdo
        
    Returns:
        str: Conteúdo principal extraído
    """
    if not texto or not isinstance(texto, str):
        return ""
    
    # Dividir em parágrafos/blocos
    blocos = re.split(r'\n\s*\n', texto)
    
    # Procurar blocos que contêm o conteúdo principal
    conteudo_principal = []
    palavras_chave_inicio = [
        'EXTRATO', 'RELATO', 'DENÚNCIA', 'COMUNICAÇÃO', 'DECLARA', 
        'INFORMA', 'SOLICITA', 'RECLAMA', 'RELATA', 'AFIRMA',
        'GOSTARIA', 'VENHO', 'PRESENTE', 'TRATA-SE'
    ]
    
    encontrou_inicio = False
    
    for bloco in blocos:
        bloco_limpo = bloco.strip()
        
        # Verificar se o bloco contém palavras que indicam início do conteúdo
        if any(palavra in bloco_limpo.upper() for palavra in palavras_chave_inicio):
            encontrou_inicio = True
        
        # Se encontrou início ou se o bloco tem conteúdo substantivo
        if encontrou_inicio or (len(bloco_limpo) > 100 and not bloco_limpo.isupper()):
            # Remover linhas de metadados dentro do bloco
            linhas = bloco_limpo.split('\n')
            linhas_validas = []
            
            for linha in linhas:
                linha = linha.strip()
                # Pular linhas que são claramente metadados
                if (linha and 
                    not re.match(r'^(PROTOCOLO|DISPONIBILIZADA|DATA|NR|MOVIMENTO):', linha, re.I) and
                    not re.match(r'^\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}', linha) and
                    len(linha) > 10):
                    linhas_validas.append(linha)
            
            if linhas_validas:
                conteudo_principal.append(' '.join(linhas_validas))
    
    # Juntar todo o conteúdo principal encontrado
    resultado = ' '.join(conteudo_principal)
    
    # Limpar espaços múltiplos e normalizar
    resultado = re.sub(r'\s+', ' ', resultado)
    resultado = resultado.strip()
    
    # Se não encontrou conteúdo principal, tentar abordagem mais simples
    if len(resultado) < 50:
        # Pegar todo o texto e remover apenas os metadados mais óbvios
        resultado = texto
        # Remover padrões de metadados conhecidos
        padroes_remover = [
            r'PROTOCOLO[:\s][^\n]+\n?',
            r'DISPONIBILIZADA[^\n]+\n?',
            r'DATA[:\s]\d{2}/\d{2}/\d{4}[^\n]*\n?',
            r'^\d+,\d{2}/\d{2}/\d{4}[^\n]+\n',  # linhas começando com ID,data
        ]
        for padrao in padroes_remover:
            resultado = re.sub(padrao, '', resultado, flags=re.IGNORECASE | re.MULTILINE)
        
        resultado = re.sub(r'\s+', ' ', resultado).strip()
    
    return resultado


def limpar_texto_relato(texto: str) -> str:
    """
    Limpa e extrai o conteúdo relevante do relato, removendo metadados excessivos.
    
    Args:
        texto (str): Texto bruto do relato com metadados
        
    Returns:
        str: Texto limpo focado no conteúdo substantivo
    """
    if not texto or not isinstance(texto, str):
        return ""
    
    # Corrigir caracteres corrompidos comuns
    texto = texto.replace('', 'Ã')
    texto = texto.replace('', 'Ç')
    texto = texto.replace('', 'Õ')
    
    # Remover linhas com apenas metadados (protocolo, data, etc)
    linhas = texto.split('\n')
    linhas_limpas = []
    
    # Padrões de metadados para ignorar
    padroes_metadata = [
        r'^PROTOCOLO[:\s]',
        r'^DISPONIBILIZADA[:\s]',
        r'^DATA[:\s]',
        r'^NÚMERO[:\s]',
        r'^NR[:\s]',
        r'^MOVIMENTO[:\s]',
        r'^\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}',  # datas/horas soltas
        r'^[A-Z\s]+>>[A-Z\s]+>>',  # hierarquias de categorias
        r'^\([A-Z]{2,}\)$',  # siglas entre parênteses
    ]
    
    # Identificar início do conteúdo real (após "EXTRATO" ou similar)
    inicio_conteudo = False
    palavras_inicio = ['EXTRATO', 'RELATO', 'DENÚNCIA', 'COMUNICAÇÃO', 'INFORMAÇÃO', 'SOLICITA', 'RECLAMA']
    
    for linha in linhas:
        linha = linha.strip()
        
        # Pular linhas vazias
        if not linha:
            continue
            
        # Verificar se chegamos ao conteúdo real
        if any(palavra in linha.upper() for palavra in palavras_inicio):
            inicio_conteudo = True
            
        # Se ainda não chegamos ao conteúdo, verificar se é metadado
        if not inicio_conteudo:
            e_metadata = any(re.match(padrao, linha, re.IGNORECASE) for padrao in padroes_metadata)
            if e_metadata:
                continue
                
        # Se a linha tem conteúdo substantivo (mais de 20 caracteres e não é só maiúsculas)
        if len(linha) > 20 and not (linha.isupper() and len(linha.split()) < 5):
            linhas_limpas.append(linha)
    
    # Juntar as linhas limpas
    texto_limpo = ' '.join(linhas_limpas)
    
    # Remover espaços múltiplos
    texto_limpo = re.sub(r'\s+', ' ', texto_limpo)
    
    # Se o texto limpo ficou muito curto, usar o texto original com limpeza básica
    if len(texto_limpo) < 50 and len(texto) > 100:
        # Fazer apenas limpeza básica
        texto_limpo = re.sub(r'\s+', ' ', texto)
        # Remover apenas os metadados mais óbvios
        texto_limpo = re.sub(r'PROTOCOLO[:\s][^\s]+\s*', '', texto_limpo, flags=re.IGNORECASE)
        texto_limpo = re.sub(r'DISPONIBILIZADA[^:]+:\s*\d{2}/\d{2}/\d{4}\s*', '', texto_limpo, flags=re.IGNORECASE)
    
    return texto_limpo.strip()


def carregar_csv_com_fallback(path: str) -> pd.DataFrame:
    """
    Tenta carregar um CSV em utf-8, se falhar tenta latin1.
    Args:
        path (str): Caminho do arquivo CSV
    Returns:
        pd.DataFrame: DataFrame carregado
    """
    try:
        df = pd.read_csv(path, encoding='utf-8', sep=None, engine='python')
        logging.info(f"Arquivo CSV carregado com codificação utf-8: {path}")
        return df
    except UnicodeDecodeError:
        df = pd.read_csv(path, encoding='latin1', sep=None, engine='python')
        logging.info(f"Arquivo CSV carregado com codificação latin1: {path}")
        return df


def main(
    coluna_texto: str, 
    area: str = "SAUDE",
    output_csv: str = "data/output/relatos_classificados_multilabel.csv",
    input_csv: str = "data/input/ouvidorias.csv"
) -> None:
    """
    Função principal para processar o arquivo CSV e classificar os relatos em múltiplos subtemas.
    Args:
        coluna_texto (str): Nome da coluna que contém o texto a ser classificado
        area (str): Área para classificação (SAUDE, EDUCACAO, MEIO_AMBIENTE)
        output_csv (str): Caminho para salvar o arquivo CSV processado
        input_csv (str): Caminho do arquivo CSV de entrada
    """
    
    # Usar input_csv recebido
    if not os.path.exists(input_csv):
        logging.error(f"Arquivo de entrada não encontrado: {input_csv}")
        print(f"Erro: Arquivo '{input_csv}' não encontrado.")
        return
    
    # Carregar variáveis de ambiente
    env_vars = carregar_variaveis_ambiente()
    
    # Usar FIREWORKS_API_KEY
    api_key = env_vars.get("FIREWORKS_API_KEY")
    if not api_key:
        logging.error("ERRO: Variável de ambiente FIREWORKS_API_KEY não encontrada no arquivo .env.")
        print("Configure a chave API no arquivo .env: FIREWORKS_API_KEY='sua-chave-api'")
        return
    
    # Carregar configurações
    config = carregar_configuracao_yaml()
    if not config:
        return
    
    # Obter modelo
    model_name = config.get('LLM_MODEL')
    if not model_name:
        logging.error("Erro: LLM_MODEL não encontrado no arquivo de configuração")
        return
    
    # Obter subtemas da área especificada
    subtemas = obter_subtemas_por_area(config, area)
    if not subtemas:
        logging.error(f"Erro: Nenhum subtema encontrado para a área '{area}'")
        print(f"Áreas disponíveis: SAUDE, EDUCACAO, MEIO_AMBIENTE")
        return
    
    logging.info(f"Usando modelo: {model_name}")
    logging.info(f"Área: {area}")
    logging.info(f"Subtemas disponíveis: {subtemas}")
    logging.info(f"Coluna a analisar: '{coluna_texto}'")
    
    # Carregar o CSV
    try:
        df = carregar_csv_com_fallback(input_csv)
        logging.info(f"Arquivo carregado com {len(df)} registros.")
    except Exception as e:
        logging.error(f"Erro ao carregar arquivo CSV: {e}")
        return
    
    # Verificar se a coluna especificada existe
    if coluna_texto not in df.columns:
        logging.error(f"Coluna '{coluna_texto}' não encontrada no arquivo.")
        print(f"Colunas disponíveis: {', '.join(df.columns)}")
        return
    
    # Filtrar registros sem texto na coluna especificada
    df_filtrado = df.dropna(subset=[coluna_texto])
    df_filtrado = df_filtrado[df_filtrado[coluna_texto].str.strip() != '']
    
    logging.info(f"Processando {len(df_filtrado)} registros com texto válido na coluna '{coluna_texto}'.")
    
    # Aplicar classificação multilabel
    resultados_subtemas = []
    contadores_subtemas = {subtema: 0 for subtema in subtemas}
    tempo_inicio_processamento = time.time()
    tempos_classificacao = []
    erros_count = 0
    
    logging.info(f"Iniciando processamento de {len(df_filtrado)} registros...")
    perf_logger.info(f"\n{'='*80}")
    perf_logger.info(f"NOVA SESSÃO DE CLASSIFICAÇÃO - {datetime.now()}")
    perf_logger.info(f"Área: {area} | Modelo: {model_name} | Total de registros: {len(df_filtrado)}")
    perf_logger.info(f"{'='*80}\n")
    
    for idx, row in tqdm(df_filtrado.iterrows(), total=len(df_filtrado), desc="Classificando relatos"):
        texto = str(row[coluna_texto])
        tempo_inicio_item = time.time()
        
        # Limpar o texto antes de classificar
        texto_limpo = extrair_conteudo_principal(texto)
        
        # Se o texto limpo ficou muito curto, usar o original
        if len(texto_limpo) < 30 and len(texto) > 50:
            texto_limpo = texto
        
        subtemas_identificados = classificar_relato_multilabel(
            texto_limpo, subtemas, area, model_name, api_key, idx
        )
        
        tempo_item = time.time() - tempo_inicio_item
        tempos_classificacao.append(tempo_item)
        
        if not subtemas_identificados and texto.strip():
            erros_count += 1
        
        # Contar ocorrências de cada subtema
        for subtema in subtemas_identificados:
            if subtema in contadores_subtemas:
                contadores_subtemas[subtema] += 1
        
        # Armazenar resultado como string separada por vírgulas
        resultado_str = ", ".join(subtemas_identificados) if subtemas_identificados else ""
        resultados_subtemas.append(resultado_str)
        
        # Log de progresso a cada 10 registros
        if (idx + 1) % 10 == 0:
            tempo_medio = sum(tempos_classificacao) / len(tempos_classificacao)
            tempo_decorrido = time.time() - tempo_inicio_processamento
            tempo_estimado = tempo_medio * (len(df_filtrado) - (idx + 1))
            logging.info(f"Progresso: {idx + 1}/{len(df_filtrado)} - Tempo médio: {tempo_medio:.2f}s/registro - ETA: {tempo_estimado/60:.1f} min")
        
        # Pequena pausa para evitar rate limiting
        time.sleep(0.1)
    
    # Adicionar resultados ao DataFrame
    df_resultado = df_filtrado.copy()
    df_resultado['SUBTEMAS_IDENTIFICADOS'] = resultados_subtemas
    df_resultado['TEXTO_ANALISADO'] = df_resultado[coluna_texto]
    df_resultado['AREA_CLASSIFICACAO'] = area
    
    # Adicionar colunas binárias para cada subtema (para análises posteriores)
    for subtema in subtemas:
        df_resultado[f'SUBTEMA_{subtema.replace(" ", "_").replace("(", "").replace(")", "").upper()}'] = [
            1 if subtema in resultado.split(", ") else 0 
            for resultado in resultados_subtemas
        ]
    
    # Criar diretório de saída se não existir
    os.makedirs(os.path.dirname(output_csv), exist_ok=True)
    
    # Salvar resultado
    df_resultado.to_csv(output_csv, index=False)
    
    # Estatísticas
    tempo_total_processamento = time.time() - tempo_inicio_processamento
    total_processados = len(resultados_subtemas)
    total_com_subtemas = sum(1 for r in resultados_subtemas if r)
    percentual_com_subtemas = (total_com_subtemas / total_processados * 100) if total_processados > 0 else 0
    tempo_medio_final = sum(tempos_classificacao) / len(tempos_classificacao) if tempos_classificacao else 0
    
    # Log final de performance
    perf_logger.info(f"\n{'='*80}")
    perf_logger.info(f"RESUMO DA SESSÃO")
    perf_logger.info(f"Tempo total de processamento: {tempo_total_processamento/60:.1f} minutos")
    perf_logger.info(f"Tempo médio por registro: {tempo_medio_final:.2f} segundos")
    perf_logger.info(f"Total de erros: {erros_count}")
    perf_logger.info(f"Taxa de sucesso: {((total_processados - erros_count) / total_processados * 100):.1f}%")
    perf_logger.info(f"{'='*80}\n")
    
    logging.info(f"Processamento concluído!")
    logging.info(f"Tempo total: {tempo_total_processamento/60:.1f} minutos")
    logging.info(f"Tempo médio por registro: {tempo_medio_final:.2f}s")
    logging.info(f"Total de registros processados: {total_processados}")
    logging.info(f"Registros com subtemas identificados: {total_com_subtemas} ({percentual_com_subtemas:.1f}%)")
    logging.info(f"Total de erros: {erros_count}")
    
    print(f"\n{'='*60}")
    print(f"PROCESSAMENTO MULTILABEL CONCLUÍDO")
    print(f"{'='*60}")
    print(f"Área analisada: {area}")
    print(f"Total de registros processados: {total_processados}")
    print(f"Registros com subtemas identificados: {total_com_subtemas} ({percentual_com_subtemas:.1f}%)")
    print(f"\nContagem por subtema:")
    print("-" * 40)
    
    for subtema, count in sorted(contadores_subtemas.items(), key=lambda x: x[1], reverse=True):
        if count > 0:
            percentual = (count / total_processados * 100)
            print(f"{subtema:35}: {count:4d} ({percentual:.1f}%)")
    
    print(f"\nArquivo salvo em: {output_csv}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Classifica relatos em múltiplos subtemas usando LLM individual por registro.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
EXEMPLOS DE USO:

1. Classificar na área de Saúde:
   python src/analisadores/analisador_multilabel.py Teor --area SAUDE

2. Classificar na área de Educação:
   python src/analisadores/analisador_multilabel.py Teor --area EDUCACAO

3. Classificar na área de Meio Ambiente:
   python src/analisadores/analisador_multilabel.py Teor --area MEIO_AMBIENTE

4. Especificar arquivo de saída:
   python src/analisadores/analisador_multilabel.py Teor --area SAUDE --output custom_output.csv

5. Especificar arquivo de entrada:
   python src/analisadores/analisador_multilabel.py relato --area EDUCACAO2 --input data/input/TE2_JanJun2025.csv --output data/output/classificacao_educacao2.csv
        """
    )
    
    parser.add_argument(
        "coluna_texto",
        type=str,
        help="Nome da coluna que contém o texto a ser classificado (ex: 'Teor')"
    )
    
    parser.add_argument(
        "--area",
        type=str,
        choices=["SAUDE", "EDUCACAO", "MEIO_AMBIENTE", "EDUCACAO2"],
        default="SAUDE",
        help="Área para classificação (padrão: SAUDE)"
    )
    
    parser.add_argument(
        "--output",
        type=str,
        default="data/output/relatos_classificados_multilabel.csv",
        help="Caminho para salvar o arquivo CSV processado"
    )
    parser.add_argument(
        "--input",
        type=str,
        default="data/input/ouvidorias.csv",
        help="Caminho do arquivo CSV de entrada (padrão: data/input/ouvidorias.csv)"
    )

    args = parser.parse_args()
    main(args.coluna_texto, args.area, args.output, args.input) 