#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para classificação individual de relatos usando LLM.
"""

import os
import time
import pandas as pd
import together
import yaml
from tqdm import tqdm
import logging

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def carregar_configuracao_yaml(caminho_config='config/config.yml'):
    """
    Carrega configurações do arquivo YAML.
    
    Args:
        caminho_config (str): Caminho para o arquivo de configuração
        
    Returns:
        dict: Dicionário com as configurações ou None se ocorrer erro
    """
    try:
        with open(caminho_config, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        return config
    except FileNotFoundError:
        logging.error(f"Erro: Arquivo de configuração '{caminho_config}' não encontrado.")
        return None
    except yaml.YAMLError as e:
        logging.error(f"Erro ao carregar configuração YAML: {e}")
        return None

def carregar_variaveis_ambiente():
    """
    Carrega variáveis de ambiente do arquivo .env.
    
    Returns:
        dict: Dicionário com as variáveis de ambiente
    """
    env_vars = {}
    try:
        with open('.env', 'r', encoding='utf-8') as file:
            for linha in file:
                linha = linha.strip()
                if linha and not linha.startswith('#') and '=' in linha:
                    chave, valor = linha.split('=', 1)
                    env_vars[chave] = valor.strip("'\"")
    except FileNotFoundError:
        logging.error("Erro: Arquivo .env não encontrado.")
    return env_vars

def classificar_relato(relato, assunto, model_name='meta-llama/Llama-3.2-3B-Instruct-Turbo', api_key=None):
    """
    Classifica um relato individual usando o modelo LLM.
    
    Args:
        relato (str): Texto do relato para classificar
        assunto (str): Assunto de classificação
        model_name (str): Nome do modelo LLM a ser utilizado
        api_key (str): Chave API da Together AI
        
    Returns:
        int: 1 se o relato refere-se ao assunto, 0 caso contrário
    """
    try:
        cliente = together.Together(api_key=api_key)
        
        prompt = f"""Você é um classificador de textos especializado em análise de documentos. Sua tarefa é determinar se um relato específico se refere ao assunto "{assunto}".

INSTRUÇÃO:
Analise cuidadosamente o relato abaixo e responda APENAS "SIM" ou "NAO" se o relato se refere especificamente ao assunto "{assunto}".

RELATO:
{relato}

ASSUNTO: {assunto}

RESPOSTA:"""

        resposta = cliente.completions.create(
            model=model_name,
            prompt=prompt,
            max_tokens=10,
            temperature=0
        )
        
        resposta_texto = resposta.choices[0].text.strip().upper()
        
        # Interpretar a resposta
        if 'SIM' in resposta_texto:
            return 1
        elif 'NAO' in resposta_texto or 'NÃO' in resposta_texto:
            return 0
        else:
            logging.warning(f"Resposta ambígua: '{resposta_texto}'. Assumindo 0.")
            return 0
            
    except Exception as e:
        logging.error(f"Erro ao classificar relato: {e}")
        return 0

def main(coluna_texto, output_csv="data/output/relatos_classificados_individual.csv"):
    """
    Função principal para processar o arquivo CSV e classificar os relatos.
    
    Args:
        coluna_texto (str): Nome da coluna que contém o texto a ser classificado
        output_csv (str): Caminho para salvar o arquivo CSV processado
    """
    
    # Arquivo de entrada fixo
    input_csv = "data/input/ouvidorias.csv"
    
    if not os.path.exists(input_csv):
        logging.error(f"Arquivo de entrada não encontrado: {input_csv}")
        print(f"Erro: Arquivo '{input_csv}' não encontrado.")
        return
    
    # Carregar variáveis de ambiente
    env_vars = carregar_variaveis_ambiente()
    
    # Verificar variável de ambiente para API key
    api_key = env_vars.get("TOGETHER_API_KEY")
    if not api_key:
        logging.error("ERRO: Variável de ambiente TOGETHER_API_KEY não encontrada no arquivo .env.")
        print("Configure a chave API no arquivo .env: TOGETHER_API_KEY='sua-chave-api'")
        return
    
    # Carregar configurações
    config = carregar_configuracao_yaml()
    if not config:
        return
    
    # Exibir configuração carregada para depuração
    logging.info(f"Configuração carregada: {config}")
    
    # Obter o modelo e assunto
    model_name = config.get('MODEL')
    assunto = config.get('ASSUNTO')
    
    logging.info(f"Modelo encontrado: {model_name}")
    logging.info(f"Assunto encontrado: {assunto}")
    
    if not model_name or not assunto:
        logging.error("Erro: Configurações incompletas no arquivo de configuração")
        print(f"Necessário definir ASSUNTO e MODEL no arquivo config/config.yml.")
        return
    
    logging.info(f"Usando modelo: {model_name} para classificar coluna '{coluna_texto}' sobre '{assunto}'")
    
    # Carregar o CSV
    try:
        df = pd.read_csv(input_csv)
        logging.info(f"Arquivo carregado com {len(df)} registros.")
    except Exception as e:
        logging.error(f"Erro ao carregar arquivo CSV: {e}")
        return
    
    # Verificar se a coluna especificada existe
    if coluna_texto not in df.columns:
        logging.error(f"Coluna '{coluna_texto}' não encontrada no arquivo.")
        print(f"Colunas disponíveis: {', '.join(df.columns)}")
        return
    
    # Filtrar registros sem texto na coluna especificada
    df_filtrado = df.dropna(subset=[coluna_texto])
    df_filtrado = df_filtrado[df_filtrado[coluna_texto].str.strip() != '']
    
    logging.info(f"Processando {len(df_filtrado)} registros com texto válido na coluna '{coluna_texto}'.")
    
    # Aplicar classificação individual
    resultados = []
    for idx, row in tqdm(df_filtrado.iterrows(), total=len(df_filtrado), desc="Classificando relatos"):
        texto = str(row[coluna_texto])
        resultado = classificar_relato(texto, assunto, model_name, api_key)
        resultados.append(resultado)
        
        # Pequena pausa para evitar rate limiting
        time.sleep(0.1)
    
    # Adicionar resultados ao DataFrame
    df_resultado = df_filtrado.copy()
    df_resultado['REFERE_ASSUNTO'] = resultados
    df_resultado['TEXTO_ANALISADO'] = df_resultado[coluna_texto]
    
    # Criar diretório de saída se não existir
    os.makedirs(os.path.dirname(output_csv), exist_ok=True)
    
    # Salvar resultado
    df_resultado.to_csv(output_csv, index=False)
    
    # Estatísticas
    total_positivos = sum(resultados)
    total_processados = len(resultados)
    percentual = (total_positivos / total_processados * 100) if total_processados > 0 else 0
    
    logging.info(f"Processamento concluído!")
    logging.info(f"Total de registros processados: {total_processados}")
    logging.info(f"Casos classificados como positivos: {total_positivos} ({percentual:.1f}%)")
    logging.info(f"Arquivo salvo em: {output_csv}")
    
    print(f"\n{'='*50}")
    print(f"PROCESSAMENTO CONCLUÍDO")
    print(f"{'='*50}")
    print(f"Total de registros processados: {total_processados}")
    print(f"Casos classificados como positivos: {total_positivos} ({percentual:.1f}%)")
    print(f"Arquivo salvo em: {output_csv}")

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Processa o arquivo ouvidorias.csv para classificar textos com LLM.")
    parser.add_argument(
        "coluna_texto",
        type=str,
        help="Nome da coluna que contém o texto a ser classificado (ex: 'Teor')"
    )
    parser.add_argument(
        "--output_csv",
        type=str,
        default="data/output/relatos_classificados_individual.csv",
        help="Caminho para salvar o arquivo CSV processado (padrão: data/output/relatos_classificados_individual.csv)"
    )

    args = parser.parse_args()
    main(args.coluna_texto, args.output_csv) 