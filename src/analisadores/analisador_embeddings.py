import pandas as pd
import yaml
import os
from dotenv import load_dotenv
import logging
from typing import Dict, Any, List, Tuple
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import time
from tqdm import tqdm

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def carregar_configuracao(config_path: str = "config/config.yml") -> Dict[str, Any]:
    """
    Carrega as configurações do arquivo YAML.

    Args:
        config_path (str): Caminho para o arquivo de configuração.

    Returns:
        Dict[str, Any]: Dicionário com as configurações.
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logging.info(f"Configuração carregada de {config_path}: {config}")
        if not config.get("ASSUNTO"):
            logging.error("Chave 'ASSUNTO' não encontrada no arquivo de configuração")
            raise ValueError("Chave 'ASSUNTO' não encontrada no arquivo de configuração")
        return config
    except FileNotFoundError:
        logging.error(f"Arquivo de configuração {config_path} não encontrado.")
        # Tentar localização alternativa (para retrocompatibilidade)
        alt_path = "config.yml"
        logging.info(f"Tentando localização alternativa: {alt_path}")
        try:
            with open(alt_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logging.info(f"Configuração carregada de {alt_path}: {config}")
            return config
        except FileNotFoundError:
            logging.error(f"Arquivo de configuração também não encontrado em: {alt_path}")
            raise
    except yaml.YAMLError as e:
        logging.error(f"Erro ao parsear o arquivo de configuração {config_path}: {e}")
        raise
    except ValueError as e:
        raise

def gerar_termos_referencia(assunto: str) -> List[str]:
    """
    Gera uma lista de termos de referência para o assunto especificado.
    
    Args:
        assunto (str): O assunto para gerar termos (ex: "Oftalmologia").
        
    Returns:
        List[str]: Lista de termos relacionados ao assunto.
    """
    termos = {
        "Oftalmologia": [
            "problema de visão", 
            "oftalmologista", 
            "olho", 
            "vista", 
            "enxergar", 
            "óculos", 
            "lente", 
            "catarata", 
            "glaucoma", 
            "cirurgia ocular", 
            "conjuntivite", 
            "retina", 
            "córnea",
            "estrabismo",
            "miopia",
            "hipermetropia",
            "astigmatismo",
            "pressão ocular"
        ]
    }
    
    # Se o assunto não estiver no dicionário, usar apenas o próprio assunto
    return termos.get(assunto, [assunto])

def processar_embeddings(relatos: List[str], assunto: str, modelo_embeddings: str = "all-MiniLM-L6-v2", limiar: float = 0.2) -> List[int]:
    """
    Processa relatos usando embeddings e similaridade de cosseno.

    Args:
        relatos (List[str]): Lista de relatos para processar.
        assunto (str): Assunto para verificar nos relatos.
        modelo_embeddings (str): Nome do modelo de embeddings a usar.
        limiar (float): Limiar de similaridade para considerar um relato relacionado.

    Returns:
        List[int]: Lista de resultados (1 = referente ao assunto, 0 = não referente).
    """
    logging.info(f"Carregando modelo de embeddings '{modelo_embeddings}'...")
    try:
        modelo = SentenceTransformer(modelo_embeddings)
    except Exception as e:
        logging.error(f"Erro ao carregar o modelo de embeddings: {e}")
        raise
    
    # Gerar termos de referência para o assunto
    termos_referencia = gerar_termos_referencia(assunto)
    logging.info(f"Termos de referência para '{assunto}': {termos_referencia}")
    
    # Gerar embeddings para os termos de referência
    logging.info("Gerando embeddings para os termos de referência...")
    embeddings_referencia = modelo.encode(termos_referencia, show_progress_bar=False)
    
    resultados = []
    batch_size = 32  # Tamanho do lote para gerar embeddings
    
    logging.info(f"Gerando embeddings e calculando similaridade para {len(relatos)} relatos...")
    
    # Processar em lotes para economizar memória
    for i in tqdm(range(0, len(relatos), batch_size)):
        lote_atual = relatos[i:i+batch_size]
        
        # Gerar embeddings para o lote
        embeddings_lote = modelo.encode(lote_atual, show_progress_bar=False)
        
        # Para cada relato no lote
        for j, embedding_relato in enumerate(embeddings_lote):
            # Calcular similaridade com cada termo de referência
            similaridades = cosine_similarity([embedding_relato], embeddings_referencia)[0]
            
            # Pegar a maior similaridade
            max_similaridade = np.max(similaridades)
            termo_mais_similar = termos_referencia[np.argmax(similaridades)]
            
            # Verificar se a similaridade máxima está acima do limiar
            if max_similaridade >= limiar:
                resultados.append(1)
                logging.debug(f"Relato {i+j+1} é relevante (similaridade: {max_similaridade:.4f}, termo: '{termo_mais_similar}')")
            else:
                resultados.append(0)
                logging.debug(f"Relato {i+j+1} não é relevante (similaridade: {max_similaridade:.4f})")
    
    return resultados

def processar_csv(arquivo_entrada: str, arquivo_saida: str, config: Dict[str, Any], limiar_similaridade: float = 0.2) -> None:
    """
    Lê um arquivo CSV, analisa a coluna 'RELATO' com embeddings e similaridade, e salva o resultado.

    Args:
        arquivo_entrada (str): Caminho do arquivo CSV de entrada.
        arquivo_saida (str): Caminho para salvar o CSV processado.
        config (Dict[str, Any]): Dicionário com as configurações (ASSUNTO).
        limiar_similaridade (float): Limiar de similaridade para considerar um relato como relevante.
    """
    assunto = config["ASSUNTO"]

    try:
        logging.info(f"Lendo o arquivo CSV de entrada: {arquivo_entrada}")
        df = pd.read_csv(arquivo_entrada, header=None, names=['RELATO'], quotechar='"', escapechar='\\', on_bad_lines='warn')
    except FileNotFoundError:
        logging.error(f"Arquivo CSV de entrada '{arquivo_entrada}' não encontrado.")
        raise
    except Exception as e:
        logging.error(f"Erro ao ler o arquivo CSV '{arquivo_entrada}': {e}")
        raise

    if "RELATO" not in df.columns:
        logging.error(f"A coluna 'RELATO' não foi encontrada no arquivo '{arquivo_entrada}'.")
        raise ValueError(f"Coluna 'RELATO' não encontrada em {arquivo_entrada}.")

    logging.info(f"Iniciando processamento com embeddings e similaridade de {len(df)} relatos.")
    
    # Converter a coluna RELATO para uma lista
    relatos = df["RELATO"].astype(str).tolist()
    
    # Processar os relatos com embeddings
    start_time = time.time()
    resultados = processar_embeddings(relatos, assunto, limiar=limiar_similaridade)
    processing_time = time.time() - start_time
    
    # Garantir que temos o mesmo número de resultados que de relatos
    if len(resultados) < len(relatos):
        logging.warning(f"Número de resultados ({len(resultados)}) menor que o número de relatos ({len(relatos)}). Preenchendo com zeros.")
        resultados.extend([0] * (len(relatos) - len(resultados)))
    elif len(resultados) > len(relatos):
        logging.warning(f"Número de resultados ({len(resultados)}) maior que o número de relatos ({len(relatos)}). Truncando.")
        resultados = resultados[:len(relatos)]
    
    df["REFERE_ASSUNTO"] = resultados
    logging.info(f"Processamento concluído em {processing_time:.2f} segundos.")
    
    # Calcular estatísticas
    total_positivos = sum(resultados)
    total_registros = len(resultados)
    porcentagem = (total_positivos / total_registros) * 100 if total_registros > 0 else 0
    
    logging.info(f"Estatísticas: {total_positivos} de {total_registros} relatos ({porcentagem:.2f}%) referem-se a '{assunto}'.")
    logging.info(f"Limiar de similaridade utilizado: {limiar_similaridade}")

    try:
        df.to_csv(arquivo_saida, index=False, encoding='utf-8')
        logging.info(f"Arquivo CSV processado salvo em: {arquivo_saida}")
    except Exception as e:
        logging.error(f"Erro ao salvar o arquivo CSV em '{arquivo_saida}': {e}")
        raise

def main(csv_input_path: str, csv_output_path: str, limiar_similaridade: float = 0.2) -> None:
    """
    Função principal para executar o pipeline de análise de relatos com embeddings.

    Args:
        csv_input_path (str): Caminho para o arquivo CSV de entrada.
        csv_output_path (str): Caminho para salvar o arquivo CSV de saída.
        limiar_similaridade (float): Limiar de similaridade para considerar um relato como relevante.
    """
    logging.info(f"Iniciando o processo de análise de relatos com embeddings (limiar: {limiar_similaridade}).")
    load_dotenv()  # Carregar variáveis de ambiente, se necessário

    try:
        config = carregar_configuracao()
        processar_csv(csv_input_path, csv_output_path, config, limiar_similaridade)
        logging.info("Pipeline de análise de relatos com embeddings concluído com sucesso.")
    except Exception as e:
        logging.error(f"Ocorreu um erro no pipeline: {e}")

if __name__ == "__main__":
    pass 