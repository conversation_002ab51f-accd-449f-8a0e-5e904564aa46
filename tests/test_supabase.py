#!/usr/bin/env python3
"""
Script to test Supabase connection and setup.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from supabase import create_client, Client
from dotenv import load_dotenv

def test_supabase_connection():
    """Test connection to Supabase."""
    print("🔗 Testing Supabase connection...")
    
    # Load environment variables
    load_dotenv()
    
    # Get Supabase credentials
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_KEY")
    
    if not url or not key:
        print("❌ Supabase credentials not found in .env file")
        return False
    
    print(f"📍 URL: {url}")
    print(f"🔑 Key: {key[:20]}...")
    
    try:
        # Create Supabase client
        supabase: Client = create_client(url, key)
        
        # Test connection by trying to access auth
        response = supabase.auth.get_session()
        print("✅ Supabase client created successfully")
        
        # Test database access (this might fail if tables don't exist yet)
        try:
            result = supabase.table("files").select("count").execute()
            print("✅ Database connection successful")
        except Exception as db_error:
            print(f"⚠️  Database tables not found (expected): {db_error}")
            print("   This is normal if you haven't run the setup SQL yet")
        
        return True
        
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return False

def test_storage_access():
    """Test Supabase Storage access."""
    print("\n📦 Testing Supabase Storage...")
    
    # Load environment variables
    load_dotenv()
    
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_KEY")
    
    try:
        supabase: Client = create_client(url, key)
        
        # List storage buckets
        buckets = supabase.storage.list_buckets()
        print(f"📁 Available buckets: {[bucket.name for bucket in buckets]}")
        
        # Check if our bucket exists
        bucket_name = "simple-class-files"
        bucket_exists = any(bucket.name == bucket_name for bucket in buckets)
        
        if bucket_exists:
            print(f"✅ Bucket '{bucket_name}' exists")
        else:
            print(f"⚠️  Bucket '{bucket_name}' not found")
            print("   You need to create it in the Supabase dashboard")
        
        return bucket_exists
        
    except Exception as e:
        print(f"❌ Storage access failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Supabase Connection Test")
    print("=" * 40)
    
    # Test basic connection
    connection_ok = test_supabase_connection()
    
    # Test storage access
    storage_ok = test_storage_access()
    
    print("\n" + "=" * 40)
    print("📊 Test Results:")
    print(f"   Connection: {'✅ OK' if connection_ok else '❌ FAILED'}")
    print(f"   Storage: {'✅ OK' if storage_ok else '⚠️  NEEDS SETUP'}")
    
    if connection_ok:
        print("\n🎉 Supabase is ready to use!")
        if not storage_ok:
            print("\n📝 Next steps:")
            print("   1. Go to your Supabase dashboard")
            print("   2. Navigate to Storage")
            print("   3. Create a bucket named 'simple-class-files'")
            print("   4. Set it as private (not public)")
    else:
        print("\n❌ Please check your Supabase credentials")

if __name__ == "__main__":
    main()
