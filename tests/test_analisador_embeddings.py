import numpy as np
import types
import pytest
from src.analisadores import analisador_embeddings as ae

class DummyModel:
    def encode(self, texts, show_progress_bar=False):
        if isinstance(texts, list):
            return [self.encode(t) for t in texts]
        # reference terms and "good" relatos share the same vector
        if 'bad' in texts:
            return np.array([0.0, 1.0])
        return np.array([1.0, 0.0])

def test_gerar_termos_referencia_default():
    assert ae.gerar_termos_referencia('X') == ['X']


def test_processar_embeddings(monkeypatch):
    monkeypatch.setattr(ae, 'SentenceTransformer', lambda *a, **k: DummyModel())
    relatos = ['good relato', 'bad relato']
    res = ae.processar_embeddings(relatos, 'Qualquer', limiar=0.5)
    assert res == [1, 0]
