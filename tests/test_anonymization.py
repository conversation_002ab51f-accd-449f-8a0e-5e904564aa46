"""
Tests for anonymization endpoints.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from httpx import AsyncClient

from tests.conftest import ANONYMIZATION_ENDPOINTS


class TestAnonymizationEndpoints:
    """Test anonymization endpoints."""

    def test_anonymize_text_success(self, client: TestClient, auth_headers: dict, sample_text_for_anonymization: str):
        """Test successful text anonymization."""
        response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": sample_text_for_anonymization,
                "language": "pt"
            }
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "anonymized_text" in result
        assert "entities_found" in result
        assert "processing_time" in result
        
        # Check that PII was actually anonymized
        anonymized = result["anonymized_text"]
        assert "<PERSON>" not in anonymized
        assert "123.456.789-00" not in anonymized
        assert "<EMAIL>" not in anonymized
        
        # Check entities found
        entities = result["entities_found"]
        assert isinstance(entities, list)
        assert len(entities) > 0

    def test_anonymize_text_no_auth(self, client: TestClient, sample_text_for_anonymization: str):
        """Test text anonymization without authentication."""
        response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/text",
            json={
                "text": sample_text_for_anonymization,
                "language": "pt"
            }
        )
        
        assert response.status_code == 401

    def test_anonymize_text_empty_text(self, client: TestClient, auth_headers: dict):
        """Test anonymization with empty text."""
        response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": "",
                "language": "pt"
            }
        )
        
        assert response.status_code == 400
        result = response.json()
        assert "detail" in result

    def test_anonymize_text_invalid_language(self, client: TestClient, auth_headers: dict):
        """Test anonymization with invalid language."""
        response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": "Some text",
                "language": "invalid"
            }
        )
        
        assert response.status_code == 400

    def test_anonymize_text_custom_entities(self, client: TestClient, auth_headers: dict):
        """Test anonymization with custom entity selection."""
        text = "João Silva mora na Rua das Flores, 123. Telefone: (21) 99999-9999"
        
        response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": text,
                "language": "pt",
                "entities": ["PERSON"]  # Only anonymize person names
            }
        )
        
        assert response.status_code == 200
        result = response.json()
        
        anonymized = result["anonymized_text"]
        # Person name should be anonymized
        assert "João Silva" not in anonymized
        # But address and phone might still be there (depending on implementation)

    def test_anonymize_text_different_languages(self, client: TestClient, auth_headers: dict):
        """Test anonymization with different languages."""
        texts = {
            "pt": "João Silva mora no Rio de Janeiro.",
            "en": "John Smith lives in New York."
        }
        
        for lang, text in texts.items():
            response = client.post(
                f"{ANONYMIZATION_ENDPOINTS}/text",
                headers=auth_headers,
                json={
                    "text": text,
                    "language": lang
                }
            )
            
            assert response.status_code == 200
            result = response.json()
            assert "anonymized_text" in result

    def test_anonymize_pdf_success(self, client: TestClient, auth_headers: dict):
        """Test successful PDF anonymization."""
        # Create a simple PDF-like content for testing
        pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n"
        
        files = {
            'file': ('test.pdf', pdf_content, 'application/pdf')
        }
        data = {
            'language': 'pt',
            'entities': '["PERSON", "CPF", "EMAIL"]'
        }
        
        response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/pdf",
            headers=auth_headers,
            files=files,
            data=data
        )
        
        # This might fail if PDF processing is not fully implemented
        assert response.status_code in [200, 501]  # 501 = Not Implemented

    def test_anonymize_pdf_no_auth(self, client: TestClient):
        """Test PDF anonymization without authentication."""
        pdf_content = b"%PDF-1.4\ntest content"
        files = {
            'file': ('test.pdf', pdf_content, 'application/pdf')
        }
        
        response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/pdf",
            files=files
        )
        
        assert response.status_code == 401

    def test_anonymize_pdf_invalid_file(self, client: TestClient, auth_headers: dict):
        """Test PDF anonymization with invalid file."""
        files = {
            'file': ('test.txt', 'Not a PDF', 'text/plain')
        }
        
        response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/pdf",
            headers=auth_headers,
            files=files
        )
        
        assert response.status_code == 400

    def test_anonymize_large_text(self, client: TestClient, auth_headers: dict):
        """Test anonymization with large text input."""
        # Create a large text (but within reasonable limits)
        large_text = "João Silva " * 1000  # Repeat name many times
        
        response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": large_text,
                "language": "pt"
            }
        )
        
        assert response.status_code == 200
        result = response.json()
        
        # Should handle large text efficiently
        assert "processing_time" in result
        assert result["processing_time"] < 30.0  # Should complete within 30 seconds

    def test_anonymize_text_with_no_pii(self, client: TestClient, auth_headers: dict):
        """Test anonymization with text containing no PII."""
        clean_text = "Este é um texto sem informações pessoais. Apenas conteúdo genérico."
        
        response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": clean_text,
                "language": "pt"
            }
        )
        
        assert response.status_code == 200
        result = response.json()
        
        # Text should remain mostly unchanged
        assert result["anonymized_text"] == clean_text or len(result["entities_found"]) == 0

    def test_anonymize_text_special_characters(self, client: TestClient, auth_headers: dict):
        """Test anonymization with special characters and encoding."""
        text_with_special = "João da Silva (CPF: 123.456.789-00) — email: joã**************"
        
        response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": text_with_special,
                "language": "pt"
            }
        )
        
        assert response.status_code == 200
        result = response.json()
        assert "anonymized_text" in result

    @pytest.mark.asyncio
    async def test_anonymize_text_async(self, async_client: AsyncClient, auth_headers: dict):
        """Test text anonymization with async client."""
        response = await async_client.post(
            f"{ANONYMIZATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": "João Silva, CPF 123.456.789-00",
                "language": "pt"
            }
        )
        
        assert response.status_code == 200

    def test_anonymize_text_validation_errors(self, client: TestClient, auth_headers: dict):
        """Test various validation errors."""
        # Missing required fields
        response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={}
        )
        assert response.status_code == 422
        
        # Invalid entity types
        response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": "Some text",
                "language": "pt",
                "entities": ["INVALID_ENTITY"]
            }
        )
        assert response.status_code in [400, 422]

    def test_anonymize_text_performance(self, client: TestClient, auth_headers: dict):
        """Test anonymization performance with various text sizes."""
        text_sizes = [100, 1000, 5000]  # Different text lengths
        
        for size in text_sizes:
            text = "João Silva, CPF 123.456.789-00. " * (size // 30)
            
            response = client.post(
                f"{ANONYMIZATION_ENDPOINTS}/text",
                headers=auth_headers,
                json={
                    "text": text,
                    "language": "pt"
                }
            )
            
            assert response.status_code == 200
            result = response.json()
            
            # Performance should scale reasonably
            processing_time = result.get("processing_time", 0)
            assert processing_time < 60.0  # Should complete within 1 minute

    def test_anonymize_concurrent_requests(self, client: TestClient, auth_headers: dict):
        """Test handling of concurrent anonymization requests."""
        import threading
        
        results = []
        
        def anonymize_text():
            response = client.post(
                f"{ANONYMIZATION_ENDPOINTS}/text",
                headers=auth_headers,
                json={
                    "text": "João Silva, CPF 123.456.789-00",
                    "language": "pt"
                }
            )
            results.append(response.status_code)
        
        # Create multiple threads
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=anonymize_text)
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        # All requests should succeed
        assert all(status == 200 for status in results)
