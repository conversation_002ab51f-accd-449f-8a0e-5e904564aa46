#!/usr/bin/env python3
"""
Teste simples de classificação sem otimização.

Este script testa apenas a classificação básica sem o sistema de otimização
para verificar se o problema está no filtro ou na classificação em si.
"""

import asyncio
import sys
from pathlib import Path

# Adicionar projeto root ao path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.api.services.optimized_batch_processing_service import OptimizedBatchProcessingService
from src.api.models.batch_processing import BatchProcessingRequest

async def test_simple_classification():
    """Teste simples sem otimização."""
    
    print("🔧 TESTE SIMPLES SEM OTIMIZAÇÃO")
    print("=" * 60)
    
    # Criar diretório temporário com o arquivo
    import tempfile
    import shutil
    
    test_dir = Path(tempfile.mkdtemp())
    source_file = Path("data/input/relatorio_teste_educacao.pdf")
    dest_file = test_dir / "relatorio_teste_educacao.pdf"
    shutil.copy2(source_file, dest_file)
    
    try:
        # Serviço SEM otimização
        service = OptimizedBatchProcessingService(
            enable_token_optimization=False,  # DESABILITAR otimização
            max_workers=1
        )
        
        print("✅ Serviço criado SEM otimização")
        
        # Request de processamento
        request = BatchProcessingRequest(
            folder_path=str(test_dir),
            area="EDUCACAO",
            chunk_size=1000,
            overlap=200,
            parallel_workers=1
        )
        
        print(f"📁 Processando: {dest_file}")
        print(f"🎯 Área: EDUCACAO")
        
        # Iniciar processamento
        job_id = await service.start_batch_processing(request=request, user_id="test-user")
        print(f"🆔 Job ID: {job_id}")
        
        # Aguardar completar
        while True:
            status = service.get_job_status(job_id)
            if status and status.status.value in ['completed', 'failed', 'cancelled']:
                break
            await asyncio.sleep(0.1)
        
        print(f"✅ Status: {status.status.value}")
        
        # Obter resultados
        results = service.get_job_results(job_id)
        if results and results.pdf_results:
            pdf_result = results.pdf_results[0]
            print(f"📄 Arquivo: {pdf_result.filename}")
            print(f"📑 Chunks: {pdf_result.total_chunks}")
            print(f"🎯 Subtemas: {pdf_result.subtemas_finais}")
            
            if pdf_result.subtemas_finais:
                print("✅ CLASSIFICAÇÃO FUNCIONOU!")
                for subtema in pdf_result.subtemas_finais:
                    print(f"   - {subtema}")
            else:
                print("❌ Nenhum subtema encontrado")
                if pdf_result.error:
                    print(f"   Erro: {pdf_result.error}")
        else:
            print("❌ Nenhum resultado obtido")
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Limpar
        if test_dir.exists():
            shutil.rmtree(test_dir)
        print(f"🧹 Diretório limpo: {test_dir}")

if __name__ == "__main__":
    asyncio.run(test_simple_classification())