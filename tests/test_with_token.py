#!/usr/bin/env python3
"""
Auto-generated test script with valid JWT token.
"""

import requests
import json
from pathlib import Path

# Valid JWT token for testing
TEST_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************.YT6HI5T3rSrIjUvb_p0W1WjOQ_MVA2dqHvkHKv7Fyg4"

def test_with_valid_token():
    """Test API endpoints with valid token."""
    base_url = "http://localhost:8000"
    
    headers = {
        "Authorization": f"Bearer {TEST_TOKEN}"
    }
    
    print("🧪 Testing with valid JWT token...")
    
    # Test file listing
    try:
        response = requests.get(
            f"{base_url}/api/v1/files/",
            headers=headers,
            timeout=10
        )
        
        print(f"📋 File listing: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   Files: {result.get('total_count', 0)}")
        else:
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_with_valid_token()
