"""
Tests for CrewAI API endpoints in Simple Class.

This module tests the FastAPI integration with CrewAI workflows
to ensure proper API functionality and error handling.
"""

import pytest
import json
import io
from unittest.mock import Mock, patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import UploadFile

# Import API components
try:
    from src.api.main import app
    from src.api.routers.crew_router import get_processing_crew, _job_status
    API_AVAILABLE = True
except ImportError:
    app = None
    get_processing_crew = None
    _job_status = None
    API_AVAILABLE = False


@pytest.mark.skipif(not API_AVAILABLE, reason="API not available")
class TestCrewAPIEndpoints:
    """Test cases for CrewAI API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_processing_crew(self):
        """Mock ProcessingCrew for testing."""
        mock_crew = Mock()
        mock_crew.get_available_agents.return_value = {
            "anonymization": True,
            "classification": True,
            "report": True
        }
        mock_crew.get_supported_workflows.return_value = [
            "full_pipeline", "anonymize_classify", "classify_report",
            "anonymize_only", "classify_only", "report_only"
        ]
        mock_crew.kickoff.return_value = {
            "success": True,
            "execution_id": "test_execution_123",
            "workflow_type": "full_pipeline",
            "execution_time": 2.5,
            "results": {
                "anonymization": {"success": True, "anonymized_text": "Test <PERSON>"},
                "classification": {"success": True, "subtemas_identificados": ["Infraestrutura"]},
                "report": {"success": True, "report_content": "Test report"}
            },
            "tasks_completed": 3,
            "total_tasks": 3
        }
        return mock_crew
    
    def test_health_check_endpoint(self, client, mock_processing_crew):
        """Test health check endpoint."""
        with patch('src.api.routers.crew_router.get_processing_crew', return_value=mock_processing_crew):
            response = client.get("/api/v1/crew/health")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert data["crewai_available"] is True
            assert "agents" in data
            assert "supported_workflows" in data
    
    def test_health_check_unhealthy(self, client):
        """Test health check when CrewAI is not available."""
        with patch('src.api.routers.crew_router.get_processing_crew', side_effect=Exception("CrewAI not available")):
            response = client.get("/api/v1/crew/health")
            
            assert response.status_code == 503
            data = response.json()
            assert data["status"] == "unhealthy"
            assert "error" in data
    
    def test_get_workflows_endpoint(self, client):
        """Test get workflows endpoint."""
        response = client.get("/api/v1/crew/workflows")
        
        assert response.status_code == 200
        data = response.json()
        assert "workflows" in data
        assert "full_pipeline" in data["workflows"]
        assert "anonymize_only" in data["workflows"]
        
        # Check workflow structure
        full_pipeline = data["workflows"]["full_pipeline"]
        assert "description" in full_pipeline
        assert "agents" in full_pipeline
        assert "input_required" in full_pipeline
        assert "output" in full_pipeline
    
    def test_process_workflow_text_input(self, client, mock_processing_crew):
        """Test processing workflow with text input."""
        with patch('src.api.routers.crew_router.get_processing_crew', return_value=mock_processing_crew):
            request_data = {
                "workflow_type": "full_pipeline",
                "text": "Problema com infraestrutura da escola",
                "area": "EDUCACAO",
                "language": "pt"
            }
            
            response = client.post("/api/v1/crew/process", json=request_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["execution_id"] == "test_execution_123"
            assert data["workflow_type"] == "full_pipeline"
            assert data["execution_time"] == 2.5
            assert "results" in data
    
    def test_process_workflow_invalid_type(self, client, mock_processing_crew):
        """Test processing workflow with invalid workflow type."""
        with patch('src.api.routers.crew_router.get_processing_crew', return_value=mock_processing_crew):
            request_data = {
                "workflow_type": "invalid_workflow",
                "text": "Test text"
            }
            
            response = client.post("/api/v1/crew/process", json=request_data)
            
            assert response.status_code == 400
            assert "Unsupported workflow type" in response.json()["detail"]
    
    def test_process_workflow_missing_text(self, client, mock_processing_crew):
        """Test processing workflow without required text input."""
        with patch('src.api.routers.crew_router.get_processing_crew', return_value=mock_processing_crew):
            request_data = {
                "workflow_type": "anonymize_only"
                # Missing text field
            }
            
            response = client.post("/api/v1/crew/process", json=request_data)
            
            assert response.status_code == 400
            assert "Text input required" in response.json()["detail"]
    
    def test_process_workflow_crewai_error(self, client, mock_processing_crew):
        """Test processing workflow when CrewAI execution fails."""
        mock_processing_crew.kickoff.return_value = {
            "success": False,
            "error": "Execution failed",
            "execution_id": "failed_execution_123"
        }
        
        with patch('src.api.routers.crew_router.get_processing_crew', return_value=mock_processing_crew):
            request_data = {
                "workflow_type": "full_pipeline",
                "text": "Test text",
                "area": "EDUCACAO"
            }
            
            response = client.post("/api/v1/crew/process", json=request_data)
            
            assert response.status_code == 200  # API call succeeds but workflow fails
            data = response.json()
            assert data["success"] is False
            assert data["error"] == "Execution failed"
    
    def test_process_file_csv_small(self, client, mock_processing_crew):
        """Test processing small CSV file."""
        # Create test CSV content
        csv_content = """Teor,Area
Problema com infraestrutura escolar,EDUCACAO
Falta de transporte,EDUCACAO
Bullying na escola,EDUCACAO"""
        
        csv_file = io.StringIO(csv_content)
        
        with patch('src.api.routers.crew_router.get_processing_crew', return_value=mock_processing_crew):
            files = {"file": ("test.csv", csv_file, "text/csv")}
            data = {
                "workflow_type": "full_pipeline",
                "area": "EDUCACAO",
                "text_column": "Teor"
            }
            
            response = client.post("/api/v1/crew/process-file", files=files, data=data)
            
            assert response.status_code == 200
            result = response.json()
            assert "job_id" in result
            assert result["status"] in ["completed", "accepted"]
    
    def test_process_file_invalid_format(self, client):
        """Test processing file with invalid format."""
        # Create test file with wrong extension
        txt_content = "This is not a CSV file"
        txt_file = io.StringIO(txt_content)
        
        files = {"file": ("test.txt", txt_file, "text/plain")}
        data = {"workflow_type": "full_pipeline"}
        
        response = client.post("/api/v1/crew/process-file", files=files, data=data)
        
        assert response.status_code == 400
        assert "Only CSV files are supported" in response.json()["detail"]
    
    def test_process_file_missing_column(self, client):
        """Test processing CSV file with missing text column."""
        # Create CSV without the expected column
        csv_content = """Wrong_Column,Area
Some text,EDUCACAO"""
        
        csv_file = io.StringIO(csv_content)
        
        files = {"file": ("test.csv", csv_file, "text/csv")}
        data = {
            "workflow_type": "full_pipeline",
            "text_column": "Teor"  # This column doesn't exist
        }
        
        response = client.post("/api/v1/crew/process-file", files=files, data=data)
        
        assert response.status_code == 400
        assert "Column 'Teor' not found" in response.json()["detail"]
    
    def test_job_status_endpoint(self, client):
        """Test job status endpoint."""
        # Mock job status
        job_id = "test_job_123"
        _job_status[job_id] = {
            "job_id": job_id,
            "status": "completed",
            "progress": 1.0,
            "result": {"success": True},
            "error": None,
            "created_at": "2024-01-01T10:00:00",
            "updated_at": "2024-01-01T10:05:00"
        }
        
        response = client.get(f"/api/v1/crew/jobs/{job_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["job_id"] == job_id
        assert data["status"] == "completed"
        assert data["progress"] == 1.0
        
        # Clean up
        del _job_status[job_id]
    
    def test_job_status_not_found(self, client):
        """Test job status endpoint with non-existent job."""
        response = client.get("/api/v1/crew/jobs/nonexistent_job")
        
        assert response.status_code == 404
        assert "Job nonexistent_job not found" in response.json()["detail"]
    
    def test_list_jobs_endpoint(self, client):
        """Test list jobs endpoint."""
        # Mock some jobs
        _job_status["job1"] = {
            "job_id": "job1",
            "status": "completed",
            "created_at": "2024-01-01T10:00:00"
        }
        _job_status["job2"] = {
            "job_id": "job2", 
            "status": "running",
            "created_at": "2024-01-01T11:00:00"
        }
        
        response = client.get("/api/v1/crew/jobs")
        
        assert response.status_code == 200
        data = response.json()
        assert "jobs" in data
        assert "total" in data
        assert len(data["jobs"]) >= 2
        
        # Clean up
        del _job_status["job1"]
        del _job_status["job2"]
    
    def test_delete_job_endpoint(self, client):
        """Test delete job endpoint."""
        # Create a job to delete
        job_id = "job_to_delete"
        _job_status[job_id] = {
            "job_id": job_id,
            "status": "completed"
        }
        
        response = client.delete(f"/api/v1/crew/jobs/{job_id}")
        
        assert response.status_code == 200
        assert f"Job {job_id} deleted successfully" in response.json()["message"]
        assert job_id not in _job_status
    
    def test_delete_job_not_found(self, client):
        """Test delete job endpoint with non-existent job."""
        response = client.delete("/api/v1/crew/jobs/nonexistent_job")
        
        assert response.status_code == 404
        assert "Job nonexistent_job not found" in response.json()["detail"]


@pytest.mark.skipif(not API_AVAILABLE, reason="API not available")
class TestCrewAPIIntegration:
    """Integration tests for CrewAI API."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_api_documentation_includes_crew_endpoints(self, client):
        """Test that API documentation includes CrewAI endpoints."""
        response = client.get("/openapi.json")
        
        assert response.status_code == 200
        openapi_spec = response.json()
        
        # Check that CrewAI endpoints are included
        paths = openapi_spec.get("paths", {})
        crew_endpoints = [path for path in paths.keys() if "/crew/" in path]
        
        assert len(crew_endpoints) > 0
        assert "/api/v1/crew/health" in paths
        assert "/api/v1/crew/process" in paths
        assert "/api/v1/crew/workflows" in paths
    
    def test_crew_endpoints_in_docs(self, client):
        """Test that CrewAI endpoints appear in documentation."""
        response = client.get("/docs")
        assert response.status_code == 200
        
        # The docs page should load successfully
        # In a real test, you might parse the HTML to check for specific content
    
    def test_crew_router_tags(self, client):
        """Test that CrewAI endpoints have correct tags."""
        response = client.get("/openapi.json")
        openapi_spec = response.json()
        
        # Check that crew endpoints have the correct tag
        crew_process_endpoint = openapi_spec["paths"]["/api/v1/crew/process"]["post"]
        assert "🤖 CrewAI Workflows" in crew_process_endpoint["tags"]


if __name__ == "__main__":
    pytest.main([__file__])
