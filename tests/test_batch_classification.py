"""
Tests for batch classification functionality in ClassificationService.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from typing import List

from src.api.services.classification_service import ClassificationService, BatchClassificationConfig
from src.api.models.batch_processing import TextChunk, ChunkResult


@pytest.fixture
def classification_service():
    """Create a ClassificationService instance for testing."""
    return ClassificationService()


@pytest.fixture
def sample_chunks() -> List[TextChunk]:
    """Create sample text chunks for testing."""
    return [
        TextChunk(
            text="A escola não tem merenda adequada para os alunos.",
            chunk_id=1,
            page_start=1,
            page_end=1,
            char_start=0,
            char_end=50,
            pdf_path="/test/doc1.pdf",
            token_count=12
        ),
        TextChunk(
            text="O transporte escolar está atrasado todos os dias.",
            chunk_id=2,
            page_start=1,
            page_end=1,
            char_start=51,
            char_end=100,
            pdf_path="/test/doc1.pdf",
            token_count=10
        ),
        TextChunk(
            text="Problemas com bullying na escola precisam ser resolvidos.",
            chunk_id=3,
            page_start=2,
            page_end=2,
            char_start=101,
            char_end=160,
            pdf_path="/test/doc1.pdf",
            token_count=11
        )
    ]


@pytest.fixture
def batch_config():
    """Create a batch configuration for testing."""
    return BatchClassificationConfig(
        max_workers=2,
        max_retries=2,
        retry_delay=0.1,
        timeout_per_chunk=5.0,
        batch_size=2,
        rate_limit_delay=0.05
    )


class TestBatchClassification:
    """Test batch classification functionality."""

    @pytest.mark.asyncio
    async def test_classify_chunks_batch_empty_list(self, classification_service):
        """Test batch classification with empty chunk list."""
        result = await classification_service.classify_chunks_batch([], "EDUCACAO")
        assert result == []

    @pytest.mark.asyncio
    async def test_classify_chunks_batch_success(self, classification_service, sample_chunks, batch_config):
        """Test successful batch classification of chunks."""
        # Mock the multilabel classification function
        with patch('src.api.services.classification_service.classificar_relato_multilabel') as mock_classify:
            mock_classify.side_effect = [
                ["ALIMENTACAO_ESCOLAR"],
                ["TRANSPORTE"],
                ["BULLYING"]
            ]
            
            # Mock environment variables
            with patch.object(classification_service, 'env_vars', {"FIREWORKS_API_KEY": "test_key"}):
                results = await classification_service.classify_chunks_batch(
                    sample_chunks, "EDUCACAO", config=batch_config
                )
                
                assert len(results) == 3
                assert all(isinstance(result, ChunkResult) for result in results)
                assert results[0].subtemas == ["ALIMENTACAO_ESCOLAR"]
                assert results[1].subtemas == ["TRANSPORTE"]
                assert results[2].subtemas == ["BULLYING"]
                
                # Check that all chunks have confidence scores
                for result in results:
                    assert len(result.confidence_scores) == len(result.subtemas)
                    for subtema in result.subtemas:
                        assert subtema in result.confidence_scores
                        assert 0.0 <= result.confidence_scores[subtema] <= 1.0

    @pytest.mark.asyncio
    async def test_classify_chunks_batch_with_errors(self, classification_service, sample_chunks, batch_config):
        """Test batch classification with some chunks failing."""
        # Mock the multilabel classification function to fail on second chunk
        with patch('src.api.services.classification_service.classificar_relato_multilabel') as mock_classify:
            mock_classify.side_effect = [
                ["ALIMENTACAO_ESCOLAR"],
                Exception("API Error"),
                ["BULLYING"]
            ]
            
            # Mock environment variables
            with patch.object(classification_service, 'env_vars', {"FIREWORKS_API_KEY": "test_key"}):
                results = await classification_service.classify_chunks_batch(
                    sample_chunks, "EDUCACAO", config=batch_config
                )
                
                assert len(results) == 3
                assert results[0].error is None
                assert results[1].error is not None
                assert results[2].error is None
                
                # Check successful results
                assert results[0].subtemas == ["ALIMENTACAO_ESCOLAR"]
                assert results[2].subtemas == ["BULLYING"]
                
                # Check failed result
                assert results[1].subtemas == []
                assert "API Error" in results[1].error

    @pytest.mark.asyncio
    async def test_classify_chunks_batch_with_retry(self, classification_service, sample_chunks):
        """Test batch classification with retry logic."""
        config = BatchClassificationConfig(max_retries=2, retry_delay=0.01)
        
        # Mock the multilabel classification function to fail twice then succeed
        call_count = 0
        def mock_classify_with_retry(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise Exception("Temporary API Error")
            return ["ALIMENTACAO_ESCOLAR"]
        
        with patch('src.api.services.classification_service.classificar_relato_multilabel') as mock_classify:
            mock_classify.side_effect = mock_classify_with_retry
            
            # Mock environment variables
            with patch.object(classification_service, 'env_vars', {"FIREWORKS_API_KEY": "test_key"}):
                # Test with single chunk to verify retry logic
                results = await classification_service.classify_chunks_batch(
                    [sample_chunks[0]], "EDUCACAO", config=config
                )
                
                assert len(results) == 1
                assert results[0].error is None
                assert results[0].subtemas == ["ALIMENTACAO_ESCOLAR"]
                assert call_count == 3  # Failed twice, succeeded on third try

    @pytest.mark.asyncio
    async def test_classify_chunks_batch_timeout(self, classification_service, sample_chunks):
        """Test batch classification with timeout."""
        config = BatchClassificationConfig(timeout_per_chunk=0.01, max_retries=1)
        
        # Mock the multilabel classification function to be slow
        async def slow_classify(*args, **kwargs):
            await asyncio.sleep(0.1)  # Longer than timeout
            return ["ALIMENTACAO_ESCOLAR"]
        
        with patch('src.api.services.classification_service.classificar_relato_multilabel') as mock_classify:
            mock_classify.side_effect = slow_classify
            
            # Mock environment variables
            with patch.object(classification_service, 'env_vars', {"FIREWORKS_API_KEY": "test_key"}):
                results = await classification_service.classify_chunks_batch(
                    [sample_chunks[0]], "EDUCACAO", config=config
                )
                
                assert len(results) == 1
                assert results[0].error is not None
                assert "Timeout" in results[0].error

    @pytest.mark.asyncio
    async def test_classify_chunks_batch_progress_callback(self, classification_service, sample_chunks, batch_config):
        """Test batch classification with progress callback."""
        progress_calls = []
        
        def progress_callback(processed: int, total: int):
            progress_calls.append((processed, total))
        
        # Mock the multilabel classification function
        with patch('src.api.services.classification_service.classificar_relato_multilabel') as mock_classify:
            mock_classify.return_value = ["ALIMENTACAO_ESCOLAR"]
            
            # Mock environment variables
            with patch.object(classification_service, 'env_vars', {"FIREWORKS_API_KEY": "test_key"}):
                await classification_service.classify_chunks_batch(
                    sample_chunks, "EDUCACAO", config=batch_config, progress_callback=progress_callback
                )
                
                # Should have progress updates (batch_size=2, so 2 batches for 3 chunks)
                assert len(progress_calls) >= 1
                assert progress_calls[-1] == (3, 3)  # Final call should show all processed

    def test_generate_confidence_scores(self, classification_service):
        """Test confidence score generation."""
        subtemas = ["ALIMENTACAO_ESCOLAR", "TRANSPORTE", "BULLYING"]
        scores = classification_service._generate_confidence_scores(subtemas)
        
        assert len(scores) == 3
        assert scores["ALIMENTACAO_ESCOLAR"] == 0.7  # First gets highest
        assert scores["TRANSPORTE"] == 0.6  # Second gets lower
        assert scores["BULLYING"] == 0.5  # Third gets even lower
        
        # All scores should be between 0 and 1
        for score in scores.values():
            assert 0.0 <= score <= 1.0

    def test_extract_evidence_texts(self, classification_service, sample_chunks):
        """Test evidence text extraction."""
        chunk = sample_chunks[0]  # "A escola não tem merenda adequada para os alunos."
        subtemas = ["ALIMENTACAO_ESCOLAR"]
        
        evidence = classification_service._extract_evidence_texts(chunk, subtemas)
        
        assert "ALIMENTACAO_ESCOLAR" in evidence
        assert len(evidence["ALIMENTACAO_ESCOLAR"]) <= 200
        assert "merenda" in evidence["ALIMENTACAO_ESCOLAR"].lower()

    def test_find_evidence_for_subtema(self, classification_service):
        """Test finding evidence for specific subtema."""
        text = "a escola não tem merenda adequada para os alunos"
        evidence = classification_service._find_evidence_for_subtema(text, "ALIMENTACAO_ESCOLAR")
        
        assert evidence != ""
        assert "merenda" in evidence.lower()
        assert len(evidence) <= 200

    def test_truncate_evidence(self, classification_service):
        """Test evidence text truncation."""
        long_text = "Este é um texto muito longo " * 20  # > 200 chars
        truncated = classification_service._truncate_evidence(long_text, 200)
        
        assert len(truncated) <= 203  # 200 + "..."
        assert truncated.endswith("...")
        
        # Test short text (no truncation)
        short_text = "Texto curto"
        not_truncated = classification_service._truncate_evidence(short_text, 200)
        assert not_truncated == short_text
        assert not not_truncated.endswith("...")

    @pytest.mark.asyncio
    async def test_classify_single_chunk_internal(self, classification_service, sample_chunks):
        """Test internal single chunk classification."""
        chunk = sample_chunks[0]
        
        # Mock the multilabel classification function
        with patch('src.api.services.classification_service.classificar_relato_multilabel') as mock_classify:
            mock_classify.return_value = ["ALIMENTACAO_ESCOLAR", "INFRAESTRUTURA"]
            
            # Mock environment variables
            with patch.object(classification_service, 'env_vars', {"FIREWORKS_API_KEY": "test_key"}):
                result = await classification_service._classify_single_chunk_internal(chunk, "EDUCACAO")
                
                assert isinstance(result, ChunkResult)
                assert result.chunk == chunk
                assert result.subtemas == ["ALIMENTACAO_ESCOLAR", "INFRAESTRUTURA"]
                assert result.error is None
                assert result.processing_time > 0
                
                # Check confidence scores
                assert len(result.confidence_scores) == 2
                assert "ALIMENTACAO_ESCOLAR" in result.confidence_scores
                assert "INFRAESTRUTURA" in result.confidence_scores

    @pytest.mark.asyncio
    async def test_classify_single_chunk_internal_error(self, classification_service, sample_chunks):
        """Test internal single chunk classification with error."""
        chunk = sample_chunks[0]
        
        # Mock the multilabel classification function to raise an error
        with patch('src.api.services.classification_service.classificar_relato_multilabel') as mock_classify:
            mock_classify.side_effect = Exception("Classification failed")
            
            # Mock environment variables
            with patch.object(classification_service, 'env_vars', {"FIREWORKS_API_KEY": "test_key"}):
                with pytest.raises(Exception, match="Classification failed"):
                    await classification_service._classify_single_chunk_internal(chunk, "EDUCACAO")

    def test_batch_classification_config_defaults(self):
        """Test default batch configuration values."""
        config = BatchClassificationConfig()
        
        assert config.max_workers == 4
        assert config.max_retries == 3
        assert config.retry_delay == 1.0
        assert config.timeout_per_chunk == 30.0
        assert config.batch_size == 10
        assert config.rate_limit_delay == 0.1

    def test_batch_classification_config_custom(self):
        """Test custom batch configuration values."""
        config = BatchClassificationConfig(
            max_workers=8,
            max_retries=5,
            retry_delay=2.0,
            timeout_per_chunk=60.0,
            batch_size=20,
            rate_limit_delay=0.5
        )
        
        assert config.max_workers == 8
        assert config.max_retries == 5
        assert config.retry_delay == 2.0
        assert config.timeout_per_chunk == 60.0
        assert config.batch_size == 20
        assert config.rate_limit_delay == 0.5