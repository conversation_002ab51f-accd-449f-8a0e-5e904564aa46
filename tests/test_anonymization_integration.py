#!/usr/bin/env python3
"""
Test script for anonymization integration with education classification data.

Tests the official MPRJ anonymization tools within our LangGraph framework
using real education data from our classification system.
"""

import sys
import os
import pandas as pd
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from src.tools.anonymization_tool import AnonymizationTool


def test_anonymization_with_education_data():
    """Test anonymization tool with our education classification data."""
    
    print("🔥 Testing MPRJ Anonymization Integration")
    print("=" * 50)
    
    # Initialize anonymization tool
    print("📋 Initializing anonymization tool...")
    try:
        anonymizer = AnonymizationTool()
        print("✅ Anonymization tool initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize anonymization tool: {e}")
        return False
    
    # Load education data
    data_file = "data/output/ouvidorias_educacao_fireworks_multilabel.csv"
    if not os.path.exists(data_file):
        print(f"❌ Education data file not found: {data_file}")
        return False
    
    print(f"📄 Loading education data from: {data_file}")
    df = pd.read_csv(data_file)
    print(f"✅ Loaded {len(df)} records")
    
    # Test with sample records
    print("\n🧪 Testing anonymization on sample records...")
    
    test_results = []
    sample_size = min(5, len(df))
    
    for idx in range(sample_size):
        row = df.iloc[idx]
        
        print(f"\n--- Test {idx + 1}/{sample_size} ---")
        
        # Test with 'Assunto Resumido'
        if 'Assunto Resumido' in row and pd.notna(row['Assunto Resumido']):
            text = str(row['Assunto Resumido'])
            print(f"📝 Original text: {text[:100]}...")
            
            result = anonymizer.anonymize_text(text)
            
            if result['success']:
                print(f"✅ Anonymized: {result['anonymized_text'][:100]}...")
                print(f"🔍 Entities found: {result['entities_count']}")
                
                for entity in result['entities_found']:
                    print(f"   • {entity['entity_type']}: {entity['text']} (score: {entity['score']:.2f})")
                
                test_results.append({
                    'success': True,
                    'entities_count': result['entities_count'],
                    'original_length': len(text),
                    'anonymized_length': len(result['anonymized_text'])
                })
            else:
                print(f"❌ Anonymization failed: {result.get('error', 'Unknown error')}")
                test_results.append({'success': False})
    
    # Summary statistics
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    successful_tests = [r for r in test_results if r.get('success', False)]
    total_entities = sum(r.get('entities_count', 0) for r in successful_tests)
    
    print(f"✅ Successful tests: {len(successful_tests)}/{len(test_results)}")
    print(f"🔍 Total entities detected: {total_entities}")
    
    if successful_tests:
        avg_entities = total_entities / len(successful_tests)
        print(f"📈 Average entities per text: {avg_entities:.1f}")
    
    # Test supported entities
    print(f"\n🎯 Supported entity types:")
    for entity_type in anonymizer.get_supported_entities():
        print(f"   • {entity_type}")
    
    return len(successful_tests) > 0


def test_specific_brazilian_entities():
    """Test recognition of specific Brazilian entities."""
    
    print("\n🇧🇷 Testing Brazilian-specific entity recognition")
    print("=" * 50)
    
    anonymizer = AnonymizationTool()
    
    # Test cases for Brazilian entities
    test_cases = [
        {
            "text": "João Silva, CPF 123.456.789-00, estuda na Escola Municipal Professor Carlos Drummond.",
            "expected_entities": ["PERSON", "CPF", "ESCOLA"]
        },
        {
            "text": "Maria mora na Rua das Flores, 123, Copacabana, Rio de Janeiro.",
            "expected_entities": ["PERSON", "ENDEREÇO"]
        },
        {
            "text": "O aluno da FAETEC de Marechal Hermes relatou problemas na alimentação.",
            "expected_entities": ["ESCOLA"]
        },
        {
            "text": "Contato: <EMAIL>, telefone (21) 99999-9999.",
            "expected_entities": ["EMAIL_ADDRESS", "PHONE_NUMBER"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Brazilian Entity Test {i} ---")
        print(f"📝 Text: {test_case['text']}")
        
        result = anonymizer.anonymize_text(test_case['text'])
        
        if result['success']:
            print(f"✅ Anonymized: {result['anonymized_text']}")
            
            detected_types = [e['entity_type'] for e in result['entities_found']]
            print(f"🔍 Detected: {detected_types}")
            print(f"🎯 Expected: {test_case['expected_entities']}")
            
            # Check if expected entities were found
            found_expected = any(expected in detected_types for expected in test_case['expected_entities'])
            if found_expected:
                print("✅ Expected entities detected")
            else:
                print("⚠️ Some expected entities not detected")
        else:
            print(f"❌ Test failed: {result.get('error', 'Unknown error')}")


def main():
    """Main test function."""
    
    print("🚀 MPRJ Anonymization Integration Test")
    print("=" * 60)
    
    # Test 1: Integration with education data
    success1 = test_anonymization_with_education_data()
    
    # Test 2: Brazilian-specific entities
    test_specific_brazilian_entities()
    
    # Final result
    print("\n" + "=" * 60)
    if success1:
        print("🎉 Integration test completed successfully!")
        print("✅ MPRJ anonymization tools are working within LangGraph framework")
        print("✅ Compatible with education classification data")
        print("✅ Ready for multi-agent workflows")
    else:
        print("❌ Integration test failed")
        print("🔧 Check dependencies and configuration")
    
    return success1


if __name__ == "__main__":
    main()
