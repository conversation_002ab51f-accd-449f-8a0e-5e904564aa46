#!/usr/bin/env python3
"""
Script to test file upload functionality.
"""

import requests
import json
from pathlib import Path

def test_file_upload():
    """Test file upload endpoint."""
    print("📁 Testing file upload...")
    
    # API base URL
    base_url = "http://localhost:8000"
    
    # Mock JWT token (since we don't have real auth yet)
    # In production, this would be a real JWT token from authentication
    mock_token = "mock-jwt-token-for-testing"
    
    headers = {
        "Authorization": f"Bearer {mock_token}"
    }
    
    # Test file path
    test_file = Path(__file__).parent.parent / "test_data" / "sample_ouvidoria.csv"
    
    if not test_file.exists():
        print(f"❌ Test file not found: {test_file}")
        return False
    
    print(f"📄 Uploading file: {test_file.name}")
    
    # Prepare file and metadata
    files = {
        'file': ('sample_ouvidoria.csv', open(test_file, 'rb'), 'text/csv')
    }
    
    data = {
        'metadata': json.dumps({
            "description": "Dados de teste de ouvidoria",
            "tags": ["test", "ouvidoria", "educacao"],
            "source": "upload_test"
        }),
        'process_immediately': 'false'
    }
    
    try:
        # Make upload request
        response = requests.post(
            f"{base_url}/api/v1/files/upload",
            headers=headers,
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Upload successful!")
            print(f"   File ID: {result.get('file_id')}")
            print(f"   Filename: {result.get('filename')}")
            print(f"   File size: {result.get('file_size')} bytes")
            print(f"   File type: {result.get('file_type')}")
            print(f"   Download URL: {result.get('download_url')}")
            return True
        else:
            print(f"❌ Upload failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Upload request failed: {e}")
        return False
    
    finally:
        files['file'][1].close()

def test_file_list():
    """Test file listing endpoint."""
    print("\n📋 Testing file listing...")
    
    base_url = "http://localhost:8000"
    mock_token = "mock-jwt-token-for-testing"
    
    headers = {
        "Authorization": f"Bearer {mock_token}"
    }
    
    try:
        response = requests.get(
            f"{base_url}/api/v1/files/",
            headers=headers,
            timeout=10
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ File listing successful!")
            print(f"   Total files: {result.get('total_count', 0)}")
            print(f"   Total size: {result.get('total_size', 0)} bytes")
            
            files = result.get('files', [])
            for i, file_info in enumerate(files[:3], 1):  # Show first 3 files
                print(f"   File {i}: {file_info.get('filename', 'Unknown')}")
            
            return True
        else:
            print(f"❌ Listing failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Listing request failed: {e}")
        return False

def test_file_stats():
    """Test file statistics endpoint."""
    print("\n📊 Testing file statistics...")
    
    base_url = "http://localhost:8000"
    mock_token = "mock-jwt-token-for-testing"
    
    headers = {
        "Authorization": f"Bearer {mock_token}"
    }
    
    try:
        response = requests.get(
            f"{base_url}/api/v1/files/stats/summary",
            headers=headers,
            timeout=10
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Statistics successful!")
            print(f"   Total files: {result.get('total_files', 0)}")
            print(f"   Total size: {result.get('total_size', 0)} bytes")
            print(f"   Files by type: {result.get('files_by_type', {})}")
            print(f"   Files by status: {result.get('files_by_status', {})}")
            return True
        else:
            print(f"❌ Statistics failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Statistics request failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 File Upload API Test")
    print("=" * 40)
    
    # Test upload
    upload_ok = test_file_upload()
    
    # Test listing
    list_ok = test_file_list()
    
    # Test statistics
    stats_ok = test_file_stats()
    
    print("\n" + "=" * 40)
    print("📊 Test Results:")
    print(f"   Upload: {'✅ OK' if upload_ok else '❌ FAILED'}")
    print(f"   Listing: {'✅ OK' if list_ok else '❌ FAILED'}")
    print(f"   Statistics: {'✅ OK' if stats_ok else '❌ FAILED'}")
    
    if upload_ok and list_ok and stats_ok:
        print("\n🎉 All file API tests passed!")
    else:
        print("\n⚠️  Some tests failed. Check the API logs.")

if __name__ == "__main__":
    main()
