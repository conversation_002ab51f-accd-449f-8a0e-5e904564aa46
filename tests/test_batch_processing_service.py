"""
Tests for BatchProcessingService.

This module contains comprehensive tests for the PDF batch processing
functionality including service tests, integration tests, and performance tests.
"""

import pytest
import asyncio
import tempfile
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime

from src.api.services.batch_processing_service import (
    BatchProcessingService,
    ResultAggregator,
    CSVExporter,
    BatchJobManager,
    JobProgressCallback
)
from src.api.models.batch_processing import (
    BatchProcessingRequest,
    JobStatus,
    JobStatusResponse,
    TextChunk,
    ChunkResult,
    PDFClassificationResult,
    BatchResult
)


class TestResultAggregator:
    """Tests for ResultAggregator component."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.aggregator = ResultAggregator()
    
    def test_aggregate_empty_results(self):
        """Test aggregation with empty results."""
        pdf_path = "/test/path/document.pdf"
        result = self.aggregator.aggregate_pdf_results([], pdf_path)
        
        assert result.pdf_path == pdf_path
        assert result.filename == "document.pdf"
        assert result.error == "No chunks to process"
        assert result.subtemas_finais == []
    
    def test_aggregate_single_chunk_result(self):
        """Test aggregation with single chunk result."""
        chunk = TextChunk(
            text="Test content about education",
            chunk_id=0,
            page_start=1,
            page_end=1,
            char_start=0,
            char_end=30,
            pdf_path="/test/path/document.pdf"
        )
        
        chunk_result = ChunkResult(
            chunk=chunk,
            subtemas=["INFRAESTRUTURA_ESCOLAR"],
            confidence_scores={"INFRAESTRUTURA_ESCOLAR": 0.8},
            evidence_texts={"INFRAESTRUTURA_ESCOLAR": "education infrastructure"},
            processing_time=1.0
        )
        
        result = self.aggregator.aggregate_pdf_results([chunk_result], chunk.pdf_path)
        
        assert result.filename == "document.pdf"
        assert result.subtemas_finais == ["INFRAESTRUTURA_ESCOLAR"]
        assert result.confidence_scores["INFRAESTRUTURA_ESCOLAR"] == 0.8
        assert result.evidence_texts["INFRAESTRUTURA_ESCOLAR"] == "education infrastructure"
        assert result.total_chunks == 1
        assert result.processing_time == 1.0
        assert result.error is None
    
    def test_aggregate_multiple_chunks_with_same_subtema(self):
        """Test aggregation with multiple chunks identifying same subtema."""
        chunk1 = TextChunk(
            text="School infrastructure needs improvement",
            chunk_id=0,
            page_start=1,
            page_end=1,
            char_start=0,
            char_end=40,
            pdf_path="/test/path/document.pdf"
        )
        
        chunk2 = TextChunk(
            text="Educational facilities require maintenance",
            chunk_id=1,
            page_start=1,
            page_end=2,
            char_start=40,
            char_end=85,
            pdf_path="/test/path/document.pdf"
        )
        
        chunk_result1 = ChunkResult(
            chunk=chunk1,
            subtemas=["INFRAESTRUTURA_ESCOLAR"],
            confidence_scores={"INFRAESTRUTURA_ESCOLAR": 0.7},
            evidence_texts={"INFRAESTRUTURA_ESCOLAR": "infrastructure needs"},
            processing_time=1.0
        )
        
        chunk_result2 = ChunkResult(
            chunk=chunk2,
            subtemas=["INFRAESTRUTURA_ESCOLAR"],
            confidence_scores={"INFRAESTRUTURA_ESCOLAR": 0.9},
            evidence_texts={"INFRAESTRUTURA_ESCOLAR": "facilities require"},
            processing_time=1.2
        )
        
        result = self.aggregator.aggregate_pdf_results([chunk_result1, chunk_result2], chunk1.pdf_path)
        
        assert result.subtemas_finais == ["INFRAESTRUTURA_ESCOLAR"]
        # Confidence should be average of both chunks: (0.7 + 0.9) / 2 = 0.8
        assert result.confidence_scores["INFRAESTRUTURA_ESCOLAR"] == 0.8
        assert result.total_chunks == 2
        assert result.processing_time == 2.2
    
    def test_aggregate_multiple_subtemas_with_limit(self):
        """Test aggregation respects max_subtemas limit."""
        chunk = TextChunk(
            text="Complex text with multiple topics",
            chunk_id=0,
            page_start=1,
            page_end=1,
            char_start=0,
            char_end=33,
            pdf_path="/test/path/document.pdf"
        )
        
        chunk_result = ChunkResult(
            chunk=chunk,
            subtemas=["INFRAESTRUTURA_ESCOLAR", "QUALIDADE_ENSINO", "GESTAO_ESCOLAR", "CURRICULO"],
            confidence_scores={
                "INFRAESTRUTURA_ESCOLAR": 0.9,
                "QUALIDADE_ENSINO": 0.8,
                "GESTAO_ESCOLAR": 0.7,
                "CURRICULO": 0.6
            },
            evidence_texts={
                "INFRAESTRUTURA_ESCOLAR": "infrastructure",
                "QUALIDADE_ENSINO": "quality",
                "GESTAO_ESCOLAR": "management",
                "CURRICULO": "curriculum"
            },
            processing_time=1.0
        )
        
        result = self.aggregator.aggregate_pdf_results([chunk_result], chunk.pdf_path)
        
        # Should only include top 3 subtemas by confidence
        assert len(result.subtemas_finais) <= 3
        assert "INFRAESTRUTURA_ESCOLAR" in result.subtemas_finais
        assert "QUALIDADE_ENSINO" in result.subtemas_finais
        assert "GESTAO_ESCOLAR" in result.subtemas_finais
        assert "CURRICULO" not in result.subtemas_finais
    
    def test_aggregate_with_error_chunks(self):
        """Test aggregation handles chunks with errors."""
        chunk1 = TextChunk(
            text="Valid content",
            chunk_id=0,
            page_start=1,
            page_end=1,
            char_start=0,
            char_end=13,
            pdf_path="/test/path/document.pdf"
        )
        
        chunk2 = TextChunk(
            text="Error content",
            chunk_id=1,
            page_start=2,
            page_end=2,
            char_start=13,
            char_end=26,
            pdf_path="/test/path/document.pdf"
        )
        
        valid_result = ChunkResult(
            chunk=chunk1,
            subtemas=["INFRAESTRUTURA_ESCOLAR"],
            confidence_scores={"INFRAESTRUTURA_ESCOLAR": 0.8},
            evidence_texts={"INFRAESTRUTURA_ESCOLAR": "valid content"},
            processing_time=1.0
        )
        
        error_result = ChunkResult(
            chunk=chunk2,
            subtemas=[],
            confidence_scores={},
            evidence_texts={},
            processing_time=0.0,
            error="Classification failed"
        )
        
        result = self.aggregator.aggregate_pdf_results([valid_result, error_result], chunk1.pdf_path)
        
        # Should only use valid results
        assert result.subtemas_finais == ["INFRAESTRUTURA_ESCOLAR"]
        assert result.total_chunks == 2
        assert result.processing_time == 1.0  # Only valid chunk time
        assert result.error is None


class TestCSVExporter:
    """Tests for CSVExporter component."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.temp_dir = tempfile.mkdtemp()
        self.exporter = CSVExporter(self.temp_dir)
    
    def test_export_empty_batch_results(self):
        """Test CSV export with empty batch results."""
        batch_result = BatchResult(
            job_id="test-job-123",
            folder_path="/test/path",
            area="EDUCACAO",
            pdf_results=[],
            total_pdfs=0,
            status=JobStatus.COMPLETED
        )
        
        csv_path = self.exporter.export_batch_results(batch_result)
        
        assert Path(csv_path).exists()
        assert "batch_results_EDUCACAO_" in csv_path
        assert csv_path.endswith(".csv")
        
        # Check CSV content
        with open(csv_path, 'r') as f:
            content = f.read()
            assert "filename" in content
            assert "pdf_path" in content
            assert "subtemas_finais" in content
    
    def test_export_batch_results_with_data(self):
        """Test CSV export with actual batch results."""
        pdf_result = PDFClassificationResult(
            pdf_path="/test/path/document.pdf",
            filename="document.pdf",
            subtemas_finais=["INFRAESTRUTURA_ESCOLAR", "QUALIDADE_ENSINO"],
            confidence_scores={
                "INFRAESTRUTURA_ESCOLAR": 0.8,
                "QUALIDADE_ENSINO": 0.7
            },
            evidence_texts={
                "INFRAESTRUTURA_ESCOLAR": "infrastructure evidence",
                "QUALIDADE_ENSINO": "quality evidence"
            },
            total_pages=5,
            total_chunks=10,
            processing_time=15.5,
            area="EDUCACAO",
            timestamp=datetime.utcnow()
        )
        
        batch_result = BatchResult(
            job_id="test-job-123",
            folder_path="/test/path",
            area="EDUCACAO",
            pdf_results=[pdf_result],
            total_pdfs=1,
            status=JobStatus.COMPLETED
        )
        
        csv_path = self.exporter.export_batch_results(batch_result)
        
        assert Path(csv_path).exists()
        
        # Check CSV content
        with open(csv_path, 'r') as f:
            content = f.read()
            assert "document.pdf" in content
            assert "INFRAESTRUTURA_ESCOLAR;QUALIDADE_ENSINO" in content
            assert "5" in content  # total_pages
            assert "10" in content  # total_chunks
            assert "15.5" in content  # processing_time
    
    def test_generate_csv_headers(self):
        """Test CSV header generation."""
        headers = self.exporter._generate_csv_headers("EDUCACAO")
        
        # Check base headers
        assert "filename" in headers
        assert "pdf_path" in headers
        assert "subtemas_finais" in headers
        assert "total_pages" in headers
        assert "processing_time" in headers
        
        # Check that subtema-specific headers are included
        # (This depends on the actual configuration)
        subtema_headers = [h for h in headers if h.startswith("subtema_")]
        assert len(subtema_headers) > 0


class TestBatchJobManager:
    """Tests for BatchJobManager component."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.manager = BatchJobManager()
    
    def test_create_job(self):
        """Test job creation."""
        job_id = self.manager.create_job(
            folder_path="/test/path",
            area="EDUCACAO",
            user_id="user-123"
        )
        
        assert job_id is not None
        assert len(job_id) > 0
        assert job_id in self.manager.jobs
        
        job_data = self.manager.jobs[job_id]
        assert job_data["folder_path"] == "/test/path"
        assert job_data["area"] == "EDUCACAO"
        assert job_data["user_id"] == "user-123"
        assert job_data["status"] == JobStatus.PENDING
        assert job_data["progress"] == 0.0
    
    def test_get_job_status(self):
        """Test job status retrieval."""
        job_id = self.manager.create_job(
            folder_path="/test/path",
            area="EDUCACAO",
            user_id="user-123"
        )
        
        status = self.manager.get_job_status(job_id)
        
        assert status is not None
        assert status.job_id == job_id
        assert status.status == JobStatus.PENDING
        assert status.progress == 0.0
        assert status.processed_files == 0
        assert status.total_files == 0
    
    def test_get_nonexistent_job_status(self):
        """Test status retrieval for non-existent job."""
        status = self.manager.get_job_status("nonexistent-job")
        assert status is None
    
    @pytest.mark.asyncio
    async def test_update_progress(self):
        """Test job progress update."""
        job_id = self.manager.create_job(
            folder_path="/test/path",
            area="EDUCACAO",
            user_id="user-123"
        )
        
        await self.manager.update_progress(
            job_id=job_id,
            progress=0.5,
            current_file="test.pdf",
            processed_files=5,
            total_files=10
        )
        
        job_data = self.manager.jobs[job_id]
        assert job_data["progress"] == 0.5
        assert job_data["current_file"] == "test.pdf"
        assert job_data["processed_files"] == 5
        assert job_data["total_files"] == 10
        assert job_data["status"] == JobStatus.PROCESSING.value
    
    def test_complete_job(self):
        """Test job completion."""
        job_id = self.manager.create_job(
            folder_path="/test/path",
            area="EDUCACAO",
            user_id="user-123"
        )
        
        batch_result = BatchResult(
            job_id=job_id,
            folder_path="/test/path",
            area="EDUCACAO",
            pdf_results=[],
            total_pdfs=0,
            status=JobStatus.COMPLETED
        )
        
        self.manager.complete_job(job_id, batch_result)
        
        job_data = self.manager.jobs[job_id]
        assert job_data["status"] == JobStatus.COMPLETED.value
        assert job_data["progress"] == 1.0
        assert job_data["results"] is not None
    
    def test_fail_job(self):
        """Test job failure."""
        job_id = self.manager.create_job(
            folder_path="/test/path",
            area="EDUCACAO",
            user_id="user-123"
        )
        
        error_message = "Processing failed due to error"
        self.manager.fail_job(job_id, error_message)
        
        job_data = self.manager.jobs[job_id]
        assert job_data["status"] == JobStatus.FAILED.value
        assert job_data["error_message"] == error_message


class TestBatchProcessingService:
    """Tests for BatchProcessingService component."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.service = BatchProcessingService()
    
    @pytest.mark.asyncio
    async def test_start_batch_processing_nonexistent_folder(self):
        """Test batch processing with non-existent folder."""
        request = BatchProcessingRequest(
            folder_path="/nonexistent/path",
            area="EDUCACAO"
        )
        
        with pytest.raises(ValueError, match="Folder does not exist"):
            await self.service.start_batch_processing(request, "user-123")
    
    @pytest.mark.asyncio
    async def test_start_batch_processing_valid_folder(self):
        """Test batch processing with valid folder."""
        # Create temporary directory with test PDF
        temp_dir = tempfile.mkdtemp()
        test_pdf = Path(temp_dir) / "test.pdf"
        test_pdf.write_text("Mock PDF content")  # Mock PDF file
        
        request = BatchProcessingRequest(
            folder_path=str(temp_dir),
            area="EDUCACAO"
        )
        
        # Mock the PDF processing components
        with patch.object(self.service, '_find_pdf_files', return_value=[test_pdf]):
            with patch.object(self.service, '_process_batch_async', new_callable=AsyncMock):
                job_id = await self.service.start_batch_processing(request, "user-123")
                
                assert job_id is not None
                assert len(job_id) > 0
                assert job_id in self.service.job_manager.jobs
    
    def test_find_pdf_files_empty_folder(self):
        """Test PDF file discovery in empty folder."""
        temp_dir = tempfile.mkdtemp()
        pdf_files = self.service._find_pdf_files(temp_dir)
        
        assert pdf_files == []
    
    def test_find_pdf_files_with_pdfs(self):
        """Test PDF file discovery with actual PDF files."""
        temp_dir = tempfile.mkdtemp()
        
        # Create test PDF files
        pdf1 = Path(temp_dir) / "test1.pdf"
        pdf2 = Path(temp_dir) / "test2.pdf"
        txt_file = Path(temp_dir) / "test.txt"
        
        pdf1.write_text("Mock PDF 1")
        pdf2.write_text("Mock PDF 2")
        txt_file.write_text("Text file")
        
        pdf_files = self.service._find_pdf_files(temp_dir)
        
        assert len(pdf_files) == 2
        assert pdf1 in pdf_files
        assert pdf2 in pdf_files
        assert all(f.suffix.lower() == '.pdf' for f in pdf_files)
    
    def test_get_job_status(self):
        """Test job status retrieval."""
        job_id = self.service.job_manager.create_job(
            folder_path="/test/path",
            area="EDUCACAO",
            user_id="user-123"
        )
        
        status = self.service.get_job_status(job_id)
        
        assert status is not None
        assert status.job_id == job_id
        assert status.status == JobStatus.PENDING
    
    def test_get_job_results(self):
        """Test job results retrieval."""
        job_id = self.service.job_manager.create_job(
            folder_path="/test/path",
            area="EDUCACAO",
            user_id="user-123"
        )
        
        # Complete job with results
        batch_result = BatchResult(
            job_id=job_id,
            folder_path="/test/path",
            area="EDUCACAO",
            pdf_results=[],
            total_pdfs=0,
            status=JobStatus.COMPLETED
        )
        
        self.service.job_manager.complete_job(job_id, batch_result)
        
        results = self.service.get_job_results(job_id)
        
        assert results is not None
        assert results.job_id == job_id
        assert results.status == JobStatus.COMPLETED
    
    def test_cancel_job(self):
        """Test job cancellation."""
        job_id = self.service.job_manager.create_job(
            folder_path="/test/path",
            area="EDUCACAO",
            user_id="user-123"
        )
        
        success = self.service.cancel_job(job_id)
        
        assert success is True
        
        job_data = self.service.job_manager.jobs[job_id]
        assert job_data["status"] == JobStatus.CANCELLED.value
    
    def test_cancel_completed_job(self):
        """Test cancellation of completed job (should fail)."""
        job_id = self.service.job_manager.create_job(
            folder_path="/test/path",
            area="EDUCACAO",
            user_id="user-123"
        )
        
        # Complete the job first
        batch_result = BatchResult(
            job_id=job_id,
            folder_path="/test/path",
            area="EDUCACAO",
            pdf_results=[],
            total_pdfs=0,
            status=JobStatus.COMPLETED
        )
        
        self.service.job_manager.complete_job(job_id, batch_result)
        
        # Try to cancel (should fail)
        success = self.service.cancel_job(job_id)
        
        assert success is False
    
    def test_cleanup_old_jobs(self):
        """Test cleanup of old jobs."""
        # Create a job
        job_id = self.service.job_manager.create_job(
            folder_path="/test/path",
            area="EDUCACAO",
            user_id="user-123"
        )
        
        # Complete it
        batch_result = BatchResult(
            job_id=job_id,
            folder_path="/test/path",
            area="EDUCACAO",
            pdf_results=[],
            total_pdfs=0,
            status=JobStatus.COMPLETED
        )
        
        self.service.job_manager.complete_job(job_id, batch_result)
        
        # Manually set old timestamp
        self.service.job_manager.jobs[job_id]["updated_at"] = "2020-01-01T00:00:00"
        
        # Cleanup
        cleaned_count = self.service.cleanup_old_jobs(days_old=1)
        
        assert cleaned_count == 1
        assert job_id not in self.service.job_manager.jobs


class TestJobProgressCallback:
    """Tests for JobProgressCallback component."""
    
    @pytest.mark.asyncio
    async def test_update_progress_with_callback(self):
        """Test progress update with callback function."""
        callback_called = False
        callback_args = None
        
        async def test_callback(job_id, progress, current_file, processed_files, total_files, status):
            nonlocal callback_called, callback_args
            callback_called = True
            callback_args = (job_id, progress, current_file, processed_files, total_files, status)
        
        progress_callback = JobProgressCallback("test-job", test_callback)
        
        await progress_callback.update_progress(
            progress=0.5,
            current_file="test.pdf",
            processed_files=5,
            total_files=10
        )
        
        assert callback_called is True
        assert callback_args[0] == "test-job"
        assert callback_args[1] == 0.5
        assert callback_args[2] == "test.pdf"
        assert callback_args[3] == 5
        assert callback_args[4] == 10
        assert callback_args[5] == JobStatus.PROCESSING
    
    @pytest.mark.asyncio
    async def test_update_progress_without_callback(self):
        """Test progress update without callback function."""
        progress_callback = JobProgressCallback("test-job", None)
        
        # Should not raise any exception
        await progress_callback.update_progress(
            progress=0.5,
            current_file="test.pdf",
            processed_files=5,
            total_files=10
        )
        
        assert progress_callback.job_id == "test-job"
        assert progress_callback.last_update is not None


# Performance and Integration Tests

class TestBatchProcessingPerformance:
    """Performance tests for batch processing."""
    
    @pytest.mark.asyncio
    @pytest.mark.slow
    async def test_batch_processing_performance(self):
        """Test batch processing performance with multiple PDFs."""
        # Create temporary directory with multiple test PDFs
        temp_dir = tempfile.mkdtemp()
        
        # Create 10 test PDF files
        pdf_files = []
        for i in range(10):
            pdf_path = Path(temp_dir) / f"test_{i}.pdf"
            pdf_path.write_text(f"Mock PDF content for file {i}")
            pdf_files.append(pdf_path)
        
        service = BatchProcessingService()
        
        # Mock PDF processing to avoid actual LLM calls
        with patch.object(service.pdf_extractor, 'extract_and_chunk') as mock_extract:
            with patch.object(service.chunk_classifier, 'classify_chunks') as mock_classify:
                # Mock extraction to return simple chunks
                mock_extract.return_value = [
                    TextChunk(
                        text="Mock chunk content",
                        chunk_id=0,
                        page_start=1,
                        page_end=1,
                        char_start=0,
                        char_end=20,
                        pdf_path=str(pdf_files[0])
                    )
                ]
                
                # Mock classification to return simple results
                mock_classify.return_value = [
                    ChunkResult(
                        chunk=mock_extract.return_value[0],
                        subtemas=["INFRAESTRUTURA_ESCOLAR"],
                        confidence_scores={"INFRAESTRUTURA_ESCOLAR": 0.8},
                        evidence_texts={"INFRAESTRUTURA_ESCOLAR": "mock evidence"},
                        processing_time=0.1
                    )
                ]
                
                request = BatchProcessingRequest(
                    folder_path=str(temp_dir),
                    area="EDUCACAO",
                    parallel_workers=4
                )
                
                import time
                start_time = time.time()
                
                job_id = await service.start_batch_processing(request, "user-123")
                
                # Wait for processing to complete
                max_wait = 30  # 30 seconds max wait
                wait_time = 0
                while wait_time < max_wait:
                    status = service.get_job_status(job_id)
                    if status.status in [JobStatus.COMPLETED, JobStatus.FAILED]:
                        break
                    await asyncio.sleep(0.1)
                    wait_time += 0.1
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                # Check that processing completed within reasonable time
                assert processing_time < 10.0  # Should complete within 10 seconds
                
                # Check final status
                final_status = service.get_job_status(job_id)
                assert final_status.status == JobStatus.COMPLETED
                
                # Check results
                results = service.get_job_results(job_id)
                assert results is not None
                assert results.total_pdfs == 10
                assert results.successful_pdfs == 10
                assert len(results.failed_pdfs) == 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])