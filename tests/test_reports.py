"""
Tests for report generation endpoints.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from httpx import AsyncClient

from tests.conftest import REPORTS_ENDPOINTS


class TestReportEndpoints:
    """Test report generation endpoints."""

    def test_generate_individual_report_success(self, client: TestClient, auth_headers: dict):
        """Test successful individual report generation."""
        response = client.post(
            f"{REPORTS_ENDPOINTS}/individual",
            headers=auth_headers,
            json={
                "file_id": "test-file-id",
                "subtema": "ALIMENTACAO_ESCOLAR",
                "area_politica_publica": "EDUCACAO"
            }
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "report_id" in result
        assert "content" in result
        assert "subtema" in result
        assert "area_politica_publica" in result
        assert "generated_at" in result
        assert "processing_time" in result
        
        # Check content structure
        content = result["content"]
        assert isinstance(content, str)
        assert len(content) > 0

    def test_generate_individual_report_no_auth(self, client: TestClient):
        """Test individual report generation without authentication."""
        response = client.post(
            f"{REPORTS_ENDPOINTS}/individual",
            json={
                "file_id": "test-file-id",
                "subtema": "ALIMENTACAO_ESCOLAR",
                "area_politica_publica": "EDUCACAO"
            }
        )
        
        assert response.status_code == 401

    def test_generate_individual_report_invalid_file(self, client: TestClient, auth_headers: dict):
        """Test individual report generation with invalid file ID."""
        response = client.post(
            f"{REPORTS_ENDPOINTS}/individual",
            headers=auth_headers,
            json={
                "file_id": "non-existent-file",
                "subtema": "ALIMENTACAO_ESCOLAR",
                "area_politica_publica": "EDUCACAO"
            }
        )
        
        assert response.status_code == 404

    def test_generate_individual_report_invalid_subtema(self, client: TestClient, auth_headers: dict):
        """Test individual report generation with invalid subtema."""
        response = client.post(
            f"{REPORTS_ENDPOINTS}/individual",
            headers=auth_headers,
            json={
                "file_id": "test-file-id",
                "subtema": "INVALID_SUBTEMA",
                "area_politica_publica": "EDUCACAO"
            }
        )
        
        assert response.status_code == 400

    def test_generate_overview_report_success(self, client: TestClient, auth_headers: dict):
        """Test successful overview report generation."""
        response = client.post(
            f"{REPORTS_ENDPOINTS}/overview",
            headers=auth_headers,
            json={
                "file_id": "test-file-id",
                "area_politica_publica": "EDUCACAO"
            }
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "report_id" in result
        assert "content" in result
        assert "area_politica_publica" in result
        assert "generated_at" in result
        assert "processing_time" in result
        assert "statistics" in result
        
        # Check statistics structure
        stats = result["statistics"]
        assert "total_cases" in stats
        assert "subtemas_distribution" in stats

    def test_generate_overview_report_no_auth(self, client: TestClient):
        """Test overview report generation without authentication."""
        response = client.post(
            f"{REPORTS_ENDPOINTS}/overview",
            json={
                "file_id": "test-file-id",
                "area_politica_publica": "EDUCACAO"
            }
        )
        
        assert response.status_code == 401

    def test_list_reports_success(self, client: TestClient, auth_headers: dict):
        """Test listing user reports."""
        response = client.get(
            f"{REPORTS_ENDPOINTS}/",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "reports" in result
        assert "total" in result
        assert "page" in result
        assert "per_page" in result
        assert isinstance(result["reports"], list)

    def test_list_reports_with_filters(self, client: TestClient, auth_headers: dict):
        """Test listing reports with filters."""
        response = client.get(
            f"{REPORTS_ENDPOINTS}/",
            headers=auth_headers,
            params={
                "area_politica_publica": "EDUCACAO",
                "report_type": "individual"
            }
        )
        
        assert response.status_code == 200
        result = response.json()
        assert "reports" in result

    def test_list_reports_no_auth(self, client: TestClient):
        """Test listing reports without authentication."""
        response = client.get(f"{REPORTS_ENDPOINTS}/")
        
        assert response.status_code == 401

    def test_get_report_success(self, client: TestClient, auth_headers: dict):
        """Test getting specific report."""
        report_id = "test-report-id"
        
        response = client.get(
            f"{REPORTS_ENDPOINTS}/{report_id}",
            headers=auth_headers
        )
        
        # This will depend on your mock setup
        assert response.status_code in [200, 404]

    def test_get_report_not_found(self, client: TestClient, auth_headers: dict):
        """Test getting non-existent report."""
        response = client.get(
            f"{REPORTS_ENDPOINTS}/non-existent-id",
            headers=auth_headers
        )
        
        assert response.status_code == 404

    def test_get_report_no_auth(self, client: TestClient):
        """Test getting report without authentication."""
        response = client.get(f"{REPORTS_ENDPOINTS}/test-report-id")
        
        assert response.status_code == 401

    def test_delete_report_success(self, client: TestClient, auth_headers: dict):
        """Test successful report deletion."""
        report_id = "test-report-id"
        
        response = client.delete(
            f"{REPORTS_ENDPOINTS}/{report_id}",
            headers=auth_headers
        )
        
        # This will depend on your implementation
        assert response.status_code in [200, 204, 404]

    def test_delete_report_no_auth(self, client: TestClient):
        """Test deleting report without authentication."""
        response = client.delete(f"{REPORTS_ENDPOINTS}/test-report-id")
        
        assert response.status_code == 401

    def test_generate_report_different_areas(self, client: TestClient, auth_headers: dict):
        """Test report generation for different policy areas."""
        areas = ["EDUCACAO", "SAUDE", "MEIO_AMBIENTE"]
        
        for area in areas:
            response = client.post(
                f"{REPORTS_ENDPOINTS}/overview",
                headers=auth_headers,
                json={
                    "file_id": "test-file-id",
                    "area_politica_publica": area
                }
            )
            
            assert response.status_code == 200
            result = response.json()
            assert result["area_politica_publica"] == area

    @pytest.mark.asyncio
    async def test_generate_report_async(self, async_client: AsyncClient, auth_headers: dict):
        """Test report generation with async client."""
        response = await async_client.post(
            f"{REPORTS_ENDPOINTS}/individual",
            headers=auth_headers,
            json={
                "file_id": "test-file-id",
                "subtema": "ALIMENTACAO_ESCOLAR",
                "area_politica_publica": "EDUCACAO"
            }
        )
        
        assert response.status_code == 200

    def test_report_content_quality(self, client: TestClient, auth_headers: dict):
        """Test that generated reports have quality content."""
        response = client.post(
            f"{REPORTS_ENDPOINTS}/individual",
            headers=auth_headers,
            json={
                "file_id": "test-file-id",
                "subtema": "ALIMENTACAO_ESCOLAR",
                "area_politica_publica": "EDUCACAO"
            }
        )
        
        assert response.status_code == 200
        result = response.json()
        
        content = result["content"]
        # Check for basic report structure
        assert len(content) > 100  # Should be substantial
        # Could add more specific content checks here

    def test_report_generation_performance(self, client: TestClient, auth_headers: dict):
        """Test report generation performance."""
        response = client.post(
            f"{REPORTS_ENDPOINTS}/individual",
            headers=auth_headers,
            json={
                "file_id": "test-file-id",
                "subtema": "ALIMENTACAO_ESCOLAR",
                "area_politica_publica": "EDUCACAO"
            }
        )
        
        assert response.status_code == 200
        result = response.json()
        
        # Should complete within reasonable time
        processing_time = result.get("processing_time", 0)
        assert processing_time < 60.0  # Should complete within 1 minute

    def test_concurrent_report_generation(self, client: TestClient, auth_headers: dict):
        """Test handling of concurrent report generation requests."""
        import threading
        
        results = []
        
        def generate_report():
            response = client.post(
                f"{REPORTS_ENDPOINTS}/individual",
                headers=auth_headers,
                json={
                    "file_id": "test-file-id",
                    "subtema": "ALIMENTACAO_ESCOLAR",
                    "area_politica_publica": "EDUCACAO"
                }
            )
            results.append(response.status_code)
        
        # Create multiple threads
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=generate_report)
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        # All requests should succeed
        assert all(status == 200 for status in results)

    def test_report_validation_errors(self, client: TestClient, auth_headers: dict):
        """Test various validation errors in report generation."""
        # Missing required fields
        response = client.post(
            f"{REPORTS_ENDPOINTS}/individual",
            headers=auth_headers,
            json={}
        )
        assert response.status_code == 422
        
        # Invalid area/subtema combination
        response = client.post(
            f"{REPORTS_ENDPOINTS}/individual",
            headers=auth_headers,
            json={
                "file_id": "test-file-id",
                "subtema": "ALIMENTACAO_ESCOLAR",  # Education subtema
                "area_politica_publica": "SAUDE"   # But health area
            }
        )
        assert response.status_code == 400
