"""
Tests for the optimization system components.

This module contains comprehensive tests for the token optimization system
including ChunkRelevanceFilter, SemanticSummarizer, HierarchicalClassifier,
and TokenBudgetManager.
"""

import pytest
import asyncio
import tempfile
import time
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta

from src.api.services.chunk_relevance_filter import ChunkRelevanceFilter, RelevanceScore
from src.api.services.semantic_summarizer import SemanticS<PERSON>marizer, SummarizedChunk
from src.api.services.hierarchical_classifier import HierarchicalClassifier, ClassificationPipeline
from src.api.services.token_budget_manager import TokenBudgetManager, PriorityLevel, BudgetStatus
from src.api.services.optimized_batch_processing_service import OptimizedBatchProcessingService
from src.api.models.batch_processing import (
    TextChunk,
    ChunkResult,
    BatchProcessingRequest
)


class TestChunkRelevanceFilter:
    """Tests for ChunkRelevanceFilter component."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.filter = ChunkRelevanceFilter(
            relevance_threshold=0.3,
            max_chunks_per_pdf=10
        )
    
    def test_initialization(self):
        """Test filter initialization."""
        assert self.filter.relevance_threshold == 0.3
        assert self.filter.max_chunks_per_pdf == 10
        assert self.filter.use_keywords == True
        assert len(self.filter.area_keywords) > 0
    
    def test_area_keywords_loaded(self):
        """Test that area keywords are loaded correctly."""
        assert 'EDUCACAO' in self.filter.area_keywords
        assert 'SAUDE' in self.filter.area_keywords
        assert 'MEIO_AMBIENTE' in self.filter.area_keywords
        
        # Check some keywords exist
        educacao_keywords = self.filter.area_keywords['EDUCACAO']
        assert any('educacao' in keyword for keyword in educacao_keywords)
    
    @pytest.mark.asyncio
    async def test_filter_empty_chunks(self):
        """Test filtering with empty chunks."""
        chunks = []
        filtered_chunks, metrics = await self.filter.filter_chunks(chunks, 'EDUCACAO')
        
        assert len(filtered_chunks) == 0
        assert metrics.total_chunks == 0
        assert metrics.filtered_chunks == 0
    
    @pytest.mark.asyncio
    async def test_filter_relevant_chunks(self):
        """Test filtering with relevant chunks."""
        chunks = [
            TextChunk(
                text="A escola precisa de melhorias na infraestrutura educacional",
                chunk_id=0,
                page_start=1,
                page_end=1,
                char_start=0,
                char_end=50,
                pdf_path="/test/path/doc.pdf"
            ),
            TextChunk(
                text="O hospital implementou novos protocolos de saúde",
                chunk_id=1,
                page_start=1,
                page_end=1,
                char_start=50,
                char_end=100,
                pdf_path="/test/path/doc.pdf"
            )
        ]
        
        filtered_chunks, metrics = await self.filter.filter_chunks(chunks, 'EDUCACAO')
        
        # Should keep education-related chunk and filter out health-related
        assert len(filtered_chunks) <= len(chunks)
        assert metrics.total_chunks == 2
        assert metrics.reduction_percentage >= 0
        assert metrics.tokens_saved >= 0
    
    @pytest.mark.asyncio
    async def test_filter_threshold_enforcement(self):
        """Test that relevance threshold is enforced."""
        # Set high threshold
        self.filter.relevance_threshold = 0.9
        
        chunks = [
            TextChunk(
                text="Texto genérico sem palavras-chave específicas da área",
                chunk_id=0,
                page_start=1,
                page_end=1,
                char_start=0,
                char_end=50,
                pdf_path="/test/path/doc.pdf"
            )
        ]
        
        filtered_chunks, metrics = await self.filter.filter_chunks(chunks, 'EDUCACAO')
        
        # Should filter out chunk due to high threshold
        assert len(filtered_chunks) == 0
        assert metrics.tokens_saved > 0
    
    def test_get_filtering_config(self):
        """Test getting filtering configuration."""
        config = self.filter.get_filtering_config()
        
        assert 'relevance_threshold' in config
        assert 'max_chunks_per_pdf' in config
        assert 'use_keywords' in config
        assert 'areas_loaded' in config
        assert config['relevance_threshold'] == 0.3
    
    def test_update_threshold(self):
        """Test updating relevance threshold."""
        original_threshold = self.filter.relevance_threshold
        new_threshold = 0.7
        
        self.filter.update_threshold(new_threshold)
        
        assert self.filter.relevance_threshold == new_threshold
        assert self.filter.relevance_threshold != original_threshold


class TestSemanticSummarizer:
    """Tests for SemanticSummarizer component."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.summarizer = SemanticSummarizer(
            similarity_threshold=0.7,
            max_cluster_size=3
        )
    
    def test_initialization(self):
        """Test summarizer initialization."""
        assert self.summarizer.similarity_threshold == 0.7
        assert self.summarizer.max_cluster_size == 3
        assert self.summarizer.min_cluster_size == 2
        assert self.summarizer.preserve_evidence == True
    
    @pytest.mark.asyncio
    async def test_summarize_empty_chunks(self):
        """Test summarization with empty chunks."""
        chunks = []
        summaries, metrics = await self.summarizer.summarize_chunks(chunks, 'EDUCACAO')
        
        assert len(summaries) == 0
        assert metrics.original_chunks == 0
        assert metrics.summarized_chunks == 0
    
    @pytest.mark.asyncio
    async def test_summarize_single_chunk(self):
        """Test summarization with single chunk."""
        chunks = [
            TextChunk(
                text="A escola precisa de melhorias na infraestrutura educacional",
                chunk_id=0,
                page_start=1,
                page_end=1,
                char_start=0,
                char_end=50,
                pdf_path="/test/path/doc.pdf"
            )
        ]
        
        summaries, metrics = await self.summarizer.summarize_chunks(chunks, 'EDUCACAO')
        
        assert len(summaries) == 1
        assert summaries[0].summarized_text == chunks[0].text
        assert summaries[0].compression_ratio == 1.0
        assert metrics.original_chunks == 1
        assert metrics.summarized_chunks == 1
    
    @pytest.mark.asyncio
    async def test_summarize_similar_chunks(self):
        """Test summarization with similar chunks."""
        chunks = [
            TextChunk(
                text="A escola precisa de melhorias na infraestrutura educacional",
                chunk_id=0,
                page_start=1,
                page_end=1,
                char_start=0,
                char_end=50,
                pdf_path="/test/path/doc.pdf"
            ),
            TextChunk(
                text="A instituição de ensino requer reformas na estrutura escolar",
                chunk_id=1,
                page_start=1,
                page_end=1,
                char_start=50,
                char_end=100,
                pdf_path="/test/path/doc.pdf"
            ),
            TextChunk(
                text="O estabelecimento educacional necessita de melhorias físicas",
                chunk_id=2,
                page_start=2,
                page_end=2,
                char_start=100,
                char_end=150,
                pdf_path="/test/path/doc.pdf"
            )
        ]
        
        # Mock embedding model to return similar embeddings
        with patch.object(self.summarizer, 'embedding_model') as mock_model:
            if mock_model:
                mock_model.encode.return_value = [[0.1, 0.2, 0.3], [0.15, 0.25, 0.35], [0.12, 0.22, 0.32]]
        
        summaries, metrics = await self.summarizer.summarize_chunks(chunks, 'EDUCACAO')
        
        # Should create fewer summaries than original chunks
        assert len(summaries) <= len(chunks)
        assert metrics.original_chunks == 3
        assert metrics.compression_ratio <= 1.0
        assert metrics.tokens_saved >= 0
    
    def test_get_summarization_config(self):
        """Test getting summarization configuration."""
        config = self.summarizer.get_summarization_config()
        
        assert 'similarity_threshold' in config
        assert 'max_cluster_size' in config
        assert 'min_cluster_size' in config
        assert 'preserve_evidence' in config
        assert config['similarity_threshold'] == 0.7
    
    def test_update_similarity_threshold(self):
        """Test updating similarity threshold."""
        original_threshold = self.summarizer.similarity_threshold
        new_threshold = 0.8
        
        self.summarizer.update_similarity_threshold(new_threshold)
        
        assert self.summarizer.similarity_threshold == new_threshold
        assert self.summarizer.similarity_threshold != original_threshold


class TestHierarchicalClassifier:
    """Tests for HierarchicalClassifier component."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.classifier = HierarchicalClassifier()
    
    def test_initialization(self):
        """Test classifier initialization."""
        assert self.classifier.pipeline_config is not None
        assert self.classifier.chunk_classifier is not None
        assert self.classifier.relevance_filter is not None
        assert self.classifier.semantic_summarizer is not None
    
    @pytest.mark.asyncio
    async def test_classify_empty_chunks(self):
        """Test classification with empty chunks."""
        chunks = []
        results, metrics = await self.classifier.classify_chunks_hierarchical(chunks, 'EDUCACAO')
        
        assert len(results) == 0
        assert metrics.total_stages == 0
        assert metrics.total_tokens_saved == 0
    
    @pytest.mark.asyncio
    async def test_classify_single_chunk(self):
        """Test classification with single chunk."""
        chunks = [
            TextChunk(
                text="A escola precisa de melhorias na infraestrutura educacional",
                chunk_id=0,
                page_start=1,
                page_end=1,
                char_start=0,
                char_end=50,
                pdf_path="/test/path/doc.pdf"
            )
        ]
        
        # Mock the chunk classifier to return predictable results
        with patch.object(self.classifier.chunk_classifier, 'classify_chunks') as mock_classify:
            mock_classify.return_value = [
                ChunkResult(
                    chunk=chunks[0],
                    subtemas=['INFRAESTRUTURA_ESCOLAR'],
                    confidence_scores={'INFRAESTRUTURA_ESCOLAR': 0.8},
                    evidence_texts={'INFRAESTRUTURA_ESCOLAR': 'escola precisa melhorias'},
                    processing_time=1.0
                )
            ]
            
            results, metrics = await self.classifier.classify_chunks_hierarchical(chunks, 'EDUCACAO')
            
            assert len(results) == 1
            assert results[0].subtemas == ['INFRAESTRUTURA_ESCOLAR']
            assert metrics.total_stages > 0
            assert metrics.total_processing_time > 0
    
    @pytest.mark.asyncio
    async def test_hierarchical_reduction(self):
        """Test that hierarchical processing reduces tokens."""
        chunks = [
            TextChunk(
                text="A escola precisa de melhorias na infraestrutura educacional",
                chunk_id=0,
                page_start=1,
                page_end=1,
                char_start=0,
                char_end=50,
                pdf_path="/test/path/doc.pdf"
            ),
            TextChunk(
                text="O hospital implementou novos protocolos de saúde",
                chunk_id=1,
                page_start=1,
                page_end=1,
                char_start=50,
                char_end=100,
                pdf_path="/test/path/doc.pdf"
            ),
            TextChunk(
                text="Texto genérico sem relevância para educação",
                chunk_id=2,
                page_start=2,
                page_end=2,
                char_start=100,
                char_end=150,
                pdf_path="/test/path/doc.pdf"
            )
        ]
        
        # Mock components for predictable behavior
        with patch.object(self.classifier.chunk_classifier, 'classify_chunks') as mock_classify:
            mock_classify.return_value = [
                ChunkResult(
                    chunk=chunks[0],
                    subtemas=['INFRAESTRUTURA_ESCOLAR'],
                    confidence_scores={'INFRAESTRUTURA_ESCOLAR': 0.8},
                    evidence_texts={'INFRAESTRUTURA_ESCOLAR': 'escola precisa melhorias'},
                    processing_time=1.0
                )
            ]
            
            results, metrics = await self.classifier.classify_chunks_hierarchical(chunks, 'EDUCACAO')
            
            # Should reduce number of chunks processed
            assert len(results) <= len(chunks)
            assert metrics.total_tokens_saved > 0
            assert metrics.final_reduction_ratio > 0
    
    def test_get_pipeline_config(self):
        """Test getting pipeline configuration."""
        config = self.classifier.get_pipeline_config()
        
        assert 'enable_area_detection' in config
        assert 'enable_relevance_filtering' in config
        assert 'enable_semantic_summarization' in config
        assert 'early_stopping_enabled' in config
    
    def test_update_pipeline_config(self):
        """Test updating pipeline configuration."""
        original_value = self.classifier.pipeline_config.enable_area_detection
        new_value = not original_value
        
        self.classifier.update_pipeline_config(enable_area_detection=new_value)
        
        assert self.classifier.pipeline_config.enable_area_detection == new_value
        assert self.classifier.pipeline_config.enable_area_detection != original_value


class TestTokenBudgetManager:
    """Tests for TokenBudgetManager component."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.temp_dir = tempfile.mkdtemp()
        self.budget_manager = TokenBudgetManager(
            budget_config={'daily_limit': 10000, 'hourly_limit': 1000},
            storage_path=self.temp_dir
        )
    
    @pytest.mark.asyncio
    async def test_token_allocation_approval(self):
        """Test token allocation approval."""
        approved, reason, allocated = await self.budget_manager.request_token_allocation(
            estimated_tokens=500,
            job_id="test-job-1",
            user_id="test-user",
            area="EDUCACAO",
            priority=PriorityLevel.MEDIUM
        )
        
        assert approved == True
        assert allocated == 500
        assert "approved" in reason.lower()
    
    @pytest.mark.asyncio
    async def test_token_allocation_daily_limit(self):
        """Test token allocation with daily limit exceeded."""
        # Request tokens that exceed daily limit
        approved, reason, allocated = await self.budget_manager.request_token_allocation(
            estimated_tokens=15000,  # Exceeds daily limit of 10000
            job_id="test-job-2",
            user_id="test-user",
            area="EDUCACAO",
            priority=PriorityLevel.MEDIUM
        )
        
        assert approved == False
        assert allocated == 0
        assert "daily limit" in reason.lower()
    
    @pytest.mark.asyncio
    async def test_token_usage_recording(self):
        """Test recording token usage."""
        operation_id = "test-operation-1"
        tokens_used = 1000
        
        await self.budget_manager.record_token_usage(
            operation_id=operation_id,
            operation_type="classification",
            tokens_used=tokens_used,
            processing_time=2.5,
            chunks_processed=10,
            area="EDUCACAO",
            user_id="test-user"
        )
        
        # Check that usage was recorded
        metrics = self.budget_manager.get_current_metrics()
        assert metrics.total_used >= tokens_used
        assert len(self.budget_manager.usage_history) > 0
    
    def test_budget_metrics_calculation(self):
        """Test budget metrics calculation."""
        metrics = self.budget_manager.get_current_metrics()
        
        assert metrics.total_allocated == 10000
        assert metrics.total_used >= 0
        assert metrics.total_remaining >= 0
        assert 0 <= metrics.usage_percentage <= 100
        assert isinstance(metrics.status, BudgetStatus)
    
    def test_optimization_suggestions(self):
        """Test optimization suggestions generation."""
        # Add some usage history
        self.budget_manager.usage_history = [
            self.budget_manager.usage_history[0] if self.budget_manager.usage_history else 
            type('Usage', (), {
                'tokens_used': 1000,
                'chunks_processed': 5,
                'timestamp': datetime.utcnow()
            })() for _ in range(10)
        ]
        
        suggestions = self.budget_manager.get_optimization_suggestions()
        
        assert isinstance(suggestions, list)
        # Should have suggestions when usage is high
        if suggestions:
            assert all(hasattr(s, 'suggestion_type') for s in suggestions)
            assert all(hasattr(s, 'potential_savings') for s in suggestions)
    
    def test_get_budget_config(self):
        """Test getting budget configuration."""
        config = self.budget_manager.get_budget_config()
        
        assert 'daily_limit' in config
        assert 'hourly_limit' in config
        assert 'cost_per_token' in config
        assert config['daily_limit'] == 10000
    
    def test_update_budget_config(self):
        """Test updating budget configuration."""
        new_limit = 20000
        self.budget_manager.update_budget_config(daily_limit=new_limit)
        
        assert self.budget_manager.budget_allocation.daily_limit == new_limit
        
        config = self.budget_manager.get_budget_config()
        assert config['daily_limit'] == new_limit


class TestOptimizedBatchProcessingService:
    """Tests for OptimizedBatchProcessingService component."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.service = OptimizedBatchProcessingService(
            enable_token_optimization=True,
            max_workers=2
        )
    
    def test_initialization_with_optimization(self):
        """Test service initialization with optimization enabled."""
        assert self.service.enable_token_optimization == True
        assert self.service.relevance_filter is not None
        assert self.service.semantic_summarizer is not None
        assert self.service.hierarchical_classifier is not None
        assert self.service.token_budget_manager is not None
    
    def test_initialization_without_optimization(self):
        """Test service initialization without optimization."""
        service = OptimizedBatchProcessingService(enable_token_optimization=False)
        
        assert service.enable_token_optimization == False
        assert service.relevance_filter is None
        assert service.semantic_summarizer is None
        assert service.hierarchical_classifier is None
        assert service.token_budget_manager is None
        assert service.chunk_classifier is not None
    
    def test_get_optimization_stats(self):
        """Test getting optimization statistics."""
        stats = self.service.get_optimization_stats()
        
        assert 'optimization_enabled' in stats
        assert stats['optimization_enabled'] == True
        assert 'budget_metrics' in stats
        assert 'filter_config' in stats
        assert 'summarizer_config' in stats
        assert 'pipeline_config' in stats
    
    def test_get_optimization_suggestions(self):
        """Test getting optimization suggestions."""
        suggestions = self.service.get_optimization_suggestions()
        
        assert isinstance(suggestions, list)
        # Suggestions might be empty if no usage history
    
    def test_update_optimization_config(self):
        """Test updating optimization configuration."""
        new_threshold = 0.8
        self.service.update_optimization_config(relevance_threshold=new_threshold)
        
        # Check that configuration was updated
        config = self.service.get_optimization_stats()
        assert config['filter_config']['relevance_threshold'] == new_threshold
    
    @pytest.mark.asyncio
    async def test_start_batch_processing_invalid_folder(self):
        """Test batch processing with invalid folder."""
        request = BatchProcessingRequest(
            folder_path="/nonexistent/path",
            area="EDUCACAO"
        )
        
        with pytest.raises(ValueError, match="Folder does not exist"):
            await self.service.start_batch_processing(request, "test-user")
    
    @pytest.mark.asyncio
    async def test_estimate_total_tokens(self):
        """Test token estimation."""
        # Create temporary PDFs
        temp_dir = tempfile.mkdtemp()
        pdf_files = []
        
        for i in range(3):
            pdf_file = Path(temp_dir) / f"test_{i}.pdf"
            pdf_file.write_text(f"Mock PDF content {i}")
            pdf_files.append(pdf_file)
        
        # Mock PDF extraction
        with patch.object(self.service.pdf_extractor, 'extract_and_chunk') as mock_extract:
            mock_extract.return_value = [
                TextChunk(
                    text="Mock chunk content",
                    chunk_id=0,
                    page_start=1,
                    page_end=1,
                    char_start=0,
                    char_end=20,
                    pdf_path=str(pdf_files[0]),
                    token_count=100
                )
            ]
            
            estimated_tokens = await self.service._estimate_total_tokens(pdf_files, None)
            
            assert estimated_tokens > 0
            assert estimated_tokens == 300  # 100 tokens per PDF * 3 PDFs


class TestIntegrationOptimization:
    """Integration tests for the complete optimization system."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_optimization(self):
        """Test complete end-to-end optimization pipeline."""
        # Create test chunks
        chunks = [
            TextChunk(
                text="A escola precisa de melhorias na infraestrutura educacional",
                chunk_id=0,
                page_start=1,
                page_end=1,
                char_start=0,
                char_end=50,
                pdf_path="/test/path/doc.pdf"
            ),
            TextChunk(
                text="O hospital implementou novos protocolos de saúde",
                chunk_id=1,
                page_start=1,
                page_end=1,
                char_start=50,
                char_end=100,
                pdf_path="/test/path/doc.pdf"
            ),
            TextChunk(
                text="Texto genérico sem relevância específica",
                chunk_id=2,
                page_start=2,
                page_end=2,
                char_start=100,
                char_end=150,
                pdf_path="/test/path/doc.pdf"
            )
        ]
        
        # Initialize optimization components
        relevance_filter = ChunkRelevanceFilter(relevance_threshold=0.3)
        semantic_summarizer = SemanticSummarizer(similarity_threshold=0.7)
        hierarchical_classifier = HierarchicalClassifier()
        
        # Step 1: Filter by relevance
        filtered_chunks, filter_metrics = await relevance_filter.filter_chunks(chunks, 'EDUCACAO')
        
        # Step 2: Summarize similar chunks
        summaries, summary_metrics = await semantic_summarizer.summarize_chunks(filtered_chunks, 'EDUCACAO')
        
        # Step 3: Classify hierarchically
        results, hierarchical_metrics = await hierarchical_classifier.classify_chunks_hierarchical(
            filtered_chunks, 'EDUCACAO'
        )
        
        # Verify optimization achieved
        assert len(filtered_chunks) <= len(chunks)
        assert filter_metrics.tokens_saved >= 0
        assert summary_metrics.compression_ratio <= 1.0
        assert hierarchical_metrics.total_tokens_saved >= 0
        assert hierarchical_metrics.final_reduction_ratio >= 0
        
        # Calculate total optimization
        total_original_tokens = sum(
            chunk.token_count or len(chunk.text) // 4 
            for chunk in chunks
        )
        total_optimized_tokens = total_original_tokens - hierarchical_metrics.total_tokens_saved
        optimization_ratio = hierarchical_metrics.total_tokens_saved / total_original_tokens
        
        # Should achieve significant optimization
        assert optimization_ratio >= 0.1  # At least 10% reduction
        
        print(f"Optimization achieved: {optimization_ratio:.2%} token reduction")
        print(f"Original tokens: {total_original_tokens}")
        print(f"Optimized tokens: {total_optimized_tokens}")
        print(f"Tokens saved: {hierarchical_metrics.total_tokens_saved}")
    
    @pytest.mark.asyncio
    async def test_budget_integration(self):
        """Test integration with budget management."""
        budget_manager = TokenBudgetManager(
            budget_config={'daily_limit': 5000, 'hourly_limit': 500}
        )
        
        # Request allocation
        approved, reason, allocated = await budget_manager.request_token_allocation(
            estimated_tokens=1000,
            job_id="test-job",
            user_id="test-user",
            area="EDUCACAO",
            priority=PriorityLevel.HIGH
        )
        
        assert approved == True
        assert allocated == 1000
        
        # Record usage
        await budget_manager.record_token_usage(
            operation_id="test-operation",
            operation_type="classification",
            tokens_used=800,  # Less than allocated (optimization worked)
            processing_time=2.0,
            chunks_processed=10,
            area="EDUCACAO",
            user_id="test-user"
        )
        
        # Check metrics
        metrics = budget_manager.get_current_metrics()
        assert metrics.total_used >= 800
        assert metrics.total_remaining > 0
        
        # Should have suggestions when usage is recorded
        suggestions = budget_manager.get_optimization_suggestions()
        assert isinstance(suggestions, list)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])