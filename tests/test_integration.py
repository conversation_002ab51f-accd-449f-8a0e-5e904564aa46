"""
Integration tests for the complete API workflow.
"""

import pytest
import json
from fastapi.testclient import Test<PERSON><PERSON>

from tests.conftest import (
    API_PREFIX, AUTH_ENDPOINTS, FILES_ENDPOINTS, 
    ANONYMIZATION_ENDPOINTS, CLASSIFICATION_ENDPOINTS, REPORTS_ENDPOINTS
)


@pytest.mark.integration
class TestCompleteWorkflow:
    """Test complete API workflow from authentication to report generation."""

    def test_complete_workflow_success(self, client: TestClient, sample_csv_content: str):
        """Test complete workflow: login -> upload -> classify -> anonymize -> report."""
        
        # Step 1: Login
        login_response = client.post(
            f"{AUTH_ENDPOINTS}/login",
            json={
                "email": "<EMAIL>",
                "password": "test123"
            }
        )
        assert login_response.status_code == 200
        
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Step 2: Upload file
        files = {
            'file': ('workflow_test.csv', sample_csv_content, 'text/csv')
        }
        metadata = {
            "description": "Integration test file",
            "tags": ["integration", "test"]
        }
        data = {
            'metadata': json.dumps(metadata)
        }
        
        upload_response = client.post(
            f"{FILES_ENDPOINTS}/upload",
            headers=headers,
            files=files,
            data=data
        )
        assert upload_response.status_code == 200
        
        file_id = upload_response.json()["file_id"]
        
        # Step 3: Classify text
        classify_response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/text",
            headers=headers,
            json={
                "text": "Problema na merenda escolar da escola municipal",
                "classification_type": "multilabel"
            }
        )
        assert classify_response.status_code == 200
        
        classification = classify_response.json()
        assert "area_politica_publica" in classification
        
        # Step 4: Anonymize text
        anonymize_response = client.post(
            f"{ANONYMIZATION_ENDPOINTS}/text",
            headers=headers,
            json={
                "text": "João Silva, CPF 123.456.789-00, estuda na Escola Municipal Santos Dumont",
                "language": "pt"
            }
        )
        assert anonymize_response.status_code == 200
        
        anonymization = anonymize_response.json()
        assert "anonymized_text" in anonymization
        
        # Step 5: Generate report
        report_response = client.post(
            f"{REPORTS_ENDPOINTS}/individual",
            headers=headers,
            json={
                "file_id": file_id,
                "subtema": "ALIMENTACAO_ESCOLAR",
                "area_politica_publica": "EDUCACAO"
            }
        )
        assert report_response.status_code == 200
        
        report = report_response.json()
        assert "report_id" in report
        assert "content" in report

    def test_workflow_with_file_classification(self, client: TestClient, sample_csv_content: str):
        """Test workflow with file classification."""
        
        # Login
        login_response = client.post(
            f"{AUTH_ENDPOINTS}/login",
            json={"email": "<EMAIL>", "password": "test123"}
        )
        assert login_response.status_code == 200
        
        headers = {"Authorization": f"Bearer {login_response.json()['access_token']}"}
        
        # Upload and classify file in one step
        files = {
            'file': ('classify_test.csv', sample_csv_content, 'text/csv')
        }
        data = {
            'classification_type': 'multilabel',
            'text_column': 'relato'
        }
        
        classify_response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/file",
            headers=headers,
            files=files,
            data=data
        )
        assert classify_response.status_code == 200
        
        result = classify_response.json()
        assert "file_id" in result
        assert "results" in result
        assert len(result["results"]) > 0

    def test_workflow_error_handling(self, client: TestClient):
        """Test workflow error handling and recovery."""
        
        # Try to access protected endpoint without auth
        response = client.get(f"{FILES_ENDPOINTS}/")
        assert response.status_code == 401
        
        # Login with wrong credentials
        response = client.post(
            f"{AUTH_ENDPOINTS}/login",
            json={"email": "<EMAIL>", "password": "wrong"}
        )
        assert response.status_code == 401
        
        # Login correctly
        login_response = client.post(
            f"{AUTH_ENDPOINTS}/login",
            json={"email": "<EMAIL>", "password": "test123"}
        )
        assert login_response.status_code == 200
        
        headers = {"Authorization": f"Bearer {login_response.json()['access_token']}"}
        
        # Try to upload invalid file
        files = {
            'file': ('invalid.txt', 'Not a CSV', 'text/plain')
        }
        response = client.post(
            f"{FILES_ENDPOINTS}/upload",
            headers=headers,
            files=files
        )
        assert response.status_code == 400
        
        # Try to classify empty text
        response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/text",
            headers=headers,
            json={"text": "", "classification_type": "multilabel"}
        )
        assert response.status_code == 400

    def test_workflow_performance(self, client: TestClient, sample_csv_content: str):
        """Test workflow performance with timing."""
        import time
        
        start_time = time.time()
        
        # Login
        login_response = client.post(
            f"{AUTH_ENDPOINTS}/login",
            json={"email": "<EMAIL>", "password": "test123"}
        )
        assert login_response.status_code == 200
        
        headers = {"Authorization": f"Bearer {login_response.json()['access_token']}"}
        
        # Upload file
        files = {
            'file': ('performance_test.csv', sample_csv_content, 'text/csv')
        }
        upload_response = client.post(
            f"{FILES_ENDPOINTS}/upload",
            headers=headers,
            files=files
        )
        assert upload_response.status_code == 200
        
        # Classify text
        classify_response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/text",
            headers=headers,
            json={
                "text": "Problema na escola municipal",
                "classification_type": "multilabel"
            }
        )
        assert classify_response.status_code == 200
        
        total_time = time.time() - start_time
        
        # Should complete within reasonable time
        assert total_time < 30.0  # 30 seconds max

    def test_concurrent_workflows(self, client: TestClient, sample_csv_content: str):
        """Test multiple concurrent workflows."""
        import threading
        import time
        
        results = []
        
        def run_workflow():
            try:
                # Login
                login_response = client.post(
                    f"{AUTH_ENDPOINTS}/login",
                    json={"email": "<EMAIL>", "password": "test123"}
                )
                if login_response.status_code != 200:
                    results.append(False)
                    return
                
                headers = {"Authorization": f"Bearer {login_response.json()['access_token']}"}
                
                # Upload file
                files = {
                    'file': (f'concurrent_test_{threading.current_thread().ident}.csv', 
                            sample_csv_content, 'text/csv')
                }
                upload_response = client.post(
                    f"{FILES_ENDPOINTS}/upload",
                    headers=headers,
                    files=files
                )
                
                results.append(upload_response.status_code == 200)
                
            except Exception as e:
                print(f"Workflow error: {e}")
                results.append(False)
        
        # Create multiple threads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=run_workflow)
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        # All workflows should succeed
        assert all(results)

    def test_api_health_check(self, client: TestClient):
        """Test API health and basic endpoints."""
        
        # Test root endpoint
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        
        # Test health endpoint
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"

    def test_api_documentation(self, client: TestClient):
        """Test API documentation endpoints."""
        
        # Test OpenAPI schema
        response = client.get("/openapi.json")
        assert response.status_code == 200
        schema = response.json()
        assert "openapi" in schema
        assert "info" in schema
        assert "paths" in schema
        
        # Test Swagger UI
        response = client.get("/docs")
        assert response.status_code == 200
        
        # Test ReDoc
        response = client.get("/redoc")
        assert response.status_code == 200


@pytest.mark.integration
class TestAPIConsistency:
    """Test API consistency and standards compliance."""

    def test_response_format_consistency(self, client: TestClient):
        """Test that all endpoints return consistent response formats."""
        
        # Login to get token
        login_response = client.post(
            f"{AUTH_ENDPOINTS}/login",
            json={"email": "<EMAIL>", "password": "test123"}
        )
        assert login_response.status_code == 200
        
        headers = {"Authorization": f"Bearer {login_response.json()['access_token']}"}
        
        # Test various endpoints for consistent error format
        endpoints_to_test = [
            (f"{FILES_ENDPOINTS}/non-existent", "GET"),
            (f"{REPORTS_ENDPOINTS}/non-existent", "GET"),
        ]
        
        for endpoint, method in endpoints_to_test:
            if method == "GET":
                response = client.get(endpoint, headers=headers)
            
            if response.status_code == 404:
                data = response.json()
                assert "detail" in data

    def test_cors_headers(self, client: TestClient):
        """Test CORS headers are properly set."""
        
        # Test preflight request
        response = client.options(
            f"{AUTH_ENDPOINTS}/login",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            }
        )
        
        # Should handle CORS properly
        assert response.status_code in [200, 204, 405]

    def test_content_type_handling(self, client: TestClient):
        """Test proper content type handling."""
        
        # Test JSON content type
        response = client.post(
            f"{AUTH_ENDPOINTS}/login",
            json={"email": "<EMAIL>", "password": "test123"},
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 200
        
        # Test invalid content type for JSON endpoint
        response = client.post(
            f"{AUTH_ENDPOINTS}/login",
            data="email=<EMAIL>&password=test123",
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        assert response.status_code == 422

    def test_rate_limiting_behavior(self, client: TestClient):
        """Test rate limiting behavior across endpoints."""
        
        # Make multiple requests to test rate limiting
        # Note: This test assumes rate limiting is configured
        responses = []
        for i in range(10):
            response = client.post(
                f"{AUTH_ENDPOINTS}/login",
                json={"email": "<EMAIL>", "password": "test123"}
            )
            responses.append(response.status_code)
        
        # Should get mostly 200s (successful logins)
        success_count = sum(1 for status in responses if status == 200)
        assert success_count > 0  # At least some should succeed
