"""
Tests for CrewAI integration in Simple Class.

This module tests the CrewAI agents, crews, and API integration
to ensure proper functionality and error handling.
"""

import pytest
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

# Import CrewAI components with fallback handling
try:
    from src.agents.anonymization_agent import AnonymizationAgent
    from src.agents.classification_agent import ClassificationAgent
    from src.agents.report_agent import ReportAgent
    from src.crews.processing_crew import ProcessingCrew
    CREWAI_AVAILABLE = True
except ImportError:
    AnonymizationAgent = None
    ClassificationAgent = None
    ReportAgent = None
    ProcessingCrew = None
    CREWAI_AVAILABLE = False


class TestAnonymizationAgent:
    """Test cases for AnonymizationAgent."""
    
    @pytest.fixture
    def mock_anonymization_tool(self):
        """Mock anonymization tool for testing."""
        mock_tool = Mock()
        mock_tool.anonymize_text.return_value = {
            "anonymized_text": "Test <PERSON> lives at <ENDEREÇO>",
            "entities_found": [
                {
                    "entity_type": "PERSON",
                    "start": 5,
                    "end": 10,
                    "score": 0.95,
                    "text": "João"
                }
            ],
            "entities_count": 1,
            "success": True
        }
        return mock_tool
    
    @pytest.fixture
    def anonymization_agent(self, mock_anonymization_tool):
        """Create AnonymizationAgent with mocked tools."""
        if not CREWAI_AVAILABLE:
            pytest.skip("CrewAI not available")
        
        with patch('src.agents.anonymization_agent.AnonymizationTool', return_value=mock_anonymization_tool):
            agent = AnonymizationAgent()
            agent.text_anonymizer = mock_anonymization_tool
            return agent
    
    def test_agent_initialization(self, anonymization_agent):
        """Test agent initialization."""
        assert anonymization_agent.role == "Data Privacy Specialist"
        assert "LGPD compliance" in anonymization_agent.goal
        assert anonymization_agent.text_anonymizer is not None
    
    def test_text_anonymization_task(self, anonymization_agent):
        """Test text anonymization task execution."""
        task_input = {
            "text": "João Silva mora na Rua das Flores, 123",
            "language": "pt"
        }
        
        result = anonymization_agent.execute_task(task_input)
        
        assert result["success"] is True
        assert result["task_type"] == "text_anonymization"
        assert "anonymized_text" in result
        assert "entities_found" in result
        assert result["agent"] == "Data Privacy Specialist"
    
    def test_invalid_input_handling(self, anonymization_agent):
        """Test handling of invalid inputs."""
        # Empty input
        result = anonymization_agent.execute_task({})
        assert result["success"] is False
        assert "No text or pdf_path provided" in result["error"]
        
        # Both text and pdf_path provided
        result = anonymization_agent.execute_task({
            "text": "test",
            "pdf_path": "test.pdf"
        })
        assert result["success"] is False
        assert "Provide either text or pdf_path, not both" in result["error"]
    
    def test_get_supported_entities(self, anonymization_agent):
        """Test getting supported entity types."""
        entities = anonymization_agent.get_supported_entities()
        expected_entities = ["PERSON", "EMAIL_ADDRESS", "PHONE_NUMBER", "CPF", "ESCOLA", "ENDEREÇO"]
        
        for entity in expected_entities:
            assert entity in entities


class TestClassificationAgent:
    """Test cases for ClassificationAgent."""
    
    @pytest.fixture
    def mock_classification_function(self):
        """Mock classification function for testing."""
        def mock_classify(relato, subtemas, area, model_name, api_key):
            # Return mock classification results
            return ["Infraestrutura", "Transporte"]
        
        return mock_classify
    
    @pytest.fixture
    def classification_agent(self, mock_classification_function):
        """Create ClassificationAgent with mocked functions."""
        if not CREWAI_AVAILABLE:
            pytest.skip("CrewAI not available")
        
        with patch('src.agents.classification_agent.classificar_relato_multilabel', mock_classification_function):
            agent = ClassificationAgent()
            return agent
    
    def test_agent_initialization(self, classification_agent):
        """Test agent initialization."""
        assert classification_agent.role == "Text Classification Specialist"
        assert "multilabel classification" in classification_agent.goal
        assert "EDUCACAO" in classification_agent.available_areas
        assert "SAUDE" in classification_agent.available_areas
    
    def test_text_classification_task(self, classification_agent):
        """Test text classification task execution."""
        task_input = {
            "text": "Problema com a infraestrutura da escola e transporte escolar",
            "area": "EDUCACAO",
            "model_name": "meta-llama/Llama-3.3-70B-Instruct-Turbo"
        }
        
        with patch.object(classification_agent, 'config', {'LLM_MODEL': 'test-model'}):
            with patch.object(classification_agent, 'env_vars', {'TOGETHER_API_KEY': 'test-key'}):
                result = classification_agent.execute_task(task_input)
        
        assert result["success"] is True
        assert result["task_type"] == "multilabel_classification"
        assert result["area"] == "EDUCACAO"
        assert "subtemas_identificados" in result
        assert "colunas_binarias" in result
    
    def test_invalid_area_handling(self, classification_agent):
        """Test handling of invalid classification area."""
        task_input = {
            "text": "Test text",
            "area": "INVALID_AREA"
        }
        
        result = classification_agent.execute_task(task_input)
        assert result["success"] is False
        assert "Invalid area" in result["error"]
    
    def test_get_available_areas(self, classification_agent):
        """Test getting available classification areas."""
        areas = classification_agent.get_available_areas()
        assert "EDUCACAO" in areas
        assert "SAUDE" in areas
        assert "MEIO_AMBIENTE" in areas
    
    def test_get_subtemas_for_area(self, classification_agent):
        """Test getting subtemas for specific area."""
        subtemas = classification_agent.get_subtemas_for_area("EDUCACAO")
        assert len(subtemas) > 0
        assert "Infraestrutura" in subtemas


class TestReportAgent:
    """Test cases for ReportAgent."""
    
    @pytest.fixture
    def mock_claude_function(self):
        """Mock Claude report generation function."""
        def mock_generate_report(prompt, model_name, api_key):
            return "# Relatório Tático\n\nEste é um relatório de teste gerado pelo Claude."
        
        return mock_generate_report
    
    @pytest.fixture
    def sample_dataframe(self):
        """Create sample DataFrame for testing."""
        return pd.DataFrame({
            'text': [
                'Problema com infraestrutura escolar',
                'Falta de transporte escolar',
                'Bullying na escola'
            ],
            'SUBTEMA_INFRAESTRUTURA': [1, 0, 0],
            'SUBTEMA_TRANSPORTE': [0, 1, 0],
            'SUBTEMA_BULLYING': [0, 0, 1]
        })
    
    @pytest.fixture
    def report_agent(self, mock_claude_function):
        """Create ReportAgent with mocked functions."""
        if not CREWAI_AVAILABLE:
            pytest.skip("CrewAI not available")
        
        with patch('src.agents.report_agent.gerar_relatorio_com_claude', mock_claude_function):
            with patch('src.agents.report_agent.obter_colunas_subtemas', return_value=['SUBTEMA_INFRAESTRUTURA']):
                with patch('src.agents.report_agent.analisar_estatisticas_subtemas', return_value={'Infraestrutura': {'total': 1}}):
                    with patch('src.agents.report_agent.gerar_prompt_multiplos_subtemas', return_value="Test prompt"):
                        agent = ReportAgent()
                        agent.config = {'ANTHROPIC_API_KEY': 'test-key'}
                        return agent
    
    def test_agent_initialization(self, report_agent):
        """Test agent initialization."""
        assert report_agent.role == "Tactical Report Analyst"
        assert "tactical reports" in report_agent.goal
        assert "overview" in report_agent.report_types
    
    def test_overview_report_generation(self, report_agent, sample_dataframe):
        """Test overview report generation."""
        task_input = {
            "data": sample_dataframe,
            "report_type": "overview",
            "area": "EDUCACAO"
        }
        
        result = report_agent.execute_task(task_input)
        
        assert result["success"] is True
        assert result["task_type"] == "overview_report"
        assert "report_content" in result
        assert result["area"] == "EDUCACAO"
    
    def test_invalid_report_type(self, report_agent, sample_dataframe):
        """Test handling of invalid report type."""
        task_input = {
            "data": sample_dataframe,
            "report_type": "invalid_type"
        }
        
        result = report_agent.execute_task(task_input)
        assert result["success"] is False
        assert "Invalid report type" in result["error"]
    
    def test_get_available_report_types(self, report_agent):
        """Test getting available report types."""
        types = report_agent.get_available_report_types()
        assert "overview" in types
        assert "subtema" in types
        assert "criminal" in types


class TestProcessingCrew:
    """Test cases for ProcessingCrew."""
    
    @pytest.fixture
    def mock_agents(self):
        """Create mock agents for testing."""
        mock_anon = Mock()
        mock_anon.execute_task.return_value = {
            "success": True,
            "anonymized_text": "Anonymized text",
            "entities_found": []
        }
        
        mock_class = Mock()
        mock_class.execute_task.return_value = {
            "success": True,
            "subtemas_identificados": ["Infraestrutura"],
            "colunas_binarias": {"SUBTEMA_INFRAESTRUTURA": 1}
        }
        
        mock_report = Mock()
        mock_report.execute_task.return_value = {
            "success": True,
            "report_content": "Test report"
        }
        
        return mock_anon, mock_class, mock_report
    
    @pytest.fixture
    def processing_crew(self, mock_agents):
        """Create ProcessingCrew with mocked agents."""
        if not CREWAI_AVAILABLE:
            pytest.skip("CrewAI not available")
        
        mock_anon, mock_class, mock_report = mock_agents
        
        with patch('src.crews.processing_crew.AnonymizationAgent', return_value=mock_anon):
            with patch('src.crews.processing_crew.ClassificationAgent', return_value=mock_class):
                with patch('src.crews.processing_crew.ReportAgent', return_value=mock_report):
                    crew = ProcessingCrew()
                    crew.anonymization_agent = mock_anon
                    crew.classification_agent = mock_class
                    crew.report_agent = mock_report
                    return crew
    
    def test_crew_initialization(self, processing_crew):
        """Test crew initialization."""
        assert processing_crew.name == "ProcessingCrew"
        assert "full_pipeline" in processing_crew.supported_workflows
        assert processing_crew.anonymization_agent is not None
        assert processing_crew.classification_agent is not None
        assert processing_crew.report_agent is not None
    
    def test_full_pipeline_workflow(self, processing_crew):
        """Test full pipeline workflow execution."""
        workflow_input = {
            "workflow_type": "full_pipeline",
            "text": "Test text for processing",
            "area": "EDUCACAO"
        }
        
        result = processing_crew.kickoff(workflow_input)
        
        assert result["success"] is True
        assert result["workflow_type"] == "full_pipeline"
        assert "execution_id" in result
        assert "execution_time" in result
    
    def test_partial_workflow(self, processing_crew):
        """Test partial workflow execution."""
        workflow_input = {
            "workflow_type": "anonymize_classify",
            "text": "Test text for processing",
            "area": "EDUCACAO"
        }
        
        result = processing_crew.kickoff(workflow_input)
        
        assert result["success"] is True
        assert result["workflow_type"] == "anonymize_classify"
    
    def test_invalid_workflow_type(self, processing_crew):
        """Test handling of invalid workflow type."""
        workflow_input = {
            "workflow_type": "invalid_workflow",
            "text": "Test text"
        }
        
        result = processing_crew.kickoff(workflow_input)
        assert result["success"] is False
        assert "Invalid inputs provided" in result["error"]
    
    def test_get_supported_workflows(self, processing_crew):
        """Test getting supported workflow types."""
        workflows = processing_crew.get_supported_workflows()
        assert "full_pipeline" in workflows
        assert "anonymize_only" in workflows
        assert "classify_only" in workflows
    
    def test_get_available_agents(self, processing_crew):
        """Test getting available agents status."""
        agents = processing_crew.get_available_agents()
        assert agents["anonymization"] is True
        assert agents["classification"] is True
        assert agents["report"] is True


@pytest.mark.skipif(not CREWAI_AVAILABLE, reason="CrewAI not available")
class TestCrewAIFallback:
    """Test fallback functionality when CrewAI is not available."""
    
    def test_fallback_mode_initialization(self):
        """Test that agents can initialize in fallback mode."""
        with patch('src.agents.base_agent.Agent', None):
            # This should not raise an exception
            from src.agents.base_agent import BaseAgent
            
            class TestAgent(BaseAgent):
                def get_tools(self):
                    return []
                
                def execute_task(self, task_input):
                    return {"success": True}
                
                def get_required_config_keys(self):
                    return []
            
            agent = TestAgent("test", "test goal", "test backstory")
            assert agent.agent is None  # Should be None in fallback mode
    
    def test_crew_fallback_execution(self):
        """Test crew execution in fallback mode."""
        # This test would verify that crews can still execute
        # their workflows even without CrewAI installed
        pass


if __name__ == "__main__":
    pytest.main([__file__])
