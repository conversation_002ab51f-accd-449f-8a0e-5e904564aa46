#!/usr/bin/env python3
"""
Test anonymization on the original input data: ouvidorias_educacao.csv

This script tests the MPRJ anonymization tools on the raw input data
to demonstrate the anonymization capabilities before classification.
"""

import sys
import os
import pandas as pd
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from src.tools.anonymization_tool import AnonymizationTool


def test_anonymization_on_input_data():
    """Test anonymization on the original input CSV file."""
    
    print("🔥 Testing MPRJ Anonymization on Input Data")
    print("=" * 60)
    
    # Initialize anonymization tool
    print("📋 Initializing anonymization tool...")
    try:
        anonymizer = AnonymizationTool()
        print("✅ Anonymization tool initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize anonymization tool: {e}")
        return False
    
    # Load input data
    input_file = "data/input/ouvidorias_educacao.csv"
    if not os.path.exists(input_file):
        print(f"❌ Input data file not found: {input_file}")
        return False
    
    print(f"📄 Loading input data from: {input_file}")
    try:
        df = pd.read_csv(input_file, encoding='utf-8', sep=';')
        print(f"✅ Loaded {len(df)} records")
        print(f"📊 Columns: {list(df.columns)}")
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return False
    
    # Analyze first few records
    print(f"\n🔍 Analyzing sample records for PII entities...")
    
    # Columns to anonymize
    text_columns = []
    for col in ['Assunto Resumido', 'Assunto Inteiro Teor', 'Noticiado']:
        if col in df.columns:
            text_columns.append(col)
    
    print(f"📝 Text columns to anonymize: {text_columns}")
    
    if not text_columns:
        print("❌ No text columns found for anonymization")
        return False
    
    # Test on sample records
    sample_size = min(10, len(df))
    print(f"\n🧪 Testing anonymization on {sample_size} sample records...")
    
    results = []
    total_entities = 0
    
    for idx in range(sample_size):
        row = df.iloc[idx]
        
        print(f"\n--- Record {idx + 1}/{sample_size} ---")
        
        record_entities = 0
        record_results = {}
        
        for col in text_columns:
            if col in row and pd.notna(row[col]):
                text = str(row[col])
                
                if len(text.strip()) > 0:
                    print(f"📝 {col}: {text[:80]}...")
                    
                    result = anonymizer.anonymize_text(text)
                    
                    if result['success']:
                        print(f"✅ Anonymized: {result['anonymized_text'][:80]}...")
                        print(f"🔍 Entities found: {result['entities_count']}")
                        
                        for entity in result['entities_found']:
                            print(f"   • {entity['entity_type']}: '{entity['text']}' (score: {entity['score']:.2f})")
                        
                        record_entities += result['entities_count']
                        record_results[col] = {
                            'original': text,
                            'anonymized': result['anonymized_text'],
                            'entities': result['entities_found']
                        }
                    else:
                        print(f"❌ Anonymization failed: {result.get('error', 'Unknown error')}")
                        record_results[col] = {'error': result.get('error', 'Unknown error')}
        
        total_entities += record_entities
        results.append({
            'record_index': idx,
            'entities_count': record_entities,
            'results': record_results
        })
        
        print(f"📊 Record {idx + 1} total entities: {record_entities}")
    
    # Summary statistics
    print(f"\n📊 ANONYMIZATION TEST SUMMARY")
    print("=" * 40)
    
    successful_records = [r for r in results if r['entities_count'] > 0]
    
    print(f"✅ Records processed: {len(results)}")
    print(f"🔍 Records with entities: {len(successful_records)}")
    print(f"📈 Total entities detected: {total_entities}")
    
    if successful_records:
        avg_entities = total_entities / len(results)
        print(f"📊 Average entities per record: {avg_entities:.1f}")
    
    # Entity type breakdown
    entity_types = {}
    for result in results:
        for col_result in result['results'].values():
            if 'entities' in col_result:
                for entity in col_result['entities']:
                    entity_type = entity['entity_type']
                    entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
    
    if entity_types:
        print(f"\n🎯 Entity Types Detected:")
        for entity_type, count in sorted(entity_types.items(), key=lambda x: x[1], reverse=True):
            print(f"   • {entity_type}: {count} occurrences")
    
    # Create anonymized sample output
    print(f"\n💾 Creating anonymized sample output...")
    
    # Anonymize first 5 records completely
    sample_df = df.head(5).copy()
    
    for col in text_columns:
        if col in sample_df.columns:
            anonymized_col = f"{col}_ANONIMIZADO"
            sample_df[anonymized_col] = sample_df[col].apply(
                lambda x: anonymizer.anonymize_text(str(x))['anonymized_text'] 
                if pd.notna(x) and str(x).strip() else x
            )
    
    # Save sample
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"data/output/ouvidorias_educacao_anonimizado_sample_{timestamp}.csv"
    sample_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"✅ Anonymized sample saved: {output_file}")
    
    # Show comparison example
    if len(results) > 0 and 'Assunto Resumido' in results[0]['results']:
        print(f"\n📋 EXAMPLE COMPARISON:")
        print("-" * 50)
        
        example = results[0]['results']['Assunto Resumido']
        if 'original' in example:
            print(f"🔸 Original: {example['original'][:100]}...")
            print(f"🔹 Anonymized: {example['anonymized'][:100]}...")
    
    return total_entities > 0


def main():
    """Main test function."""
    
    print("🚀 MPRJ Anonymization Test on Input Data")
    print("=" * 70)
    
    success = test_anonymization_on_input_data()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Anonymization test completed successfully!")
        print("✅ MPRJ anonymization tools detected PII entities in input data")
        print("✅ Ready to anonymize full dataset")
        print("✅ Integration with classification pipeline confirmed")
    else:
        print("❌ Anonymization test failed")
        print("🔧 Check dependencies and input data format")
    
    return success


if __name__ == "__main__":
    main()
