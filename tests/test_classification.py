"""
Tests for classification endpoints.
"""

import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient

from tests.conftest import CLASSIFICATION_ENDPOINTS


class TestClassificationEndpoints:
    """Test classification endpoints."""

    def test_classify_text_success(self, client: TestClient, auth_headers: dict, sample_text_for_classification: str):
        """Test successful text classification."""
        response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/multilabel",
            headers=auth_headers,
            json={
                "text": sample_text_for_classification,
                "area": "EDUCACAO",
                "max_subtemas": 3
            }
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "area" in result
        assert result["area"] == "EDUCACAO"
        assert "subtemas" in result
        assert "processing_time" in result
        assert result["success"] is True

        # Check that classification makes sense
        assert isinstance(result["subtemas"], list)
        assert len(result["subtemas"]) > 0
        assert isinstance(result["processing_time"], (int, float))

    def test_classify_text_unilabel(self, client: TestClient, auth_headers: dict, sample_text_for_classification: str):
        """Test unilabel classification."""
        response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": sample_text_for_classification,
                "classification_type": "unilabel"
            }
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "tipo_unidade" in result
        assert "cre" in result
        assert result["tipo_unidade"] in ["Estadual", "Municipal", "Privada"]
        assert result["cre"] in ["1ª CRE", "2ª CRE", "3ª CRE", "4ª CRE", "5ª CRE", 
                                 "6ª CRE", "7ª CRE", "8ª CRE", "9ª CRE", "10ª CRE", 
                                 "11ª CRE", "Não se aplica"]

    def test_classify_text_no_auth(self, client: TestClient, sample_text_for_classification: str):
        """Test classification without authentication."""
        response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/text",
            json={
                "text": sample_text_for_classification,
                "classification_type": "multilabel"
            }
        )
        
        assert response.status_code == 401

    def test_classify_text_empty_text(self, client: TestClient, auth_headers: dict):
        """Test classification with empty text."""
        response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": "",
                "classification_type": "multilabel"
            }
        )
        
        assert response.status_code == 400
        result = response.json()
        assert "detail" in result

    def test_classify_text_invalid_type(self, client: TestClient, auth_headers: dict):
        """Test classification with invalid type."""
        response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": "Some text",
                "classification_type": "invalid_type"
            }
        )
        
        assert response.status_code == 422

    def test_classify_file_success(self, client: TestClient, auth_headers: dict, sample_csv_content: str):
        """Test successful file classification."""
        files = {
            'file': ('test.csv', sample_csv_content, 'text/csv')
        }
        data = {
            'classification_type': 'multilabel',
            'text_column': 'relato'
        }
        
        response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/file",
            headers=auth_headers,
            files=files,
            data=data
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "file_id" in result
        assert "total_rows" in result
        assert "processed_rows" in result
        assert "results" in result
        assert "processing_time" in result
        
        # Check results structure
        results = result["results"]
        assert isinstance(results, list)
        if len(results) > 0:
            first_result = results[0]
            assert "row_id" in first_result
            assert "area_politica_publica" in first_result
            assert "subtema" in first_result

    def test_classify_file_no_auth(self, client: TestClient, sample_csv_content: str):
        """Test file classification without authentication."""
        files = {
            'file': ('test.csv', sample_csv_content, 'text/csv')
        }
        
        response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/file",
            files=files
        )
        
        assert response.status_code == 401

    def test_classify_file_invalid_format(self, client: TestClient, auth_headers: dict):
        """Test file classification with invalid file format."""
        files = {
            'file': ('test.txt', 'Not a CSV file', 'text/plain')
        }
        
        response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/file",
            headers=auth_headers,
            files=files
        )
        
        assert response.status_code == 400

    def test_classify_file_missing_column(self, client: TestClient, auth_headers: dict):
        """Test file classification with missing text column."""
        csv_content = "id,name\n1,John\n2,Jane"
        files = {
            'file': ('test.csv', csv_content, 'text/csv')
        }
        data = {
            'classification_type': 'multilabel',
            'text_column': 'relato'  # Column doesn't exist
        }
        
        response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/file",
            headers=auth_headers,
            files=files,
            data=data
        )
        
        assert response.status_code == 400
        result = response.json()
        assert "column" in result["detail"].lower()

    def test_classify_different_areas(self, client: TestClient, auth_headers: dict):
        """Test classification with texts from different policy areas."""
        test_texts = {
            "education": "Problema na merenda escolar da escola municipal.",
            "health": "Demora no atendimento no posto de saúde.",
            "environment": "Poluição do ar próximo à fábrica."
        }
        
        for area, text in test_texts.items():
            response = client.post(
                f"{CLASSIFICATION_ENDPOINTS}/text",
                headers=auth_headers,
                json={
                    "text": text,
                    "classification_type": "multilabel"
                }
            )
            
            assert response.status_code == 200
            result = response.json()
            assert "area_politica_publica" in result

    @pytest.mark.asyncio
    async def test_classify_text_async(self, async_client: AsyncClient, auth_headers: dict):
        """Test text classification with async client."""
        response = await async_client.post(
            f"{CLASSIFICATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": "Problema na escola municipal",
                "classification_type": "multilabel"
            }
        )
        
        assert response.status_code == 200

    def test_classify_large_file(self, client: TestClient, auth_headers: dict):
        """Test classification with large CSV file."""
        # Create a larger CSV file
        rows = ["id,relato"]
        for i in range(100):
            rows.append(f"{i},Problema na escola municipal número {i}")
        
        large_csv = "\n".join(rows)
        files = {
            'file': ('large.csv', large_csv, 'text/csv')
        }
        data = {
            'classification_type': 'multilabel',
            'text_column': 'relato'
        }
        
        response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/file",
            headers=auth_headers,
            files=files,
            data=data
        )
        
        assert response.status_code == 200
        result = response.json()
        assert result["total_rows"] == 100

    def test_classify_performance(self, client: TestClient, auth_headers: dict):
        """Test classification performance."""
        text = "Problema na merenda escolar da escola municipal João Silva."
        
        response = client.post(
            f"{CLASSIFICATION_ENDPOINTS}/text",
            headers=auth_headers,
            json={
                "text": text,
                "classification_type": "multilabel"
            }
        )
        
        assert response.status_code == 200
        result = response.json()
        
        # Should complete within reasonable time
        processing_time = result.get("processing_time", 0)
        assert processing_time < 30.0  # Should complete within 30 seconds

    def test_classify_concurrent_requests(self, client: TestClient, auth_headers: dict):
        """Test handling of concurrent classification requests."""
        import threading
        
        results = []
        
        def classify_text():
            response = client.post(
                f"{CLASSIFICATION_ENDPOINTS}/text",
                headers=auth_headers,
                json={
                    "text": "Problema na escola municipal",
                    "classification_type": "multilabel"
                }
            )
            results.append(response.status_code)
        
        # Create multiple threads
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=classify_text)
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        # All requests should succeed
        assert all(status == 200 for status in results)
