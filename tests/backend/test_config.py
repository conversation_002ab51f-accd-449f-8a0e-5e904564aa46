"""
Unit tests for configuration modules.

Tests cover embedding models configuration, settings management,
and environment variable handling.
"""

import pytest
import os
from unittest.mock import patch, Mock

from backend.src.config.embedding_models import (
    EmbeddingModels,
    ModelConfig,
    get_model_from_env,
    select_best_model_for_portuguese,
    select_model_for_performance,
    get_model_recommendations
)

from backend.src.config.settings import (
    Settings,
    SemanticProcessingSettings,
    RAGSystemSettings,
    get_semantic_processing_config,
    get_recommended_model_for_language,
    create_env_template
)


class TestEmbeddingModels:
    """Test embedding models configuration."""
    
    def test_get_model_config(self):
        """Test getting model configuration."""
        config = EmbeddingModels.get_model_config("default")
        
        assert isinstance(config, ModelConfig)
        assert config.name == "Default (MiniLM-L6-v2)"
        assert config.model_id == "all-MiniLM-L6-v2"
        assert config.embedding_dim == 384
        assert "general" in config.recommended_for
    
    def test_get_model_config_invalid(self):
        """Test getting invalid model configuration."""
        with pytest.raises(ValueError, match="Unknown model type"):
            EmbeddingModels.get_model_config("nonexistent")
    
    def test_get_recommended_for_language(self):
        """Test getting recommended models for language."""
        pt_models = EmbeddingModels.get_recommended_for_language("pt")
        
        assert len(pt_models) > 0
        assert "multilingual" in pt_models or "portuguese" in pt_models
        
        # Should be sorted by performance score
        scores = [EmbeddingModels.MODELS[model].performance_score for model in pt_models]
        assert scores == sorted(scores, reverse=True)
    
    def test_get_recommended_for_use_case(self):
        """Test getting recommended models for use case."""
        legal_models = EmbeddingModels.get_recommended_for_use_case("legal_documents")
        
        assert len(legal_models) > 0
        assert any("legal" in model or "portuguese" in model for model in legal_models)
    
    def test_get_all_models(self):
        """Test getting all models."""
        all_models = EmbeddingModels.get_all_models()
        
        assert isinstance(all_models, dict)
        assert len(all_models) >= 5  # We defined at least 5 models
        assert "default" in all_models
        assert "multilingual" in all_models
    
    def test_get_model_summary(self):
        """Test getting model summary."""
        summary = EmbeddingModels.get_model_summary()
        
        assert isinstance(summary, dict)
        for model_type, info in summary.items():
            assert "name" in info
            assert "description" in info
            assert "languages" in info
            assert "embedding_dim" in info
            assert "performance_score" in info


class TestModelSelection:
    """Test model selection functions."""
    
    def test_select_best_model_for_portuguese(self):
        """Test Portuguese model selection."""
        model_id = select_best_model_for_portuguese()
        
        assert isinstance(model_id, str)
        assert len(model_id) > 0
        # Should be multilingual model for Portuguese
        assert "multilingual" in model_id.lower()
    
    def test_select_model_for_performance(self):
        """Test performance-based model selection."""
        # Test low memory constraint
        low_mem_model = select_model_for_performance("low")
        config = next(c for c in EmbeddingModels.MODELS.values() if c.model_id == low_mem_model)
        assert config.memory_usage == "low"
        
        # Test high memory constraint
        high_mem_model = select_model_for_performance("high")
        assert isinstance(high_mem_model, str)
    
    def test_get_model_recommendations(self):
        """Test getting model recommendations."""
        recommendations = get_model_recommendations()
        
        assert isinstance(recommendations, dict)
        assert "portuguese_legal" in recommendations
        assert "fast_api" in recommendations
        assert "high_accuracy" in recommendations
        assert "low_memory" in recommendations
        assert "multilingual" in recommendations
    
    @patch.dict(os.environ, {"EMBEDDING_MODEL": "custom-model"})
    def test_get_model_from_env_direct(self):
        """Test getting model from direct environment variable."""
        model = get_model_from_env()
        assert model == "custom-model"
    
    @patch.dict(os.environ, {"EMBEDDING_MODEL_TYPE": "multilingual"})
    def test_get_model_from_env_type(self):
        """Test getting model from model type environment variable."""
        model = get_model_from_env()
        expected = EmbeddingModels.MODELS["multilingual"].model_id
        assert model == expected
    
    @patch.dict(os.environ, {"LANGUAGE": "pt"})
    def test_get_model_from_env_language(self):
        """Test getting model from language environment variable."""
        with patch.dict(os.environ, {}, clear=True):
            os.environ["LANGUAGE"] = "pt"
            model = get_model_from_env()
            expected = EmbeddingModels.MODELS["multilingual"].model_id
            assert model == expected


class TestSettings:
    """Test settings management."""
    
    def test_settings_initialization(self):
        """Test settings initialization."""
        settings = Settings()
        
        assert isinstance(settings.semantic_processing, SemanticProcessingSettings)
        assert isinstance(settings.rag_system, RAGSystemSettings)
    
    @patch.dict(os.environ, {
        "EMBEDDING_CACHE_SIZE": "500",
        "EMBEDDING_BATCH_SIZE": "16",
        "NORMALIZE_EMBEDDINGS": "false"
    })
    def test_semantic_processing_settings_from_env(self):
        """Test semantic processing settings from environment."""
        settings = Settings()
        
        assert settings.semantic_processing.cache_size == 500
        assert settings.semantic_processing.batch_size == 16
        assert settings.semantic_processing.normalize_embeddings is False
    
    @patch.dict(os.environ, {
        "MAX_SEARCH_RESULTS": "20",
        "MAX_RESPONSE_TOKENS": "1500",
        "INCLUDE_CITATIONS": "false"
    })
    def test_rag_system_settings_from_env(self):
        """Test RAG system settings from environment."""
        settings = Settings()
        
        assert settings.rag_system.max_search_results == 20
        assert settings.rag_system.max_response_tokens == 1500
        assert settings.rag_system.include_citations is False
    
    def test_get_model_config(self):
        """Test getting model configuration."""
        settings = Settings()
        config = settings.get_model_config()
        
        assert isinstance(config, dict)
        assert "model_name" in config
        assert "cache_size" in config
        assert "batch_size" in config
        assert "normalize_embeddings" in config
    
    def test_get_environment_info(self):
        """Test getting environment information."""
        settings = Settings()
        info = settings.get_environment_info()
        
        assert isinstance(info, dict)
        assert "python_version" in info
        assert "environment" in info
        assert "model_name" in info
        assert "cache_enabled" in info
    
    def test_validate_settings_valid(self):
        """Test settings validation with valid settings."""
        settings = Settings()
        validation = settings.validate_settings()
        
        assert validation["valid"] is True
        assert len(validation["errors"]) == 0
    
    @patch.dict(os.environ, {
        "EMBEDDING_CACHE_SIZE": "-1",
        "DEFAULT_SIMILARITY_THRESHOLD": "1.5"
    })
    def test_validate_settings_invalid(self):
        """Test settings validation with invalid settings."""
        settings = Settings()
        validation = settings.validate_settings()
        
        assert validation["valid"] is False
        assert len(validation["errors"]) > 0
        assert any("Cache size must be positive" in error for error in validation["errors"])
        assert any("Similarity threshold must be between 0.0 and 1.0" in error for error in validation["errors"])
    
    def test_to_dict(self):
        """Test converting settings to dictionary."""
        settings = Settings()
        settings_dict = settings.to_dict()
        
        assert isinstance(settings_dict, dict)
        assert "semantic_processing" in settings_dict
        assert "rag_system" in settings_dict
        
        # Check that sensitive information is masked
        assert "***" in settings_dict["rag_system"]["database_url"] or "password" not in settings_dict["rag_system"]["database_url"].lower()


class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_get_semantic_processing_config(self):
        """Test getting semantic processing configuration."""
        config = get_semantic_processing_config()
        
        assert isinstance(config, dict)
        assert "model_name" in config
        assert "cache_size" in config
    
    def test_get_recommended_model_for_language(self):
        """Test getting recommended model for language."""
        pt_model = get_recommended_model_for_language("pt")
        en_model = get_recommended_model_for_language("en")
        
        assert isinstance(pt_model, str)
        assert isinstance(en_model, str)
        assert len(pt_model) > 0
        assert len(en_model) > 0
    
    def test_create_env_template(self):
        """Test creating environment template."""
        template = create_env_template()
        
        assert isinstance(template, str)
        assert "EMBEDDING_MODEL" in template
        assert "EMBEDDING_CACHE_SIZE" in template
        assert "DEFAULT_SIMILARITY_THRESHOLD" in template
        assert "DATABASE_URL" in template
        
        # Should contain comments
        assert "#" in template
        assert "RAG System Configuration" in template


class TestConfigIntegration:
    """Test integration between different config components."""
    
    def test_model_config_consistency(self):
        """Test consistency between model configs and settings."""
        settings = Settings()
        model_name = settings.semantic_processing.model_name
        
        # Model name should be valid
        assert isinstance(model_name, str)
        assert len(model_name) > 0
        
        # Should be either a direct model ID or from our registry
        model_ids = [config.model_id for config in EmbeddingModels.MODELS.values()]
        assert model_name in model_ids or model_name.startswith("all-") or "/" in model_name
    
    def test_settings_and_factory_consistency(self):
        """Test consistency between settings and factory functions."""
        config = get_semantic_processing_config()
        
        # Config should have all required fields for service creation
        required_fields = ["model_name", "cache_size", "batch_size", "max_workers", "normalize_embeddings", "enable_cache"]
        for field in required_fields:
            assert field in config


if __name__ == "__main__":
    pytest.main([__file__])
