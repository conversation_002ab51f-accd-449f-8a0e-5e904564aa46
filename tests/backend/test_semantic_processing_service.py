"""
Unit tests for SemanticProcessingService.

Tests cover embedding generation, caching, batch processing,
and configuration management.
"""

import pytest
import numpy as np
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Import the service and related classes
from backend.src.services.semantic_processing_service import (
    SemanticProcessingService,
    LRUEmbeddingCache,
    CacheEntry,
    EmbeddingConfig,
    create_semantic_service,
    create_multilingual_service,
    create_portuguese_service
)


class TestLRUEmbeddingCache:
    """Test the LRU embedding cache."""
    
    def test_cache_initialization(self):
        """Test cache initialization with default parameters."""
        cache = LRUEmbeddingCache()
        assert cache.max_size == 1000
        assert cache.ttl.total_seconds() == 24 * 3600  # 24 hours
        assert len(cache.cache) == 0
        assert cache.stats["hits"] == 0
        assert cache.stats["misses"] == 0
    
    def test_cache_put_and_get(self):
        """Test basic cache put and get operations."""
        cache = LRUEmbeddingCache(max_size=10)
        
        # Create test embedding
        embedding = np.array([1.0, 2.0, 3.0])
        text = "test text"
        
        # Put and get
        cache.put(text, embedding)
        retrieved = cache.get(text)
        
        assert retrieved is not None
        np.testing.assert_array_equal(retrieved, embedding)
        assert cache.stats["hits"] == 1
        assert cache.stats["misses"] == 0
    
    def test_cache_miss(self):
        """Test cache miss behavior."""
        cache = LRUEmbeddingCache()
        
        result = cache.get("nonexistent text")
        assert result is None
        assert cache.stats["misses"] == 1
        assert cache.stats["hits"] == 0
    
    def test_cache_eviction(self):
        """Test LRU eviction when cache is full."""
        cache = LRUEmbeddingCache(max_size=2)
        
        # Fill cache to capacity
        cache.put("text1", np.array([1.0]))
        cache.put("text2", np.array([2.0]))
        
        # Add one more item (should evict oldest)
        cache.put("text3", np.array([3.0]))
        
        # text1 should be evicted
        assert cache.get("text1") is None
        assert cache.get("text2") is not None
        assert cache.get("text3") is not None
        assert cache.stats["evictions"] == 1
    
    def test_cache_ttl_expiration(self):
        """Test TTL-based expiration."""
        cache = LRUEmbeddingCache(ttl_hours=0.001)  # Very short TTL
        
        embedding = np.array([1.0, 2.0])
        cache.put("test", embedding)
        
        # Should be available immediately
        assert cache.get("test") is not None
        
        # Mock time passage
        with patch('backend.src.services.semantic_processing_service.datetime') as mock_datetime:
            future_time = datetime.now() + timedelta(hours=1)
            mock_datetime.now.return_value = future_time
            
            # Should be expired
            result = cache.get("test")
            assert result is None
            assert cache.stats["expired"] >= 1
    
    def test_cache_stats(self):
        """Test cache statistics."""
        cache = LRUEmbeddingCache(max_size=5)
        
        # Add some entries
        for i in range(3):
            cache.put(f"text{i}", np.array([float(i)]))
        
        # Get some entries
        cache.get("text0")  # hit
        cache.get("text1")  # hit
        cache.get("nonexistent")  # miss
        
        stats = cache.get_stats()
        assert stats["hits"] == 2
        assert stats["misses"] == 1
        assert stats["size"] == 3
        assert stats["max_size"] == 5
        assert stats["hit_rate"] == (2/3) * 100


class TestSemanticProcessingService:
    """Test the main SemanticProcessingService."""
    
    @pytest.fixture
    def mock_sentence_transformer(self):
        """Mock SentenceTransformer for testing."""
        with patch('backend.src.services.semantic_processing_service.SentenceTransformer') as mock_st:
            mock_model = Mock()
            mock_model.encode.return_value = np.array([0.1, 0.2, 0.3, 0.4])
            mock_model.get_sentence_embedding_dimension.return_value = 384
            mock_st.return_value = mock_model
            yield mock_model
    
    def test_service_initialization(self, mock_sentence_transformer):
        """Test service initialization."""
        service = SemanticProcessingService(
            model_name="test-model",
            cache_size=100,
            enable_cache=True
        )
        
        assert service.model_name == "test-model"
        assert service.config.cache_size == 100
        assert service.embedding_cache is not None
        assert service.embedding_model is not None
    
    def test_service_initialization_without_cache(self, mock_sentence_transformer):
        """Test service initialization without cache."""
        service = SemanticProcessingService(enable_cache=False)
        
        assert service.embedding_cache is None
        assert service.embedding_model is not None
    
    def test_generate_embedding_success(self, mock_sentence_transformer):
        """Test successful embedding generation."""
        service = SemanticProcessingService(enable_cache=False)
        
        text = "test text for embedding"
        embedding = service.generate_embedding(text)
        
        assert isinstance(embedding, np.ndarray)
        assert embedding.shape == (4,)  # Based on mock return
        mock_sentence_transformer.encode.assert_called_once_with(text)
    
    def test_generate_embedding_with_cache(self, mock_sentence_transformer):
        """Test embedding generation with caching."""
        service = SemanticProcessingService(cache_size=10)
        
        text = "test text"
        
        # First call - should generate and cache
        embedding1 = service.generate_embedding(text)
        assert mock_sentence_transformer.encode.call_count == 1
        
        # Second call - should use cache
        embedding2 = service.generate_embedding(text)
        assert mock_sentence_transformer.encode.call_count == 1  # No additional calls
        
        np.testing.assert_array_equal(embedding1, embedding2)
        assert service.stats["cache_hits"] == 1
        assert service.stats["cache_misses"] == 1
    
    def test_generate_embedding_empty_text(self, mock_sentence_transformer):
        """Test embedding generation with empty text."""
        service = SemanticProcessingService()
        
        with pytest.raises(ValueError, match="Input text cannot be empty"):
            service.generate_embedding("")
        
        with pytest.raises(ValueError, match="Input text cannot be empty"):
            service.generate_embedding("   ")
    
    @pytest.mark.asyncio
    async def test_generate_embeddings_batch(self, mock_sentence_transformer):
        """Test batch embedding generation."""
        service = SemanticProcessingService()
        
        texts = ["text1", "text2", "text3"]
        mock_sentence_transformer.encode.return_value = np.array([
            [0.1, 0.2, 0.3],
            [0.4, 0.5, 0.6], 
            [0.7, 0.8, 0.9]
        ])
        
        embeddings = await service.generate_embeddings_batch(texts)
        
        assert embeddings.shape == (3, 3)
        mock_sentence_transformer.encode.assert_called_once_with(texts)
        assert service.stats["batch_requests"] == 1
    
    @pytest.mark.asyncio
    async def test_generate_embeddings_batch_empty_list(self, mock_sentence_transformer):
        """Test batch embedding with empty list."""
        service = SemanticProcessingService()
        
        with pytest.raises(ValueError, match="Input texts list cannot be empty"):
            await service.generate_embeddings_batch([])
    
    def test_compute_similarity(self, mock_sentence_transformer):
        """Test similarity computation."""
        service = SemanticProcessingService()
        
        emb1 = np.array([1.0, 0.0, 0.0])
        emb2 = np.array([0.0, 1.0, 0.0])
        emb3 = np.array([1.0, 0.0, 0.0])
        
        # Orthogonal vectors should have similarity 0
        similarity1 = service.compute_similarity(emb1, emb2)
        assert abs(similarity1) < 1e-10
        
        # Identical vectors should have similarity 1
        similarity2 = service.compute_similarity(emb1, emb3)
        assert abs(similarity2 - 1.0) < 1e-10
    
    def test_get_model_info(self, mock_sentence_transformer):
        """Test model info retrieval."""
        service = SemanticProcessingService(model_name="test-model")
        
        info = service.get_model_info()
        
        assert info["model_name"] == "test-model"
        assert info["model_loaded"] is True
        assert info["cache_enabled"] is True
        assert "processing_stats" in info
        assert "cache_stats" in info
    
    def test_clear_cache(self, mock_sentence_transformer):
        """Test cache clearing."""
        service = SemanticProcessingService(cache_size=10)
        
        # Add something to cache
        service.generate_embedding("test text")
        assert service.embedding_cache.get_stats()["size"] > 0
        
        # Clear cache
        service.clear_cache()
        assert service.embedding_cache.get_stats()["size"] == 0
    
    def test_get_stats(self, mock_sentence_transformer):
        """Test statistics retrieval."""
        service = SemanticProcessingService()
        
        # Generate some activity
        service.generate_embedding("test1")
        service.generate_embedding("test1")  # Cache hit
        service.generate_embedding("test2")
        
        stats = service.get_stats()
        
        assert stats["total_requests"] == 3
        assert stats["cache_hits"] == 1
        assert stats["cache_misses"] == 2
        assert "cache_hit_rate" in stats
        assert "cache_stats" in stats


class TestFactoryFunctions:
    """Test factory functions for service creation."""
    
    @patch('backend.src.services.semantic_processing_service.SentenceTransformer')
    def test_create_semantic_service(self, mock_st):
        """Test create_semantic_service factory function."""
        mock_model = Mock()
        mock_st.return_value = mock_model
        
        service = create_semantic_service("multilingual")
        
        assert service.model_name == "paraphrase-multilingual-MiniLM-L12-v2"
        assert isinstance(service, SemanticProcessingService)
    
    @patch('backend.src.services.semantic_processing_service.SentenceTransformer')
    def test_create_multilingual_service(self, mock_st):
        """Test create_multilingual_service factory function."""
        mock_model = Mock()
        mock_st.return_value = mock_model
        
        service = create_multilingual_service()
        
        assert "multilingual" in service.model_name.lower()
        assert isinstance(service, SemanticProcessingService)
    
    @patch('backend.src.services.semantic_processing_service.SentenceTransformer')
    def test_create_portuguese_service(self, mock_st):
        """Test create_portuguese_service factory function."""
        mock_model = Mock()
        mock_st.return_value = mock_model
        
        service = create_portuguese_service()
        
        assert "portuguese" in service.model_name.lower() or "neuralmind" in service.model_name.lower()
        assert isinstance(service, SemanticProcessingService)


class TestErrorHandling:
    """Test error handling scenarios."""
    
    def test_initialization_without_sentence_transformers(self):
        """Test initialization when sentence-transformers is not available."""
        with patch('backend.src.services.semantic_processing_service.SENTENCE_TRANSFORMERS_AVAILABLE', False):
            with pytest.raises(ImportError, match="sentence-transformers package is required"):
                SemanticProcessingService()
    
    @patch('backend.src.services.semantic_processing_service.SentenceTransformer')
    def test_model_loading_failure(self, mock_st):
        """Test handling of model loading failure."""
        mock_st.side_effect = Exception("Model loading failed")
        
        with pytest.raises(RuntimeError, match="Could not load embedding model"):
            SemanticProcessingService()
    
    @patch('backend.src.services.semantic_processing_service.SentenceTransformer')
    def test_embedding_generation_failure(self, mock_st):
        """Test handling of embedding generation failure."""
        mock_model = Mock()
        mock_model.encode.side_effect = Exception("Encoding failed")
        mock_st.return_value = mock_model
        
        service = SemanticProcessingService()
        
        with pytest.raises(RuntimeError, match="Failed to generate embedding"):
            service.generate_embedding("test text")
        
        assert service.stats["errors"] == 1


if __name__ == "__main__":
    pytest.main([__file__])
