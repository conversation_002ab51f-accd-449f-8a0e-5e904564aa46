"""
Integration tests for the complete RAG system.

Tests the end-to-end functionality of the RAG system including:
- Document indexing with SemanticProcessingService
- Vector storage and retrieval
- Query processing and response generation
- API endpoints integration
"""

import pytest
import asyncio
import numpy as np
from unittest.mock import Mock, patch, AsyncMock
from typing import List, Dict, Any

# Import RAG services
from backend.src.services.semantic_processing_service import SemanticProcessingService
from backend.src.services.document_indexing_service import DocumentIndexingService
from backend.src.services.vector_storage_service import VectorStorageService
from backend.src.services.retrieval_service import RetrievalService
from backend.src.services.response_generation_service import ResponseGenerationService


class TestRAGSystemIntegration:
    """Test complete RAG system integration."""
    
    @pytest.fixture
    def mock_database(self):
        """Mock database connection."""
        mock_db = Mock()
        mock_db.execute = AsyncMock()
        mock_db.fetch = AsyncMock()
        mock_db.fetchrow = AsyncMock()
        return mock_db
    
    @pytest.fixture
    def mock_semantic_service(self):
        """Mock semantic processing service."""
        with patch('backend.src.services.semantic_processing_service.SentenceTransformer'):
            service = SemanticProcessingService(enable_cache=False)
            
            # Mock embedding generation
            service.generate_embedding = Mock(return_value=np.array([0.1, 0.2, 0.3, 0.4]))
            service.generate_embeddings_batch = AsyncMock(return_value=np.array([
                [0.1, 0.2, 0.3, 0.4],
                [0.5, 0.6, 0.7, 0.8],
                [0.9, 1.0, 1.1, 1.2]
            ]))
            
            return service
    
    @pytest.fixture
    def sample_document_chunks(self):
        """Sample document chunks for testing."""
        return [
            {
                "id": "chunk_1",
                "document_id": "doc_1",
                "content": "Este é o primeiro chunk do documento sobre direito civil.",
                "chunk_index": 0,
                "metadata": {"page": 1, "section": "Introdução"}
            },
            {
                "id": "chunk_2", 
                "document_id": "doc_1",
                "content": "O prazo para recurso em processos civis é de 15 dias.",
                "chunk_index": 1,
                "metadata": {"page": 2, "section": "Prazos"}
            },
            {
                "id": "chunk_3",
                "document_id": "doc_1", 
                "content": "A competência territorial é definida pelo domicílio do réu.",
                "chunk_index": 2,
                "metadata": {"page": 3, "section": "Competência"}
            }
        ]
    
    @pytest.fixture
    def sample_search_results(self):
        """Sample search results from vector database."""
        return [
            {
                "chunk_id": "chunk_2",
                "document_id": "doc_1",
                "content": "O prazo para recurso em processos civis é de 15 dias.",
                "similarity_score": 0.95,
                "metadata": {"page": 2, "section": "Prazos"}
            },
            {
                "chunk_id": "chunk_3",
                "document_id": "doc_1",
                "content": "A competência territorial é definida pelo domicílio do réu.",
                "similarity_score": 0.78,
                "metadata": {"page": 3, "section": "Competência"}
            }
        ]
    
    def test_document_indexing_service_integration(self, mock_database, mock_semantic_service, sample_document_chunks):
        """Test document indexing service with semantic processing."""
        indexing_service = DocumentIndexingService()
        indexing_service.semantic_service = mock_semantic_service
        indexing_service.db = mock_database
        
        # Mock database responses
        mock_database.fetch.return_value = sample_document_chunks
        mock_database.execute.return_value = None
        
        # Test indexing
        result = asyncio.run(indexing_service.index_document("doc_1"))
        
        # Verify semantic service was called for each chunk
        assert mock_semantic_service.generate_embedding.call_count == len(sample_document_chunks)
        
        # Verify database operations
        assert mock_database.execute.called
        assert result["status"] == "success"
        assert result["chunks_indexed"] == len(sample_document_chunks)
    
    def test_vector_storage_service_integration(self, mock_database, mock_semantic_service, sample_search_results):
        """Test vector storage service with semantic processing."""
        storage_service = VectorStorageService()
        storage_service.semantic_service = mock_semantic_service
        storage_service.db = mock_database
        
        # Mock database search results
        mock_database.fetch.return_value = [
            {
                "chunk_id": result["chunk_id"],
                "document_id": result["document_id"],
                "content": result["content"],
                "similarity": result["similarity_score"],
                "metadata": result["metadata"]
            }
            for result in sample_search_results
        ]
        
        # Test search
        query = "Qual é o prazo para recurso?"
        results = asyncio.run(storage_service.search_similar_chunks(query, limit=5))
        
        # Verify semantic service generated query embedding
        mock_semantic_service.generate_embedding.assert_called_with(query)
        
        # Verify results
        assert len(results) == len(sample_search_results)
        assert results[0]["similarity_score"] > results[1]["similarity_score"]  # Sorted by similarity
    
    def test_retrieval_service_integration(self, mock_database, mock_semantic_service, sample_search_results):
        """Test retrieval service with query analysis."""
        retrieval_service = RetrievalService()
        retrieval_service.semantic_service = mock_semantic_service
        retrieval_service.vector_storage = Mock()
        retrieval_service.vector_storage.search_similar_chunks = AsyncMock(return_value=sample_search_results)
        
        # Test retrieval
        query = "Qual é o prazo para recurso em processos civis?"
        results = asyncio.run(retrieval_service.retrieve_relevant_chunks(query))
        
        # Verify query was processed
        assert len(results) > 0
        assert results[0]["similarity_score"] >= results[-1]["similarity_score"]  # Sorted
        
        # Verify analytics (if enabled)
        retrieval_service.vector_storage.search_similar_chunks.assert_called_once()
    
    def test_response_generation_service_integration(self, mock_semantic_service, sample_search_results):
        """Test response generation with retrieved chunks."""
        response_service = ResponseGenerationService()
        response_service.semantic_service = mock_semantic_service
        
        # Mock LLM response
        with patch.object(response_service, '_generate_llm_response') as mock_llm:
            mock_llm.return_value = {
                "response": "O prazo para recurso em processos civis é de 15 dias, conforme estabelecido na legislação.",
                "citations": [
                    {
                        "chunk_id": "chunk_2",
                        "content": "O prazo para recurso em processos civis é de 15 dias.",
                        "page": 2,
                        "relevance": 0.95
                    }
                ]
            }
            
            # Test response generation
            query = "Qual é o prazo para recurso?"
            response = asyncio.run(response_service.generate_response(query, sample_search_results))
            
            # Verify response structure
            assert "response" in response
            assert "citations" in response
            assert "metadata" in response
            assert len(response["citations"]) > 0
            
            # Verify LLM was called with proper context
            mock_llm.assert_called_once()
            call_args = mock_llm.call_args[0]
            assert query in call_args[0]  # Query in prompt
            assert any(chunk["content"] in call_args[0] for chunk in sample_search_results)  # Chunks in context
    
    @pytest.mark.asyncio
    async def test_end_to_end_rag_workflow(self, mock_database, mock_semantic_service):
        """Test complete end-to-end RAG workflow."""
        # Initialize all services
        indexing_service = DocumentIndexingService()
        indexing_service.semantic_service = mock_semantic_service
        indexing_service.db = mock_database
        
        storage_service = VectorStorageService()
        storage_service.semantic_service = mock_semantic_service
        storage_service.db = mock_database
        
        retrieval_service = RetrievalService()
        retrieval_service.semantic_service = mock_semantic_service
        retrieval_service.vector_storage = storage_service
        
        response_service = ResponseGenerationService()
        response_service.semantic_service = mock_semantic_service
        
        # Mock database operations
        mock_database.fetch.return_value = [
            {
                "id": "chunk_1",
                "document_id": "doc_1",
                "content": "O prazo para recurso é de 15 dias úteis.",
                "chunk_index": 0,
                "metadata": {"page": 1}
            }
        ]
        
        mock_database.fetchrow.return_value = {
            "chunk_id": "chunk_1",
            "document_id": "doc_1", 
            "content": "O prazo para recurso é de 15 dias úteis.",
            "similarity": 0.92,
            "metadata": {"page": 1}
        }
        
        # Mock LLM response
        with patch.object(response_service, '_generate_llm_response') as mock_llm:
            mock_llm.return_value = {
                "response": "O prazo para recurso é de 15 dias úteis.",
                "citations": [{"chunk_id": "chunk_1", "page": 1, "relevance": 0.92}]
            }
            
            # Step 1: Index document
            index_result = await indexing_service.index_document("doc_1")
            assert index_result["status"] == "success"
            
            # Step 2: Query the system
            query = "Qual é o prazo para recurso?"
            
            # Step 3: Retrieve relevant chunks
            chunks = await retrieval_service.retrieve_relevant_chunks(query)
            assert len(chunks) > 0
            
            # Step 4: Generate response
            response = await response_service.generate_response(query, chunks)
            
            # Verify complete workflow
            assert "response" in response
            assert "citations" in response
            assert len(response["citations"]) > 0
            assert "15 dias" in response["response"]
    
    def test_error_handling_in_integration(self, mock_database, mock_semantic_service):
        """Test error handling in integrated services."""
        indexing_service = DocumentIndexingService()
        indexing_service.semantic_service = mock_semantic_service
        indexing_service.db = mock_database
        
        # Mock database error
        mock_database.execute.side_effect = Exception("Database connection failed")
        
        # Test error handling
        result = asyncio.run(indexing_service.index_document("doc_1"))
        
        assert result["status"] == "error"
        assert "error" in result
        assert "Database connection failed" in str(result["error"])
    
    def test_performance_with_large_batch(self, mock_semantic_service):
        """Test performance with large batch of embeddings."""
        # Create large batch of texts
        texts = [f"Texto de teste número {i}" for i in range(100)]
        
        # Mock batch processing
        mock_semantic_service.generate_embeddings_batch.return_value = np.random.rand(100, 384)
        
        # Test batch processing
        result = asyncio.run(mock_semantic_service.generate_embeddings_batch(texts))
        
        assert result.shape == (100, 384)
        mock_semantic_service.generate_embeddings_batch.assert_called_once_with(texts)
    
    def test_caching_behavior_in_integration(self, mock_database):
        """Test caching behavior across services."""
        with patch('backend.src.services.semantic_processing_service.SentenceTransformer'):
            # Create service with caching enabled
            semantic_service = SemanticProcessingService(enable_cache=True, cache_size=10)
            semantic_service.generate_embedding = Mock(return_value=np.array([0.1, 0.2, 0.3]))
            
            storage_service = VectorStorageService()
            storage_service.semantic_service = semantic_service
            storage_service.db = mock_database
            
            # Mock database response
            mock_database.fetch.return_value = []
            
            # Make multiple queries with same text
            query = "Teste de cache"
            
            asyncio.run(storage_service.search_similar_chunks(query))
            asyncio.run(storage_service.search_similar_chunks(query))  # Should use cache
            
            # Verify embedding was generated only once (cached)
            assert semantic_service.generate_embedding.call_count == 1
    
    def test_multilingual_support(self, mock_database):
        """Test multilingual support in RAG system."""
        with patch('backend.src.services.semantic_processing_service.SentenceTransformer'):
            # Create multilingual service
            semantic_service = SemanticProcessingService(model_name="paraphrase-multilingual-MiniLM-L12-v2")
            semantic_service.generate_embedding = Mock(return_value=np.array([0.1, 0.2, 0.3]))
            
            storage_service = VectorStorageService()
            storage_service.semantic_service = semantic_service
            storage_service.db = mock_database
            
            # Mock database response
            mock_database.fetch.return_value = []
            
            # Test queries in different languages
            queries = [
                "Qual é o prazo para recurso?",  # Portuguese
                "What is the deadline for appeal?",  # English
                "¿Cuál es el plazo para apelar?"  # Spanish
            ]
            
            for query in queries:
                asyncio.run(storage_service.search_similar_chunks(query))
            
            # Verify all queries were processed
            assert semantic_service.generate_embedding.call_count == len(queries)


class TestRAGAPIIntegration:
    """Test RAG API endpoints integration."""
    
    @pytest.fixture
    def mock_rag_services(self):
        """Mock all RAG services for API testing."""
        services = {
            'indexing': Mock(),
            'retrieval': Mock(), 
            'response': Mock()
        }
        
        # Mock service responses
        services['indexing'].index_document = AsyncMock(return_value={
            "status": "success",
            "chunks_indexed": 5,
            "document_id": "doc_1"
        })
        
        services['retrieval'].retrieve_relevant_chunks = AsyncMock(return_value=[
            {
                "chunk_id": "chunk_1",
                "content": "Conteúdo relevante",
                "similarity_score": 0.9,
                "metadata": {"page": 1}
            }
        ])
        
        services['response'].generate_response = AsyncMock(return_value={
            "response": "Resposta gerada pelo sistema",
            "citations": [{"chunk_id": "chunk_1", "page": 1}],
            "metadata": {"query_time": 0.5, "model": "test"}
        })
        
        return services
    
    def test_rag_query_endpoint_integration(self, mock_rag_services):
        """Test RAG query endpoint with all services."""
        # This would test the actual API endpoint
        # For now, we simulate the endpoint logic
        
        query_request = {
            "query": "Qual é o prazo para recurso?",
            "document_ids": ["doc_1"],
            "generate_response": True,
            "max_results": 5
        }
        
        # Simulate endpoint processing
        async def process_rag_query(request):
            # Step 1: Retrieve chunks
            chunks = await mock_rag_services['retrieval'].retrieve_relevant_chunks(
                request["query"]
            )
            
            # Step 2: Generate response if requested
            if request["generate_response"]:
                response = await mock_rag_services['response'].generate_response(
                    request["query"], chunks
                )
                return {
                    "query": request["query"],
                    "chunks": chunks,
                    "response": response["response"],
                    "citations": response["citations"],
                    "metadata": response["metadata"]
                }
            else:
                return {
                    "query": request["query"],
                    "chunks": chunks
                }
        
        # Test the simulated endpoint
        result = asyncio.run(process_rag_query(query_request))
        
        # Verify response structure
        assert "query" in result
        assert "chunks" in result
        assert "response" in result
        assert "citations" in result
        assert len(result["chunks"]) > 0


if __name__ == "__main__":
    pytest.main([__file__])
