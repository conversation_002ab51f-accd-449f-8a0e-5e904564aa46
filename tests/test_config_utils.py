import os
import yaml
import pytest
from src.utils import config_utils

def test_carregar_configuracao_ok(tmp_path):
    cfg_file = tmp_path / 'cfg.yml'
    cfg = {'ASSUNTO': 'Teste', 'LLM_MODEL': 'model-x'}
    cfg_file.write_text(yaml.dump(cfg), encoding='utf-8')
    loaded = config_utils.carregar_configuracao(str(cfg_file))
    assert loaded['ASSUNTO'] == 'Teste'
    assert loaded['LLM_MODEL'] == 'model-x'


def test_carregar_configuracao_missing_key(tmp_path):
    cfg_file = tmp_path / 'cfg.yml'
    cfg_file.write_text('ASSUNTO: Teste', encoding='utf-8')
    with pytest.raises(ValueError):
        config_utils.carregar_configuracao(str(cfg_file))


def test_carregar_variaveis_ambiente(monkeypatch):
    monkeypatch.setenv('TOGETHER_API_KEY', '123')
    monkeypatch.setenv('ANTHROPIC_API_KEY', 'abc')
    env = config_utils.carregar_variaveis_ambiente()
    assert env['TOGETHER_API_KEY'] == '123'
    assert env['ANTHROPIC_API_KEY'] == 'abc'
