"""
Tests for file management endpoints.
"""

import pytest
import json
from io import BytesIO
from fastapi.testclient import Test<PERSON><PERSON>
from httpx import AsyncClient

from tests.conftest import FILES_ENDPOINTS


class TestFileEndpoints:
    """Test file management endpoints."""

    def test_upload_csv_success(self, client: TestClient, auth_headers: dict, sample_csv_content: str):
        """Test successful CSV file upload."""
        files = {
            'file': ('test.csv', sample_csv_content, 'text/csv')
        }
        data = {
            'metadata': json.dumps({
                "description": "Test CSV file",
                "tags": ["test", "csv"]
            })
        }
        
        response = client.post(
            f"{FILES_ENDPOINTS}/upload",
            headers=auth_headers,
            files=files,
            data=data
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "file_id" in result
        assert result["filename"] == "test.csv"
        assert result["file_type"] == "text/csv"
        assert "file_size" in result
        assert "created_at" in result
        assert result["status"] == "uploaded"

    def test_upload_file_no_auth(self, client: TestClient, sample_csv_content: str):
        """Test file upload without authentication."""
        files = {
            'file': ('test.csv', sample_csv_content, 'text/csv')
        }
        
        response = client.post(
            f"{FILES_ENDPOINTS}/upload",
            files=files
        )
        
        assert response.status_code == 403

    def test_upload_file_invalid_type(self, client: TestClient, auth_headers: dict):
        """Test upload of unsupported file type."""
        files = {
            'file': ('test.txt', 'Some text content', 'text/plain')
        }
        
        response = client.post(
            f"{FILES_ENDPOINTS}/upload",
            headers=auth_headers,
            files=files
        )
        
        # In development mode, file type validation might be relaxed
        assert response.status_code in [200, 400]
        if response.status_code == 400:
            result = response.json()
            assert "detail" in result
            assert "file type" in result["detail"].lower()

    def test_upload_file_too_large(self, client: TestClient, auth_headers: dict):
        """Test upload of file that's too large."""
        # Create a large file content (simulate > 10MB)
        large_content = "x" * (11 * 1024 * 1024)  # 11MB
        files = {
            'file': ('large.csv', large_content, 'text/csv')
        }
        
        response = client.post(
            f"{FILES_ENDPOINTS}/upload",
            headers=auth_headers,
            files=files
        )
        
        assert response.status_code == 413

    def test_upload_empty_file(self, client: TestClient, auth_headers: dict):
        """Test upload of empty file."""
        files = {
            'file': ('empty.csv', '', 'text/csv')
        }
        
        response = client.post(
            f"{FILES_ENDPOINTS}/upload",
            headers=auth_headers,
            files=files
        )
        
        assert response.status_code == 400
        result = response.json()
        assert "empty" in result["detail"].lower()

    def test_upload_malformed_csv(self, client: TestClient, auth_headers: dict):
        """Test upload of malformed CSV file."""
        malformed_csv = "id,name\n1,John\n2,Jane,Extra,Columns\n3"
        files = {
            'file': ('malformed.csv', malformed_csv, 'text/csv')
        }
        
        response = client.post(
            f"{FILES_ENDPOINTS}/upload",
            headers=auth_headers,
            files=files
        )
        
        # Should still upload but might have warnings
        assert response.status_code in [200, 400]

    def test_list_files_success(self, client: TestClient, auth_headers: dict):
        """Test listing user files."""
        response = client.get(
            f"{FILES_ENDPOINTS}/",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "files" in result
        assert "total" in result
        assert "page" in result
        assert "per_page" in result
        assert isinstance(result["files"], list)

    def test_list_files_with_pagination(self, client: TestClient, auth_headers: dict):
        """Test listing files with pagination parameters."""
        response = client.get(
            f"{FILES_ENDPOINTS}/",
            headers=auth_headers,
            params={"page": 1, "per_page": 5}
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert result["page"] == 1
        assert result["per_page"] == 5

    def test_list_files_no_auth(self, client: TestClient):
        """Test listing files without authentication."""
        response = client.get(f"{FILES_ENDPOINTS}/")
        
        assert response.status_code == 403

    def test_get_file_info_success(self, client: TestClient, auth_headers: dict):
        """Test getting file information."""
        file_id = "test-file-id"
        
        response = client.get(
            f"{FILES_ENDPOINTS}/{file_id}",
            headers=auth_headers
        )
        
        # This will depend on your mock setup
        assert response.status_code in [200, 404]

    def test_get_file_info_not_found(self, client: TestClient, auth_headers: dict):
        """Test getting info for non-existent file."""
        response = client.get(
            f"{FILES_ENDPOINTS}/non-existent-id",
            headers=auth_headers
        )
        
        assert response.status_code == 404

    def test_get_file_info_no_auth(self, client: TestClient):
        """Test getting file info without authentication."""
        response = client.get(f"{FILES_ENDPOINTS}/test-file-id")
        
        assert response.status_code == 403

    def test_delete_file_success(self, client: TestClient, auth_headers: dict):
        """Test successful file deletion."""
        file_id = "test-file-id"
        
        response = client.delete(
            f"{FILES_ENDPOINTS}/{file_id}",
            headers=auth_headers
        )
        
        # This will depend on your implementation
        assert response.status_code in [200, 204, 404]

    def test_delete_file_not_found(self, client: TestClient, auth_headers: dict):
        """Test deleting non-existent file."""
        response = client.delete(
            f"{FILES_ENDPOINTS}/non-existent-id",
            headers=auth_headers
        )
        
        assert response.status_code == 404

    def test_delete_file_no_auth(self, client: TestClient):
        """Test deleting file without authentication."""
        response = client.delete(f"{FILES_ENDPOINTS}/test-file-id")
        
        assert response.status_code == 403

    def test_upload_with_metadata(self, client: TestClient, auth_headers: dict, sample_csv_content: str):
        """Test file upload with custom metadata."""
        files = {
            'file': ('test_with_metadata.csv', sample_csv_content, 'text/csv')
        }
        metadata = {
            "description": "Test file with metadata",
            "tags": ["test", "metadata", "csv"],
            "category": "education",
            "source": "manual_upload"
        }
        data = {
            'metadata': json.dumps(metadata)
        }
        
        response = client.post(
            f"{FILES_ENDPOINTS}/upload",
            headers=auth_headers,
            files=files,
            data=data
        )
        
        assert response.status_code == 200
        result = response.json()
        
        # Check that metadata is preserved
        assert "metadata" in result
        if result["metadata"]:
            assert result["metadata"]["description"] == metadata["description"]

    def test_upload_without_metadata(self, client: TestClient, auth_headers: dict, sample_csv_content: str):
        """Test file upload without metadata."""
        files = {
            'file': ('test_no_metadata.csv', sample_csv_content, 'text/csv')
        }
        
        response = client.post(
            f"{FILES_ENDPOINTS}/upload",
            headers=auth_headers,
            files=files
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert "file_id" in result
        assert result["filename"] == "test_no_metadata.csv"

    @pytest.mark.asyncio
    async def test_upload_async(self, async_client: AsyncClient, auth_headers: dict, sample_csv_content: str):
        """Test file upload with async client."""
        files = {
            'file': ('async_test.csv', sample_csv_content, 'text/csv')
        }
        
        response = await async_client.post(
            f"{FILES_ENDPOINTS}/upload",
            headers=auth_headers,
            files=files
        )
        
        assert response.status_code == 200

    def test_file_validation_csv_headers(self, client: TestClient, auth_headers: dict):
        """Test CSV file validation for required headers."""
        # CSV without required headers
        invalid_csv = "name,description\nJohn,Test\nJane,Test2"
        files = {
            'file': ('invalid_headers.csv', invalid_csv, 'text/csv')
        }
        
        response = client.post(
            f"{FILES_ENDPOINTS}/upload",
            headers=auth_headers,
            files=files
        )
        
        # Should either accept with warnings or reject
        assert response.status_code in [200, 400]

    def test_concurrent_uploads(self, client: TestClient, auth_headers: dict, sample_csv_content: str):
        """Test handling of concurrent file uploads."""
        import threading
        import time
        
        results = []
        
        def upload_file(filename):
            files = {
                'file': (filename, sample_csv_content, 'text/csv')
            }
            response = client.post(
                f"{FILES_ENDPOINTS}/upload",
                headers=auth_headers,
                files=files
            )
            results.append(response.status_code)
        
        # Create multiple threads for concurrent uploads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=upload_file, args=[f"concurrent_{i}.csv"])
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All uploads should succeed
        assert all(status == 200 for status in results)
