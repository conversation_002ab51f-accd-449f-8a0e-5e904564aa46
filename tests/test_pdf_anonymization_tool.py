"""
Testes unitários para o PDFAnonymizationTool.
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import fitz  # PyMuPDF

from src.tools.pdf_anonymization_tool import PDFAnonymizationTool, create_pdf_anonymization_tool


class TestPDFAnonymizationTool:
    """Testes para a classe PDFAnonymizationTool."""
    
    def setup_method(self):
        """Setup para cada teste."""
        self.tool = PDFAnonymizationTool()
    
    def test_init_default_parameters(self):
        """Testa inicialização com parâmetros padrão."""
        tool = PDFAnonymizationTool()
        assert tool.header_footer_threshold == 0.3
        assert tool.min_text_length == 10
        assert tool.preserve_structure is True
        assert tool.anonymizer is not None
    
    def test_init_custom_parameters(self):
        """Testa inicialização com parâmetros customizados."""
        tool = PDFAnonymizationTool(
            header_footer_threshold=0.5,
            min_text_length=20,
            preserve_structure=False
        )
        assert tool.header_footer_threshold == 0.5
        assert tool.min_text_length == 20
        assert tool.preserve_structure is False
    
    def test_extract_clean_text_file_not_found(self):
        """Testa extração com arquivo inexistente."""
        result = self.tool.extract_clean_text("arquivo_inexistente.pdf")
        
        assert result["success"] is False
        assert "not found" in result["error"]
        assert result["text"] == ""
        assert result["pages"] == 0
    
    @patch('fitz.open')
    def test_extract_clean_text_empty_pdf(self, mock_fitz_open):
        """Testa extração com PDF vazio."""
        # Mock PDF vazio
        mock_doc = Mock()
        mock_doc.__len__ = Mock(return_value=0)
        mock_fitz_open.return_value = mock_doc
        
        with tempfile.NamedTemporaryFile(suffix='.pdf') as tmp_file:
            result = self.tool.extract_clean_text(tmp_file.name)
        
        assert result["success"] is False
        assert "no pages" in result["error"]
        assert result["pages"] == 0
    
    @patch('fitz.open')
    def test_extract_clean_text_success(self, mock_fitz_open):
        """Testa extração bem-sucedida."""
        # Mock PDF com conteúdo
        mock_page = Mock()
        mock_page.get_text.return_value = "Cabeçalho\nTexto principal do documento\nRodapé"
        mock_page.get_text.side_effect = lambda format=None: {
            "blocks": [
                (0, 0, 100, 20, "Cabeçalho", 0, 0),
                (0, 50, 100, 70, "Texto principal do documento", 0, 0),
                (0, 100, 100, 120, "Rodapé", 0, 0)
            ]
        }.get(format, "Cabeçalho\nTexto principal do documento\nRodapé")
        
        mock_doc = Mock()
        mock_doc.__len__ = Mock(return_value=1)
        mock_doc.__getitem__ = Mock(return_value=mock_page)
        mock_doc.__iter__ = Mock(return_value=iter([mock_page]))
        mock_doc.close = Mock()
        mock_fitz_open.return_value = mock_doc
        
        with tempfile.NamedTemporaryFile(suffix='.pdf') as tmp_file:
            result = self.tool.extract_clean_text(tmp_file.name)
        
        assert result["success"] is True
        assert result["pages"] == 1
        assert len(result["text"]) > 0
    
    def test_identify_common_elements_empty(self):
        """Testa identificação de elementos comuns com lista vazia."""
        result = self.tool._identify_common_elements([])
        assert result == []
    
    def test_identify_common_elements_no_common(self):
        """Testa identificação sem elementos comuns."""
        candidates = ["Header 1", "Header 2", "Header 3"]
        result = self.tool._identify_common_elements(candidates)
        assert result == []
    
    def test_identify_common_elements_with_common(self):
        """Testa identificação com elementos comuns."""
        # Com threshold 0.3, precisa aparecer em pelo menos 30% das páginas
        # "Header comum" aparece 3/5 = 60% (acima do threshold)
        # "Header diferente" aparece 2/5 = 40% (acima do threshold)
        # "Header raro" aparece 1/5 = 20% (abaixo do threshold)
        candidates = ["Header comum", "Header comum", "Header comum", "Header diferente", "Header diferente"]
        result = self.tool._identify_common_elements(candidates)
        assert "Header comum" in result
        assert "Header diferente" in result
    
    def test_clean_text_no_headers_footers(self):
        """Testa limpeza de texto sem headers/footers."""
        page_texts = ["Linha 1\nLinha 2\nLinha 3"]
        result = self.tool._clean_text(page_texts, [], [])
        assert "Linha 1" in result
        assert "Linha 2" in result
        assert "Linha 3" in result
    
    def test_clean_text_with_headers_footers(self):
        """Testa limpeza de texto com headers/footers."""
        page_texts = ["Header\nTexto principal\nFooter"]
        headers = ["Header"]
        footers = ["Footer"]
        result = self.tool._clean_text(page_texts, headers, footers)
        assert "Header" not in result
        assert "Footer" not in result
        assert "Texto principal" in result
    
    def test_clean_text_preserve_structure_true(self):
        """Testa limpeza preservando estrutura."""
        tool = PDFAnonymizationTool(preserve_structure=True)
        page_texts = ["Linha 1\nLinha 2", "Linha 3\nLinha 4"]
        result = tool._clean_text(page_texts, [], [])
        assert "\n\n" in result  # Separação entre páginas
    
    def test_clean_text_preserve_structure_false(self):
        """Testa limpeza sem preservar estrutura."""
        tool = PDFAnonymizationTool(preserve_structure=False)
        page_texts = ["Linha 1\nLinha 2", "Linha 3\nLinha 4"]
        result = tool._clean_text(page_texts, [], [])
        # Deve ter espaços em vez de quebras de linha dentro das páginas
        assert " " in result
    
    @patch.object(PDFAnonymizationTool, 'extract_clean_text')
    def test_anonymize_pdf_extraction_failure(self, mock_extract):
        """Testa anonimização com falha na extração."""
        mock_extract.return_value = {
            "success": False,
            "error": "Erro de teste",
            "text": "",
            "pages": 0,
            "headers_removed": [],
            "footers_removed": []
        }
        
        result = self.tool.anonymize_pdf("test.pdf")
        
        assert result["success"] is False
        assert result["error"] == "Erro de teste"
        assert result["anonymized_text"] == ""
    
    @patch.object(PDFAnonymizationTool, 'extract_clean_text')
    def test_anonymize_pdf_success(self, mock_extract):
        """Testa anonimização bem-sucedida."""
        mock_extract.return_value = {
            "success": True,
            "text": "João Silva mora na Rua das Flores",
            "pages": 1,
            "headers_removed": [],
            "footers_removed": []
        }
        
        # Mock do anonymizer
        with patch.object(self.tool.anonymizer, 'anonymize_text') as mock_anonymize:
            mock_anonymize.return_value = {
                "success": True,
                "anonymized_text": "<PERSON> mora na <ENDEREÇO>",
                "entities_found": [
                    {"entity_type": "PERSON", "start": 0, "end": 10, "score": 0.9, "text": "João Silva"}
                ],
                "entities_count": 1
            }
            
            result = self.tool.anonymize_pdf("test.pdf")
        
        assert result["success"] is True
        assert result["anonymized_text"] == "<PERSON> mora na <ENDEREÇO>"
        assert result["extraction_metadata"]["pages"] == 1
        assert result["anonymization_metadata"]["entities_count"] == 1


class TestCreatePDFAnonymizationTool:
    """Testes para a função create_pdf_anonymization_tool."""
    
    def test_create_tool_returns_callable(self):
        """Testa se a função retorna um callable."""
        tool_func = create_pdf_anonymization_tool()
        assert callable(tool_func)
    
    @patch.object(PDFAnonymizationTool, 'anonymize_pdf')
    def test_tool_function_calls_anonymize_pdf(self, mock_anonymize):
        """Testa se a função do tool chama anonymize_pdf."""
        mock_anonymize.return_value = {"success": True, "test": "result"}
        
        tool_func = create_pdf_anonymization_tool()
        result = tool_func("test.pdf", "pt")
        
        mock_anonymize.assert_called_once_with("test.pdf", "pt")
        assert result == {"success": True, "test": "result"}
    
    def test_tool_function_default_language(self):
        """Testa função do tool com idioma padrão."""
        with patch.object(PDFAnonymizationTool, 'anonymize_pdf') as mock_anonymize:
            mock_anonymize.return_value = {"success": True}
            
            tool_func = create_pdf_anonymization_tool()
            tool_func("test.pdf")
            
            mock_anonymize.assert_called_once_with("test.pdf", "pt")


@pytest.fixture
def sample_pdf_path():
    """Fixture que cria um PDF temporário para testes."""
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
        # Criar um PDF simples usando PyMuPDF
        doc = fitz.open()
        page = doc.new_page()
        page.insert_text((72, 72), "Texto de teste para PDF")
        doc.save(tmp_file.name)
        doc.close()
        
        yield tmp_file.name
        
        # Cleanup
        Path(tmp_file.name).unlink(missing_ok=True)


class TestPDFAnonymizationToolIntegration:
    """Testes de integração com PDF real."""
    
    def test_extract_from_real_pdf(self, sample_pdf_path):
        """Testa extração de texto de PDF real."""
        tool = PDFAnonymizationTool()
        result = tool.extract_clean_text(sample_pdf_path)
        
        assert result["success"] is True
        assert result["pages"] == 1
        assert "Texto de teste" in result["text"]
    
    def test_anonymize_real_pdf(self, sample_pdf_path):
        """Testa anonimização de PDF real."""
        tool = PDFAnonymizationTool()
        result = tool.anonymize_pdf(sample_pdf_path)
        
        assert result["success"] is True
        assert len(result["anonymized_text"]) > 0
        assert result["extraction_metadata"]["pages"] == 1
