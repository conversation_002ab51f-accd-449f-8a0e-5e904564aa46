"""
Dados de exemplo para testes
"""

import pandas as pd


def get_sample_ouvidorias_data():
    """Dados de exemplo de ouvidorias"""
    return pd.DataFrame({
        'RELATO': [
            'Paciente com problema de visão aguarda consulta oftalmológica há 6 meses no Hospital Municipal',
            'Solicitação de exame de sangue para diagnóstico de diabetes foi negada sem justificativa',
            'Demora na marcação de cirurgia de catarata para idoso de 75 anos',
            'Problema com medicamento para hipertensão não disponível na farmácia básica',
            'Consulta de oftalmologia cancelada sem aviso prévio ao paciente',
            'Falta de médico oftalmologista no Centro de Saúde da região',
            'Equipamento de exame oftalmológico quebrado há 3 meses',
            'Paciente diabético sem acesso a insulina na rede pública',
            'Demora na regulação para cirurgia de retina urgente',
            'Atendimento inadequado na recepção do hospital'
        ],
        'Teor': [
            'Paciente com problema de visão aguarda consulta oftalmológica há 6 meses no Hospital Municipal',
            'Solicitação de exame de sangue para diagnóstico de diabetes foi negada sem justificativa',
            'Demora na marcação de cirurgia de catarata para idoso de 75 anos',
            'Problema com medicamento para hipertensão não disponível na farmácia básica',
            'Consulta de oftalmologia cancelada sem aviso prévio ao paciente',
            'Falta de médico oftalmologista no Centro de Saúde da região',
            'Equipamento de exame oftalmológico quebrado há 3 meses',
            'Paciente diabético sem acesso a insulina na rede pública',
            'Demora na regulação para cirurgia de retina urgente',
            'Atendimento inadequado na recepção do hospital'
        ],
        'Data': [
            '2024-01-15',
            '2024-01-16',
            '2024-01-17',
            '2024-01-18',
            '2024-01-19',
            '2024-01-20',
            '2024-01-21',
            '2024-01-22',
            '2024-01-23',
            '2024-01-24'
        ],
        'Origem': [
            'Ouvidoria Municipal',
            'Ouvidoria Estadual',
            'Ouvidoria Municipal',
            'Ouvidoria Municipal',
            'Ouvidoria Municipal',
            'Ouvidoria Estadual',
            'Ouvidoria Municipal',
            'Ouvidoria Estadual',
            'Ouvidoria Municipal',
            'Ouvidoria Municipal'
        ],
        'ID': list(range(1, 11))
    })


def get_sample_classified_data():
    """Dados de exemplo já classificados"""
    return pd.DataFrame({
        'RELATO': [
            'Paciente com problema de visão aguarda consulta oftalmológica há 6 meses',
            'Demora na marcação de cirurgia de catarata para idoso de 75 anos',
            'Consulta de oftalmologia cancelada sem aviso prévio ao paciente',
            'Falta de médico oftalmologista no Centro de Saúde da região',
            'Equipamento de exame oftalmológico quebrado há 3 meses',
            'Demora na regulação para cirurgia de retina urgente'
        ],
        'TEXTO_ANALISADO': [
            'Paciente com problema de visão aguarda consulta oftalmológica há 6 meses',
            'Demora na marcação de cirurgia de catarata para idoso de 75 anos',
            'Consulta de oftalmologia cancelada sem aviso prévio ao paciente',
            'Falta de médico oftalmologista no Centro de Saúde da região',
            'Equipamento de exame oftalmológico quebrado há 3 meses',
            'Demora na regulação para cirurgia de retina urgente'
        ],
        'REFERE_ASSUNTO': [1, 1, 1, 1, 1, 1],
        'Data': [
            '2024-01-15',
            '2024-01-17',
            '2024-01-19',
            '2024-01-20',
            '2024-01-21',
            '2024-01-23'
        ],
        'ID': [1, 3, 5, 6, 7, 9]
    })


def get_sample_multilabel_data():
    """Dados de exemplo com classificação multilabel"""
    return pd.DataFrame({
        'RELATO': [
            'Paciente com problema de visão aguarda consulta oftalmológica há 6 meses',
            'Demora na regulação para cirurgia de catarata',
            'Exame de diagnóstico por imagem cancelado sem justificativa',
            'Auditoria encontrou irregularidades na gestão de medicamentos',
            'Problema na regulação de consultas especializadas'
        ],
        'TEXTO_ANALISADO': [
            'Paciente com problema de visão aguarda consulta oftalmológica há 6 meses',
            'Demora na regulação para cirurgia de catarata',
            'Exame de diagnóstico por imagem cancelado sem justificativa',
            'Auditoria encontrou irregularidades na gestão de medicamentos',
            'Problema na regulação de consultas especializadas'
        ],
        'SUBTEMAS_IDENTIFICADOS': [
            'Oftalmologia',
            'Oftalmologia, Regulação em Saúde',
            'Diagnose (laboratório e imagem)',
            'Auditoria',
            'Regulação em Saúde'
        ],
        'AREA_CLASSIFICACAO': ['SAUDE', 'SAUDE', 'SAUDE', 'SAUDE', 'SAUDE'],
        'SUBTEMA_OFTALMOLOGIA': [1, 1, 0, 0, 0],
        'SUBTEMA_REGULACAO_EM_SAUDE': [0, 1, 0, 0, 1],
        'SUBTEMA_DIAGNOSE_LABORATORIO_E_IMAGEM': [0, 0, 1, 0, 0],
        'SUBTEMA_AUDITORIA': [0, 0, 0, 1, 0],
        'ID': [1, 2, 3, 4, 5]
    })


def get_sample_comparison_data():
    """Dados de exemplo para comparação de métodos"""
    return {
        'individual': {
            'total_processed': 100,
            'positive_cases': 25,
            'percentage': 25.0,
            'processing_time': 120.5,
            'model_used': 'meta-llama/Llama-4-Scout-17B-16E-Instruct'
        },
        'batch': {
            'total_processed': 100,
            'positive_cases': 23,
            'percentage': 23.0,
            'processing_time': 85.2,
            'model_used': 'meta-llama/Llama-4-Scout-17B-16E-Instruct'
        },
        'embeddings': {
            'total_processed': 100,
            'positive_cases': 28,
            'percentage': 28.0,
            'processing_time': 45.8,
            'model_used': 'sentence-transformers/all-MiniLM-L6-v2'
        }
    }


def get_sample_config_data():
    """Configuração de exemplo para testes"""
    return {
        'system': {
            'name': 'Simple Class Test',
            'version': '0.2.0'
        },
        'models': {
            'llm_model': 'test-llm-model',
            'report_model': 'test-report-model',
            'embedding_model': 'test-embedding-model'
        },
        'classification': {
            'subject': 'Oftalmologia',
            'batch_size': 5
        },
        'subtemas': {
            'SAUDE': [
                'Oftalmologia',
                'Regulação em Saúde',
                'Diagnose (laboratório e imagem)',
                'Auditoria',
                'Oncologia'
            ],
            'EDUCACAO': [
                'Educação Infantil',
                'Ensino Fundamental',
                'Ensino Médio'
            ]
        },
        'files': {
            'input_dir': 'test_data/input',
            'output_dir': 'test_data/output',
            'temp_dir': 'test_data/temp'
        }
    }


def get_sample_report_templates():
    """Templates de exemplo para relatórios"""
    return {
        'tactical': """
# ANÁLISE TÁTICA DE CASOS RELACIONADOS A {assunto_maiusculo}

Você recebeu {total_casos} denúncias relacionadas especificamente a **{assunto}**.

## DENÚNCIAS ANALISADAS:
{denuncias_formatadas}

## ANÁLISE SOLICITADA:
Realize uma análise tática profunda dos casos apresentados.
""",
        'overview': """
# RELATÓRIO DE VISÃO GERAL

## DADOS PROCESSADOS:
- Total de registros: {total_registros}
- Casos positivos: {casos_positivos}
- Taxa de positividade: {percentual_positivos:.1f}%

## ASSUNTO: {assunto_maiusculo}

Análise geral dos resultados de classificação.
""",
        'multilabel': """
# RELATÓRIO MULTILABEL - {area}

## DADOS:
- Total de registros: {total_registros}
- Registros com subtemas: {registros_com_subtemas}

## DISTRIBUIÇÃO POR SUBTEMAS:
{subtemas_contagem}

Análise detalhada da classificação multilabel.
"""
    }


def get_sample_anonymization_data():
    """Dados de exemplo para testes de anonimização"""
    return pd.DataFrame({
        'Teor': [
            'João Silva, 45 anos, relata problema de saúde',
            'Maria Santos precisa de consulta médica urgente',
            'Paciente sem informações pessoais identificáveis',
            'Dr. Pedro Oliveira atendeu mal o paciente',
            'Telefone (11) 99999-9999 para contato com José'
        ],
        'Data': [
            '2024-01-01',
            '2024-01-02',
            '2024-01-03',
            '2024-01-04',
            '2024-01-05'
        ],
        'ID': [1, 2, 3, 4, 5]
    })


def get_sample_invalid_data():
    """Dados de exemplo com problemas para testes de validação"""
    return pd.DataFrame({
        'Teor': [
            'Texto válido',
            '',  # Texto vazio
            None,  # Valor nulo
            '   ',  # Apenas espaços
            'Outro texto válido'
        ],
        'Coluna_Com_Nulls': [
            'Valor 1',
            None,
            'Valor 3',
            None,
            'Valor 5'
        ],
        'ID': [1, 2, 3, 4, 5]
    })


# Funções auxiliares para criar arquivos de teste
def create_test_csv(data, filepath):
    """Cria arquivo CSV para teste"""
    if isinstance(data, dict):
        df = pd.DataFrame(data)
    else:
        df = data
    
    df.to_csv(filepath, index=False)
    return filepath


def create_test_config(config_data, filepath):
    """Cria arquivo de configuração para teste"""
    import yaml
    
    with open(filepath, 'w', encoding='utf-8') as f:
        yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
    
    return filepath


def create_test_template(template_content, filepath):
    """Cria arquivo de template para teste"""
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(template_content)
    
    return filepath
