#!/usr/bin/env python3
"""
Script to run API tests with different configurations.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}")
    print(f"   Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"   Error: {e.stderr.strip()}")
        if e.stdout:
            print(f"   Output: {e.stdout.strip()}")
        return False


def run_all_tests():
    """Run all tests with coverage."""
    cmd = ["poetry", "run", "pytest", "-v", "--cov=src", "--cov-report=term-missing"]
    return run_command(cmd, "Running all tests with coverage")


def run_unit_tests():
    """Run only unit tests."""
    cmd = ["poetry", "run", "pytest", "-v", "-m", "unit"]
    return run_command(cmd, "Running unit tests")


def run_integration_tests():
    """Run only integration tests."""
    cmd = ["poetry", "run", "pytest", "-v", "-m", "integration"]
    return run_command(cmd, "Running integration tests")


def run_auth_tests():
    """Run authentication tests."""
    cmd = ["poetry", "run", "pytest", "-v", "tests/test_auth.py"]
    return run_command(cmd, "Running authentication tests")


def run_files_tests():
    """Run file management tests."""
    cmd = ["poetry", "run", "pytest", "-v", "tests/test_files.py"]
    return run_command(cmd, "Running file management tests")


def run_anonymization_tests():
    """Run anonymization tests."""
    cmd = ["poetry", "run", "pytest", "-v", "tests/test_anonymization.py"]
    return run_command(cmd, "Running anonymization tests")


def run_classification_tests():
    """Run classification tests."""
    cmd = ["poetry", "run", "pytest", "-v", "tests/test_classification.py"]
    return run_command(cmd, "Running classification tests")


def run_reports_tests():
    """Run report generation tests."""
    cmd = ["poetry", "run", "pytest", "-v", "tests/test_reports.py"]
    return run_command(cmd, "Running report generation tests")


def run_fast_tests():
    """Run fast tests only (exclude slow tests)."""
    cmd = ["poetry", "run", "pytest", "-v", "-m", "not slow"]
    return run_command(cmd, "Running fast tests")


def run_with_html_coverage():
    """Run tests with HTML coverage report."""
    cmd = ["poetry", "run", "pytest", "-v", "--cov=src", "--cov-report=html:htmlcov"]
    return run_command(cmd, "Running tests with HTML coverage report")


def check_test_environment():
    """Check if test environment is properly set up."""
    print("🔍 Checking test environment...")
    
    # Check if pytest is installed
    try:
        import pytest
        print(f"✅ pytest installed: {pytest.__version__}")
    except ImportError:
        print("❌ pytest not installed")
        return False
    
    # Check if required test dependencies are available
    required_deps = ["httpx", "pytest_asyncio", "pytest_mock"]
    for dep in required_deps:
        try:
            __import__(dep)
            print(f"✅ {dep} available")
        except ImportError:
            print(f"❌ {dep} not available")
            return False
    
    # Check if test files exist
    test_files = [
        "tests/conftest.py",
        "tests/test_auth.py",
        "tests/test_files.py",
        "tests/test_anonymization.py",
        "tests/test_classification.py",
        "tests/test_reports.py"
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"✅ {test_file} exists")
        else:
            print(f"❌ {test_file} missing")
            return False
    
    print("✅ Test environment is properly set up")
    return True


def main():
    """Main function to handle command line arguments and run tests."""
    parser = argparse.ArgumentParser(description="Run API tests")
    parser.add_argument(
        "test_type",
        nargs="?",
        default="all",
        choices=[
            "all", "unit", "integration", "auth", "files", 
            "anonymization", "classification", "reports", 
            "fast", "coverage", "check"
        ],
        help="Type of tests to run"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--no-cov",
        action="store_true",
        help="Skip coverage reporting"
    )
    
    args = parser.parse_args()
    
    print("🧪 Simple Class API Test Runner")
    print("=" * 50)
    
    # Check environment first
    if args.test_type == "check":
        success = check_test_environment()
        sys.exit(0 if success else 1)
    
    if not check_test_environment():
        print("\n❌ Test environment check failed. Please fix the issues above.")
        sys.exit(1)
    
    # Run the specified tests
    success = False
    
    if args.test_type == "all":
        success = run_all_tests()
    elif args.test_type == "unit":
        success = run_unit_tests()
    elif args.test_type == "integration":
        success = run_integration_tests()
    elif args.test_type == "auth":
        success = run_auth_tests()
    elif args.test_type == "files":
        success = run_files_tests()
    elif args.test_type == "anonymization":
        success = run_anonymization_tests()
    elif args.test_type == "classification":
        success = run_classification_tests()
    elif args.test_type == "reports":
        success = run_reports_tests()
    elif args.test_type == "fast":
        success = run_fast_tests()
    elif args.test_type == "coverage":
        success = run_with_html_coverage()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests completed successfully!")
        print("\n💡 Next steps:")
        print("   - Review test coverage report")
        print("   - Check for any failing tests")
        print("   - Add more tests if needed")
        if args.test_type == "coverage":
            print("   - Open htmlcov/index.html to view detailed coverage")
    else:
        print("❌ Some tests failed. Please check the output above.")
        print("\n🔧 Troubleshooting:")
        print("   - Check if all dependencies are installed")
        print("   - Verify API endpoints are working")
        print("   - Review test configuration")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
