"""
Unit tests for PDFTextExtractor with intelligent chunking.

Tests cover various PDF formats, sizes, and edge cases to ensure robust
text extraction and chunking functionality.
"""

import pytest
import tempfile
import fitz  # PyMuPDF
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from typing import List

from src.tools.pdf_text_extractor import PDFTextExtractor, create_pdf_text_extractor
from src.api.models.batch_processing import TextChunk


class TestPDFTextExtractor:
    """Test suite for PDFTextExtractor class."""
    
    @pytest.fixture
    def extractor(self):
        """Create a PDFTextExtractor instance for testing."""
        return PDFTextExtractor(chunk_size=500, overlap=100)
    
    @pytest.fixture
    def sample_text(self):
        """Sample text for testing chunking logic."""
        return """
        Este é o primeiro parágrafo do documento. Ele contém informações importantes sobre o assunto principal.
        
        O segundo parágrafo continua a discussão. Aqui temos mais detalhes e explicações sobre o tópico.
        
        O terceiro parágrafo apresenta conclusões. É importante notar que este texto é usado para testes.
        
        Finalmente, o último parágrafo encerra o documento. Ele resume os pontos principais discutidos anteriormente.
        """
    
    @pytest.fixture
    def create_test_pdf(self):
        """Factory function to create test PDF files."""
        def _create_pdf(text: str, filename: str = None) -> Path:
            if filename is None:
                temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
                pdf_path = Path(temp_file.name)
                temp_file.close()
            else:
                pdf_path = Path(filename)
            
            # Create PDF with PyMuPDF
            doc = fitz.open()
            page = doc.new_page()
            page.insert_text((50, 50), text, fontsize=12)
            doc.save(str(pdf_path))
            doc.close()
            
            return pdf_path
        return _create_pdf
    
    def test_initialization_valid_parameters(self):
        """Test PDFTextExtractor initialization with valid parameters."""
        extractor = PDFTextExtractor(chunk_size=1000, overlap=200)
        assert extractor.chunk_size == 1000
        assert extractor.overlap == 200
        assert extractor.header_footer_threshold == 0.3
        assert extractor.min_text_length == 10
        assert extractor.preserve_structure is True
    
    def test_initialization_invalid_chunk_size(self):
        """Test initialization fails with invalid chunk size."""
        with pytest.raises(ValueError, match="chunk_size must be at least 100 tokens"):
            PDFTextExtractor(chunk_size=50)
    
    def test_initialization_invalid_overlap(self):
        """Test initialization fails with invalid overlap."""
        with pytest.raises(ValueError, match="overlap must be less than chunk_size"):
            PDFTextExtractor(chunk_size=500, overlap=600)
        
        with pytest.raises(ValueError, match="overlap cannot be negative"):
            PDFTextExtractor(chunk_size=500, overlap=-10)
    
    def test_validate_pdf_nonexistent_file(self, extractor):
        """Test PDF validation fails for nonexistent file."""
        assert not extractor.validate_pdf("nonexistent.pdf")
    
    def test_validate_pdf_wrong_extension(self, extractor):
        """Test PDF validation fails for wrong file extension."""
        with tempfile.NamedTemporaryFile(suffix='.txt') as temp_file:
            assert not extractor.validate_pdf(temp_file.name)
    
    def test_validate_pdf_valid_file(self, extractor, create_test_pdf):
        """Test PDF validation succeeds for valid PDF."""
        pdf_path = create_test_pdf("Test content for validation")
        try:
            assert extractor.validate_pdf(str(pdf_path))
        finally:
            pdf_path.unlink()
    
    def test_validate_pdf_empty_document(self, extractor):
        """Test PDF validation fails for empty document."""
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            pdf_path = Path(temp_file.name)
        
        try:
            # Create PDF with empty page (PyMuPDF requires at least one page)
            doc = fitz.open()
            page = doc.new_page()
            # Don't add any text to the page
            doc.save(str(pdf_path))
            doc.close()
            
            # Should fail validation because there's no meaningful text
            assert not extractor.validate_pdf(str(pdf_path))
        finally:
            pdf_path.unlink()
    
    def test_estimate_tokens(self, extractor):
        """Test token estimation functionality."""
        # Empty text
        assert extractor._estimate_tokens("") == 0
        
        # Short text
        assert extractor._estimate_tokens("test") == 1
        
        # Longer text (roughly 4 chars per token)
        text = "Este é um texto de teste com várias palavras"
        expected_tokens = max(1, len(text) // 4)
        assert extractor._estimate_tokens(text) == expected_tokens
    
    def test_split_into_sentences(self, extractor):
        """Test sentence splitting functionality."""
        text = "Esta é a primeira frase. Esta é a segunda frase! Esta é a terceira frase?"
        sentences = extractor._split_into_sentences(text)
        
        assert len(sentences) == 3
        assert "Esta é a primeira frase" in sentences[0]
        assert "Esta é a segunda frase" in sentences[1]
        assert "Esta é a terceira frase" in sentences[2]
    
    def test_split_into_paragraphs(self, extractor, sample_text):
        """Test paragraph splitting functionality."""
        paragraphs = extractor._split_into_paragraphs(sample_text)
        
        # Should have 4 paragraphs
        assert len(paragraphs) == 4
        assert "primeiro parágrafo" in paragraphs[0]
        assert "segundo parágrafo" in paragraphs[1]
        assert "terceiro parágrafo" in paragraphs[2]
        assert "último parágrafo" in paragraphs[3]
    
    def test_get_overlap_text_sentence_based(self, extractor):
        """Test overlap text extraction with sentence boundaries."""
        text = "Esta é a primeira frase. Esta é a segunda frase. Esta é a terceira frase."
        overlap = extractor._get_overlap_text(text)
        
        # Should include complete sentences that fit in overlap
        assert overlap
        assert "frase" in overlap
    
    def test_get_overlap_text_word_based(self, extractor):
        """Test overlap text extraction falls back to word-based."""
        # Very long sentence that won't fit in overlap
        long_sentence = "Esta é uma frase muito longa " * 50
        overlap = extractor._get_overlap_text(long_sentence)
        
        # Should fall back to word-based overlap
        assert overlap
        assert len(overlap.split()) > 0
    
    def test_get_overlap_text_no_overlap(self):
        """Test overlap text when overlap is disabled."""
        extractor = PDFTextExtractor(chunk_size=500, overlap=0)
        text = "Some test text here."
        overlap = extractor._get_overlap_text(text)
        
        assert overlap == ""
    
    def test_create_single_chunk(self, extractor):
        """Test creation of individual text chunks."""
        text = "Este é um texto de teste para criar um chunk."
        page_mapping = {i: 1 for i in range(len(text))}
        
        chunk = extractor._create_single_chunk(
            text=text,
            chunk_id=0,
            char_start=0,
            page_mapping=page_mapping,
            pdf_path="/test/path.pdf"
        )
        
        assert chunk is not None
        assert chunk.text == text.strip()
        assert chunk.chunk_id == 0
        assert chunk.page_start == 1
        assert chunk.page_end == 1
        assert chunk.char_start == 0
        assert chunk.char_end == len(text)
        assert chunk.pdf_path == "/test/path.pdf"
        assert chunk.token_count > 0
    
    def test_create_chunks_simple_text(self, extractor):
        """Test chunk creation with simple text."""
        text = "Parágrafo um.\n\nParágrafo dois.\n\nParágrafo três."
        page_mapping = {i: 1 for i in range(len(text))}
        
        chunks = extractor._create_chunks(text, page_mapping, "/test/path.pdf")
        
        assert len(chunks) >= 1
        assert all(isinstance(chunk, TextChunk) for chunk in chunks)
        assert all(chunk.pdf_path == "/test/path.pdf" for chunk in chunks)
    
    def test_create_chunks_with_overlap(self, extractor):
        """Test chunk creation includes proper overlap."""
        # Create text that will definitely need multiple chunks
        long_text = "Este é um parágrafo muito longo. " * 100
        page_mapping = {i: 1 for i in range(len(long_text))}
        
        chunks = extractor._create_chunks(long_text, page_mapping, "/test/path.pdf")
        
        # Should create multiple chunks
        assert len(chunks) > 1
        
        # Check that chunks have sequential IDs
        for i, chunk in enumerate(chunks):
            assert chunk.chunk_id == i
    
    def test_extract_and_chunk_invalid_pdf(self, extractor):
        """Test extract_and_chunk with invalid PDF."""
        chunks = extractor.extract_and_chunk("nonexistent.pdf")
        assert chunks == []
    
    def test_extract_and_chunk_valid_pdf(self, extractor, create_test_pdf):
        """Test extract_and_chunk with valid PDF."""
        # Create a real test PDF
        test_content = "Este é um conteúdo de teste para o PDF. " * 10
        pdf_path = create_test_pdf(test_content)
        
        try:
            chunks = extractor.extract_and_chunk(pdf_path)
            
            # Should create at least one chunk
            assert len(chunks) >= 1
            assert all(isinstance(chunk, TextChunk) for chunk in chunks)
            assert all(chunk.text.strip() for chunk in chunks)  # Ensure chunks have content
            
            # Check chunk properties
            for chunk in chunks:
                assert chunk.pdf_path == str(pdf_path)
                assert chunk.page_start >= 1
                assert chunk.page_end >= chunk.page_start
                assert chunk.token_count > 0
                
        finally:
            pdf_path.unlink()
    
    def test_factory_function(self):
        """Test the factory function creates proper extractor."""
        extractor = create_pdf_text_extractor(chunk_size=800, overlap=150)
        
        assert isinstance(extractor, PDFTextExtractor)
        assert extractor.chunk_size == 800
        assert extractor.overlap == 150
    
    def test_factory_function_defaults(self):
        """Test factory function with default parameters."""
        extractor = create_pdf_text_extractor()
        
        assert isinstance(extractor, PDFTextExtractor)
        assert extractor.chunk_size == 1000
        assert extractor.overlap == 200


class TestPDFTextExtractorIntegration:
    """Integration tests with actual PDF files."""
    
    @pytest.fixture
    def create_multi_page_pdf(self):
        """Create a multi-page PDF for testing."""
        def _create_pdf(pages_content: List[str]) -> Path:
            temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
            pdf_path = Path(temp_file.name)
            temp_file.close()
            
            doc = fitz.open()
            for i, content in enumerate(pages_content):
                page = doc.new_page()
                page.insert_text((50, 50), content, fontsize=12)
            
            doc.save(str(pdf_path))
            doc.close()
            return pdf_path
        return _create_pdf
    
    def test_multi_page_pdf_extraction(self, create_multi_page_pdf):
        """Test extraction from multi-page PDF."""
        pages = [
            "Conteúdo da primeira página com informações importantes.",
            "Segunda página contém mais detalhes sobre o assunto.",
            "Terceira página apresenta as conclusões finais."
        ]
        
        pdf_path = create_multi_page_pdf(pages)
        extractor = PDFTextExtractor(chunk_size=200, overlap=50)
        
        try:
            chunks = extractor.extract_and_chunk(pdf_path)
            
            # Should extract chunks from multiple pages
            assert len(chunks) >= 1
            
            # Check page mapping
            page_numbers = set()
            for chunk in chunks:
                page_numbers.add(chunk.page_start)
                page_numbers.add(chunk.page_end)
            
            # Should have references to multiple pages
            assert len(page_numbers) >= 2
            
        finally:
            pdf_path.unlink()
    
    def test_pdf_with_headers_footers(self, create_multi_page_pdf):
        """Test PDF processing with repeated headers and footers."""
        pages = [
            "HEADER\nConteúdo da primeira página.\nFOOTER",
            "HEADER\nConteúdo da segunda página.\nFOOTER",
            "HEADER\nConteúdo da terceira página.\nFOOTER"
        ]
        
        pdf_path = create_multi_page_pdf(pages)
        extractor = PDFTextExtractor(chunk_size=200, overlap=50)
        
        try:
            chunks = extractor.extract_and_chunk(pdf_path)
            
            # Should create chunks
            assert len(chunks) >= 1
            
            # Headers and footers should be removed from chunk content
            for chunk in chunks:
                # The exact header/footer removal depends on the threshold
                # Just check that we have meaningful content
                assert len(chunk.text.strip()) > 0
                
        finally:
            pdf_path.unlink()
    
    def test_large_pdf_chunking(self, create_multi_page_pdf):
        """Test chunking of large PDF content."""
        # Create very large content that will definitely require multiple chunks
        # Create multiple distinct paragraphs to ensure proper chunking
        paragraphs = []
        for i in range(20):  # More paragraphs
            paragraph = f"Este é o parágrafo número {i+1} com muitas informações importantes sobre diversos assuntos relevantes para o teste de chunking. " * 30  # More repetitions
            paragraphs.append(paragraph)
        
        large_content = "\n\n".join(paragraphs)  # Join with paragraph breaks
        pages = [large_content]  # Single page with very large content
        
        pdf_path = create_multi_page_pdf(pages)
        extractor = PDFTextExtractor(chunk_size=100, overlap=20)  # Very small chunk size to force splitting
        
        try:
            chunks = extractor.extract_and_chunk(pdf_path)
            
            # Should create at least one chunk (may be multiple)
            assert len(chunks) >= 1
            
            # Check chunk sizes are reasonable
            for chunk in chunks:
                assert chunk.token_count > 0
                assert len(chunk.text) > 0
                
        finally:
            pdf_path.unlink()


class TestEdgeCases:
    """Test edge cases and error conditions."""
    
    def test_empty_text_chunking(self):
        """Test chunking with empty text."""
        extractor = PDFTextExtractor()
        chunks = extractor._create_chunks("", {}, "/test/path.pdf")
        assert chunks == []
    
    def test_single_word_text(self):
        """Test chunking with single word."""
        extractor = PDFTextExtractor(chunk_size=100, overlap=20)
        chunks = extractor._create_chunks("palavra", {0: 1}, "/test/path.pdf")
        
        assert len(chunks) == 1
        assert chunks[0].text == "palavra"
    
    def test_chunk_creation_error_handling(self):
        """Test error handling in chunk creation."""
        extractor = PDFTextExtractor()
        
        # Test with invalid page mapping
        chunk = extractor._create_single_chunk(
            text="test",
            chunk_id=0,
            char_start=0,
            page_mapping={},  # Empty mapping
            pdf_path="/test/path.pdf"
        )
        
        # Should still create chunk with default page 1
        assert chunk is not None
        assert chunk.page_start == 1
        assert chunk.page_end == 1
    
    def test_very_small_chunk_size(self):
        """Test behavior with very small chunk size."""
        with pytest.raises(ValueError):
            PDFTextExtractor(chunk_size=50)  # Below minimum
    
    def test_zero_overlap(self):
        """Test chunking with zero overlap."""
        extractor = PDFTextExtractor(chunk_size=200, overlap=0)
        text = "Primeiro parágrafo.\n\nSegundo parágrafo.\n\nTerceiro parágrafo."
        chunks = extractor._create_chunks(text, {i: 1 for i in range(len(text))}, "/test/path.pdf")
        
        assert len(chunks) >= 1
        # With zero overlap, chunks should not overlap
        for chunk in chunks:
            assert chunk.token_count > 0


@pytest.mark.integration
class TestRealPDFFiles:
    """Integration tests with real PDF files if available."""
    
    def test_with_sample_pdf_if_exists(self):
        """Test with sample PDF file if it exists in test data."""
        sample_pdf = Path("data/input/teste_anonimizacao.pdf")
        
        if sample_pdf.exists():
            extractor = PDFTextExtractor(chunk_size=1000, overlap=200)
            chunks = extractor.extract_and_chunk(sample_pdf)
            
            # Basic validation
            assert len(chunks) >= 1
            assert all(isinstance(chunk, TextChunk) for chunk in chunks)
            assert all(chunk.token_count > 0 for chunk in chunks)
            assert all(len(chunk.text.strip()) > 0 for chunk in chunks)
        else:
            pytest.skip("Sample PDF file not available")