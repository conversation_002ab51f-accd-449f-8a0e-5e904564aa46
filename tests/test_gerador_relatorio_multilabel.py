#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Testes para o gerador de relatórios multilabel.
"""

import pytest
import pandas as pd
import tempfile
import os
from unittest.mock import patch, MagicMock

from src.reports.gerador_relatorio_multilabel import (
    carregar_dados_multilabel,
    obter_colunas_subtemas,
    filtrar_por_subtema,
    filtrar_por_multiplos_subtemas,
    analisar_estatisticas_subtemas,
    carregar_template_multilabel,
    gerar_prompt_subtema_individual,
    gerar_prompt_multiplos_subtemas,
    salvar_relatorio
)

class TestCarregarDados:
    """Testes para carregamento de dados."""
    
    def test_carregar_dados_sucesso(self):
        """Testa carregamento bem-sucedido de dados."""
        # Criar arquivo temporário
        data = {
            'Teor': ['Relato 1', 'Relato 2'],
            'SUBTEMA_ONCOLOGIA': [1, 0],
            'SUBTEMA_CARDIOLOGIA': [0, 1]
        }
        df = pd.DataFrame(data)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            
            resultado = carregar_dados_multilabel(f.name)
            
        os.unlink(f.name)
        
        assert resultado is not None
        assert len(resultado) == 2
        assert 'SUBTEMA_ONCOLOGIA' in resultado.columns
    
    def test_carregar_dados_arquivo_inexistente(self):
        """Testa carregamento de arquivo inexistente."""
        resultado = carregar_dados_multilabel('arquivo_inexistente.csv')
        assert resultado is None

class TestColunaSubtemas:
    """Testes para identificação de colunas de subtemas."""
    
    def test_obter_colunas_subtemas(self):
        """Testa identificação de colunas de subtemas."""
        data = {
            'Teor': ['Relato 1'],
            'SUBTEMA_ONCOLOGIA': [1],
            'SUBTEMA_CARDIOLOGIA': [0],
            'OUTRA_COLUNA': ['valor']
        }
        df = pd.DataFrame(data)
        
        colunas = obter_colunas_subtemas(df)
        
        assert len(colunas) == 2
        assert 'SUBTEMA_ONCOLOGIA' in colunas
        assert 'SUBTEMA_CARDIOLOGIA' in colunas
        assert 'OUTRA_COLUNA' not in colunas

class TestFiltros:
    """Testes para filtros de dados."""
    
    def setup_method(self):
        """Configuração para cada teste."""
        self.data = {
            'Teor': ['Relato 1', 'Relato 2', 'Relato 3'],
            'SUBTEMA_ONCOLOGIA': [1, 0, 1],
            'SUBTEMA_CARDIOLOGIA': [0, 1, 0],
            'SUBTEMA_NEUROLOGIA': [0, 0, 1]
        }
        self.df = pd.DataFrame(self.data)
    
    def test_filtrar_por_subtema_existente(self):
        """Testa filtro por subtema existente."""
        resultado = filtrar_por_subtema(self.df, 'SUBTEMA_ONCOLOGIA')
        
        assert len(resultado) == 2
        assert all(resultado['SUBTEMA_ONCOLOGIA'] == 1)
    
    def test_filtrar_por_subtema_inexistente(self):
        """Testa filtro por subtema inexistente."""
        resultado = filtrar_por_subtema(self.df, 'SUBTEMA_INEXISTENTE')
        
        assert len(resultado) == 0
    
    def test_filtrar_por_multiplos_subtemas(self):
        """Testa filtro por múltiplos subtemas."""
        subtemas = ['SUBTEMA_ONCOLOGIA', 'SUBTEMA_CARDIOLOGIA']
        resultado = filtrar_por_multiplos_subtemas(self.df, subtemas)
        
        assert len(resultado) == 3  # Todos os registros têm pelo menos um subtema

class TestEstatisticas:
    """Testes para análise de estatísticas."""
    
    def test_analisar_estatisticas_subtemas(self):
        """Testa análise de estatísticas por subtemas."""
        data = {
            'Teor': ['Relato 1', 'Relato 2', 'Relato 3', 'Relato 4'],
            'SUBTEMA_ONCOLOGIA': [1, 0, 1, 0],
            'SUBTEMA_CARDIOLOGIA': [0, 1, 0, 0]
        }
        df = pd.DataFrame(data)
        colunas = ['SUBTEMA_ONCOLOGIA', 'SUBTEMA_CARDIOLOGIA']
        
        estatisticas = analisar_estatisticas_subtemas(df, colunas)
        
        assert 'Oncologia' in estatisticas
        assert 'Cardiologia' in estatisticas
        assert estatisticas['Oncologia']['total'] == 2
        assert estatisticas['Cardiologia']['total'] == 1
        assert estatisticas['Oncologia']['percentual'] == 50.0
        assert estatisticas['Cardiologia']['percentual'] == 25.0

class TestTemplates:
    """Testes para carregamento de templates."""
    
    def test_carregar_template_individual(self):
        """Testa carregamento de template individual."""
        # Mock do arquivo de template
        with patch('builtins.open', create=True) as mock_open:
            mock_open.return_value.__enter__.return_value.read.return_value = "Template: {assunto}"
            
            resultado = carregar_template_multilabel('individual')
            
            assert resultado == "Template: {assunto}"
    
    def test_carregar_template_inexistente(self):
        """Testa carregamento de template inexistente."""
        with patch('builtins.open', side_effect=FileNotFoundError):
            resultado = carregar_template_multilabel('inexistente')
            
            assert resultado is None

class TestGeracaoPrompts:
    """Testes para geração de prompts."""
    
    def test_gerar_prompt_subtema_individual(self):
        """Testa geração de prompt para subtema individual."""
        data = {
            'Teor': ['Relato sobre câncer', 'Outro relato sobre câncer'],
            'SUBTEMA_ONCOLOGIA': [1, 1]
        }
        df = pd.DataFrame(data)
        
        with patch('src.reports.gerador_relatorio_multilabel.carregar_template_multilabel') as mock_template:
            mock_template.return_value = "Assunto: {assunto}\nCasos: {total_casos}\n{denuncias_formatadas}"

            # Mock das funções de contexto para evitar dependências
            with patch('src.reports.gerador_relatorio_multilabel._obter_contexto_subtema_para_relatorio') as mock_contexto:
                mock_contexto.return_value = {
                    'definicao': 'Casos relacionados a oncologia',
                    'palavras_chave_formatadas': 'câncer, tumor',
                    'foco_funcional': 'Análise especializada em oncologia'
                }

                resultado = gerar_prompt_subtema_individual(df, 'Oncologia', 'SAUDE')

                assert 'Oncologia' in resultado
                assert '2' in resultado  # total de casos
                assert 'Relato sobre câncer' in resultado
    
    def test_gerar_prompt_multiplos_subtemas(self):
        """Testa geração de prompt para múltiplos subtemas."""
        data = {
            'Teor': ['Relato 1', 'Relato 2'],
            'SUBTEMA_ONCOLOGIA': [1, 0],
            'SUBTEMA_CARDIOLOGIA': [0, 1]
        }
        df = pd.DataFrame(data)
        
        estatisticas = {
            'Oncologia': {
                'coluna': 'SUBTEMA_ONCOLOGIA',
                'total': 1,
                'percentual': 50.0,
                'casos': df[df['SUBTEMA_ONCOLOGIA'] == 1]
            },
            'Cardiologia': {
                'coluna': 'SUBTEMA_CARDIOLOGIA',
                'total': 1,
                'percentual': 50.0,
                'casos': df[df['SUBTEMA_CARDIOLOGIA'] == 1]
            }
        }
        
        with patch('src.reports.gerador_relatorio_multilabel.carregar_template_multilabel') as mock_template:
            mock_template.return_value = "Área: {area}\nSubtemas: {lista_subtemas}\n{estatisticas_formatadas}"
            
            resultado = gerar_prompt_multiplos_subtemas(df, estatisticas, 'SAUDE')
            
            assert 'SAUDE' in resultado
            assert 'Oncologia' in resultado
            assert 'Cardiologia' in resultado

class TestSalvarRelatorio:
    """Testes para salvamento de relatórios."""
    
    def test_salvar_relatorio_sucesso(self):
        """Testa salvamento bem-sucedido de relatório."""
        conteudo = "# Relatório de Teste\n\nConteúdo do relatório."
        
        # Usar um arquivo temporário real
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            arquivo_temp = f.name
        
        try:
            # Mock apenas para retornar nosso arquivo temporário
            with patch('src.reports.gerador_relatorio_multilabel.os.path.join') as mock_join:
                mock_join.return_value = arquivo_temp
                
                with patch('src.reports.gerador_relatorio_multilabel.os.makedirs'):
                    resultado = salvar_relatorio(conteudo, 'teste.md')
                    
                    assert resultado is True
                    assert os.path.exists(arquivo_temp)
                    
                    with open(arquivo_temp, 'r', encoding='utf-8') as f:
                        conteudo_salvo = f.read()
                    
                    assert conteudo_salvo == conteudo
        finally:
            # Limpar arquivo temporário
            if os.path.exists(arquivo_temp):
                os.unlink(arquivo_temp)

class TestIntegracao:
    """Testes de integração."""
    
    def test_fluxo_completo_dados_validos(self):
        """Testa fluxo completo com dados válidos."""
        # Criar dados de teste
        data = {
            'Teor': ['Relato sobre câncer de mama', 'Problema cardíaco grave', 'Outro caso de câncer'],
            'SUBTEMA_ONCOLOGIA': [1, 0, 1],
            'SUBTEMA_CARDIOLOGIA': [0, 1, 0],
            'AREA_CLASSIFICACAO': ['SAUDE', 'SAUDE', 'SAUDE']
        }
        df = pd.DataFrame(data)
        
        # Testar carregamento
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            
            dados_carregados = carregar_dados_multilabel(f.name)
            
        os.unlink(f.name)
        
        # Testar análise
        colunas_subtemas = obter_colunas_subtemas(dados_carregados)
        estatisticas = analisar_estatisticas_subtemas(dados_carregados, colunas_subtemas)
        
        # Verificações
        assert dados_carregados is not None
        assert len(colunas_subtemas) == 2
        assert 'Oncologia' in estatisticas
        assert 'Cardiologia' in estatisticas
        assert estatisticas['Oncologia']['total'] == 2
        assert estatisticas['Cardiologia']['total'] == 1
    
    def test_dados_sem_casos_positivos(self):
        """Testa comportamento com dados sem casos positivos."""
        data = {
            'Teor': ['Relato 1', 'Relato 2'],
            'SUBTEMA_ONCOLOGIA': [0, 0],
            'SUBTEMA_CARDIOLOGIA': [0, 0]
        }
        df = pd.DataFrame(data)
        
        colunas_subtemas = obter_colunas_subtemas(df)
        estatisticas = analisar_estatisticas_subtemas(df, colunas_subtemas)
        
        assert all(stats['total'] == 0 for stats in estatisticas.values())
    
    def test_dados_com_texto_analisado(self):
        """Testa comportamento com coluna TEXTO_ANALISADO."""
        data = {
            'Teor': ['Texto original 1', 'Texto original 2'],
            'TEXTO_ANALISADO': ['Texto processado 1', 'Texto processado 2'],
            'SUBTEMA_ONCOLOGIA': [1, 0]
        }
        df = pd.DataFrame(data)
        
        casos_positivos = filtrar_por_subtema(df, 'SUBTEMA_ONCOLOGIA')
        
        with patch('src.reports.gerador_relatorio_multilabel.carregar_template_multilabel') as mock_template:
            mock_template.return_value = "{denuncias_formatadas}"

            # Mock das funções de contexto
            with patch('src.reports.gerador_relatorio_multilabel._obter_contexto_subtema_para_relatorio') as mock_contexto:
                mock_contexto.return_value = {
                    'definicao': 'Casos relacionados a oncologia',
                    'palavras_chave_formatadas': 'câncer, tumor',
                    'foco_funcional': 'Análise especializada em oncologia'
                }

                prompt = gerar_prompt_subtema_individual(casos_positivos, 'Oncologia', 'SAUDE')

                # Deve usar TEXTO_ANALISADO em vez de Teor
                assert 'Texto processado 1' in prompt
                assert 'Texto original 1' not in prompt

if __name__ == '__main__':
    pytest.main([__file__]) 