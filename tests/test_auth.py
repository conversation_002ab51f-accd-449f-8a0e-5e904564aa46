#!/usr/bin/env python3
"""
Script to test authentication endpoints.
"""

import requests
import json

def test_login():
    """Test login endpoint."""
    print("🔐 Testing login endpoint...")
    
    base_url = "http://localhost:8000"
    
    # Test credentials
    credentials = {
        "email": "<EMAIL>",
        "password": "test123"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/auth/login",
            json=credentials,
            timeout=10
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Login successful!")
            print(f"   Access token: {result['access_token'][:50]}...")
            print(f"   Token type: {result['token_type']}")
            print(f"   Expires in: {result['expires_in']} seconds")
            print(f"   User: {result['user']['email']}")
            print(f"   Organization: {result['user']['organization']}")
            
            return result['access_token']
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login request failed: {e}")
        return None

def test_protected_endpoint(token):
    """Test protected endpoint with token."""
    print("\n🔒 Testing protected endpoint...")
    
    base_url = "http://localhost:8000"
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    try:
        # Test /auth/me endpoint
        response = requests.get(
            f"{base_url}/api/v1/auth/me",
            headers=headers,
            timeout=10
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Protected endpoint access successful!")
            print(f"   User ID: {result['id']}")
            print(f"   Email: {result['email']}")
            print(f"   Full name: {result['full_name']}")
            print(f"   Active: {result['is_active']}")
            return True
        else:
            print(f"❌ Protected endpoint failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Protected endpoint request failed: {e}")
        return False

def test_auth_status(token):
    """Test auth status endpoint."""
    print("\n📋 Testing auth status endpoint...")
    
    base_url = "http://localhost:8000"
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    try:
        response = requests.get(
            f"{base_url}/api/v1/auth/status",
            headers=headers,
            timeout=10
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Auth status successful!")
            print(f"   Authenticated: {result['authenticated']}")
            print(f"   Permissions: {len(result['permissions'])} total")
            for perm in result['permissions']:
                print(f"     - {perm}")
            return True
        else:
            print(f"❌ Auth status failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Auth status request failed: {e}")
        return False

def test_file_upload_with_auth(token):
    """Test file upload with authentication."""
    print("\n📁 Testing file upload with authentication...")
    
    base_url = "http://localhost:8000"
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    # Create a simple test file content
    test_content = "id,data,relato\n1,2024-01-01,Test data for authentication"
    
    files = {
        'file': ('test_auth.csv', test_content, 'text/csv')
    }
    
    data = {
        'metadata': json.dumps({
            "description": "Test file with authentication",
            "tags": ["test", "auth"]
        })
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/files/upload",
            headers=headers,
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ File upload with auth successful!")
            print(f"   File ID: {result['file_id']}")
            print(f"   Filename: {result['filename']}")
            print(f"   Size: {result['file_size']} bytes")
            return True
        else:
            print(f"❌ File upload failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ File upload request failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Authentication System Test")
    print("=" * 50)
    
    # Test login
    token = test_login()
    
    if not token:
        print("\n❌ Cannot proceed without valid token")
        return
    
    # Test protected endpoints
    me_ok = test_protected_endpoint(token)
    status_ok = test_auth_status(token)
    upload_ok = test_file_upload_with_auth(token)
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Login: {'✅ OK' if token else '❌ FAILED'}")
    print(f"   User Info: {'✅ OK' if me_ok else '❌ FAILED'}")
    print(f"   Auth Status: {'✅ OK' if status_ok else '❌ FAILED'}")
    print(f"   File Upload: {'✅ OK' if upload_ok else '❌ FAILED'}")
    
    if token and me_ok and status_ok and upload_ok:
        print("\n🎉 All authentication tests passed!")
        print("\n💡 Next steps:")
        print("   - Authentication system is working")
        print("   - Protected endpoints are secure")
        print("   - File upload with auth is functional")
        print("   - Ready for production use")
    else:
        print("\n⚠️  Some authentication tests failed")

if __name__ == "__main__":
    main()
