"""
Testes para core.config
"""

import pytest
import tempfile
import os
from unittest.mock import patch, Mock

from src.core.config import Config, get_config, get_llm_config, get_model_config
from src.core.exceptions import ConfigurationError


class TestConfig:
    """Testes para a classe Config"""
    
    def test_config_new_format(self, temp_config_file):
        """Teste com novo formato de configuração"""
        config = Config(temp_config_file)
        
        assert config.get_subject() == "Oftalmologia"
        
        model_config = config.get_model_config()
        assert model_config["llm_model"] == "test-model"
        assert model_config["report_model"] == "test-report-model"
        assert model_config["embedding_model"] == "test-embedding-model"
    
    def test_config_legacy_format(self):
        """Teste com formato legado"""
        legacy_config = """
ASSUNTO: "Cardiologia"
LLM_MODEL: "legacy-model"
REPORT_MODEL: "legacy-report-model"
SUBTEMAS_SAUDE:
  - "Cardiologia"
  - "Neurologia"
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
            f.write(legacy_config)
            temp_file = f.name
        
        try:
            config = Config(temp_file)
            
            assert config.get_subject() == "Cardiologia"
            
            model_config = config.get_model_config()
            assert model_config["llm_model"] == "legacy-model"
            assert model_config["report_model"] == "legacy-report-model"
        finally:
            os.unlink(temp_file)
    
    def test_config_file_not_found(self):
        """Teste com arquivo de configuração não encontrado"""
        with pytest.raises(ConfigurationError, match="Configuration file not found"):
            Config("arquivo_inexistente.yml")
    
    def test_config_invalid_yaml(self):
        """Teste com YAML inválido"""
        invalid_yaml = "invalid: yaml: content: ["
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
            f.write(invalid_yaml)
            temp_file = f.name
        
        try:
            with pytest.raises(ConfigurationError, match="Error parsing configuration file"):
                Config(temp_file)
        finally:
            os.unlink(temp_file)
    
    def test_config_missing_required_keys(self):
        """Teste com chaves obrigatórias ausentes"""
        incomplete_config = """
system:
  name: "Test"
models:
  llm_model: "test"
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
            f.write(incomplete_config)
            temp_file = f.name
        
        try:
            with pytest.raises(ConfigurationError, match="Configuration must contain"):
                Config(temp_file)
        finally:
            os.unlink(temp_file)
    
    def test_get_subtemas_new_format(self, temp_config_file):
        """Teste de obtenção de subtemas no novo formato"""
        config = Config(temp_config_file)
        
        subtemas = config.get_subtemas("SAUDE")
        assert "Oftalmologia" in subtemas
        assert "Regulação em Saúde" in subtemas
        assert "Diagnose" in subtemas
    
    def test_get_subtemas_legacy_format(self):
        """Teste de obtenção de subtemas no formato legado"""
        legacy_config = """
ASSUNTO: "Teste"
SUBTEMAS_SAUDE:
  - "Cardiologia"
  - "Neurologia"
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
            f.write(legacy_config)
            temp_file = f.name
        
        try:
            config = Config(temp_file)
            subtemas = config.get_subtemas("SAUDE")
            assert "Cardiologia" in subtemas
            assert "Neurologia" in subtemas
        finally:
            os.unlink(temp_file)
    
    def test_get_subtemas_area_not_found(self, temp_config_file):
        """Teste com área não encontrada"""
        config = Config(temp_config_file)
        
        subtemas = config.get_subtemas("AREA_INEXISTENTE")
        assert subtemas == []
    
    def test_get_file_config(self, temp_config_file):
        """Teste de configuração de arquivos"""
        config = Config(temp_config_file)
        
        file_config = config.get_file_config()
        assert file_config["input_dir"] == "test_data/input"
        assert file_config["output_dir"] == "test_data/output"
        assert file_config["temp_dir"] == "test_data/temp"
    
    def test_get_api_config(self, temp_config_file):
        """Teste de configuração da API"""
        config = Config(temp_config_file)
        
        api_config = config.get_api_config()
        # Usa valores padrão pois não estão no temp_config_file
        assert api_config["host"] == "0.0.0.0"
        assert api_config["port"] == 8000
    
    def test_get_logging_config(self, temp_config_file):
        """Teste de configuração de logging"""
        config = Config(temp_config_file)
        
        logging_config = config.get_logging_config()
        # Usa valores padrão pois não estão no temp_config_file
        assert logging_config["level"] == "INFO"
        assert "%(asctime)s" in logging_config["format"]
    
    @patch.dict(os.environ, {
        'TOGETHER_API_KEY': 'test-together-key',
        'ANTHROPIC_API_KEY': 'test-anthropic-key'
    })
    def test_environment_variables(self, temp_config_file):
        """Teste de carregamento de variáveis de ambiente"""
        config = Config(temp_config_file)
        
        assert config.get_env("TOGETHER_API_KEY") == "test-together-key"
        assert config.get_env("ANTHROPIC_API_KEY") == "test-anthropic-key"
        assert config.get_env("NONEXISTENT_KEY") is None
        assert config.get_env("NONEXISTENT_KEY", "default") == "default"


class TestConfigGlobalFunctions:
    """Testes para funções globais de configuração"""
    
    def test_get_config_singleton(self, temp_config_file):
        """Teste do padrão singleton"""
        with patch('src.core.config.Config') as mock_config_class:
            mock_instance = Mock()
            mock_config_class.return_value = mock_instance
            
            # Primeira chamada
            config1 = get_config()
            
            # Segunda chamada deve retornar a mesma instância
            config2 = get_config()
            
            assert config1 is config2
            mock_config_class.assert_called_once()
    
    def test_get_llm_config(self, mock_config):
        """Teste de obtenção de configuração LLM"""
        mock_config.get_model_config.return_value = {
            "llm_model": "test-llm-model"
        }
        mock_config.get_subject.return_value = "Test Subject"
        mock_config.get_env.return_value = "test-api-key"
        
        with patch('src.core.config.get_config', return_value=mock_config):
            llm_config = get_llm_config()
            
            assert llm_config["model"] == "test-llm-model"
            assert llm_config["subject"] == "Test Subject"
            assert llm_config["api_key"] == "test-api-key"
    
    def test_get_model_config_function(self, mock_config):
        """Teste de obtenção de configuração de modelos"""
        expected_config = {
            "llm_model": "test-llm",
            "report_model": "test-report",
            "embedding_model": "test-embedding"
        }
        mock_config.get_model_config.return_value = expected_config
        
        with patch('src.core.config.get_config', return_value=mock_config):
            model_config = get_model_config()
            
            assert model_config == expected_config
    
    def test_reload_config(self):
        """Teste de reload da configuração"""
        from src.core.config import reload_config
        import src.core.config
        
        # Definir uma instância
        src.core.config._config_instance = Mock()
        
        # Recarregar
        reload_config()
        
        # Verificar que foi limpa
        assert src.core.config._config_instance is None


class TestConfigEdgeCases:
    """Testes para casos extremos"""
    
    def test_config_with_empty_file(self):
        """Teste com arquivo vazio"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
            f.write("")  # Arquivo vazio
            temp_file = f.name
        
        try:
            config = Config(temp_file)
            # Arquivo vazio resulta em None, que deve falhar na validação
            with pytest.raises(ConfigurationError):
                config._validate_config()
        finally:
            os.unlink(temp_file)
    
    def test_config_with_none_values(self):
        """Teste com valores None"""
        config_with_none = """
classification:
  subject: null
models:
  llm_model: null
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
            f.write(config_with_none)
            temp_file = f.name
        
        try:
            config = Config(temp_file)
            
            # Valores None devem ser tratados adequadamente
            assert config.get_subject() is None
            
            model_config = config.get_model_config()
            # Deve usar valores padrão quando None
            assert model_config["llm_model"] is not None
        finally:
            os.unlink(temp_file)
    
    def test_config_find_file_priority(self):
        """Teste de prioridade na busca de arquivos"""
        # Criar múltiplos arquivos de configuração
        configs = {
            "config/settings.yml": "classification:\n  subject: 'settings'",
            "config/config.yml": "ASSUNTO: 'config'",
            "settings.yml": "classification:\n  subject: 'root_settings'",
            "config.yml": "ASSUNTO: 'root_config'"
        }
        
        temp_files = []
        
        try:
            # Criar arquivos temporários
            for filename, content in configs.items():
                # Criar diretório se necessário
                os.makedirs(os.path.dirname(filename), exist_ok=True)
                
                with open(filename, 'w') as f:
                    f.write(content)
                temp_files.append(filename)
            
            # Testar que settings.yml tem prioridade
            config = Config()
            assert config.get_subject() == "settings"
            
        finally:
            # Limpar arquivos criados
            for filename in temp_files:
                if os.path.exists(filename):
                    os.unlink(filename)
            
            # Remover diretório config se vazio
            if os.path.exists("config") and not os.listdir("config"):
                os.rmdir("config")
