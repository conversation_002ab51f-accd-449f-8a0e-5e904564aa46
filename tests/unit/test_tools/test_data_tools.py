"""
Testes para data_tools
"""

import pytest
import pandas as pd
import tempfile
import os
from unittest.mock import patch, Mock

from src.tools.data_tools import (
    load_csv_data, 
    show_available_columns, 
    validate_data_format,
    filter_valid_texts,
    anonymize_data
)
from src.core.exceptions import ValidationError, FileNotFoundError as SimpleClassFileNotFoundError


class TestLoadCsvData:
    """Testes para load_csv_data"""
    
    def test_load_csv_data_success(self, temp_csv_file):
        """Teste de carregamento bem-sucedido"""
        result = load_csv_data(temp_csv_file)
        
        assert result["total_rows"] == 5
        assert result["total_columns"] == 3
        assert "RELATO" in result["columns"]
        assert "Teor" in result["columns"]
        assert result["encoding_used"] in ["utf-8", "latin-1", "cp1252"]
        assert result["memory_usage_mb"] > 0
    
    def test_load_csv_data_file_not_found(self):
        """Teste com arquivo não encontrado"""
        with pytest.raises(SimpleClassFileNotFoundError):
            load_csv_data("arquivo_inexistente.csv")
    
    def test_load_csv_data_specific_encoding(self, temp_csv_file):
        """Teste com encoding específico"""
        result = load_csv_data(temp_csv_file, encoding="utf-8")
        
        assert result["encoding_used"] == "utf-8"
        assert result["total_rows"] == 5
    
    def test_load_csv_data_invalid_encoding(self):
        """Teste com encoding inválido"""
        # Criar arquivo com encoding específico
        data = pd.DataFrame({"col": ["teste"]})
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            data.to_csv(f.name, index=False)
            temp_file = f.name
        
        try:
            # Tentar carregar com encoding incompatível
            with pytest.raises(ValidationError, match="Não foi possível carregar o arquivo"):
                load_csv_data(temp_file, encoding="ascii")
        finally:
            os.unlink(temp_file)


class TestShowAvailableColumns:
    """Testes para show_available_columns"""
    
    def test_show_available_columns_success(self, temp_csv_file):
        """Teste de listagem de colunas bem-sucedida"""
        columns = show_available_columns(temp_csv_file)
        
        assert isinstance(columns, list)
        assert "RELATO" in columns
        assert "Teor" in columns
        assert "ID" in columns
        assert len(columns) == 3
    
    def test_show_available_columns_file_not_found(self):
        """Teste com arquivo não encontrado"""
        with pytest.raises(SimpleClassFileNotFoundError):
            show_available_columns("arquivo_inexistente.csv")
    
    def test_show_available_columns_empty_file(self):
        """Teste com arquivo vazio"""
        # Criar arquivo CSV vazio
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write("col1,col2\n")  # Apenas cabeçalho
            temp_file = f.name
        
        try:
            columns = show_available_columns(temp_file)
            assert columns == ["col1", "col2"]
        finally:
            os.unlink(temp_file)


class TestValidateDataFormat:
    """Testes para validate_data_format"""
    
    def test_validate_data_format_success(self, temp_csv_file):
        """Teste de validação bem-sucedida"""
        result = validate_data_format(temp_csv_file)
        
        assert result["is_valid"] is True
        assert len(result["errors"]) == 0
        assert result["file_info"]["total_rows"] == 5
        assert result["file_info"]["total_columns"] == 3
    
    def test_validate_data_format_with_required_columns(self, temp_csv_file):
        """Teste com colunas obrigatórias"""
        result = validate_data_format(temp_csv_file, required_columns=["RELATO", "Teor"])
        
        assert result["is_valid"] is True
        assert len(result["errors"]) == 0
    
    def test_validate_data_format_missing_required_columns(self, temp_csv_file):
        """Teste com colunas obrigatórias ausentes"""
        result = validate_data_format(temp_csv_file, required_columns=["COLUNA_INEXISTENTE"])
        
        assert result["is_valid"] is False
        assert len(result["errors"]) > 0
        assert "Colunas obrigatórias não encontradas" in result["errors"][0]
    
    def test_validate_data_format_empty_file(self):
        """Teste com arquivo vazio"""
        # Criar arquivo CSV vazio
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write("col1,col2\n")  # Apenas cabeçalho, sem dados
            temp_file = f.name
        
        try:
            result = validate_data_format(temp_file)
            assert result["is_valid"] is False
            assert "Arquivo está vazio" in result["errors"][0]
        finally:
            os.unlink(temp_file)
    
    def test_validate_data_format_with_nulls(self):
        """Teste com valores nulos"""
        # Criar arquivo com valores nulos
        data = pd.DataFrame({
            "col1": ["a", None, "c"],
            "col2": [1, 2, None]
        })
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            data.to_csv(f.name, index=False)
            temp_file = f.name
        
        try:
            result = validate_data_format(temp_file)
            assert result["is_valid"] is True  # Warnings, não errors
            assert len(result["warnings"]) > 0
            assert "valores nulos" in result["warnings"][0]
        finally:
            os.unlink(temp_file)


class TestFilterValidTexts:
    """Testes para filter_valid_texts"""
    
    def test_filter_valid_texts_success(self, temp_directories):
        """Teste de filtragem bem-sucedida"""
        # Criar dados com textos válidos e inválidos
        data = pd.DataFrame({
            "texto": ["Texto válido", "", None, "   ", "Outro texto válido"],
            "id": [1, 2, 3, 4, 5]
        })
        
        input_file = temp_directories["input"] / "test_input.csv"
        output_file = temp_directories["output"] / "test_filtered.csv"
        
        data.to_csv(input_file, index=False)
        
        result = filter_valid_texts(
            file_path=str(input_file),
            column_name="texto",
            output_file=str(output_file)
        )
        
        assert result["original_count"] == 5
        assert result["filtered_count"] == 2  # Apenas 2 textos válidos
        assert result["removed_count"] == 3
        assert result["removal_percentage"] == 60.0
        assert os.path.exists(output_file)
        
        # Verificar arquivo filtrado
        df_filtered = pd.read_csv(output_file)
        assert len(df_filtered) == 2
        assert all(df_filtered["texto"].notna())
        assert all(df_filtered["texto"].str.strip() != "")
    
    def test_filter_valid_texts_column_not_found(self, temp_csv_file):
        """Teste com coluna não encontrada"""
        with pytest.raises(ValidationError, match="Coluna 'COLUNA_INEXISTENTE' não encontrada"):
            filter_valid_texts(
                file_path=temp_csv_file,
                column_name="COLUNA_INEXISTENTE"
            )
    
    def test_filter_valid_texts_no_output_file(self, temp_directories):
        """Teste sem arquivo de saída"""
        data = pd.DataFrame({
            "texto": ["Texto válido", "", "Outro texto"],
            "id": [1, 2, 3]
        })
        
        input_file = temp_directories["input"] / "test_input.csv"
        data.to_csv(input_file, index=False)
        
        result = filter_valid_texts(
            file_path=str(input_file),
            column_name="texto"
        )
        
        assert result["filtered_count"] == 2
        assert result["output_file"] is None


class TestAnonymizeData:
    """Testes para anonymize_data"""
    
    @pytest.mark.skipif(True, reason="Presidio não disponível em ambiente de teste")
    def test_anonymize_data_success(self, temp_directories):
        """Teste de anonimização bem-sucedida"""
        # Criar dados com informações pessoais
        data = pd.DataFrame({
            "texto": [
                "João Silva tem problema de saúde",
                "Maria Santos precisa de consulta",
                "Paciente sem informações pessoais"
            ],
            "id": [1, 2, 3]
        })
        
        input_file = temp_directories["input"] / "test_input.csv"
        output_file = temp_directories["output"] / "test_anonymized.csv"
        
        data.to_csv(input_file, index=False)
        
        with patch('presidio_analyzer.AnalyzerEngine') as mock_analyzer_class:
            with patch('presidio_anonymizer.AnonymizerEngine') as mock_anonymizer_class:
                # Configurar mocks
                mock_analyzer = Mock()
                mock_anonymizer = Mock()
                mock_analyzer_class.return_value = mock_analyzer
                mock_anonymizer_class.return_value = mock_anonymizer
                
                # Mock de resultados de análise
                mock_analyzer.analyze.return_value = [
                    Mock(start=0, end=10, entity_type="PERSON")
                ]
                
                # Mock de resultado de anonimização
                mock_result = Mock()
                mock_result.text = "PERSON_1 tem problema de saúde"
                mock_anonymizer.anonymize.return_value = mock_result
                
                result = anonymize_data(
                    column_name="texto",
                    input_file=str(input_file),
                    output_file=str(output_file)
                )
                
                assert result["total_rows"] == 3
                assert result["anonymized_rows"] >= 0
                assert os.path.exists(output_file)
    
    def test_anonymize_data_presidio_not_installed(self, temp_csv_file):
        """Teste sem Presidio instalado"""
        with patch('src.tools.data_tools.ValidationError') as mock_error:
            with patch('builtins.__import__', side_effect=ImportError):
                with pytest.raises(Exception):  # Pode ser ValidationError ou ImportError
                    anonymize_data(
                        column_name="Teor",
                        input_file=temp_csv_file
                    )
    
    def test_anonymize_data_column_not_found(self, temp_csv_file):
        """Teste com coluna não encontrada"""
        with pytest.raises(ValidationError, match="Coluna 'COLUNA_INEXISTENTE' não encontrada"):
            anonymize_data(
                column_name="COLUNA_INEXISTENTE",
                input_file=temp_csv_file
            )
