"""
Testes para classification_tools
"""

import pytest
import pandas as pd
import tempfile
import os
from unittest.mock import patch, Mock

from src.tools.classification_tools import classify_text_individual, classify_multilabel
from src.core.exceptions import ValidationError, ModelError


class TestClassifyTextIndividual:
    """Testes para classify_text_individual"""
    
    def test_classify_text_individual_success(self, temp_csv_file, mock_config, mock_together_client, temp_directories):
        """Teste de classificação individual bem-sucedida"""
        output_file = temp_directories["output"] / "test_output.csv"
        
        result = classify_text_individual(
            column_name="Teor",
            input_file=temp_csv_file,
            output_file=str(output_file)
        )
        
        # Verificar resultado
        assert result["total_processed"] == 5
        assert result["positive_cases"] == 5  # Mock sempre retorna "SIM"
        assert result["percentage"] == 100.0
        assert os.path.exists(output_file)
        
        # Verificar arquivo de saída
        df_result = pd.read_csv(output_file)
        assert "REFERE_ASSUNTO" in df_result.columns
        assert "TEXTO_ANALISADO" in df_result.columns
        assert all(df_result["REFERE_ASSUNTO"] == 1)
    
    def test_classify_text_individual_file_not_found(self, mock_config):
        """Teste com arquivo não encontrado"""
        with pytest.raises(ValidationError, match="Arquivo de entrada não encontrado"):
            classify_text_individual(
                column_name="Teor",
                input_file="arquivo_inexistente.csv"
            )
    
    def test_classify_text_individual_column_not_found(self, temp_csv_file, mock_config):
        """Teste com coluna não encontrada"""
        with pytest.raises(ValidationError, match="Coluna 'COLUNA_INEXISTENTE' não encontrada"):
            classify_text_individual(
                column_name="COLUNA_INEXISTENTE",
                input_file=temp_csv_file
            )
    
    def test_classify_text_individual_no_api_key(self, temp_csv_file):
        """Teste sem API key"""
        with patch('src.core.config.get_config') as mock_get_config:
            mock_config = Mock()
            mock_config.get_env.return_value = None  # Sem API key
            mock_get_config.return_value = mock_config
            
            with pytest.raises(ModelError, match="TOGETHER_API_KEY não encontrada"):
                classify_text_individual(
                    column_name="Teor",
                    input_file=temp_csv_file
                )


class TestClassifyMultilabel:
    """Testes para classify_multilabel"""
    
    def test_classify_multilabel_success(self, temp_csv_file, mock_config, mock_together_client, temp_directories):
        """Teste de classificação multilabel bem-sucedida"""
        # Configurar mock para retornar números de subtemas
        mock_together_client.completions.create.return_value.choices[0].text = "1,2"
        
        output_file = temp_directories["output"] / "test_multilabel.csv"
        
        result = classify_multilabel(
            column_name="Teor",
            area="SAUDE",
            input_file=temp_csv_file,
            output_file=str(output_file)
        )
        
        # Verificar resultado
        assert result["total_processed"] == 5
        assert result["area"] == "SAUDE"
        assert os.path.exists(output_file)
        
        # Verificar arquivo de saída
        df_result = pd.read_csv(output_file)
        assert "SUBTEMAS_IDENTIFICADOS" in df_result.columns
        assert "AREA_CLASSIFICACAO" in df_result.columns
    
    def test_classify_multilabel_no_subtemas(self, temp_csv_file):
        """Teste com área sem subtemas configurados"""
        with patch('src.core.config.get_config') as mock_get_config:
            mock_config = Mock()
            mock_config.get_env.return_value = "test-key"
            mock_config.get.return_value = []  # Sem subtemas
            mock_get_config.return_value = mock_config
            
            with pytest.raises(ValidationError, match="Nenhum subtema encontrado"):
                classify_multilabel(
                    column_name="Teor",
                    area="AREA_INEXISTENTE",
                    input_file=temp_csv_file
                )
    
    def test_classify_multilabel_response_nenhum(self, temp_csv_file, mock_config, mock_together_client, temp_directories):
        """Teste com resposta 'NENHUM'"""
        # Configurar mock para retornar "NENHUM"
        mock_together_client.completions.create.return_value.choices[0].text = "NENHUM"
        
        output_file = temp_directories["output"] / "test_nenhum.csv"
        
        result = classify_multilabel(
            column_name="Teor",
            area="SAUDE",
            input_file=temp_csv_file,
            output_file=str(output_file)
        )
        
        # Verificar que não há subtemas identificados
        df_result = pd.read_csv(output_file)
        assert all(df_result["SUBTEMAS_IDENTIFICADOS"] == "")
    
    @pytest.mark.parametrize("response,expected_subtemas", [
        ("1", ["Oftalmologia"]),
        ("1,2", ["Oftalmologia", "Regulação em Saúde"]),
        ("2,1", ["Oftalmologia", "Regulação em Saúde"]),  # Ordem diferente
        ("1,2,3", ["Oftalmologia", "Regulação em Saúde", "Diagnose"]),
        ("NENHUM", []),
        ("", []),
        ("abc", []),  # Resposta inválida
    ])
    def test_classify_multilabel_response_parsing(self, temp_csv_file, mock_config, mock_together_client, temp_directories, response, expected_subtemas):
        """Teste de parsing de diferentes respostas"""
        mock_together_client.completions.create.return_value.choices[0].text = response
        
        output_file = temp_directories["output"] / "test_parsing.csv"
        
        # Configurar subtemas no mock
        mock_config.get.return_value = ["Oftalmologia", "Regulação em Saúde", "Diagnose"]
        
        result = classify_multilabel(
            column_name="Teor",
            area="SAUDE",
            input_file=temp_csv_file,
            output_file=str(output_file)
        )
        
        # Verificar resultado
        df_result = pd.read_csv(output_file)
        if expected_subtemas:
            expected_str = ", ".join(expected_subtemas)
            assert all(df_result["SUBTEMAS_IDENTIFICADOS"] == expected_str)
        else:
            assert all(df_result["SUBTEMAS_IDENTIFICADOS"] == "")


class TestClassificationHelpers:
    """Testes para funções auxiliares de classificação"""
    
    def test_load_subtema_definitions_success(self):
        """Teste de carregamento de definições de subtemas"""
        from src.tools.classification_tools import _load_subtema_definitions
        
        # Criar arquivo temporário de definições
        definitions_content = """
SAUDE:
  Oftalmologia:
    definicao: "Problemas relacionados à visão e olhos"
  Regulacao:
    definicao: "Problemas de regulação e filas"
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
            f.write(definitions_content)
            temp_file = f.name
        
        try:
            definitions = _load_subtema_definitions("SAUDE", temp_file)
            assert "Oftalmologia" in definitions
            assert "Problemas relacionados à visão e olhos" in definitions["Oftalmologia"]
        finally:
            os.unlink(temp_file)
    
    def test_load_subtema_definitions_file_not_found(self):
        """Teste com arquivo de definições não encontrado"""
        from src.tools.classification_tools import _load_subtema_definitions
        
        definitions = _load_subtema_definitions("SAUDE", "arquivo_inexistente.yml")
        assert definitions == {}
    
    def test_classify_single_text_success(self, mock_together_client):
        """Teste de classificação de texto único"""
        from src.tools.classification_tools import _classify_single_text
        
        result = _classify_single_text(
            text="Problema de visão",
            subject="Oftalmologia",
            model_name="test-model",
            api_key="test-key"
        )
        
        assert result == 1  # Mock retorna "SIM"
    
    def test_classify_single_text_negative(self, mock_together_client):
        """Teste de classificação negativa"""
        from src.tools.classification_tools import _classify_single_text
        
        # Configurar mock para retornar "NAO"
        mock_together_client.completions.create.return_value.choices[0].text = "NAO"
        
        result = _classify_single_text(
            text="Problema não relacionado",
            subject="Oftalmologia",
            model_name="test-model",
            api_key="test-key"
        )
        
        assert result == 0
    
    def test_classify_single_text_ambiguous(self, mock_together_client):
        """Teste com resposta ambígua"""
        from src.tools.classification_tools import _classify_single_text
        
        # Configurar mock para retornar resposta ambígua
        mock_together_client.completions.create.return_value.choices[0].text = "TALVEZ"
        
        result = _classify_single_text(
            text="Texto ambíguo",
            subject="Oftalmologia",
            model_name="test-model",
            api_key="test-key"
        )
        
        assert result == 0  # Resposta ambígua assume 0
