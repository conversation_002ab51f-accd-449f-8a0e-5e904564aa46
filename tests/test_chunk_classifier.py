"""
Unit tests for ChunkClassifier component.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from src.api.models.batch_processing import TextChunk, ChunkResult
from src.api.services.chunk_classifier import ChunkClassifier, ChunkClassificationConfig


@pytest.fixture
def sample_chunk():
    """Create a sample text chunk for testing."""
    return TextChunk(
        text="O aluno sofreu bullying na escola. A infraestrutura da escola está deteriorada.",
        chunk_id=1,
        page_start=1,
        page_end=1,
        char_start=0,
        char_end=80,
        pdf_path="/test/document.pdf",
        token_count=20
    )


@pytest.fixture
def sample_chunks():
    """Create multiple sample chunks for batch testing."""
    return [
        TextChunk(
            text="Problema com merenda escolar. Alimentos estragados foram servidos.",
            chunk_id=1,
            page_start=1,
            page_end=1,
            char_start=0,
            char_end=65,
            pdf_path="/test/doc1.pdf"
        ),
        TextChunk(
            text="Transporte escolar atrasado. Crianças chegam tarde na escola.",
            chunk_id=2,
            page_start=2,
            page_end=2,
            char_start=66,
            char_end=125,
            pdf_path="/test/doc1.pdf"
        )
    ]


@pytest.fixture
def mock_classification_service():
    """Create a mock classification service."""
    service = Mock()
    service.get_subtemas_for_area.return_value = [
        "BULLYING",
        "INFRAESTRUTURA", 
        "ALIMENTACAO_ESCOLAR",
        "TRANSPORTE"
    ]
    return service


@pytest.fixture
def chunk_classifier(mock_classification_service):
    """Create a ChunkClassifier instance with mocked dependencies."""
    with patch('src.api.services.chunk_classifier.carregar_configuracao') as mock_config, \
         patch('src.api.services.chunk_classifier.carregar_variaveis_ambiente') as mock_env:
        
        mock_config.return_value = {'LLM_MODEL': 'test-model'}
        mock_env.return_value = {'FIREWORKS_API_KEY': 'test-key'}
        
        classifier = ChunkClassifier(mock_classification_service)
        return classifier


class TestChunkClassifier:
    """Test cases for ChunkClassifier."""
    
    def test_init_with_service(self, mock_classification_service):
        """Test initialization with provided classification service."""
        with patch('src.api.services.chunk_classifier.carregar_configuracao') as mock_config, \
             patch('src.api.services.chunk_classifier.carregar_variaveis_ambiente') as mock_env:
            
            mock_config.return_value = {'LLM_MODEL': 'test-model'}
            mock_env.return_value = {'FIREWORKS_API_KEY': 'test-key'}
            
            classifier = ChunkClassifier(mock_classification_service)
            
            assert classifier.classification_service == mock_classification_service
            assert classifier.default_config.model_name == 'test-model'
            assert classifier.default_config.api_key == 'test-key'
    
    def test_init_without_service(self):
        """Test initialization without provided classification service."""
        with patch('src.api.services.chunk_classifier.ClassificationService') as mock_service_class, \
             patch('src.api.services.chunk_classifier.carregar_configuracao') as mock_config, \
             patch('src.api.services.chunk_classifier.carregar_variaveis_ambiente') as mock_env:
            
            mock_config.return_value = {'LLM_MODEL': 'test-model'}
            mock_env.return_value = {'FIREWORKS_API_KEY': 'test-key'}
            
            classifier = ChunkClassifier()
            
            mock_service_class.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_classify_single_chunk_success(self, chunk_classifier, sample_chunk):
        """Test successful classification of a single chunk."""
        with patch('src.api.services.chunk_classifier.classificar_relato_multilabel') as mock_classify:
            mock_classify.return_value = ["BULLYING", "INFRAESTRUTURA"]
            
            result = await chunk_classifier.classify_single_chunk(sample_chunk, "EDUCACAO")
            
            assert isinstance(result, ChunkResult)
            assert result.chunk == sample_chunk
            assert result.subtemas == ["BULLYING", "INFRAESTRUTURA"]
            assert len(result.confidence_scores) == 2
            assert result.error is None
            assert result.processing_time > 0
            
            # Check confidence scores
            assert "BULLYING" in result.confidence_scores
            assert "INFRAESTRUTURA" in result.confidence_scores
            assert 0.0 <= result.confidence_scores["BULLYING"] <= 1.0
    
    @pytest.mark.asyncio
    async def test_classify_single_chunk_with_max_subtemas(self, chunk_classifier, sample_chunk):
        """Test classification with max_subtemas limit."""
        config = ChunkClassificationConfig(max_subtemas=1)
        
        with patch('src.api.services.chunk_classifier.classificar_relato_multilabel') as mock_classify:
            mock_classify.return_value = ["BULLYING", "INFRAESTRUTURA", "TRANSPORTE"]
            
            result = await chunk_classifier.classify_single_chunk(sample_chunk, "EDUCACAO", config)
            
            assert len(result.subtemas) == 1
            assert result.subtemas == ["BULLYING"]
    
    @pytest.mark.asyncio
    async def test_classify_single_chunk_error(self, chunk_classifier, sample_chunk):
        """Test error handling in single chunk classification."""
        with patch('src.api.services.chunk_classifier.classificar_relato_multilabel') as mock_classify:
            mock_classify.side_effect = Exception("API Error")
            
            result = await chunk_classifier.classify_single_chunk(sample_chunk, "EDUCACAO")
            
            assert result.error == "API Error"
            assert result.subtemas == []
            assert result.confidence_scores == {}
            assert result.processing_time > 0
    
    @pytest.mark.asyncio
    async def test_classify_chunks_success(self, chunk_classifier, sample_chunks):
        """Test successful batch classification of chunks."""
        with patch('src.api.services.chunk_classifier.classificar_relato_multilabel') as mock_classify:
            mock_classify.side_effect = [
                ["ALIMENTACAO_ESCOLAR"],
                ["TRANSPORTE"]
            ]
            
            results = await chunk_classifier.classify_chunks(sample_chunks, "EDUCACAO")
            
            assert len(results) == 2
            assert all(isinstance(r, ChunkResult) for r in results)
            assert results[0].subtemas == ["ALIMENTACAO_ESCOLAR"]
            assert results[1].subtemas == ["TRANSPORTE"]
    
    @pytest.mark.asyncio
    async def test_classify_chunks_empty_list(self, chunk_classifier):
        """Test classification with empty chunk list."""
        results = await chunk_classifier.classify_chunks([], "EDUCACAO")
        assert results == []
    
    @pytest.mark.asyncio
    async def test_classify_chunks_with_errors(self, chunk_classifier, sample_chunks):
        """Test batch classification with some chunks failing."""
        with patch('src.api.services.chunk_classifier.classificar_relato_multilabel') as mock_classify:
            mock_classify.side_effect = [
                ["ALIMENTACAO_ESCOLAR"],
                Exception("Network error")
            ]
            
            results = await chunk_classifier.classify_chunks(sample_chunks, "EDUCACAO")
            
            assert len(results) == 2
            assert results[0].error is None
            assert results[1].error == "Network error"
    
    def test_generate_confidence_scores(self, chunk_classifier):
        """Test confidence score generation."""
        subtemas = ["BULLYING", "INFRAESTRUTURA", "TRANSPORTE"]
        scores = chunk_classifier._generate_confidence_scores(subtemas, 0.8)
        
        assert len(scores) == 3
        assert scores["BULLYING"] == 0.8  # First gets highest
        assert scores["INFRAESTRUTURA"] == 0.7  # Second gets lower
        assert scores["TRANSPORTE"] == 0.6  # Third gets even lower
        
        # Test minimum threshold
        subtemas_long = ["A", "B", "C", "D", "E", "F"]
        scores_long = chunk_classifier._generate_confidence_scores(subtemas_long, 0.5)
        assert all(score >= 0.3 for score in scores_long.values())  # Minimum threshold
    
    def test_extract_evidence_texts(self, chunk_classifier, sample_chunk):
        """Test evidence text extraction."""
        subtemas = ["BULLYING", "INFRAESTRUTURA"]
        
        evidence = chunk_classifier._extract_evidence_texts(sample_chunk, subtemas, 200)
        
        assert isinstance(evidence, dict)
        assert len(evidence) <= len(subtemas)
        
        # Check that evidence texts are within length limit
        for text in evidence.values():
            assert len(text) <= 200
    
    def test_find_evidence_for_subtema(self, chunk_classifier):
        """Test finding evidence for specific subtema."""
        text = "o aluno sofreu bullying na escola. a infraestrutura está ruim."
        subtema_info = {
            'palavras_chave': ['bullying', 'agressão'],
            'definicao': 'Casos de bullying escolar'
        }
        
        evidence = chunk_classifier._find_evidence_for_subtema(
            text, "BULLYING", subtema_info, 200
        )
        
        assert "bullying" in evidence.lower()
        assert len(evidence) <= 200
    
    def test_find_evidence_no_keywords(self, chunk_classifier):
        """Test evidence extraction when no keywords match."""
        text = "problema na escola com diversos aspectos."
        subtema_info = {'palavras_chave': ['inexistente']}
        
        evidence = chunk_classifier._find_evidence_for_subtema(
            text, "PROBLEMA", subtema_info, 200
        )
        
        # Should return some text even without keyword matches
        assert len(evidence) > 0
    
    def test_truncate_evidence(self, chunk_classifier):
        """Test evidence text truncation."""
        long_text = "Este é um texto muito longo que precisa ser truncado " * 10
        
        truncated = chunk_classifier._truncate_evidence(long_text, 100)
        
        assert len(truncated) <= 103  # 100 + "..."
        assert truncated.endswith("...")
        
        # Test short text (no truncation needed)
        short_text = "Texto curto"
        not_truncated = chunk_classifier._truncate_evidence(short_text, 100)
        assert not_truncated == short_text
    
    def test_get_supported_areas(self, chunk_classifier):
        """Test getting supported classification areas."""
        areas = chunk_classifier.get_supported_areas()
        
        assert isinstance(areas, list)
        assert "EDUCACAO" in areas
        assert "SAUDE" in areas
        assert "MEIO_AMBIENTE" in areas
    
    def test_validate_area(self, chunk_classifier):
        """Test area validation."""
        assert chunk_classifier.validate_area("EDUCACAO") is True
        assert chunk_classifier.validate_area("SAUDE") is True
        assert chunk_classifier.validate_area("INVALID") is False


class TestChunkClassificationConfig:
    """Test cases for ChunkClassificationConfig."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = ChunkClassificationConfig()
        
        assert config.max_subtemas == 3
        assert config.confidence_threshold == 0.3
        assert config.evidence_max_length == 200
        assert config.model_name is None
        assert config.api_key is None
    
    def test_custom_config(self):
        """Test custom configuration values."""
        config = ChunkClassificationConfig(
            max_subtemas=5,
            confidence_threshold=0.5,
            evidence_max_length=150,
            model_name="custom-model",
            api_key="custom-key"
        )
        
        assert config.max_subtemas == 5
        assert config.confidence_threshold == 0.5
        assert config.evidence_max_length == 150
        assert config.model_name == "custom-model"
        assert config.api_key == "custom-key"