#!/usr/bin/env python3
"""
Script de teste para o tool de anonimização de PDF.

Este script demonstra como usar o PDFAnonymizationTool para:
1. Extrair texto limpo de PDFs (removendo headers/rodapés)
2. Anonimizar o texto usando recognizers brasileiros
3. Preparar o texto para processamento por LLM
"""

import sys
import argparse
from pathlib import Path
import json

# Adicionar src ao path para imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.tools.pdf_anonymization_tool import PDFAnonymizationTool, create_pdf_anonymization_tool


def test_pdf_extraction_only(pdf_path: str):
    """
    Testa apenas a extração de texto limpo do PDF.
    
    Args:
        pdf_path: Caminho para o arquivo PDF
    """
    print("🔍 TESTANDO EXTRAÇÃO DE TEXTO LIMPO")
    print("=" * 50)
    
    tool = PDFAnonymizationTool()
    result = tool.extract_clean_text(pdf_path)
    
    if result["success"]:
        print(f"✅ Extração bem-sucedida!")
        print(f"📄 Páginas processadas: {result['pages']}")
        print(f"🗑️ Headers removidos: {len(result['headers_removed'])}")
        for i, header in enumerate(result['headers_removed'], 1):
            print(f"   {i}. {header[:100]}...")
        
        print(f"🗑️ Footers removidos: {len(result['footers_removed'])}")
        for i, footer in enumerate(result['footers_removed'], 1):
            print(f"   {i}. {footer[:100]}...")
        
        print(f"\n📝 TEXTO EXTRAÍDO (primeiros 500 caracteres):")
        print("-" * 50)
        print(result["text"][:500])
        if len(result["text"]) > 500:
            print("...")
        
        print(f"\n📊 ESTATÍSTICAS:")
        print(f"   - Total de caracteres: {len(result['text'])}")
        print(f"   - Total de palavras: {len(result['text'].split())}")
        print(f"   - Total de linhas: {len(result['text'].split(chr(10)))}")
        
    else:
        print(f"❌ Erro na extração: {result['error']}")


def test_pdf_anonymization(pdf_path: str, save_output: bool = False):
    """
    Testa o processo completo de anonimização do PDF.
    
    Args:
        pdf_path: Caminho para o arquivo PDF
        save_output: Se deve salvar o resultado em arquivo
    """
    print("\n🛡️ TESTANDO ANONIMIZAÇÃO COMPLETA")
    print("=" * 50)
    
    tool = PDFAnonymizationTool()
    result = tool.anonymize_pdf(pdf_path)
    
    if result["success"]:
        print(f"✅ Anonimização bem-sucedida!")
        
        # Metadados de extração
        extraction_meta = result["extraction_metadata"]
        print(f"\n📄 EXTRAÇÃO:")
        print(f"   - Páginas: {extraction_meta['pages']}")
        print(f"   - Headers removidos: {len(extraction_meta['headers_removed'])}")
        print(f"   - Footers removidos: {len(extraction_meta['footers_removed'])}")
        
        # Metadados de anonimização
        anon_meta = result["anonymization_metadata"]
        print(f"\n🔒 ANONIMIZAÇÃO:")
        print(f"   - Entidades encontradas: {anon_meta['entities_count']}")
        
        if anon_meta["entities_found"]:
            print(f"   - Tipos de entidades:")
            entity_types = {}
            for entity in anon_meta["entities_found"]:
                entity_type = entity["entity_type"]
                entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            
            for entity_type, count in entity_types.items():
                print(f"     * {entity_type}: {count}")
        
        print(f"\n📝 TEXTO ANONIMIZADO (primeiros 500 caracteres):")
        print("-" * 50)
        print(result["anonymized_text"][:500])
        if len(result["anonymized_text"]) > 500:
            print("...")
        
        # Salvar resultado se solicitado
        if save_output:
            output_path = Path(pdf_path).with_suffix('.anonymized.txt')
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(result["anonymized_text"])
            print(f"\n💾 Texto anonimizado salvo em: {output_path}")
            
            # Salvar metadados
            metadata_path = Path(pdf_path).with_suffix('.metadata.json')
            metadata = {
                "extraction_metadata": extraction_meta,
                "anonymization_metadata": anon_meta
            }
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            print(f"📊 Metadados salvos em: {metadata_path}")
        
    else:
        print(f"❌ Erro na anonimização: {result['error']}")


def test_langgraph_tool(pdf_path: str):
    """
    Testa o tool no formato LangGraph.
    
    Args:
        pdf_path: Caminho para o arquivo PDF
    """
    print("\n🔗 TESTANDO FORMATO LANGGRAPH")
    print("=" * 50)
    
    # Criar tool no formato LangGraph
    pdf_tool = create_pdf_anonymization_tool()
    
    # Usar o tool
    result = pdf_tool(pdf_path)
    
    if result["success"]:
        print(f"✅ Tool LangGraph funcionando!")
        print(f"📄 Páginas: {result['extraction_metadata']['pages']}")
        print(f"🔒 Entidades: {result['anonymization_metadata']['entities_count']}")
        print(f"📝 Caracteres no texto final: {len(result['anonymized_text'])}")
    else:
        print(f"❌ Erro no tool LangGraph: {result['error']}")


def main():
    """Função principal do script de teste."""
    parser = argparse.ArgumentParser(
        description="Teste do tool de anonimização de PDF",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos de uso:
  python scripts/test_pdf_anonymization.py documento.pdf
  python scripts/test_pdf_anonymization.py documento.pdf --save
  python scripts/test_pdf_anonymization.py documento.pdf --extract-only
        """
    )
    
    parser.add_argument(
        "pdf_path",
        help="Caminho para o arquivo PDF a ser testado"
    )
    
    parser.add_argument(
        "--extract-only",
        action="store_true",
        help="Testar apenas a extração de texto (sem anonimização)"
    )
    
    parser.add_argument(
        "--save",
        action="store_true",
        help="Salvar o resultado anonimizado em arquivo"
    )
    
    args = parser.parse_args()
    
    # Verificar se o arquivo existe
    pdf_path = Path(args.pdf_path)
    if not pdf_path.exists():
        print(f"❌ Arquivo não encontrado: {pdf_path}")
        sys.exit(1)
    
    if not pdf_path.suffix.lower() == '.pdf':
        print(f"⚠️ Aviso: O arquivo não tem extensão .pdf: {pdf_path}")
    
    print(f"🔍 Testando PDF: {pdf_path}")
    print(f"📁 Tamanho do arquivo: {pdf_path.stat().st_size / 1024:.1f} KB")
    
    try:
        if args.extract_only:
            # Testar apenas extração
            test_pdf_extraction_only(str(pdf_path))
        else:
            # Testar extração
            test_pdf_extraction_only(str(pdf_path))
            
            # Testar anonimização completa
            test_pdf_anonymization(str(pdf_path), args.save)
            
            # Testar formato LangGraph
            test_langgraph_tool(str(pdf_path))
        
        print(f"\n✅ Testes concluídos!")
        
    except KeyboardInterrupt:
        print(f"\n⏹️ Teste interrompido pelo usuário")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erro durante o teste: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
