import sys
import types
from unittest.mock import MagicMock, patch

# Stub third-party modules that may not be installed in the test environment
fake_pandas = types.ModuleType("pandas")
fake_yaml = types.ModuleType("yaml")
fake_dotenv = types.ModuleType("dotenv")
fake_dotenv.load_dotenv = lambda *args, **kwargs: None
sys.modules.setdefault("pandas", fake_pandas)
sys.modules.setdefault("yaml", fake_yaml)
sys.modules.setdefault("dotenv", fake_dotenv)

# Create dummy together module if not installed
fake_together = types.ModuleType("together")
fake_error = types.ModuleType("together.error")
fake_error.AuthenticationError = Exception
fake_error.TogetherException = Exception
fake_together.Together = MagicMock()
fake_together.error = fake_error
sys.modules.setdefault('together', fake_together)
sys.modules.setdefault('together.error', fake_error)

import importlib
from src.analisadores import analisador_lote
importlib.reload(analisador_lote)


def make_response(text: str):
    resp = MagicMock()
    resp.choices = [MagicMock(text=text)]
    return resp


def test_processar_lote_returns_same_length():
    relatos = ["r1", "r2", "r3"]
    respostas = [
        make_response("1: SIM\n2: NAO"),
        make_response("3: SIM")
    ]
    mock_client = MagicMock()
    mock_client.completions.create.side_effect = respostas

    with patch.object(analisador_lote.together, "Together", return_value=mock_client):
        resultado = analisador_lote.processar_lote(relatos, "ASSUNTO", "modelo", tamanho_lote=2)

    assert isinstance(resultado, list)
    assert len(resultado) == len(relatos)
