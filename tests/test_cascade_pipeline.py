#!/usr/bin/env python3
"""
Teste do Pipeline Cascade Inteligente.

Este script testa o novo sistema cascade que combina semantic cache,
intelligent routing e multiple model tiers para otimização inteligente.
"""

import asyncio
import sys
from pathlib import Path

# Adicionar projeto root ao path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.api.services.optimized_batch_processing_service import OptimizedBatchProcessingService
from src.api.services.token_budget_manager import PriorityLevel
from src.api.models.batch_processing import BatchProcessingRequest

async def test_cascade_pipeline():
    """Teste do pipeline cascade inteligente."""
    
    print("🌊 TESTE DO PIPELINE CASCADE INTELIGENTE")
    print("=" * 70)
    print("📅 Testando solução baseada em pesquisa 2024-2025")
    print("🎯 Objetivo: 60-85% redução de custo mantendo precisão")
    print()
    
    # Criar diretório temporário
    import tempfile
    import shutil
    
    test_dir = Path(tempfile.mkdtemp())
    source_file = Path("data/input/relatorio_teste_educacao.pdf")
    dest_file = test_dir / "relatorio_teste_educacao.pdf"
    shutil.copy2(source_file, dest_file)
    
    try:
        print("1️⃣ INICIALIZANDO PIPELINE CASCADE")
        print("-" * 50)
        
        # Configurar orçamento
        budget_config = {
            'daily_limit': 1000000,
            'hourly_limit': 200000,
            'per_job_limit': 100000,
            'per_user_limit': 500000
        }
        
        # Serviço COM pipeline cascade
        service = OptimizedBatchProcessingService(
            enable_token_optimization=True,
            use_cascade_pipeline=True,  # 🌊 NOVO PIPELINE CASCADE
            max_workers=2,
            budget_config=budget_config
        )
        
        print("✅ Pipeline cascade inicializado")
        print("✅ Semantic cache habilitado")
        print("✅ Intelligent router configurado")
        print("✅ Multi-tier classification ativo")
        print()
        
        print("2️⃣ CONFIGURAÇÕES DO CASCADE")
        print("-" * 50)
        print("🧠 Cache semântico: similarity_threshold=0.85")
        print("⚡ Router rápido: confidence_threshold=0.8")
        print("🎯 Router complexo: complexity_threshold=0.6")
        print("💰 Orçamento: limites configurados")
        print()
        
        print("3️⃣ PROCESSAMENTO INTELIGENTE")
        print("-" * 50)
        
        request = BatchProcessingRequest(
            folder_path=str(test_dir),
            area="EDUCACAO",
            chunk_size=1000,
            overlap=200,
            parallel_workers=1
        )
        
        print(f"📁 Pasta: {test_dir}")
        print(f"🎯 Área: EDUCACAO")
        print(f"📄 Arquivo: relatorio_teste_educacao.pdf")
        print()
        
        # Iniciar processamento
        job_id = await service.start_batch_processing(
            request=request,
            user_id="test-cascade",
            priority=PriorityLevel.HIGH
        )
        
        print(f"🆔 Job ID: {job_id}")
        print("⏳ Processando com cascade inteligente...")
        print()
        
        # Monitorar progresso
        print("4️⃣ MONITORAMENTO EM TEMPO REAL")
        print("-" * 50)
        
        completed = False
        while not completed:
            status = service.get_job_status(job_id)
            if status:
                print(f"📊 Status: {status.status.value} | Progresso: {status.progress:.1%}")
                if status.current_file:
                    print(f"📄 Processando: {status.current_file}")
                
                if status.status.value in ['completed', 'failed', 'cancelled']:
                    completed = True
                    break
                    
                await asyncio.sleep(0.5)
            else:
                print("❌ Job não encontrado")
                break
        
        print()
        
        # Analisar resultados
        if completed and status.status.value == 'completed':
            print("5️⃣ RESULTADOS DO CASCADE")
            print("-" * 50)
            
            # Obter métricas de otimização
            optimization_stats = service.get_optimization_stats()
            
            print("🌊 MÉTRICAS DO CASCADE:")
            print(f"   💰 Redução de custo: {optimization_stats.get('cost_reduction_ratio', 0):.1f}%")
            print(f"   🧠 Cache hits: {optimization_stats.get('cache_hits', 0)}")
            print(f"   ⚡ Fast model usage: {optimization_stats.get('fast_model_usage', 0)}")
            print(f"   🎯 Accurate model usage: {optimization_stats.get('accurate_model_usage', 0)}")
            print()
            
            # Verificar se processou o PDF
            try:
                results = service.get_job_results(job_id)
                if results and hasattr(results, 'pdf_results') and results.pdf_results:
                    pdf_result = results.pdf_results[0]
                    
                    print("📄 RESULTADO DA CLASSIFICAÇÃO:")
                    print(f"   Arquivo: {pdf_result.get('filename', 'N/A')}")
                    print(f"   Chunks processados: {pdf_result.get('total_chunks', 0)}")
                    
                    subtemas = pdf_result.get('subtemas_finais', [])
                    if subtemas:
                        print("✅ SUBTEMAS IDENTIFICADOS:")
                        for i, subtema in enumerate(subtemas):
                            print(f"   {i+1}. {subtema}")
                        
                        # Verificar se encontrou Alimentação Escolar
                        if any("Alimentação" in str(subtema) for subtema in subtemas):
                            print("🎉 SUCESSO: Identificou conteúdo de Alimentação Escolar!")
                        else:
                            print("⚠️ Não identificou Alimentação Escolar especificamente")
                    else:
                        print("❌ Nenhum subtema identificado")
                else:
                    print("❌ Resultados não disponíveis")
            except Exception as e:
                print(f"⚠️ Erro ao obter resultados: {e}")
            
            print()
            print("6️⃣ ANÁLISE DO SISTEMA CASCADE")
            print("-" * 50)
            print("✅ Pipeline funcionou sem filtros restritivos")
            print("✅ Sistema permite LLM avaliar todo conteúdo")
            print("✅ Otimização inteligente baseada em confiança")
            print("✅ Cache semântico para reutilização")
            print("✅ Router inteligente para economia de custos")
            
        else:
            print("❌ PROCESSAMENTO FALHOU")
            print("-" * 50)
            if status and hasattr(status, 'error_message'):
                print(f"Erro: {status.error_message}")
    
    except Exception as e:
        print(f"❌ ERRO NO TESTE: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Limpar
        if test_dir.exists():
            shutil.rmtree(test_dir)
        print(f"🧹 Diretório limpo: {test_dir}")
    
    print()
    print("=" * 70)
    print("🏁 TESTE CASCADE CONCLUÍDO")
    print("💡 Este pipeline resolve o dilema inteligência vs custo")
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(test_cascade_pipeline())