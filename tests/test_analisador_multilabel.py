#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Testes unitários para o analisador multilabel.
"""

import pytest
import pandas as pd
import tempfile
import os
from unittest.mock import patch, MagicMock
import sys
from pathlib import Path

# Adicionar o diretório raiz ao PYTHONPATH
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.analisadores.analisador_multilabel import (
    carregar_configuracao_yaml,
    obter_subtemas_por_area,
    classificar_relato_multilabel
)


class TestAnalisadorMultilabel:
    """Testes para o analisador multilabel."""

    def test_obter_subtemas_por_area_saude(self):
        """Testa obtenção de subtemas da área de saúde."""
        config = {
            'SUBTEMAS_SAUDE': [
                'Oncologia',
                'Oftalmologia',
                'Reabilitação'
            ]
        }
        
        subtemas = obter_subtemas_por_area(config, 'SAUDE')
        
        assert len(subtemas) == 3
        assert 'Oncologia' in subtemas
        assert 'Oftalmologia' in subtemas
        assert 'Reabilitação' in subtemas

    def test_obter_subtemas_por_area_educacao(self):
        """Testa obtenção de subtemas da área de educação."""
        config = {
            'SUBTEMAS_EDUCACAO': [
                'Educação Infantil',
                'Ensino Fundamental',
                'Ensino Médio'
            ]
        }
        
        subtemas = obter_subtemas_por_area(config, 'EDUCACAO')
        
        assert len(subtemas) == 3
        assert 'Educação Infantil' in subtemas
        assert 'Ensino Fundamental' in subtemas

    def test_obter_subtemas_area_inexistente(self):
        """Testa obtenção de subtemas para área inexistente."""
        config = {}
        
        subtemas = obter_subtemas_por_area(config, 'AREA_INEXISTENTE')
        
        assert subtemas == []

    @patch('src.analisadores.analisador_multilabel.together.Together')
    def test_classificar_relato_multilabel_sucesso(self, mock_together):
        """Testa classificação multilabel com sucesso."""
        # Mock da resposta da API
        mock_response = MagicMock()
        mock_response.choices[0].text.strip.return_value = "1,3"
        
        mock_client = MagicMock()
        mock_client.completions.create.return_value = mock_response
        mock_together.return_value = mock_client
        
        subtemas = ['Oncologia', 'Oftalmologia', 'Reabilitação', 'Auditoria']
        relato = "Paciente com problema de visão aguarda consulta oftalmológica"
        
        resultado = classificar_relato_multilabel(
            relato, subtemas, "Saúde", "test-model", "test-key"
        )
        
        assert len(resultado) == 2
        assert 'Oncologia' in resultado
        assert 'Reabilitação' in resultado

    @patch('src.analisadores.analisador_multilabel.together.Together')
    def test_classificar_relato_multilabel_nenhum(self, mock_together):
        """Testa classificação quando nenhum subtema é relevante."""
        # Mock da resposta da API
        mock_response = MagicMock()
        mock_response.choices[0].text.strip.return_value = "NENHUM"
        
        mock_client = MagicMock()
        mock_client.completions.create.return_value = mock_response
        mock_together.return_value = mock_client
        
        subtemas = ['Oncologia', 'Oftalmologia', 'Reabilitação']
        relato = "Texto sem relação com saúde"
        
        resultado = classificar_relato_multilabel(
            relato, subtemas, "Saúde", "test-model", "test-key"
        )
        
        assert resultado == []

    @patch('src.analisadores.analisador_multilabel.together.Together')
    def test_classificar_relato_multilabel_erro_api(self, mock_together):
        """Testa tratamento de erro na API."""
        mock_together.side_effect = Exception("Erro na API")
        
        subtemas = ['Oncologia', 'Oftalmologia']
        relato = "Teste"
        
        resultado = classificar_relato_multilabel(
            relato, subtemas, "Saúde", "test-model", "test-key"
        )
        
        assert resultado == []

    @patch('src.analisadores.analisador_multilabel.together.Together')
    def test_classificar_relato_multilabel_resposta_invalida(self, mock_together):
        """Testa tratamento de resposta inválida."""
        # Mock da resposta da API com formato inválido
        mock_response = MagicMock()
        mock_response.choices[0].text.strip.return_value = "resposta_invalida"
        
        mock_client = MagicMock()
        mock_client.completions.create.return_value = mock_response
        mock_together.return_value = mock_client
        
        subtemas = ['Oncologia', 'Oftalmologia']
        relato = "Teste"
        
        resultado = classificar_relato_multilabel(
            relato, subtemas, "Saúde", "test-model", "test-key"
        )
        
        assert resultado == []

    @patch('src.analisadores.analisador_multilabel.together.Together')
    def test_classificar_relato_multilabel_limite_tres_subtemas(self, mock_together):
        """Testa que o limite de 3 subtemas é respeitado."""
        # Mock da resposta da API com mais de 3 subtemas
        mock_response = MagicMock()
        mock_response.choices[0].text.strip.return_value = "1,2,3,4,5"
        
        mock_client = MagicMock()
        mock_client.completions.create.return_value = mock_response
        mock_together.return_value = mock_client
        
        subtemas = ['Sub1', 'Sub2', 'Sub3', 'Sub4', 'Sub5']
        relato = "Teste"
        
        resultado = classificar_relato_multilabel(
            relato, subtemas, "Saúde", "test-model", "test-key"
        )
        
        # Deve retornar apenas os primeiros 3
        assert len(resultado) == 3
        assert resultado == ['Sub1', 'Sub2', 'Sub3']

    def test_carregar_configuracao_yaml_arquivo_inexistente(self):
        """Testa carregamento de arquivo de configuração inexistente."""
        resultado = carregar_configuracao_yaml('arquivo_inexistente.yml')
        assert resultado is None

    def test_carregar_configuracao_yaml_sucesso(self):
        """Testa carregamento bem-sucedido de configuração."""
        # Criar arquivo temporário de configuração
        config_content = """
ASSUNTO: "Teste"
LLM_MODEL: "test-model"
SUBTEMAS_SAUDE:
  - "Oncologia"
  - "Oftalmologia"
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
            f.write(config_content)
            temp_path = f.name
        
        try:
            config = carregar_configuracao_yaml(temp_path)
            
            assert config is not None
            assert config['ASSUNTO'] == 'Teste'
            assert config['LLM_MODEL'] == 'test-model'
            assert len(config['SUBTEMAS_SAUDE']) == 2
        finally:
            os.unlink(temp_path)


if __name__ == "__main__":
    pytest.main([__file__]) 