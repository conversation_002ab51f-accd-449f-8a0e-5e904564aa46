#!/usr/bin/env python3
"""
Teste do Pipeline de Otimização com arquivo real.

Este script demonstra o sistema completo de otimização de tokens
usando o arquivo relatorio_teste_educacao.pdf como exemplo.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from datetime import datetime

# Adicionar projeto root ao path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.api.services.optimized_batch_processing_service import OptimizedBatchProcessingService
from src.api.services.token_budget_manager import PriorityLevel
from src.api.models.batch_processing import BatchProcessingRequest

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

async def test_optimization_pipeline():
    """Teste completo do pipeline de otimização."""
    
    print("=" * 80)
    print("🚀 TESTE DO PIPELINE DE OTIMIZAÇÃO DE TOKENS")
    print("=" * 80)
    print(f"📅 Data/Hora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Arquivo: data/input/relatorio_teste_educacao.pdf")
    print(f"🎯 Área: EDUCACAO")
    print()
    
    # Verificar se o arquivo existe
    pdf_path = Path("data/input/relatorio_teste_educacao.pdf")
    if not pdf_path.exists():
        print(f"❌ Erro: Arquivo não encontrado: {pdf_path}")
        return
    
    print(f"✅ Arquivo encontrado: {pdf_path}")
    print(f"📊 Tamanho: {pdf_path.stat().st_size:,} bytes")
    print()
    
    # Criar diretório temporário apenas com o arquivo desejado
    import tempfile
    import shutil
    
    test_dir = Path(tempfile.mkdtemp())
    
    # Copiar apenas o arquivo que queremos testar
    source_file = Path("data/input/relatorio_teste_educacao.pdf")
    dest_file = test_dir / "relatorio_teste_educacao.pdf"
    shutil.copy2(source_file, dest_file)
    
    print(f"📁 Arquivo copiado para teste: {dest_file}")
    
    try:
        # Inicializar serviço otimizado
        print("1️⃣ INICIALIZANDO SERVIÇO OTIMIZADO")
        print("-" * 50)
        
        # Configurar orçamento mais alto para o teste
        budget_config = {
            'daily_limit': 1000000,
            'hourly_limit': 200000,
            'per_job_limit': 100000,  # Aumentar limite por job
            'per_user_limit': 500000
        }
        
        service = OptimizedBatchProcessingService(
            enable_token_optimization=True,
            max_workers=2,
            budget_config=budget_config
        )
        
        # Configurar filtro muito permissivo para não bloquear o LLM
        service.update_optimization_config(
            relevance_threshold=0.1,     # Muito baixo para ser permissivo
            max_chunks_per_pdf=50,       # Permitir mais chunks
            use_keywords=False,          # Desabilitar keywords para ser menos restritivo
            enable_early_stopping=False  # Não parar cedo
        )
        
        print("✅ Serviço inicializado com otimização habilitada")
        print("✅ Filtro de relevância configurado")
        print("✅ Sumarizador semântico configurado") 
        print("✅ Classificador hierárquico configurado")
        print("✅ Gerenciador de orçamento configurado")
        print()
        
        # Obter configurações de otimização
        print("2️⃣ CONFIGURAÇÕES DE OTIMIZAÇÃO")
        print("-" * 50)
        
        stats = service.get_optimization_stats()
        print(f"🎯 Threshold de relevância: {stats['filter_config']['relevance_threshold']}")
        print(f"📝 Threshold de sumarização: {stats['summarizer_config']['similarity_threshold']}")
        print(f"💰 Limite diário de tokens: {stats['budget_config']['daily_limit']:,}")
        print(f"⏰ Limite horário de tokens: {stats['budget_config']['hourly_limit']:,}")
        print()
        
        # Criar request de processamento
        print("3️⃣ CONFIGURANDO PROCESSAMENTO")
        print("-" * 50)
        
        request = BatchProcessingRequest(
            folder_path=str(test_dir),
            area="EDUCACAO",
            chunk_size=1000,
            overlap=200,
            parallel_workers=1
        )
        
        print(f"📁 Pasta: {request.folder_path}")
        print(f"🎯 Área: {request.area}")
        print(f"📝 Tamanho do chunk: {request.chunk_size}")
        print(f"🔄 Overlap: {request.overlap}")
        print()
        
        # Iniciar processamento
        print("4️⃣ INICIANDO PROCESSAMENTO OTIMIZADO")
        print("-" * 50)
        
        start_time = time.time()
        
        job_id = await service.start_batch_processing(
            request=request,
            user_id="test-user",
            priority=PriorityLevel.HIGH
        )
        
        print(f"🆔 Job ID: {job_id}")
        print("⏳ Processando...")
        print()
        
        # Monitorar progresso
        print("5️⃣ MONITORANDO PROGRESSO")
        print("-" * 50)
        
        completed = False
        last_progress = -1
        
        while not completed:
            status = service.get_job_status(job_id)
            if status:
                if status.progress != last_progress:
                    print(f"📊 Progresso: {status.progress:.1%} | Status: {status.status.value}")
                    if status.current_file:
                        print(f"📄 Processando: {status.current_file}")
                    print(f"✅ Concluídos: {status.processed_files}/{status.total_files}")
                    last_progress = status.progress
                
                if status.status.value in ['completed', 'failed', 'cancelled']:
                    completed = True
                    break
                    
                await asyncio.sleep(0.5)
            else:
                print("❌ Job não encontrado")
                break
        
        processing_time = time.time() - start_time
        print(f"⏱️ Tempo total: {processing_time:.2f}s")
        print()
        
        # Obter resultados
        if completed and status.status.value == 'completed':
            print("6️⃣ RESULTADOS DO PROCESSAMENTO")
            print("-" * 50)
            
            results = service.get_job_results(job_id)
            if results:
                print(f"📊 Total de PDFs: {results.total_pdfs}")
                print(f"✅ Processados com sucesso: {results.successful_pdfs}")
                print(f"❌ Falhas: {len(results.failed_pdfs)}")
                print(f"⏱️ Tempo de processamento: {results.total_processing_time:.2f}s")
                
                if results.csv_output_path:
                    print(f"📄 CSV gerado: {results.csv_output_path}")
                print()
                
                # Mostrar resultados do PDF
                if results.pdf_results:
                    print("7️⃣ CLASSIFICAÇÃO DO PDF")
                    print("-" * 50)
                    
                    for i, pdf_result in enumerate(results.pdf_results):
                        if pdf_result.error:
                            print(f"❌ Erro no PDF {i+1}: {pdf_result.error}")
                        else:
                            print(f"📄 Arquivo: {pdf_result.filename}")
                            print(f"📑 Páginas: {pdf_result.total_pages}")
                            print(f"🧩 Chunks: {pdf_result.total_chunks}")
                            print(f"⏱️ Tempo: {pdf_result.processing_time:.2f}s")
                            print()
                            
                            print("🎯 SUBTEMAS IDENTIFICADOS:")
                            if pdf_result.subtemas_finais:
                                for j, subtema in enumerate(pdf_result.subtemas_finais):
                                    confidence = pdf_result.confidence_scores.get(subtema, 0)
                                    print(f"   {j+1}. {subtema} (confiança: {confidence:.2%})")
                                    
                                    # Mostrar evidência se disponível
                                    evidence = pdf_result.evidence_texts.get(subtema, "")
                                    if evidence:
                                        evidence_short = evidence[:100] + "..." if len(evidence) > 100 else evidence
                                        print(f"      💡 Evidência: {evidence_short}")
                                print()
                            else:
                                print("   ❌ Nenhum subtema identificado")
                                print()
            
            # Obter estatísticas de otimização
            print("8️⃣ MÉTRICAS DE OTIMIZAÇÃO")
            print("-" * 50)
            
            final_stats = service.get_optimization_stats()
            budget_metrics = final_stats.get('budget_metrics', {})
            
            if hasattr(budget_metrics, 'total_used'):
                print(f"💰 Tokens utilizados: {budget_metrics.total_used:,}")
                print(f"📊 Uso do orçamento: {budget_metrics.usage_percentage:.1f}%")
                print(f"💵 Custo estimado: ${budget_metrics.cost_estimate:.4f}")
            else:
                print("📊 Métricas de orçamento não disponíveis")
            
            # Tentar obter métricas detalhadas do processamento
            if hasattr(service, 'processing_metrics') and service.processing_metrics:
                print()
                print("🔍 DETALHES DA OTIMIZAÇÃO:")
                for pdf_path, metrics in service.processing_metrics.items():
                    if isinstance(metrics, dict):
                        original_tokens = metrics.get('original_tokens', 0)
                        tokens_saved = metrics.get('tokens_saved', 0)
                        final_tokens = original_tokens - tokens_saved
                        reduction_ratio = (tokens_saved / original_tokens * 100) if original_tokens > 0 else 0
                        
                        print(f"   📄 {Path(pdf_path).name}:")
                        print(f"      🔢 Tokens originais: {original_tokens:,}")
                        print(f"      ✂️ Tokens economizados: {tokens_saved:,}")
                        print(f"      💾 Tokens finais: {final_tokens:,}")
                        print(f"      📈 Redução: {reduction_ratio:.1f}%")
            print()
            
            # Obter sugestões de otimização
            suggestions = service.get_optimization_suggestions()
            if suggestions:
                print("9️⃣ SUGESTÕES DE OTIMIZAÇÃO")
                print("-" * 50)
                for i, suggestion in enumerate(suggestions[:3]):
                    print(f"   {i+1}. {suggestion.suggestion_type}:")
                    print(f"      📝 {suggestion.description}")
                    print(f"      💰 Economia potencial: {suggestion.potential_savings:,} tokens")
                    print(f"      🎯 Confiança: {suggestion.confidence:.1%}")
                    print()
            else:
                print("9️⃣ SUGESTÕES DE OTIMIZAÇÃO")
                print("-" * 50)
                print("✅ Sistema já está bem otimizado!")
                print()
        
        else:
            print("❌ PROCESSAMENTO FALHOU")
            print("-" * 50)
            if status and status.error_message:
                print(f"Erro: {status.error_message}")
            print()
    
    except Exception as e:
        print(f"❌ ERRO DURANTE O TESTE: {e}")
        logger.exception("Erro no teste do pipeline")
    
    finally:
        # Limpar diretório temporário
        try:
            if 'test_dir' in locals() and test_dir.exists():
                shutil.rmtree(test_dir)
                print(f"🧹 Diretório temporário limpo: {test_dir}")
        except Exception as e:
            print(f"⚠️ Aviso: Erro ao limpar diretório temporário: {e}")
    
    print("=" * 80)
    print("🏁 TESTE CONCLUÍDO")
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(test_optimization_pipeline())