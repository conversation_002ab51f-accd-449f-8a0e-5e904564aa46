"""
Pytest configuration and fixtures for API tests.
"""

import pytest
import asyncio
from typing import Generator, AsyncGenerator
from httpx import Async<PERSON>lient
from fastapi.testclient import TestClient

from src.api.main import app


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client() -> Generator[TestClient, None, None]:
    """
    Create a test client for the FastAPI application.
    """
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """
    Create an async test client for the FastAPI application.
    """
    async with AsyncClient(app=app, base_url="http://test") as async_test_client:
        yield async_test_client


@pytest.fixture
def test_user_token(client: TestClient) -> str:
    """
    Get a real JWT token by logging in with test credentials.
    """
    response = client.post(
        f"{API_PREFIX}/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "test123"
        }
    )

    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        pytest.fail(f"Failed to get test token: {response.status_code} - {response.text}")


@pytest.fixture
def auth_headers(test_user_token: str) -> dict:
    """
    Create authorization headers with real test token.
    """
    return {"Authorization": f"Bearer {test_user_token}"}


@pytest.fixture
def sample_csv_content() -> str:
    """
    Sample CSV content for testing file uploads.
    """
    return """id,data,relato,area,subtema
1,2024-01-15,"Problema na merenda escolar da escola municipal João Silva.",EDUCACAO,ALIMENTACAO_ESCOLAR
2,2024-01-16,"Falta de mediador para criança com autismo na escola estadual Maria Santos.",EDUCACAO,EDUCACAO_ESPECIAL
3,2024-01-17,"Demora no atendimento no posto de saúde da Tijuca.",SAUDE,ATENDIMENTO_BASICO
4,2024-01-18,"Poluição do ar próximo à fábrica no bairro Industrial.",MEIO_AMBIENTE,POLUICAO_ATMOSFERICA
5,2024-01-19,"Bullying na escola municipal Pedro Alvares.",EDUCACAO,BULLYING"""


@pytest.fixture
def sample_text_for_anonymization() -> str:
    """
    Sample text containing PII for anonymization testing.
    """
    return """
    João Silva, CPF 123.456.789-00, mora na Rua das Flores, 123, Rio de Janeiro.
    Ele estuda na Escola Municipal Santos Dumont e seu telefone é (21) 99999-9999.
    O email dele é <EMAIL>.
    """


@pytest.fixture
def sample_text_for_classification() -> str:
    """
    Sample text for classification testing.
    """
    return """
    A merenda escolar da Escola Municipal João da Silva está com problemas.
    As crianças estão recebendo comida estragada e muitas estão passando mal.
    Os pais estão preocupados com a qualidade da alimentação oferecida.
    """


# Removed mock fixtures - using real API for testing


@pytest.fixture(autouse=True)
def setup_test_environment():
    """
    Setup test environment with real dependencies.
    """
    # Use real API without mocks
    # Tests will use actual endpoints and real authentication
    yield


# Using real authentication - no mocking needed


@pytest.fixture
def mock_auth_for_protected_endpoints(mocker):
    """
    Mock authentication specifically for protected endpoints.
    """
    mock_user = {
        "id": "test-user-123",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "organization": "Test Organization",
        "is_active": True
    }

    mocker.patch("src.api.dependencies.get_current_user", return_value=mock_user)
    return mock_user


# Test data constants
TEST_USER_ID = "test-user-123"
TEST_USER_EMAIL = "<EMAIL>"
TEST_FILE_ID = "test-file-id"
TEST_REPORT_ID = "test-report-id"

# API endpoints
API_PREFIX = "/api/v1"
AUTH_ENDPOINTS = f"{API_PREFIX}/auth"
FILES_ENDPOINTS = f"{API_PREFIX}/files"
ANONYMIZATION_ENDPOINTS = f"{API_PREFIX}/anonymization"
CLASSIFICATION_ENDPOINTS = f"{API_PREFIX}/classification"
REPORTS_ENDPOINTS = f"{API_PREFIX}/reports"
