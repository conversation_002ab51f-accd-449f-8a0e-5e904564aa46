import pandas as pd
from src.reports import gerador_relatorio as gr


def test_gerar_prompt_para_relatorio(tmp_path):
    data = pd.DataFrame({'RELATO': ['r1', 'r2'], 'REFERE_ASSUNTO': [1, 1]})
    prompt = gr.gerar_prompt_para_relatorio(data, 'Assunto')
    assert 'DENÚNCIA #1' in prompt
    assert 'r1' in prompt
    assert 'Assunto' in prompt


def test_salvar_relatorio(tmp_path, monkeypatch):
    monkeypatch.chdir(tmp_path)
    ok = gr.salvar_relatorio('texto', 'rel.md')
    assert ok
    assert (tmp_path / 'data' / 'output' / 'rel.md').exists()
