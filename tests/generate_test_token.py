#!/usr/bin/env python3
"""
Script to generate a test JWT token for API testing.
"""

import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from jose import jwt
from dotenv import load_dotenv

def generate_test_token():
    """Generate a test JWT token."""
    print("🔑 Generating test JWT token...")
    
    # Load environment variables
    load_dotenv()
    
    # Get secret key
    secret_key = os.getenv("API_SECRET_KEY", "simple-class-secret-key-change-in-production")
    algorithm = "HS256"
    
    # Create payload
    payload = {
        "sub": "test-user-123",  # User ID
        "email": "<EMAIL>",
        "iat": datetime.utcnow(),
        "exp": datetime.utcnow() + timedelta(hours=24)  # Expires in 24 hours
    }
    
    # Generate token
    token = jwt.encode(payload, secret_key, algorithm=algorithm)
    
    print(f"✅ Test token generated:")
    print(f"   Token: {token}")
    print(f"   User ID: {payload['sub']}")
    print(f"   Email: {payload['email']}")
    print(f"   Expires: {payload['exp']}")
    
    return token

def test_token_decode():
    """Test token decoding."""
    print("\n🔍 Testing token decode...")
    
    # Load environment variables
    load_dotenv()
    
    secret_key = os.getenv("API_SECRET_KEY", "simple-class-secret-key-change-in-production")
    algorithm = "HS256"
    
    # Generate token
    token = generate_test_token()
    
    try:
        # Decode token
        payload = jwt.decode(token, secret_key, algorithms=[algorithm])
        print("✅ Token decode successful:")
        print(f"   User ID: {payload.get('sub')}")
        print(f"   Email: {payload.get('email')}")
        print(f"   Issued at: {payload.get('iat')}")
        print(f"   Expires at: {payload.get('exp')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Token decode failed: {e}")
        return False

def create_test_script():
    """Create a test script with the token."""
    print("\n📝 Creating test script...")
    
    token = generate_test_token()
    
    test_script = f'''#!/usr/bin/env python3
"""
Auto-generated test script with valid JWT token.
"""

import requests
import json
from pathlib import Path

# Valid JWT token for testing
TEST_TOKEN = "{token}"

def test_with_valid_token():
    """Test API endpoints with valid token."""
    base_url = "http://localhost:8000"
    
    headers = {{
        "Authorization": f"Bearer {{TEST_TOKEN}}"
    }}
    
    print("🧪 Testing with valid JWT token...")
    
    # Test file listing
    try:
        response = requests.get(
            f"{{base_url}}/api/v1/files/",
            headers=headers,
            timeout=10
        )
        
        print(f"📋 File listing: {{response.status_code}}")
        if response.status_code == 200:
            result = response.json()
            print(f"   Files: {{result.get('total_count', 0)}}")
        else:
            print(f"   Error: {{response.text}}")
            
    except Exception as e:
        print(f"❌ Request failed: {{e}}")

if __name__ == "__main__":
    test_with_valid_token()
'''
    
    script_path = project_root / "scripts" / "test_with_token.py"
    with open(script_path, 'w') as f:
        f.write(test_script)
    
    print(f"✅ Test script created: {script_path}")
    print("   Run with: poetry run python scripts/test_with_token.py")

def main():
    """Main function."""
    print("🔑 JWT Token Generator for Testing")
    print("=" * 50)
    
    # Generate and test token
    decode_ok = test_token_decode()
    
    if decode_ok:
        # Create test script
        create_test_script()
    
    print("\n" + "=" * 50)
    if decode_ok:
        print("✅ Token generation successful!")
        print("\n💡 Usage:")
        print("   1. Copy the token above")
        print("   2. Use it in Authorization header: 'Bearer <token>'")
        print("   3. Or run the generated test script")
    else:
        print("❌ Token generation failed!")

if __name__ == "__main__":
    main()
