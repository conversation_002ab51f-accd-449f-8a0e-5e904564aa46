"""
Testes de integração para workflows completos
"""

import pytest
import pandas as pd
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, Mock

from src.tools.classification_tools import classify_text_individual, classify_multilabel
from src.tools.report_tools import generate_tactical_report
from src.tools.data_tools import load_csv_data, validate_data_format


@pytest.mark.integration
class TestClassificationWorkflow:
    """Testes de integração para workflow de classificação"""
    
    def test_complete_individual_classification_workflow(self, temp_directories, mock_config, mock_together_client):
        """Teste do workflow completo de classificação individual"""
        # 1. Preparar dados de entrada
        input_data = pd.DataFrame({
            'Teor': [
                'Paciente com problema de visão aguarda consulta oftalmológica há 6 meses',
                'Solicitação de exame de sangue para diagnóstico',
                'Demora na marcação de cirurgia de catarata',
                'Problema com medicamento para diabetes',
                'Consulta de oftalmologia cancelada sem aviso'
            ],
            'ID': [1, 2, 3, 4, 5]
        })
        
        input_file = temp_directories["input"] / "ouvidorias.csv"
        output_file = temp_directories["output"] / "classificados.csv"
        
        input_data.to_csv(input_file, index=False)
        
        # 2. Executar classificação
        result = classify_text_individual(
            column_name="Teor",
            input_file=str(input_file),
            output_file=str(output_file)
        )
        
        # 3. Verificar resultados
        assert result["total_processed"] == 5
        assert result["positive_cases"] == 5  # Mock sempre retorna positivo
        assert os.path.exists(output_file)
        
        # 4. Verificar arquivo de saída
        df_result = pd.read_csv(output_file)
        assert len(df_result) == 5
        assert "REFERE_ASSUNTO" in df_result.columns
        assert "TEXTO_ANALISADO" in df_result.columns
        assert all(df_result["REFERE_ASSUNTO"] == 1)
        
        # 5. Validar dados de saída
        validation_result = validate_data_format(
            str(output_file),
            required_columns=["REFERE_ASSUNTO", "TEXTO_ANALISADO"]
        )
        assert validation_result["is_valid"]
    
    def test_complete_multilabel_classification_workflow(self, temp_directories, mock_config, mock_together_client):
        """Teste do workflow completo de classificação multilabel"""
        # Configurar mock para retornar subtemas
        mock_together_client.completions.create.return_value.choices[0].text = "1,2"
        
        # 1. Preparar dados
        input_data = pd.DataFrame({
            'Teor': [
                'Paciente com problema de visão aguarda consulta oftalmológica',
                'Demora na regulação para cirurgia de catarata',
                'Exame de diagnóstico por imagem cancelado'
            ],
            'ID': [1, 2, 3]
        })
        
        input_file = temp_directories["input"] / "ouvidorias.csv"
        output_file = temp_directories["output"] / "multilabel.csv"
        
        input_data.to_csv(input_file, index=False)
        
        # 2. Executar classificação multilabel
        result = classify_multilabel(
            column_name="Teor",
            area="SAUDE",
            input_file=str(input_file),
            output_file=str(output_file)
        )
        
        # 3. Verificar resultados
        assert result["total_processed"] == 3
        assert result["area"] == "SAUDE"
        assert os.path.exists(output_file)
        
        # 4. Verificar arquivo de saída
        df_result = pd.read_csv(output_file)
        assert len(df_result) == 3
        assert "SUBTEMAS_IDENTIFICADOS" in df_result.columns
        assert "AREA_CLASSIFICACAO" in df_result.columns
        
        # Verificar colunas binárias de subtemas
        subtema_columns = [col for col in df_result.columns if col.startswith('SUBTEMA_')]
        assert len(subtema_columns) > 0


@pytest.mark.integration
class TestReportWorkflow:
    """Testes de integração para workflow de relatórios"""
    
    def test_complete_report_generation_workflow(self, temp_directories, mock_config, mock_anthropic_client):
        """Teste do workflow completo de geração de relatórios"""
        # 1. Preparar dados classificados
        classified_data = pd.DataFrame({
            'RELATO': [
                'Paciente com problema de visão aguarda consulta oftalmológica há 6 meses',
                'Demora na marcação de cirurgia de catarata',
                'Consulta de oftalmologia cancelada sem aviso'
            ],
            'TEXTO_ANALISADO': [
                'Paciente com problema de visão aguarda consulta oftalmológica há 6 meses',
                'Demora na marcação de cirurgia de catarata',
                'Consulta de oftalmologia cancelada sem aviso'
            ],
            'REFERE_ASSUNTO': [1, 1, 1]
        })
        
        input_file = temp_directories["input"] / "classificados.csv"
        output_file = temp_directories["output"] / "relatorio.md"
        template_file = temp_directories["input"] / "template.txt"
        
        classified_data.to_csv(input_file, index=False)
        
        # Criar template simples
        template_content = """
# Relatório de Análise

Total de casos: {total_casos}
Assunto: {assunto}

## Denúncias
{denuncias_formatadas}
"""
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        # 2. Gerar relatório
        result = generate_tactical_report(
            input_file=str(input_file),
            output_file=str(output_file),
            template_file=str(template_file)
        )
        
        # 3. Verificar resultados
        assert result["total_cases"] == 3
        assert result["subject"] == "Oftalmologia"
        assert os.path.exists(output_file)
        
        # 4. Verificar conteúdo do relatório
        with open(output_file, 'r', encoding='utf-8') as f:
            report_content = f.read()
        
        assert "Relatório de Teste" in report_content
        assert len(report_content) > 0


@pytest.mark.integration
class TestDataProcessingWorkflow:
    """Testes de integração para workflow de processamento de dados"""
    
    def test_complete_data_processing_workflow(self, temp_directories):
        """Teste do workflow completo de processamento de dados"""
        # 1. Criar dados com problemas
        problematic_data = pd.DataFrame({
            'Teor': [
                'Texto válido 1',
                '',  # Texto vazio
                None,  # Valor nulo
                '   ',  # Apenas espaços
                'Texto válido 2',
                'Texto com João Silva (informação pessoal)'  # Para anonimização
            ],
            'ID': [1, 2, 3, 4, 5, 6]
        })
        
        input_file = temp_directories["input"] / "dados_problematicos.csv"
        problematic_data.to_csv(input_file, index=False)
        
        # 2. Validar dados originais
        validation_result = validate_data_format(str(input_file))
        assert validation_result["is_valid"]
        assert validation_result["file_info"]["total_rows"] == 6
        
        # 3. Carregar dados
        load_result = load_csv_data(str(input_file))
        assert load_result["total_rows"] == 6
        assert load_result["total_columns"] == 2
        assert "Teor" in load_result["columns"]
        
        # 4. Filtrar textos válidos
        from src.tools.data_tools import filter_valid_texts
        
        filtered_file = temp_directories["output"] / "dados_filtrados.csv"
        filter_result = filter_valid_texts(
            file_path=str(input_file),
            column_name="Teor",
            output_file=str(filtered_file)
        )
        
        assert filter_result["original_count"] == 6
        assert filter_result["filtered_count"] == 3  # Apenas textos válidos
        assert os.path.exists(filtered_file)
        
        # 5. Verificar dados filtrados
        df_filtered = pd.read_csv(filtered_file)
        assert len(df_filtered) == 3
        assert all(df_filtered["Teor"].notna())
        assert all(df_filtered["Teor"].str.strip() != "")


@pytest.mark.integration
@pytest.mark.slow
class TestEndToEndWorkflow:
    """Testes end-to-end completos"""
    
    def test_full_classification_and_report_workflow(self, temp_directories, mock_config, mock_together_client, mock_anthropic_client):
        """Teste do workflow completo: dados -> classificação -> relatório"""
        # 1. Preparar dados iniciais
        raw_data = pd.DataFrame({
            'Teor': [
                'Paciente com problema de visão aguarda consulta oftalmológica há 6 meses',
                'Solicitação de exame de sangue para diagnóstico',
                'Demora na marcação de cirurgia de catarata',
                'Problema com medicamento para diabetes',
                'Consulta de oftalmologia cancelada sem aviso',
                'Falta de médico oftalmologista no hospital'
            ],
            'Data': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05', '2024-01-06'],
            'ID': [1, 2, 3, 4, 5, 6]
        })
        
        input_file = temp_directories["input"] / "ouvidorias_completas.csv"
        raw_data.to_csv(input_file, index=False)
        
        # 2. Executar classificação individual
        classification_file = temp_directories["output"] / "classificados.csv"
        
        classification_result = classify_text_individual(
            column_name="Teor",
            input_file=str(input_file),
            output_file=str(classification_file)
        )
        
        assert classification_result["total_processed"] == 6
        assert os.path.exists(classification_file)
        
        # 3. Criar template para relatório
        template_file = temp_directories["input"] / "template_relatorio.txt"
        template_content = """
# ANÁLISE TÁTICA DE CASOS RELACIONADOS A {assunto_maiusculo}

Total de casos analisados: {total_casos}

## DENÚNCIAS ANALISADAS:
{denuncias_formatadas}

## ANÁLISE
Análise dos casos relacionados a {assunto}.
"""
        
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        # 4. Gerar relatório
        report_file = temp_directories["output"] / "relatorio_final.md"
        
        report_result = generate_tactical_report(
            input_file=str(classification_file),
            output_file=str(report_file),
            template_file=str(template_file)
        )
        
        assert report_result["total_cases"] == 6  # Mock classifica todos como positivos
        assert os.path.exists(report_file)
        
        # 5. Verificar arquivos finais
        # Arquivo de classificação
        df_classified = pd.read_csv(classification_file)
        assert len(df_classified) == 6
        assert "REFERE_ASSUNTO" in df_classified.columns
        assert "TEXTO_ANALISADO" in df_classified.columns
        
        # Arquivo de relatório
        with open(report_file, 'r', encoding='utf-8') as f:
            report_content = f.read()
        
        assert len(report_content) > 0
        assert "Relatório de Teste" in report_content  # Do mock
        
        # 6. Validar integridade dos dados
        validation_result = validate_data_format(
            str(classification_file),
            required_columns=["REFERE_ASSUNTO", "TEXTO_ANALISADO", "Teor"]
        )
        assert validation_result["is_valid"]
    
    def test_multilabel_workflow_with_report(self, temp_directories, mock_config, mock_together_client, mock_anthropic_client):
        """Teste do workflow multilabel completo"""
        # Configurar mock para retornar diferentes subtemas
        responses = ["1", "1,2", "2", "NENHUM", "1,3"]
        mock_together_client.completions.create.side_effect = [
            Mock(choices=[Mock(text=response)]) for response in responses
        ]
        
        # 1. Preparar dados
        input_data = pd.DataFrame({
            'Teor': [
                'Problema de visão',
                'Demora na regulação para cirurgia',
                'Problema na regulação de consultas',
                'Reclamação sobre atendimento',
                'Exame de diagnóstico cancelado'
            ],
            'ID': [1, 2, 3, 4, 5]
        })
        
        input_file = temp_directories["input"] / "multilabel_input.csv"
        input_data.to_csv(input_file, index=False)
        
        # 2. Executar classificação multilabel
        multilabel_file = temp_directories["output"] / "multilabel_result.csv"
        
        multilabel_result = classify_multilabel(
            column_name="Teor",
            area="SAUDE",
            input_file=str(input_file),
            output_file=str(multilabel_file)
        )
        
        assert multilabel_result["total_processed"] == 5
        assert os.path.exists(multilabel_file)
        
        # 3. Verificar resultados multilabel
        df_multilabel = pd.read_csv(multilabel_file)
        assert len(df_multilabel) == 5
        assert "SUBTEMAS_IDENTIFICADOS" in df_multilabel.columns
        assert "AREA_CLASSIFICACAO" in df_multilabel.columns
        
        # Verificar que alguns registros têm subtemas
        has_subtemas = df_multilabel["SUBTEMAS_IDENTIFICADOS"].str.strip() != ""
        assert has_subtemas.sum() > 0  # Pelo menos alguns devem ter subtemas
        
        # 4. Gerar relatório multilabel
        from src.tools.report_tools import generate_multilabel_report
        
        # Criar template multilabel
        multilabel_template = temp_directories["input"] / "template_multilabel.txt"
        template_content = """
# RELATÓRIO MULTILABEL - {area}

Total de registros: {total_registros}
Registros com subtemas: {registros_com_subtemas}

## Distribuição:
{subtemas_contagem}
"""
        
        with open(multilabel_template, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        multilabel_report_file = temp_directories["output"] / "relatorio_multilabel.md"
        
        multilabel_report_result = generate_multilabel_report(
            input_file=str(multilabel_file),
            output_file=str(multilabel_report_file),
            report_type="individual"
        )
        
        assert multilabel_report_result["total_records"] == 5
        assert os.path.exists(multilabel_report_file)
        
        # 5. Verificar relatório multilabel
        with open(multilabel_report_file, 'r', encoding='utf-8') as f:
            report_content = f.read()
        
        assert len(report_content) > 0
