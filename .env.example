# Simple Class API - Environment Configuration Template
# Copy this file to .env and configure the values

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Secret key for JWT tokens (generate a secure random string for production)
API_SECRET_KEY=your-secret-key-change-in-production

# Token expiration times
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=30

# API metadata
API_TITLE=Simple Class API
API_DESCRIPTION=API for text processing and document classification
API_VERSION=1.0.0

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================

# Environment mode: development, testing, production
ENVIRONMENT=development

# Debug mode (only for development)
DEBUG=true

# =============================================================================
# CORS CONFIGURATION
# =============================================================================

# Allowed origins for CORS (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================

# Supabase project URL
SUPABASE_URL=https://your-project.supabase.co

# Supabase anon key (public key)
SUPABASE_KEY=your-supabase-anon-key

# Supabase service role key (private key - keep secure!)
SUPABASE_SERVICE_KEY=your-supabase-service-key

# =============================================================================
# EXTERNAL API KEYS
# =============================================================================

# Together AI API key for LLM classification
TOGETHER_API_KEY=your_together_api_key_here

# Anthropic API key for Claude models
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Fireworks AI API key for additional models
FIREWORKS_API_KEY=your_fireworks_api_key_here

# =============================================================================
# RATE LIMITING
# =============================================================================

# Rate limiting configuration
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600  # seconds

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================

# Maximum file size in bytes (50MB default)
MAX_FILE_SIZE=52428800

# Allowed file types (comma-separated)
ALLOWED_FILE_TYPES=text/csv,application/pdf,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Security headers
SECURE_COOKIES=false  # Set to true in production with HTTPS
HTTPS_ONLY=false      # Set to true in production

# =============================================================================
# MODEL CONFIGURATION
# =============================================================================

# Default models for different tasks
DEFAULT_CLASSIFICATION_MODEL=meta-llama/Llama-3.3-70B-Instruct-Turbo
DEFAULT_REPORT_MODEL=claude-3-5-sonnet-20241022

# Model timeouts (seconds)
MODEL_TIMEOUT=30
CLASSIFICATION_TIMEOUT=60
REPORT_GENERATION_TIMEOUT=120

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Development-only settings (ignored in production)
DEV_AUTO_RELOAD=true
DEV_SHOW_DOCS=true
