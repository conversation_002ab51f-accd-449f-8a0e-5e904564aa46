"""
RAG-PDF Integration Service
Integrates the new RAG system with existing PDF processing pipeline
"""

import logging
import uuid
from typing import Dict, List, Optional
import asyncio

from backend.src.services.document_indexing_service import DocumentIndexingService
from backend.src.services.pdf_processing_service import PDFProcessingService
from backend.src.services.semantic_processing_service import SemanticProcessingService


logger = logging.getLogger(__name__)


class RAGPDFIntegrationService:
    """Service to integrate RAG system with existing PDF processing"""
    
    def __init__(self):
        self.indexing_service = DocumentIndexingService()
        self.pdf_service = PDFProcessingService()
        self.semantic_service = SemanticProcessingService()
    
    async def process_and_index_pdf(
        self,
        file_path: str,
        document_id: Optional[str] = None,
        enable_rag_indexing: bool = True
    ) -> Dict:
        """
        Process PDF using existing pipeline and index for RAG
        
        Args:
            file_path: Path to PDF file
            document_id: Optional document ID, generates if not provided
            enable_rag_indexing: Whether to create RAG index
            
        Returns:
            Dict with processing and indexing results
        """
        try:
            if not document_id:
                document_id = str(uuid.uuid4())
            
            logger.info(f"Processing PDF for document {document_id}")
            
            # 1. Process PDF using existing service
            pdf_result = await self._process_pdf_with_existing_pipeline(file_path)
            
            if not pdf_result.get("success"):
                return {
                    "success": False,
                    "error": "PDF processing failed",
                    "details": pdf_result.get("error")
                }
            
            # 2. Convert existing chunks to RAG format
            rag_chunks = self._convert_to_rag_chunks(pdf_result["chunks"])
            
            # 3. Index for RAG if enabled
            indexing_result = None
            if enable_rag_indexing and rag_chunks:
                indexing_result = await self.indexing_service.index_document_chunks(
                    document_id=document_id,
                    chunks=rag_chunks
                )
            
            # 4. Return combined results
            return {
                "success": True,
                "document_id": document_id,
                "pdf_processing": {
                    "total_chunks": len(pdf_result.get("chunks", [])),
                    "total_pages": pdf_result.get("total_pages", 0),
                    "processing_time_ms": pdf_result.get("processing_time_ms", 0)
                },
                "rag_indexing": indexing_result if indexing_result else {
                    "status": "skipped",
                    "reason": "RAG indexing disabled or no chunks"
                },
                "chunks_available_for_rag": len(rag_chunks)
            }
            
        except Exception as e:
            logger.error(f"PDF processing and indexing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "document_id": document_id
            }
    
    async def _process_pdf_with_existing_pipeline(self, file_path: str) -> Dict:
        """Process PDF using existing pipeline"""
        try:
            # This would call your existing PDF processing service
            # For now, we'll simulate the structure
            
            # Simulate existing PDF processing
            chunks = await self._simulate_existing_pdf_processing(file_path)
            
            return {
                "success": True,
                "chunks": chunks,
                "total_pages": len(chunks) // 3,  # Rough estimate
                "processing_time_ms": 1500  # Simulated time
            }
            
        except Exception as e:
            logger.error(f"Existing PDF processing failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _simulate_existing_pdf_processing(self, file_path: str) -> List[Dict]:
        """
        Simulate existing PDF processing
        Replace this with actual integration to your existing service
        """
        # This is a placeholder - replace with actual integration
        chunks = [
            {
                "content": "Conteúdo do chunk 1 extraído do PDF...",
                "page_number": 1,
                "confidence_score": 0.95,
                "area_classification": "processual",
                "metadata": {"source": "existing_pipeline"}
            },
            {
                "content": "Conteúdo do chunk 2 extraído do PDF...",
                "page_number": 1,
                "confidence_score": 0.87,
                "area_classification": "civil",
                "metadata": {"source": "existing_pipeline"}
            }
        ]
        
        return chunks
    
    def _convert_to_rag_chunks(self, existing_chunks: List[Dict]) -> List[Dict]:
        """Convert existing chunk format to RAG-compatible format"""
        
        rag_chunks = []
        
        for chunk in existing_chunks:
            # Map existing fields to RAG format
            rag_chunk = {
                "content": chunk.get("content", ""),
                "page_number": chunk.get("page_number"),
                "confidence_score": chunk.get("confidence_score", 1.0),
                "area_classification": chunk.get("area_classification"),
                "metadata": {
                    **chunk.get("metadata", {}),
                    "source_pipeline": "existing_pdf_processor",
                    "conversion_timestamp": "2025-01-16T12:00:00Z"
                }
            }
            
            # Only include chunks with meaningful content
            if rag_chunk["content"] and len(rag_chunk["content"].strip()) > 10:
                rag_chunks.append(rag_chunk)
        
        return rag_chunks
    
    async def reindex_existing_document(
        self, 
        document_id: str,
        force_reindex: bool = False
    ) -> Dict:
        """Reindex an existing document for RAG"""
        try:
            # Get existing chunks from your database/storage
            existing_chunks = await self._get_existing_document_chunks(document_id)
            
            if not existing_chunks:
                return {
                    "success": False,
                    "error": "No existing chunks found for document",
                    "document_id": document_id
                }
            
            # Convert to RAG format
            rag_chunks = self._convert_to_rag_chunks(existing_chunks)
            
            # Index for RAG
            indexing_result = await self.indexing_service.index_document_chunks(
                document_id=document_id,
                chunks=rag_chunks,
                force_reindex=force_reindex
            )
            
            return {
                "success": True,
                "document_id": document_id,
                "reindexing_result": indexing_result,
                "chunks_processed": len(rag_chunks)
            }
            
        except Exception as e:
            logger.error(f"Document reindexing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "document_id": document_id
            }
    
    async def _get_existing_document_chunks(self, document_id: str) -> List[Dict]:
        """Get existing chunks from your current system"""
        # This would query your existing database/storage
        # Replace with actual implementation
        
        # Placeholder implementation
        return []
    
    async def batch_index_existing_documents(
        self, 
        document_ids: List[str],
        max_concurrent: int = 3
    ) -> Dict:
        """Batch index multiple existing documents"""
        
        results = {"successful": [], "failed": []}
        
        # Process in batches to avoid overwhelming the system
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def index_single_document(doc_id: str):
            async with semaphore:
                try:
                    result = await self.reindex_existing_document(doc_id)
                    if result["success"]:
                        results["successful"].append(doc_id)
                    else:
                        results["failed"].append({
                            "document_id": doc_id,
                            "error": result["error"]
                        })
                except Exception as e:
                    results["failed"].append({
                        "document_id": doc_id,
                        "error": str(e)
                    })
        
        # Execute batch indexing
        tasks = [index_single_document(doc_id) for doc_id in document_ids]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        return {
            "total_documents": len(document_ids),
            "successful_count": len(results["successful"]),
            "failed_count": len(results["failed"]),
            "successful_documents": results["successful"],
            "failed_documents": results["failed"]
        }
    
    async def get_integration_status(self) -> Dict:
        """Get status of RAG-PDF integration"""
        try:
            # Check if services are available
            services_status = {
                "indexing_service": "available",
                "pdf_service": "available", 
                "semantic_service": "available"
            }
            
            # Get some basic stats
            # This would query your databases for actual numbers
            stats = {
                "total_indexed_documents": 0,
                "total_indexed_chunks": 0,
                "avg_indexing_time_ms": 0
            }
            
            return {
                "integration_status": "operational",
                "services": services_status,
                "statistics": stats,
                "last_updated": "2025-01-16T12:00:00Z"
            }
            
        except Exception as e:
            logger.error(f"Failed to get integration status: {e}")
            return {
                "integration_status": "error",
                "error": str(e)
            }