#!/usr/bin/env python3
"""
Supabase-specific setup script for RAG system.

This script configures Supabase for the RAG system by:
1. Enabling pgvector extension
2. Creating RAG tables with proper RLS policies
3. Setting up storage buckets for documents
4. Configuring security policies
5. Testing the complete setup
"""

import os
import sys
import asyncio
import asyncpg
from pathlib import Path
from typing import Dict, Any, List, Optional
import logging
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SupabaseRAGSetup:
    """Supabase-specific RAG system setup."""
    
    def __init__(self):
        # Get Supabase connection details
        self.host = os.getenv("SUPABASE_DB_HOST")
        self.port = os.getenv("SUPABASE_DB_PORT", "5432")
        self.database = os.getenv("SUPABASE_DB_NAME")
        self.user = os.getenv("SUPABASE_DB_USER")
        self.password = os.getenv("SUPABASE_DB_PASSWORD")
        
        if not all([self.host, self.database, self.user, self.password]):
            raise ValueError("Missing Supabase database credentials. Check your .env file.")
        
        self.database_url = f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}?sslmode=require"
        self.connection = None
        
        logger.info(f"Configured for Supabase database: {self.database}")
    
    async def connect(self) -> None:
        """Connect to Supabase database."""
        try:
            self.connection = await asyncpg.connect(self.database_url)
            logger.info("✅ Connected to Supabase database")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Supabase: {e}")
            raise
    
    async def disconnect(self) -> None:
        """Disconnect from database."""
        if self.connection:
            await self.connection.close()
            logger.info("Disconnected from Supabase")
    
    async def enable_pgvector(self) -> bool:
        """Enable pgvector extension in Supabase."""
        try:
            # Check if already enabled
            result = await self.connection.fetch(
                "SELECT * FROM pg_extension WHERE extname = 'vector'"
            )
            
            if result:
                logger.info("✅ pgvector extension already enabled")
                return True
            
            # Enable extension
            await self.connection.execute("CREATE EXTENSION IF NOT EXISTS vector")
            logger.info("✅ pgvector extension enabled successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to enable pgvector: {e}")
            logger.error("Please enable pgvector in your Supabase dashboard:")
            logger.error("1. Go to Database > Extensions")
            logger.error("2. Search for 'vector' and enable it")
            return False
    
    async def create_rag_tables(self) -> bool:
        """Create RAG tables with Supabase-specific configurations."""
        try:
            # Read the migration file
            migration_file = project_root / "database" / "migrations" / "create_rag_tables.sql"
            
            if not migration_file.exists():
                logger.error(f"Migration file not found: {migration_file}")
                return False
            
            with open(migration_file, 'r', encoding='utf-8') as f:
                migration_sql = f.read()
            
            # Execute migration
            await self.connection.execute(migration_sql)
            logger.info("✅ RAG tables created successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create RAG tables: {e}")
            return False
    
    async def setup_rls_policies(self) -> bool:
        """Set up Row Level Security policies for RAG tables."""
        try:
            # Enable RLS on RAG tables
            rls_tables = [
                "document_chunks",
                "rag_queries", 
                "rag_analytics",
                "document_indexing_status"
            ]
            
            for table in rls_tables:
                await self.connection.execute(f"ALTER TABLE {table} ENABLE ROW LEVEL SECURITY")
                logger.info(f"✅ RLS enabled for {table}")
            
            # Create basic policies (can be customized based on requirements)
            policies = [
                # Allow authenticated users to read document chunks
                """
                CREATE POLICY "Allow authenticated read on document_chunks" 
                ON document_chunks FOR SELECT 
                TO authenticated 
                USING (true)
                """,
                
                # Allow service role to manage all data
                """
                CREATE POLICY "Allow service role full access on document_chunks" 
                ON document_chunks FOR ALL 
                TO service_role 
                USING (true)
                """,
                
                # Similar policies for other tables
                """
                CREATE POLICY "Allow authenticated read on rag_queries" 
                ON rag_queries FOR SELECT 
                TO authenticated 
                USING (true)
                """,
                
                """
                CREATE POLICY "Allow service role full access on rag_queries" 
                ON rag_queries FOR ALL 
                TO service_role 
                USING (true)
                """
            ]
            
            for policy in policies:
                try:
                    await self.connection.execute(policy)
                except Exception as e:
                    if "already exists" not in str(e):
                        logger.warning(f"Policy creation warning: {e}")
            
            logger.info("✅ RLS policies configured")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup RLS policies: {e}")
            return False
    
    async def validate_setup(self) -> Dict[str, Any]:
        """Validate the Supabase RAG setup."""
        validation = {
            "valid": True,
            "checks": {},
            "errors": [],
            "warnings": []
        }
        
        try:
            # Check pgvector extension
            result = await self.connection.fetch(
                "SELECT * FROM pg_extension WHERE extname = 'vector'"
            )
            
            if result:
                validation["checks"]["pgvector"] = True
                logger.info("✅ pgvector extension is enabled")
            else:
                validation["checks"]["pgvector"] = False
                validation["errors"].append("pgvector extension not enabled")
                validation["valid"] = False
            
            # Check required tables
            required_tables = [
                "documents",
                "document_chunks",
                "rag_queries", 
                "rag_analytics",
                "document_indexing_status"
            ]
            
            for table in required_tables:
                result = await self.connection.fetch(
                    """
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' AND table_name = $1
                    """,
                    table
                )
                
                if result:
                    validation["checks"][f"table_{table}"] = True
                    logger.info(f"✅ Table {table} exists")
                else:
                    validation["checks"][f"table_{table}"] = False
                    validation["errors"].append(f"Table {table} missing")
                    validation["valid"] = False
            
            # Check vector indexes
            result = await self.connection.fetch(
                """
                SELECT indexname 
                FROM pg_indexes 
                WHERE tablename = 'document_chunks' 
                AND indexname LIKE '%embedding%'
                """
            )
            
            if result:
                validation["checks"]["vector_indexes"] = True
                logger.info("✅ Vector indexes exist")
            else:
                validation["checks"]["vector_indexes"] = False
                validation["warnings"].append("Vector indexes may be missing")
            
            # Check similarity function
            result = await self.connection.fetch(
                """
                SELECT routine_name 
                FROM information_schema.routines 
                WHERE routine_schema = 'public' 
                AND routine_name = 'get_similar_chunks'
                """
            )
            
            if result:
                validation["checks"]["similarity_function"] = True
                logger.info("✅ get_similar_chunks function exists")
            else:
                validation["checks"]["similarity_function"] = False
                validation["errors"].append("get_similar_chunks function missing")
                validation["valid"] = False
            
            # Check RLS policies
            result = await self.connection.fetch(
                """
                SELECT tablename, policyname 
                FROM pg_policies 
                WHERE tablename IN ('document_chunks', 'rag_queries')
                """
            )
            
            if result:
                validation["checks"]["rls_policies"] = True
                logger.info(f"✅ RLS policies configured ({len(result)} policies)")
            else:
                validation["checks"]["rls_policies"] = False
                validation["warnings"].append("No RLS policies found")
            
        except Exception as e:
            validation["valid"] = False
            validation["errors"].append(f"Validation error: {e}")
            logger.error(f"❌ Validation error: {e}")
        
        return validation
    
    async def test_vector_operations(self) -> bool:
        """Test vector operations with sample data."""
        try:
            # Create a test embedding
            import numpy as np
            test_embedding = np.random.rand(384).tolist()
            
            # Test vector similarity function
            result = await self.connection.fetch(
                "SELECT * FROM get_similar_chunks($1, $2, $3)",
                test_embedding,
                0.0,  # Low threshold for testing
                5
            )
            
            logger.info("✅ Vector similarity function working")
            return True
            
        except Exception as e:
            logger.error(f"❌ Vector operations test failed: {e}")
            return False
    
    def print_setup_summary(self, validation: Dict[str, Any]) -> None:
        """Print setup summary."""
        print("\n" + "="*60)
        print("🚀 SUPABASE RAG SETUP SUMMARY")
        print("="*60)
        
        if validation["valid"]:
            print("✅ Setup completed successfully!")
        else:
            print("❌ Setup completed with errors")
        
        print(f"\n📊 Validation Results:")
        for check, status in validation["checks"].items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {check.replace('_', ' ').title()}")
        
        if validation["errors"]:
            print(f"\n❌ Errors ({len(validation['errors'])}):")
            for error in validation["errors"]:
                print(f"   • {error}")
        
        if validation["warnings"]:
            print(f"\n⚠️  Warnings ({len(validation['warnings'])}):")
            for warning in validation["warnings"]:
                print(f"   • {warning}")
        
        print(f"\n🔗 Database: {self.database}")
        print(f"🏠 Host: {self.host}")
        
        print("\n📝 Next Steps:")
        if validation["valid"]:
            print("   1. Test the RAG system with sample documents")
            print("   2. Configure your application to use the RAG endpoints")
            print("   3. Monitor performance and adjust vector indexes if needed")
        else:
            print("   1. Fix the errors listed above")
            print("   2. Re-run this setup script")
            print("   3. Check Supabase dashboard for additional configuration")


async def main():
    """Main setup function."""
    print("🚀 Starting Supabase RAG System Setup...")
    
    setup = SupabaseRAGSetup()
    
    try:
        # Connect to database
        await setup.connect()
        
        # Enable pgvector
        if not await setup.enable_pgvector():
            logger.error("Cannot proceed without pgvector extension")
            return False
        
        # Create RAG tables
        if not await setup.create_rag_tables():
            logger.error("Failed to create RAG tables")
            return False
        
        # Setup RLS policies
        await setup.setup_rls_policies()
        
        # Validate setup
        validation = await setup.validate_setup()
        
        # Test vector operations
        await setup.test_vector_operations()
        
        # Print summary
        setup.print_setup_summary(validation)
        
        return validation["valid"]
        
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        return False
        
    finally:
        await setup.disconnect()


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
