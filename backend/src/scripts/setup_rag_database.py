#!/usr/bin/env python3
"""
Database setup script for RAG system with Supabase.

This script:
1. Connects to Supabase PostgreSQL database
2. Checks if pgvector extension is available (usually pre-installed in Supabase)
3. Creates the pgvector extension if needed
4. Runs RAG table migrations
5. Validates the database setup
6. Creates sample data for testing (optional)

Supabase Configuration:
- Uses environment variables for Supabase connection
- Supports both direct PostgreSQL connection and Supabase client
- Handles Supabase-specific features like RLS policies
"""

import asyncio
import asyncpg
import os
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional
import logging
from dotenv import load_dotenv

# Add backend to path
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

# Load environment variables
load_dotenv()

try:
    from config.settings import settings
except ImportError:
    # Fallback configuration if settings module is not available
    class FallbackSettings:
        class rag_system:
            database_url = os.getenv("DATABASE_URL", "postgresql://localhost/simple_class")
    settings = FallbackSettings()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class RAGDatabaseSetup:
    """Database setup manager for RAG system with Supabase support."""

    def __init__(self, database_url: Optional[str] = None):
        # Try to get Supabase connection details first
        self.supabase_host = os.getenv("SUPABASE_DB_HOST")
        self.supabase_port = os.getenv("SUPABASE_DB_PORT", "5432")
        self.supabase_db = os.getenv("SUPABASE_DB_NAME")
        self.supabase_user = os.getenv("SUPABASE_DB_USER")
        self.supabase_password = os.getenv("SUPABASE_DB_PASSWORD")

        # Build connection URL
        if all([self.supabase_host, self.supabase_db, self.supabase_user, self.supabase_password]):
            self.database_url = f"postgresql://{self.supabase_user}:{self.supabase_password}@{self.supabase_host}:{self.supabase_port}/{self.supabase_db}?sslmode=require"
            self.is_supabase = True
            logger.info("Using Supabase PostgreSQL connection")
        else:
            self.database_url = database_url or settings.rag_system.database_url
            self.is_supabase = False
            logger.info("Using standard PostgreSQL connection")

        self.connection = None
        
    async def connect(self) -> None:
        """Connect to the database."""
        try:
            self.connection = await asyncpg.connect(self.database_url)
            logger.info("Connected to database successfully")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
    
    async def disconnect(self) -> None:
        """Disconnect from the database."""
        if self.connection:
            await self.connection.close()
            logger.info("Disconnected from database")
    
    async def check_pgvector_extension(self) -> bool:
        """Check if pgvector extension is available and enabled."""
        try:
            # Check if extension is available
            result = await self.connection.fetch(
                "SELECT * FROM pg_available_extensions WHERE name = 'vector'"
            )

            if not result:
                if self.is_supabase:
                    logger.warning("pgvector extension not found - this is unusual for Supabase")
                    logger.info("Supabase usually has pgvector pre-installed. Check your project settings.")
                else:
                    logger.error("pgvector extension is not available in this PostgreSQL installation")
                return False

            # Check if extension is installed
            result = await self.connection.fetch(
                "SELECT * FROM pg_extension WHERE extname = 'vector'"
            )

            if result:
                logger.info("✅ pgvector extension is already installed")
                return True
            else:
                if self.is_supabase:
                    logger.info("pgvector extension is available but not enabled - will enable it")
                else:
                    logger.info("pgvector extension is available but not installed")
                return False

        except Exception as e:
            logger.error(f"Error checking pgvector extension: {e}")
            return False
    
    async def install_pgvector_extension(self) -> bool:
        """Install pgvector extension."""
        try:
            await self.connection.execute("CREATE EXTENSION IF NOT EXISTS vector")
            logger.info("✅ pgvector extension enabled successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to enable pgvector extension: {e}")
            if self.is_supabase:
                logger.error("For Supabase:")
                logger.error("1. Go to your Supabase dashboard")
                logger.error("2. Navigate to SQL Editor")
                logger.error("3. Run: CREATE EXTENSION IF NOT EXISTS vector;")
                logger.error("4. Or enable it in Database > Extensions")
            else:
                logger.error("Make sure you have superuser privileges or the extension is pre-installed")
            return False
    
    async def check_documents_table(self) -> bool:
        """Check if documents table exists (required for foreign key)."""
        try:
            result = await self.connection.fetch(
                """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'documents'
                """
            )
            
            if result:
                logger.info("Documents table exists")
                return True
            else:
                logger.warning("Documents table does not exist - will create a basic one")
                return False
                
        except Exception as e:
            logger.error(f"Error checking documents table: {e}")
            return False
    
    async def create_documents_table(self) -> bool:
        """Create basic documents table if it doesn't exist."""
        try:
            await self.connection.execute("""
                CREATE TABLE IF NOT EXISTS documents (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    filename TEXT NOT NULL,
                    file_path TEXT,
                    file_size BIGINT,
                    content_type TEXT,
                    upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    processed_date TIMESTAMP WITH TIME ZONE,
                    status TEXT DEFAULT 'uploaded',
                    metadata JSONB DEFAULT '{}'
                )
            """)
            logger.info("Documents table created successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to create documents table: {e}")
            return False
    
    async def run_rag_migrations(self) -> bool:
        """Run RAG table migrations."""
        try:
            # Read migration file
            migration_file = Path(__file__).parent.parent.parent.parent / "database" / "migrations" / "create_rag_tables.sql"
            
            if not migration_file.exists():
                logger.error(f"Migration file not found: {migration_file}")
                return False
            
            with open(migration_file, 'r', encoding='utf-8') as f:
                migration_sql = f.read()
            
            # Execute migration
            await self.connection.execute(migration_sql)
            logger.info("RAG migrations executed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to run RAG migrations: {e}")
            return False
    
    async def validate_setup(self) -> Dict[str, Any]:
        """Validate the RAG database setup."""
        validation_results = {
            "valid": True,
            "checks": {},
            "errors": [],
            "warnings": []
        }
        
        try:
            # Check required tables exist
            required_tables = [
                "documents",
                "document_chunks", 
                "rag_queries",
                "rag_analytics",
                "document_indexing_status"
            ]
            
            for table in required_tables:
                result = await self.connection.fetch(
                    """
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' AND table_name = $1
                    """,
                    table
                )
                
                if result:
                    validation_results["checks"][f"{table}_exists"] = True
                    logger.info(f"✓ Table {table} exists")
                else:
                    validation_results["checks"][f"{table}_exists"] = False
                    validation_results["errors"].append(f"Table {table} does not exist")
                    validation_results["valid"] = False
                    logger.error(f"✗ Table {table} does not exist")
            
            # Check pgvector extension
            result = await self.connection.fetch(
                "SELECT * FROM pg_extension WHERE extname = 'vector'"
            )
            
            if result:
                validation_results["checks"]["pgvector_installed"] = True
                logger.info("✓ pgvector extension is installed")
            else:
                validation_results["checks"]["pgvector_installed"] = False
                validation_results["errors"].append("pgvector extension is not installed")
                validation_results["valid"] = False
                logger.error("✗ pgvector extension is not installed")
            
            # Check vector indexes
            result = await self.connection.fetch(
                """
                SELECT indexname 
                FROM pg_indexes 
                WHERE tablename = 'document_chunks' AND indexname LIKE '%embedding%'
                """
            )
            
            if result:
                validation_results["checks"]["vector_indexes"] = True
                logger.info("✓ Vector indexes exist")
            else:
                validation_results["checks"]["vector_indexes"] = False
                validation_results["warnings"].append("Vector indexes may not be properly created")
                logger.warning("⚠ Vector indexes may not be properly created")
            
            # Check get_similar_chunks function
            result = await self.connection.fetch(
                """
                SELECT routine_name 
                FROM information_schema.routines 
                WHERE routine_schema = 'public' AND routine_name = 'get_similar_chunks'
                """
            )
            
            if result:
                validation_results["checks"]["similarity_function"] = True
                logger.info("✓ get_similar_chunks function exists")
            else:
                validation_results["checks"]["similarity_function"] = False
                validation_results["errors"].append("get_similar_chunks function does not exist")
                validation_results["valid"] = False
                logger.error("✗ get_similar_chunks function does not exist")
            
        except Exception as e:
            validation_results["valid"] = False
            validation_results["errors"].append(f"Validation error: {e}")
            logger.error(f"Validation error: {e}")
        
        return validation_results
    
    async def create_sample_data(self) -> bool:
        """Create sample data for testing."""
        try:
            # Insert sample document
            doc_id = await self.connection.fetchval(
                """
                INSERT INTO documents (filename, content_type, status, metadata)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT DO NOTHING
                RETURNING id
                """,
                "sample_legal_document.pdf",
                "application/pdf", 
                "processed",
                {"source": "test", "language": "pt"}
            )
            
            if not doc_id:
                # Get existing document ID
                doc_id = await self.connection.fetchval(
                    "SELECT id FROM documents WHERE filename = $1",
                    "sample_legal_document.pdf"
                )
            
            # Insert sample chunks with embeddings (dummy embeddings for testing)
            import numpy as np
            
            sample_chunks = [
                {
                    "content": "O prazo para recurso em processos civis é de 15 dias úteis.",
                    "page": 1,
                    "metadata": {"section": "Prazos", "confidence": 0.95}
                },
                {
                    "content": "A competência territorial é definida pelo domicílio do réu.",
                    "page": 2, 
                    "metadata": {"section": "Competência", "confidence": 0.88}
                },
                {
                    "content": "Os recursos devem ser interpostos no tribunal competente.",
                    "page": 3,
                    "metadata": {"section": "Recursos", "confidence": 0.92}
                }
            ]
            
            for i, chunk in enumerate(sample_chunks):
                # Generate dummy embedding (in real use, this would come from SemanticProcessingService)
                dummy_embedding = np.random.rand(384).tolist()
                
                await self.connection.execute(
                    """
                    INSERT INTO document_chunks 
                    (document_id, chunk_index, content, page_number, embedding, metadata)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    ON CONFLICT (document_id, chunk_index) DO NOTHING
                    """,
                    doc_id,
                    i,
                    chunk["content"],
                    chunk["page"],
                    dummy_embedding,
                    chunk["metadata"]
                )
            
            logger.info("Sample data created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create sample data: {e}")
            return False
    
    async def test_similarity_search(self) -> bool:
        """Test similarity search functionality."""
        try:
            # Generate dummy query embedding
            import numpy as np
            query_embedding = np.random.rand(384).tolist()
            
            # Test similarity search function
            results = await self.connection.fetch(
                "SELECT * FROM get_similar_chunks($1, $2, $3)",
                query_embedding,
                0.0,  # Low threshold for testing
                5
            )
            
            if results:
                logger.info(f"✓ Similarity search working - found {len(results)} results")
                return True
            else:
                logger.warning("⚠ Similarity search returned no results (may be normal with dummy data)")
                return True  # Not necessarily an error
                
        except Exception as e:
            logger.error(f"✗ Similarity search test failed: {e}")
            return False


async def main():
    """Main setup function."""
    logger.info("Starting RAG database setup...")
    
    setup = RAGDatabaseSetup()
    
    try:
        # Connect to database
        await setup.connect()
        
        # Check and install pgvector
        if not await setup.check_pgvector_extension():
            if not await setup.install_pgvector_extension():
                logger.error("Failed to install pgvector extension. Setup aborted.")
                return False
        
        # Check documents table
        if not await setup.check_documents_table():
            if not await setup.create_documents_table():
                logger.error("Failed to create documents table. Setup aborted.")
                return False
        
        # Run migrations
        if not await setup.run_rag_migrations():
            logger.error("Failed to run RAG migrations. Setup aborted.")
            return False
        
        # Validate setup
        validation = await setup.validate_setup()
        
        if validation["valid"]:
            logger.info("✓ Database setup validation passed!")
        else:
            logger.error("✗ Database setup validation failed:")
            for error in validation["errors"]:
                logger.error(f"  - {error}")
            return False
        
        # Create sample data (optional)
        create_sample = input("Create sample data for testing? (y/N): ").lower().strip()
        if create_sample == 'y':
            await setup.create_sample_data()
            await setup.test_similarity_search()
        
        logger.info("RAG database setup completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        return False
        
    finally:
        await setup.disconnect()


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
