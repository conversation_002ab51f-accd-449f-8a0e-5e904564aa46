"""
Integration script to connect existing PDF processing pipeline with new RAG system
"""

import asyncio
import logging
from typing import List, Dict, Optional
from datetime import datetime

from backend.src.services.document_indexing_service import DocumentIndexingService
from backend.src.services.batch_processing_service import BatchProcessingService


logger = logging.getLogger(__name__)


class RAGIntegrationService:
    """Service to integrate existing PDF processing with RAG system"""
    
    def __init__(self):
        self.indexing_service = DocumentIndexingService()
        self.batch_service = BatchProcessingService()
    
    async def process_and_index_document(
        self, 
        document_id: str,
        force_reindex: bool = False
    ) -> Dict:
        """
        Process a document through existing pipeline and index for RAG
        
        Args:
            document_id: Document ID to process
            force_reindex: Whether to force reindexing
            
        Returns:
            Dict with processing and indexing results
        """
        try:
            logger.info(f"Starting RAG integration for document {document_id}")
            
            # Step 1: Check if document is already processed
            existing_analysis = await self.batch_service.get_document_analysis(document_id)
            
            if not existing_analysis and not force_reindex:
                # Document not processed yet, trigger processing first
                logger.info(f"Document {document_id} not processed, starting PDF processing")
                
                processing_result = await self.batch_service.process_document(document_id)
                
                if not processing_result or processing_result.get("status") != "completed":
                    return {
                        "status": "processing_failed",
                        "message": "PDF processing failed",
                        "document_id": document_id
                    }
                
                # Get the processed analysis
                existing_analysis = await self.batch_service.get_document_analysis(document_id)
            
            if not existing_analysis:
                return {
                    "status": "no_data",
                    "message": "No processed document data available",
                    "document_id": document_id
                }
            
            # Step 2: Convert existing chunks to RAG format
            rag_chunks = self._convert_chunks_to_rag_format(existing_analysis)
            
            # Step 3: Index for RAG
            indexing_result = await self.indexing_service.index_document_chunks(
                document_id=document_id,
                chunks=rag_chunks,
                force_reindex=force_reindex
            )
            
            return {
                "status": "completed",
                "document_id": document_id,
                "pdf_processing": "already_completed" if existing_analysis else "completed",
                "rag_indexing": indexing_result,
                "total_chunks_indexed": indexing_result.get("chunks_indexed", 0),
                "rag_ready": True
            }
            
        except Exception as e:
            logger.error(f"RAG integration failed for document {document_id}: {e}")
            return {
                "status": "failed",
                "document_id": document_id,
                "error": str(e),
                "rag_ready": False
            }
    
    def _convert_chunks_to_rag_format(self, document_analysis: Dict) -> List[Dict]:
        """Convert existing document analysis to RAG chunk format"""
        
        rag_chunks = []
        
        # Get chunks from existing analysis
        existing_chunks = document_analysis.get("chunks", [])
        semantic_analysis = document_analysis.get("semantic_analysis", {})
        classification_results = document_analysis.get("classification_results", {})
        
        for i, chunk in enumerate(existing_chunks):
            # Build RAG chunk from existing data
            rag_chunk = {
                "content": chunk.get("text", ""),
                "page_number": chunk.get("page_number"),
                "confidence_score": chunk.get("confidence_score", 1.0),
                "metadata": {
                    "original_chunk_id": chunk.get("id"),
                    "bbox": chunk.get("bbox"),
                    "font_info": chunk.get("font_info"),
                    "processing_metadata": chunk.get("metadata", {})
                }
            }
            
            # Add semantic information if available
            if semantic_analysis:
                chunk_semantic = semantic_analysis.get("chunks", {}).get(str(i), {})
                if chunk_semantic:
                    rag_chunk["metadata"]["semantic_score"] = chunk_semantic.get("relevance_score")
                    rag_chunk["metadata"]["key_terms"] = chunk_semantic.get("key_terms", [])
            
            # Add classification if available
            if classification_results:
                chunk_classification = classification_results.get("chunk_classifications", {}).get(str(i))
                if chunk_classification:
                    rag_chunk["area_classification"] = chunk_classification.get("predicted_area")
                    rag_chunk["confidence_score"] = chunk_classification.get("confidence", rag_chunk["confidence_score"])
            
            # Add document-level metadata
            rag_chunk["metadata"]["document_metadata"] = {
                "processing_date": document_analysis.get("created_at"),
                "total_pages": document_analysis.get("total_pages"),
                "document_type": document_analysis.get("document_type"),
                "language": document_analysis.get("language", "pt")
            }
            
            rag_chunks.append(rag_chunk)
        
        logger.info(f"Converted {len(rag_chunks)} chunks to RAG format")
        return rag_chunks
    
    async def batch_index_documents(
        self, 
        document_ids: List[str],
        max_concurrent: int = 3
    ) -> Dict:
        """
        Batch index multiple documents for RAG
        
        Args:
            document_ids: List of document IDs to index
            max_concurrent: Maximum concurrent processing
            
        Returns:
            Dict with batch processing results
        """
        
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_single_document(doc_id: str) -> Dict:
            async with semaphore:
                return await self.process_and_index_document(doc_id)
        
        # Process documents concurrently
        tasks = [process_single_document(doc_id) for doc_id in document_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Compile results
        successful = []
        failed = []
        
        for i, result in enumerate(results):
            doc_id = document_ids[i]
            
            if isinstance(result, Exception):
                failed.append({
                    "document_id": doc_id,
                    "error": str(result)
                })
            elif result.get("status") == "completed":
                successful.append(result)
            else:
                failed.append(result)
        
        return {
            "total_documents": len(document_ids),
            "successful": len(successful),
            "failed": len(failed),
            "success_rate": len(successful) / len(document_ids) if document_ids else 0,
            "successful_documents": successful,
            "failed_documents": failed,
            "batch_completed_at": datetime.utcnow().isoformat()
        }
    
    async def get_rag_status_for_documents(
        self, 
        document_ids: List[str]
    ) -> Dict:
        """Get RAG indexing status for multiple documents"""
        
        status_results = {}
        
        for doc_id in document_ids:
            try:
                status = await self.indexing_service.get_indexing_status(doc_id)
                
                if status:
                    status_results[doc_id] = {
                        "rag_indexed": status["embeddings_created"],
                        "chunks_count": status["chunks_count"],
                        "indexing_date": status["indexing_completed_at"],
                        "status": "indexed" if status["embeddings_created"] else "indexing",
                        "error": status["indexing_error"]
                    }
                else:
                    status_results[doc_id] = {
                        "rag_indexed": False,
                        "status": "not_indexed"
                    }
                    
            except Exception as e:
                status_results[doc_id] = {
                    "rag_indexed": False,
                    "status": "error",
                    "error": str(e)
                }
        
        return status_results
    
    async def migrate_existing_documents(self) -> Dict:
        """
        Migrate all existing processed documents to RAG system
        This is useful for initial setup when RAG is added to existing system
        """
        try:
            # Get all processed documents
            processed_documents = await self.batch_service.get_all_processed_documents()
            
            if not processed_documents:
                return {
                    "status": "no_documents",
                    "message": "No processed documents found for migration"
                }
            
            document_ids = [doc["id"] for doc in processed_documents]
            
            logger.info(f"Starting migration of {len(document_ids)} documents to RAG system")
            
            # Batch process all documents
            migration_result = await self.batch_index_documents(
                document_ids=document_ids,
                max_concurrent=5  # Higher concurrency for migration
            )
            
            migration_result["migration_type"] = "full_migration"
            migration_result["total_existing_documents"] = len(document_ids)
            
            logger.info(f"Migration completed: {migration_result['successful']}/{migration_result['total_documents']} documents indexed")
            
            return migration_result
            
        except Exception as e:
            logger.error(f"Document migration failed: {e}")
            return {
                "status": "migration_failed",
                "error": str(e)
            }


# CLI interface for manual operations
async def main():
    """CLI interface for RAG integration operations"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python integrate_rag_system.py migrate_all")
        print("  python integrate_rag_system.py process_document <document_id>")
        print("  python integrate_rag_system.py batch_process <doc_id1> <doc_id2> ...")
        return
    
    integration_service = RAGIntegrationService()
    command = sys.argv[1]
    
    if command == "migrate_all":
        print("Starting full migration of existing documents...")
        result = await integration_service.migrate_existing_documents()
        print(f"Migration result: {result}")
        
    elif command == "process_document" and len(sys.argv) > 2:
        document_id = sys.argv[2]
        print(f"Processing document {document_id} for RAG...")
        result = await integration_service.process_and_index_document(document_id)
        print(f"Processing result: {result}")
        
    elif command == "batch_process" and len(sys.argv) > 2:
        document_ids = sys.argv[2:]
        print(f"Batch processing {len(document_ids)} documents...")
        result = await integration_service.batch_index_documents(document_ids)
        print(f"Batch result: {result}")
        
    else:
        print("Invalid command or missing arguments")


if __name__ == "__main__":
    asyncio.run(main())