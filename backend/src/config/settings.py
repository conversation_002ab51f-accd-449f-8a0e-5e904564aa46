"""
Configuration settings for the RAG system.

This module provides centralized configuration management using environment
variables with sensible defaults for the semantic processing service.
"""

import os
from typing import Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass

from .embedding_models import get_model_from_env, EmbeddingModels


@dataclass
class SemanticProcessingSettings:
    """Settings for semantic processing service."""
    model_name: str
    cache_size: int
    cache_ttl_hours: int
    batch_size: int
    max_workers: int
    normalize_embeddings: bool
    enable_cache: bool
    similarity_threshold: float


@dataclass
class RAGSystemSettings:
    """Settings for the RAG system."""
    # Database settings
    database_url: str
    max_db_connections: int
    
    # Vector search settings
    default_similarity_threshold: float
    max_search_results: int
    
    # Response generation settings
    max_response_tokens: int
    include_citations: bool
    
    # Performance settings
    enable_analytics: bool
    log_level: str


class Settings:
    """
    Centralized settings management for the RAG system.
    
    This class loads configuration from environment variables with
    sensible defaults and validation.
    """
    
    def __init__(self):
        self._load_settings()
    
    def _load_settings(self) -> None:
        """Load all settings from environment variables."""
        self.semantic_processing = self._load_semantic_processing_settings()
        self.rag_system = self._load_rag_system_settings()
    
    def _load_semantic_processing_settings(self) -> SemanticProcessingSettings:
        """Load semantic processing settings."""
        return SemanticProcessingSettings(
            model_name=get_model_from_env(),
            cache_size=int(os.getenv("EMBEDDING_CACHE_SIZE", "1000")),
            cache_ttl_hours=int(os.getenv("EMBEDDING_CACHE_TTL_HOURS", "24")),
            batch_size=int(os.getenv("EMBEDDING_BATCH_SIZE", "32")),
            max_workers=int(os.getenv("EMBEDDING_MAX_WORKERS", "4")),
            normalize_embeddings=os.getenv("NORMALIZE_EMBEDDINGS", "true").lower() == "true",
            enable_cache=os.getenv("ENABLE_EMBEDDING_CACHE", "true").lower() == "true",
            similarity_threshold=float(os.getenv("DEFAULT_SIMILARITY_THRESHOLD", "0.7"))
        )
    
    def _load_rag_system_settings(self) -> RAGSystemSettings:
        """Load RAG system settings."""
        return RAGSystemSettings(
            database_url=os.getenv("DATABASE_URL", "postgresql://localhost/simple_class"),
            max_db_connections=int(os.getenv("MAX_DB_CONNECTIONS", "20")),
            default_similarity_threshold=float(os.getenv("DEFAULT_SIMILARITY_THRESHOLD", "0.7")),
            max_search_results=int(os.getenv("MAX_SEARCH_RESULTS", "10")),
            max_response_tokens=int(os.getenv("MAX_RESPONSE_TOKENS", "2000")),
            include_citations=os.getenv("INCLUDE_CITATIONS", "true").lower() == "true",
            enable_analytics=os.getenv("ENABLE_RAG_ANALYTICS", "true").lower() == "true",
            log_level=os.getenv("LOG_LEVEL", "INFO")
        )
    
    def get_model_config(self) -> Dict[str, Any]:
        """Get model configuration for semantic processing."""
        return {
            "model_name": self.semantic_processing.model_name,
            "cache_size": self.semantic_processing.cache_size,
            "batch_size": self.semantic_processing.batch_size,
            "max_workers": self.semantic_processing.max_workers,
            "normalize_embeddings": self.semantic_processing.normalize_embeddings,
            "enable_cache": self.semantic_processing.enable_cache
        }
    
    def get_environment_info(self) -> Dict[str, Any]:
        """Get information about the current environment."""
        return {
            "python_version": os.sys.version,
            "environment": os.getenv("ENVIRONMENT", "development"),
            "debug_mode": os.getenv("DEBUG", "false").lower() == "true",
            "model_name": self.semantic_processing.model_name,
            "cache_enabled": self.semantic_processing.enable_cache,
            "analytics_enabled": self.rag_system.enable_analytics
        }
    
    def validate_settings(self) -> Dict[str, Any]:
        """Validate current settings and return validation results."""
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Validate semantic processing settings
        if self.semantic_processing.cache_size <= 0:
            validation_results["errors"].append("Cache size must be positive")
            validation_results["valid"] = False
        
        if self.semantic_processing.batch_size <= 0:
            validation_results["errors"].append("Batch size must be positive")
            validation_results["valid"] = False
        
        if not (0.0 <= self.semantic_processing.similarity_threshold <= 1.0):
            validation_results["errors"].append("Similarity threshold must be between 0.0 and 1.0")
            validation_results["valid"] = False
        
        # Validate model availability
        if self.semantic_processing.model_name not in [config.model_id for config in EmbeddingModels.MODELS.values()]:
            validation_results["warnings"].append(f"Model {self.semantic_processing.model_name} not in recommended models")
        
        # Validate RAG system settings
        if self.rag_system.max_search_results <= 0:
            validation_results["errors"].append("Max search results must be positive")
            validation_results["valid"] = False
        
        if self.rag_system.max_response_tokens <= 0:
            validation_results["errors"].append("Max response tokens must be positive")
            validation_results["valid"] = False
        
        return validation_results
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert settings to dictionary format."""
        return {
            "semantic_processing": {
                "model_name": self.semantic_processing.model_name,
                "cache_size": self.semantic_processing.cache_size,
                "cache_ttl_hours": self.semantic_processing.cache_ttl_hours,
                "batch_size": self.semantic_processing.batch_size,
                "max_workers": self.semantic_processing.max_workers,
                "normalize_embeddings": self.semantic_processing.normalize_embeddings,
                "enable_cache": self.semantic_processing.enable_cache,
                "similarity_threshold": self.semantic_processing.similarity_threshold
            },
            "rag_system": {
                "database_url": "***" if "password" in self.rag_system.database_url.lower() else self.rag_system.database_url,
                "max_db_connections": self.rag_system.max_db_connections,
                "default_similarity_threshold": self.rag_system.default_similarity_threshold,
                "max_search_results": self.rag_system.max_search_results,
                "max_response_tokens": self.rag_system.max_response_tokens,
                "include_citations": self.rag_system.include_citations,
                "enable_analytics": self.rag_system.enable_analytics,
                "log_level": self.rag_system.log_level
            }
        }


# Global settings instance
settings = Settings()


# Utility functions for common configuration tasks
def get_semantic_processing_config() -> Dict[str, Any]:
    """Get configuration for semantic processing service."""
    return settings.get_model_config()


def get_recommended_model_for_language(language: str = "pt") -> str:
    """Get recommended model for a specific language."""
    if language == "pt":
        return EmbeddingModels.get_recommended_for_language("pt")[0]
    return EmbeddingModels.MODELS["default"].model_id


def create_env_template() -> str:
    """Create a template .env file with all available settings."""
    template = """# RAG System Configuration

# Semantic Processing Settings
EMBEDDING_MODEL=all-MiniLM-L6-v2
EMBEDDING_MODEL_TYPE=multilingual
EMBEDDING_CACHE_SIZE=1000
EMBEDDING_CACHE_TTL_HOURS=24
EMBEDDING_BATCH_SIZE=32
EMBEDDING_MAX_WORKERS=4
NORMALIZE_EMBEDDINGS=true
ENABLE_EMBEDDING_CACHE=true

# RAG System Settings
DEFAULT_SIMILARITY_THRESHOLD=0.7
MAX_SEARCH_RESULTS=10
MAX_RESPONSE_TOKENS=2000
INCLUDE_CITATIONS=true
ENABLE_RAG_ANALYTICS=true

# Database Settings
DATABASE_URL=postgresql://localhost/simple_class
MAX_DB_CONNECTIONS=20

# General Settings
ENVIRONMENT=development
DEBUG=false
LOG_LEVEL=INFO
LANGUAGE=pt
USE_CASE=legal_documents
"""
    return template


def save_env_template(file_path: str = ".env.template") -> None:
    """Save environment template to file."""
    template = create_env_template()
    with open(file_path, 'w') as f:
        f.write(template)
    print(f"Environment template saved to {file_path}")


# Export commonly used settings
__all__ = [
    "Settings",
    "SemanticProcessingSettings", 
    "RAGSystemSettings",
    "settings",
    "get_semantic_processing_config",
    "get_recommended_model_for_language",
    "create_env_template",
    "save_env_template"
]
