"""
Configuration module for the RAG system.
"""

from .embedding_models import (
    EmbeddingModels,
    ModelConfig,
    get_model_from_env,
    select_best_model_for_portuguese,
    select_model_for_performance,
    get_model_recommendations
)

from .settings import (
    Settings,
    SemanticProcessingSettings,
    RAGSystemSettings,
    settings,
    get_semantic_processing_config,
    get_recommended_model_for_language,
    create_env_template,
    save_env_template
)

__all__ = [
    # Embedding models
    "EmbeddingModels",
    "ModelConfig",
    "get_model_from_env",
    "select_best_model_for_portuguese",
    "select_model_for_performance",
    "get_model_recommendations",

    # Settings
    "Settings",
    "SemanticProcessingSettings",
    "RAGSystemSettings",
    "settings",
    "get_semantic_processing_config",
    "get_recommended_model_for_language",
    "create_env_template",
    "save_env_template"
]
