"""
Configuration for embedding models used in the RAG system.

This module defines model configurations optimized for different languages
and use cases, with special focus on Portuguese language support.
"""

from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class ModelConfig:
    """Configuration for an embedding model."""
    name: str
    model_id: str
    description: str
    languages: List[str]
    embedding_dim: int
    max_seq_length: int
    performance_score: float  # 0-10 scale
    memory_usage: str  # "low", "medium", "high"
    recommended_for: List[str]


class EmbeddingModels:
    """
    Registry of embedding models optimized for different use cases.
    
    Based on research findings:
    - Multilingual models perform better for Portuguese
    - Smaller models (MiniLM) offer good balance of speed/quality
    - Larger models (mpnet) provide better accuracy for complex queries
    """
    
    # Model configurations
    MODELS: Dict[str, ModelConfig] = {
        "default": ModelConfig(
            name="Default (MiniLM-L6-v2)",
            model_id="all-MiniLM-L6-v2",
            description="Fast and efficient model for general use",
            languages=["en", "pt", "es", "fr", "de", "it"],
            embedding_dim=384,
            max_seq_length=256,
            performance_score=7.5,
            memory_usage="low",
            recommended_for=["general", "fast_processing", "low_memory"]
        ),
        
        "multilingual": ModelConfig(
            name="Multilingual Paraphrase (MiniLM-L12-v2)",
            model_id="paraphrase-multilingual-MiniLM-L12-v2",
            description="Optimized for multilingual semantic similarity",
            languages=["pt", "en", "es", "fr", "de", "it", "nl", "pl", "ru", "zh"],
            embedding_dim=384,
            max_seq_length=128,
            performance_score=8.2,
            memory_usage="medium",
            recommended_for=["multilingual", "portuguese", "semantic_search"]
        ),
        
        "portuguese": ModelConfig(
            name="Portuguese BERT (NeuralMind)",
            model_id="neuralmind/bert-base-portuguese-cased",
            description="Specialized model for Portuguese language",
            languages=["pt"],
            embedding_dim=768,
            max_seq_length=512,
            performance_score=8.8,
            memory_usage="high",
            recommended_for=["portuguese", "high_accuracy", "legal_documents"]
        ),
        
        "large": ModelConfig(
            name="Large MPNet (all-mpnet-base-v2)",
            model_id="all-mpnet-base-v2",
            description="High-quality embeddings for complex queries",
            languages=["en", "pt", "es", "fr", "de"],
            embedding_dim=768,
            max_seq_length=384,
            performance_score=9.1,
            memory_usage="high",
            recommended_for=["high_accuracy", "complex_queries", "research"]
        ),
        
        "fast": ModelConfig(
            name="Fast MiniLM (all-MiniLM-L6-v2)",
            model_id="all-MiniLM-L6-v2",
            description="Fastest processing with good quality",
            languages=["en", "pt", "es", "fr", "de"],
            embedding_dim=384,
            max_seq_length=256,
            performance_score=7.5,
            memory_usage="low",
            recommended_for=["real_time", "high_throughput", "api_endpoints"]
        ),
        
        "legal": ModelConfig(
            name="Legal Domain (E5-base-v2)",
            model_id="intfloat/e5-base-v2",
            description="Optimized for legal and formal documents",
            languages=["en", "pt", "es", "fr"],
            embedding_dim=768,
            max_seq_length=512,
            performance_score=8.5,
            memory_usage="medium",
            recommended_for=["legal_documents", "formal_text", "compliance"]
        )
    }
    
    @classmethod
    def get_model_config(cls, model_type: str) -> ModelConfig:
        """Get configuration for a specific model type."""
        if model_type not in cls.MODELS:
            raise ValueError(f"Unknown model type: {model_type}. Available: {list(cls.MODELS.keys())}")
        return cls.MODELS[model_type]
    
    @classmethod
    def get_recommended_for_language(cls, language: str) -> List[str]:
        """Get recommended models for a specific language."""
        recommended = []
        for model_type, config in cls.MODELS.items():
            if language in config.languages:
                recommended.append(model_type)
        
        # Sort by performance score
        recommended.sort(key=lambda x: cls.MODELS[x].performance_score, reverse=True)
        return recommended
    
    @classmethod
    def get_recommended_for_use_case(cls, use_case: str) -> List[str]:
        """Get recommended models for a specific use case."""
        recommended = []
        for model_type, config in cls.MODELS.items():
            if use_case in config.recommended_for:
                recommended.append(model_type)
        
        # Sort by performance score
        recommended.sort(key=lambda x: cls.MODELS[x].performance_score, reverse=True)
        return recommended
    
    @classmethod
    def get_all_models(cls) -> Dict[str, ModelConfig]:
        """Get all available model configurations."""
        return cls.MODELS.copy()
    
    @classmethod
    def get_model_summary(cls) -> Dict[str, Dict[str, Any]]:
        """Get a summary of all models for display purposes."""
        summary = {}
        for model_type, config in cls.MODELS.items():
            summary[model_type] = {
                "name": config.name,
                "description": config.description,
                "languages": config.languages,
                "embedding_dim": config.embedding_dim,
                "performance_score": config.performance_score,
                "memory_usage": config.memory_usage,
                "recommended_for": config.recommended_for
            }
        return summary


# Environment-based model selection
def get_model_from_env() -> str:
    """
    Get model configuration from environment variables.
    
    Environment variables:
    - EMBEDDING_MODEL: Direct model ID
    - EMBEDDING_MODEL_TYPE: Model type from registry
    - LANGUAGE: Language code for automatic selection
    - USE_CASE: Use case for automatic selection
    """
    import os
    
    # Direct model specification
    if os.getenv("EMBEDDING_MODEL"):
        return os.getenv("EMBEDDING_MODEL")
    
    # Model type from registry
    model_type = os.getenv("EMBEDDING_MODEL_TYPE", "default")
    if model_type in EmbeddingModels.MODELS:
        return EmbeddingModels.MODELS[model_type].model_id
    
    # Language-based selection
    language = os.getenv("LANGUAGE", "pt")
    if language == "pt":
        # For Portuguese, prefer multilingual model
        return EmbeddingModels.MODELS["multilingual"].model_id
    
    # Use case-based selection
    use_case = os.getenv("USE_CASE", "general")
    recommended = EmbeddingModels.get_recommended_for_use_case(use_case)
    if recommended:
        return EmbeddingModels.MODELS[recommended[0]].model_id
    
    # Fallback to default
    return EmbeddingModels.MODELS["default"].model_id


# Utility functions for model selection
def select_best_model_for_portuguese() -> str:
    """Select the best model for Portuguese language processing."""
    # For Portuguese legal documents, prefer multilingual model
    # as it balances performance and compatibility
    return EmbeddingModels.MODELS["multilingual"].model_id


def select_model_for_performance(memory_constraint: str = "medium") -> str:
    """
    Select model based on performance requirements.
    
    Args:
        memory_constraint: "low", "medium", or "high"
    """
    suitable_models = [
        (model_type, config) for model_type, config in EmbeddingModels.MODELS.items()
        if config.memory_usage == memory_constraint or 
           (memory_constraint == "medium" and config.memory_usage in ["low", "medium"]) or
           (memory_constraint == "high")
    ]
    
    if not suitable_models:
        return EmbeddingModels.MODELS["default"].model_id
    
    # Select highest performance model within constraints
    best_model = max(suitable_models, key=lambda x: x[1].performance_score)
    return best_model[1].model_id


def get_model_recommendations() -> Dict[str, str]:
    """Get model recommendations for common scenarios."""
    return {
        "portuguese_legal": select_best_model_for_portuguese(),
        "fast_api": EmbeddingModels.MODELS["fast"].model_id,
        "high_accuracy": EmbeddingModels.MODELS["large"].model_id,
        "low_memory": EmbeddingModels.MODELS["default"].model_id,
        "multilingual": EmbeddingModels.MODELS["multilingual"].model_id
    }
