"""
Document Indexing Service for RAG System
Handles storage and retrieval of document chunks with vector embeddings
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import numpy as np
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from backend.src.database.connection import get_db_session
from backend.src.services.semantic_processing_service import SemanticProcessingService


logger = logging.getLogger(__name__)


class DocumentIndexingService:
    """Service for indexing documents with vector embeddings for RAG retrieval"""
    
    def __init__(self):
        self.semantic_service = SemanticProcessingService()
    
    async def index_document_chunks(
        self,
        document_id: str,
        chunks: List[Dict],
        force_reindex: bool = False
    ) -> Dict:
        """
        Index document chunks with vector embeddings
        
        Args:
            document_id: UUID of the document
            chunks: List of chunk dictionaries with content and metadata
            force_reindex: Whether to reindex if already exists
            
        Returns:
            Dict with indexing results and statistics
        """
        async with get_db_session() as session:
            try:
                # Check if document is already indexed
                if not force_reindex:
                    existing = await self._check_existing_index(session, document_id)
                    if existing:
                        logger.info(f"Document {document_id} already indexed, skipping")
                        return {
                            "status": "already_indexed",
                            "document_id": document_id,
                            "chunks_count": existing["chunks_count"]
                        }
                
                # Start indexing process
                await self._update_indexing_status(
                    session, document_id, started=True
                )
                
                # Clear existing chunks if reindexing
                if force_reindex:
                    await self._clear_existing_chunks(session, document_id)
                
                # Process chunks and create embeddings
                indexed_chunks = []
                failed_chunks = []
                
                for i, chunk in enumerate(chunks):
                    try:
                        # Generate embedding for chunk content
                        embedding = await self.semantic_service.generate_embedding(
                            chunk["content"]
                        )
                        
                        # Store chunk with embedding
                        chunk_id = await self._store_chunk_with_embedding(
                            session=session,
                            document_id=document_id,
                            chunk_index=i,
                            content=chunk["content"],
                            embedding=embedding,
                            page_number=chunk.get("page_number"),
                            confidence_score=chunk.get("confidence_score", 1.0),
                            area_classification=chunk.get("area_classification"),
                            metadata=chunk.get("metadata", {})
                        )
                        
                        indexed_chunks.append(chunk_id)
                        
                    except Exception as e:
                        logger.error(f"Failed to index chunk {i}: {e}")
                        failed_chunks.append({"index": i, "error": str(e)})
                
                # Update indexing status as completed
                await self._update_indexing_status(
                    session, document_id, completed=True, 
                    chunks_count=len(indexed_chunks)
                )
                
                await session.commit()
                
                result = {
                    "status": "completed",
                    "document_id": document_id,
                    "chunks_indexed": len(indexed_chunks),
                    "chunks_failed": len(failed_chunks),
                    "success_rate": len(indexed_chunks) / len(chunks) if chunks else 0
                }
                
                if failed_chunks:
                    result["failed_chunks"] = failed_chunks
                
                logger.info(f"Indexed {len(indexed_chunks)} chunks for document {document_id}")
                return result
                
            except Exception as e:
                # Update status with error
                await self._update_indexing_status(
                    session, document_id, error=str(e)
                )
                await session.rollback()
                logger.error(f"Document indexing failed: {e}")
                raise
    
    async def _check_existing_index(
        self, session: AsyncSession, document_id: str
    ) -> Optional[Dict]:
        """Check if document is already indexed"""
        query = text("""
            SELECT chunks_count, embeddings_created, indexing_completed_at
            FROM document_indexing_status 
            WHERE document_id = :document_id 
            AND embeddings_created = true
            AND indexing_completed_at IS NOT NULL
        """)
        
        result = await session.execute(query, {"document_id": document_id})
        row = result.fetchone()
        
        if row:
            return {
                "chunks_count": row[0],
                "embeddings_created": row[1],
                "indexing_completed_at": row[2]
            }
        return None
    
    async def _clear_existing_chunks(
        self, session: AsyncSession, document_id: str
    ):
        """Clear existing chunks for reindexing"""
        query = text("""
            DELETE FROM document_chunks WHERE document_id = :document_id
        """)
        await session.execute(query, {"document_id": document_id})
    
    async def _store_chunk_with_embedding(
        self,
        session: AsyncSession,
        document_id: str,
        chunk_index: int,
        content: str,
        embedding: np.ndarray,
        page_number: Optional[int] = None,
        confidence_score: float = 1.0,
        area_classification: Optional[str] = None,
        metadata: Optional[Dict] = None
    ) -> str:
        """Store a chunk with its vector embedding"""
        
        # Convert numpy array to list for PostgreSQL vector type
        embedding_list = embedding.tolist()
        
        query = text("""
            INSERT INTO document_chunks (
                document_id, chunk_index, content, embedding,
                page_number, confidence_score, area_classification,
                token_count, metadata
            ) VALUES (
                :document_id, :chunk_index, :content, :embedding,
                :page_number, :confidence_score, :area_classification,
                :token_count, :metadata
            ) RETURNING id
        """)
        
        # Calculate token count (rough estimation)
        token_count = len(content.split())
        
        result = await session.execute(query, {
            "document_id": document_id,
            "chunk_index": chunk_index,
            "content": content,
            "embedding": embedding_list,
            "page_number": page_number,
            "confidence_score": confidence_score,
            "area_classification": area_classification,
            "token_count": token_count,
            "metadata": metadata or {}
        })
        
        return str(result.fetchone()[0])
    
    async def _update_indexing_status(
        self,
        session: AsyncSession,
        document_id: str,
        started: bool = False,
        completed: bool = False,
        chunks_count: Optional[int] = None,
        error: Optional[str] = None
    ):
        """Update document indexing status"""
        
        if started:
            query = text("""
                INSERT INTO document_indexing_status (
                    document_id, indexing_started_at, embeddings_created
                ) VALUES (
                    :document_id, :started_at, false
                ) ON CONFLICT (document_id) DO UPDATE SET
                    indexing_started_at = :started_at,
                    embeddings_created = false,
                    indexing_error = NULL
            """)
            await session.execute(query, {
                "document_id": document_id,
                "started_at": datetime.utcnow()
            })
        
        elif completed:
            query = text("""
                UPDATE document_indexing_status SET
                    chunks_count = :chunks_count,
                    embeddings_created = true,
                    indexing_completed_at = :completed_at,
                    indexing_error = NULL
                WHERE document_id = :document_id
            """)
            await session.execute(query, {
                "document_id": document_id,
                "chunks_count": chunks_count,
                "completed_at": datetime.utcnow()
            })
        
        elif error:
            query = text("""
                UPDATE document_indexing_status SET
                    indexing_error = :error,
                    embeddings_created = false
                WHERE document_id = :document_id
            """)
            await session.execute(query, {
                "document_id": document_id,
                "error": error
            })
    
    async def get_document_chunks(
        self, 
        document_id: str, 
        limit: Optional[int] = None
    ) -> List[Dict]:
        """Get all chunks for a document"""
        async with get_db_session() as session:
            query = text("""
                SELECT id, chunk_index, content, page_number, 
                       confidence_score, area_classification, metadata
                FROM document_chunks 
                WHERE document_id = :document_id
                ORDER BY chunk_index
                LIMIT :limit
            """)
            
            result = await session.execute(query, {
                "document_id": document_id,
                "limit": limit or 1000
            })
            
            return [
                {
                    "id": str(row[0]),
                    "chunk_index": row[1],
                    "content": row[2],
                    "page_number": row[3],
                    "confidence_score": row[4],
                    "area_classification": row[5],
                    "metadata": row[6]
                }
                for row in result.fetchall()
            ]
    
    async def get_indexing_status(self, document_id: str) -> Optional[Dict]:
        """Get indexing status for a document"""
        async with get_db_session() as session:
            query = text("""
                SELECT chunks_count, embeddings_created, 
                       indexing_started_at, indexing_completed_at, indexing_error
                FROM document_indexing_status
                WHERE document_id = :document_id
            """)
            
            result = await session.execute(query, {"document_id": document_id})
            row = result.fetchone()
            
            if row:
                return {
                    "chunks_count": row[0],
                    "embeddings_created": row[1],
                    "indexing_started_at": row[2],
                    "indexing_completed_at": row[3],
                    "indexing_error": row[4]
                }
            return None
    
    async def delete_document_index(self, document_id: str) -> bool:
        """Delete all chunks and indexing data for a document"""
        async with get_db_session() as session:
            try:
                # Delete chunks (cascade will handle indexing status)
                query = text("""
                    DELETE FROM document_chunks WHERE document_id = :document_id
                """)
                result = await session.execute(query, {"document_id": document_id})
                
                await session.commit()
                
                deleted_count = result.rowcount
                logger.info(f"Deleted {deleted_count} chunks for document {document_id}")
                return True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to delete document index: {e}")
                return False