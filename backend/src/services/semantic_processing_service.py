"""
SemanticProcessingService for unified embedding generation in RAG system.

This service provides a centralized interface for semantic processing using
sentence-transformers, consolidating patterns from existing services like
SemanticCache and SemanticSummarizer.
"""

import logging
import numpy as np
import asyncio
from concurrent.futures import ThreadPoolExecutor
from typing import List, Optional, Union, Dict, Any
from pathlib import Path
import os
import hashlib
import time
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import OrderedDict

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class EmbeddingConfig:
    """Configuration for embedding models."""
    model_name: str
    cache_size: int
    batch_size: int
    max_workers: int
    normalize_embeddings: bool


@dataclass
class CacheEntry:
    """Entry in the embedding cache."""
    embedding: np.ndarray
    timestamp: datetime
    access_count: int = 0
    last_accessed: Optional[datetime] = None

    def __post_init__(self):
        if self.last_accessed is None:
            self.last_accessed = self.timestamp


class LRUEmbeddingCache:
    """
    LRU cache for embeddings with TTL and statistics.

    Features:
    - LRU eviction policy
    - TTL (time-to-live) for entries
    - Access statistics
    - Memory usage tracking
    """

    def __init__(self, max_size: int = 1000, ttl_hours: int = 24):
        self.max_size = max_size
        self.ttl = timedelta(hours=ttl_hours)
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "expired": 0
        }

    def _generate_key(self, text: str) -> str:
        """Generate cache key from text."""
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def _cleanup_expired(self) -> None:
        """Remove expired entries."""
        now = datetime.now()
        expired_keys = []

        for key, entry in self.cache.items():
            if now - entry.timestamp > self.ttl:
                expired_keys.append(key)

        for key in expired_keys:
            del self.cache[key]
            self.stats["expired"] += 1

    def get(self, text: str) -> Optional[np.ndarray]:
        """Get embedding from cache."""
        key = self._generate_key(text)

        if key in self.cache:
            entry = self.cache[key]

            # Check if expired
            if datetime.now() - entry.timestamp > self.ttl:
                del self.cache[key]
                self.stats["expired"] += 1
                self.stats["misses"] += 1
                return None

            # Move to end (most recently used)
            self.cache.move_to_end(key)

            # Update access statistics
            entry.access_count += 1
            entry.last_accessed = datetime.now()

            self.stats["hits"] += 1
            return entry.embedding

        self.stats["misses"] += 1
        return None

    def put(self, text: str, embedding: np.ndarray) -> None:
        """Store embedding in cache."""
        key = self._generate_key(text)

        # Remove if already exists
        if key in self.cache:
            del self.cache[key]

        # Evict oldest if at capacity
        while len(self.cache) >= self.max_size:
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
            self.stats["evictions"] += 1

        # Add new entry
        entry = CacheEntry(
            embedding=embedding,
            timestamp=datetime.now()
        )
        self.cache[key] = entry

        # Periodic cleanup
        if len(self.cache) % 100 == 0:
            self._cleanup_expired()

    def clear(self) -> None:
        """Clear all cache entries."""
        self.cache.clear()
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "expired": 0
        }

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = (self.stats["hits"] / max(total_requests, 1)) * 100

        return {
            **self.stats,
            "size": len(self.cache),
            "max_size": self.max_size,
            "hit_rate": hit_rate,
            "memory_usage_mb": self._estimate_memory_usage()
        }

    def _estimate_memory_usage(self) -> float:
        """Estimate memory usage in MB."""
        if not self.cache:
            return 0.0

        # Estimate based on embedding size and number of entries
        sample_entry = next(iter(self.cache.values()))
        embedding_size = sample_entry.embedding.nbytes
        total_size = embedding_size * len(self.cache)

        return total_size / (1024 * 1024)


class SemanticProcessingService:
    """
    Unified semantic processing service for RAG system.
    
    This service consolidates embedding generation patterns from:
    - SemanticCache._compute_embedding()
    - SemanticSummarizer._compute_chunk_embeddings()
    - analisador_embeddings.processar_embeddings()
    
    Features:
    - Synchronous and asynchronous embedding generation
    - Batch processing with ThreadPoolExecutor
    - Configurable models with multilingual support
    - Optional caching for performance
    - Error handling and fallback mechanisms
    """
    
    # Recommended models for different use cases
    MODELS = {
        "default": "all-MiniLM-L6-v2",
        "multilingual": "paraphrase-multilingual-MiniLM-L12-v2",
        "portuguese": "neuralmind/bert-base-portuguese-cased",
        "large": "all-mpnet-base-v2",
        "fast": "all-MiniLM-L6-v2"
    }
    
    def __init__(self, 
                 model_name: str = None,
                 cache_size: int = 1000,
                 batch_size: int = 32,
                 max_workers: int = 4,
                 normalize_embeddings: bool = True,
                 enable_cache: bool = True):
        """
        Initialize the semantic processing service.
        
        Args:
            model_name: Name of the sentence transformer model
            cache_size: Maximum number of embeddings to cache
            batch_size: Batch size for processing multiple texts
            max_workers: Number of threads for async processing
            normalize_embeddings: Whether to normalize embeddings
            enable_cache: Whether to enable embedding caching
        """
        # Use environment variable or default model
        self.model_name = (
            model_name or 
            os.getenv("EMBEDDING_MODEL", self.MODELS["default"])
        )
        
        self.config = EmbeddingConfig(
            model_name=self.model_name,
            cache_size=cache_size,
            batch_size=batch_size,
            max_workers=max_workers,
            normalize_embeddings=normalize_embeddings
        )
        
        # Initialize components
        self.embedding_model = None
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        
        # Advanced cache for embeddings
        self.enable_cache = enable_cache
        self.embedding_cache = LRUEmbeddingCache(
            max_size=cache_size,
            ttl_hours=int(os.getenv("EMBEDDING_CACHE_TTL_HOURS", "24"))
        ) if enable_cache else None
        
        # Statistics
        self.stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "batch_requests": 0,
            "errors": 0
        }
        
        # Initialize model
        self._initialize_model()
        
        logger.info(f"Initialized SemanticProcessingService with model: {self.model_name}")
    
    def _initialize_model(self) -> None:
        """Initialize sentence transformer model (pattern from SemanticCache)."""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.error("sentence-transformers not available. Install with: pip install sentence-transformers")
            raise ImportError("sentence-transformers package is required")
        
        try:
            self.embedding_model = SentenceTransformer(self.model_name)
            logger.info(f"Successfully loaded embedding model: {self.model_name}")
            
            # Log model info
            if hasattr(self.embedding_model, 'get_sentence_embedding_dimension'):
                dim = self.embedding_model.get_sentence_embedding_dimension()
                logger.info(f"Model embedding dimension: {dim}")
                
        except Exception as e:
            logger.error(f"Failed to initialize embedding model '{self.model_name}': {e}")
            raise RuntimeError(f"Could not load embedding model: {e}")
    
    def generate_embedding(self, text: str) -> np.ndarray:
        """
        Generate embedding for a single text (compatible with RAG services).
        
        This method follows the pattern from SemanticCache._compute_embedding()
        and is designed to be a drop-in replacement for RAG services.
        
        Args:
            text: Input text to embed
            
        Returns:
            numpy array containing the embedding
            
        Raises:
            RuntimeError: If model is not initialized or embedding fails
        """
        if not self.embedding_model:
            raise RuntimeError("Embedding model not initialized")
        
        if not text or not text.strip():
            raise ValueError("Input text cannot be empty")
        
        self.stats["total_requests"] += 1

        # Check cache first
        if self.embedding_cache:
            cached_embedding = self.embedding_cache.get(text)
            if cached_embedding is not None:
                self.stats["cache_hits"] += 1
                logger.debug(f"Cache hit for text: {text[:50]}...")
                return cached_embedding
        
        try:
            # Generate embedding
            embedding = self.embedding_model.encode(text)
            
            # Normalize if requested
            if self.config.normalize_embeddings:
                embedding = embedding / np.linalg.norm(embedding)
            
            # Cache the result
            if self.embedding_cache:
                self.embedding_cache.put(text, embedding)
                self.stats["cache_misses"] += 1
            
            logger.debug(f"Generated embedding for text: {text[:50]}...")
            return embedding
            
        except Exception as e:
            self.stats["errors"] += 1
            logger.error(f"Error generating embedding: {e}")
            raise RuntimeError(f"Failed to generate embedding: {e}")
    
    async def generate_embeddings_batch(self, texts: List[str]) -> np.ndarray:
        """
        Generate embeddings for multiple texts asynchronously.
        
        This method follows the pattern from SemanticSummarizer._compute_chunk_embeddings()
        using ThreadPoolExecutor to avoid blocking the event loop.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            numpy array with shape (len(texts), embedding_dim)
            
        Raises:
            RuntimeError: If model is not initialized or batch processing fails
        """
        if not self.embedding_model:
            raise RuntimeError("Embedding model not initialized")
        
        if not texts:
            raise ValueError("Input texts list cannot be empty")
        
        self.stats["batch_requests"] += 1
        
        try:
            # Process in thread pool to avoid blocking event loop
            loop = asyncio.get_event_loop()
            embeddings = await loop.run_in_executor(
                self.thread_pool,
                self._encode_batch,
                texts
            )
            
            logger.info(f"Generated {len(embeddings)} embeddings in batch")
            return embeddings
            
        except Exception as e:
            self.stats["errors"] += 1
            logger.error(f"Error in batch embedding generation: {e}")
            raise RuntimeError(f"Failed to generate batch embeddings: {e}")
    
    def _encode_batch(self, texts: List[str]) -> np.ndarray:
        """Internal method to encode batch of texts."""
        # Filter out empty texts
        valid_texts = [text for text in texts if text and text.strip()]
        
        if not valid_texts:
            raise ValueError("No valid texts to process")
        
        # Generate embeddings
        embeddings = self.embedding_model.encode(valid_texts)
        
        # Normalize if requested
        if self.config.normalize_embeddings:
            norms = np.linalg.norm(embeddings, axis=1, keepdims=True)
            embeddings = embeddings / norms
        
        return embeddings
    

    
    def compute_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """
        Compute cosine similarity between two embeddings.
        
        Args:
            embedding1: First embedding
            embedding2: Second embedding
            
        Returns:
            Cosine similarity score between -1 and 1
        """
        try:
            similarity = np.dot(embedding1, embedding2) / (
                np.linalg.norm(embedding1) * np.linalg.norm(embedding2)
            )
            return float(similarity)
        except Exception as e:
            logger.error(f"Error computing similarity: {e}")
            return 0.0
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model."""
        info = {
            "model_name": self.model_name,
            "model_loaded": self.embedding_model is not None,
            "cache_enabled": self.embedding_cache is not None,
            "cache_stats": self.embedding_cache.get_stats() if self.embedding_cache else {},
            "processing_stats": self.stats.copy()
        }
        
        if self.embedding_model:
            try:
                info["embedding_dimension"] = self.embedding_model.get_sentence_embedding_dimension()
            except:
                info["embedding_dimension"] = "unknown"
        
        return info
    
    def clear_cache(self) -> None:
        """Clear the embedding cache."""
        if self.embedding_cache:
            self.embedding_cache.clear()
            logger.info("Embedding cache cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        total_requests = self.stats["total_requests"]
        hit_rate = (self.stats["cache_hits"] / max(total_requests, 1)) * 100

        stats = {
            **self.stats,
            "cache_hit_rate": hit_rate
        }

        if self.embedding_cache:
            stats["cache_stats"] = self.embedding_cache.get_stats()

        return stats
    
    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=True)


# Factory functions for easy instantiation
def create_semantic_service(model_type: str = "default", **kwargs) -> SemanticProcessingService:
    """
    Create a SemanticProcessingService with predefined model configurations.
    
    Args:
        model_type: Type of model ("default", "multilingual", "portuguese", "large", "fast")
        **kwargs: Additional configuration parameters
        
    Returns:
        Configured SemanticProcessingService instance
    """
    model_name = SemanticProcessingService.MODELS.get(model_type, model_type)
    return SemanticProcessingService(model_name=model_name, **kwargs)


def create_multilingual_service(**kwargs) -> SemanticProcessingService:
    """Create service optimized for multilingual processing."""
    return create_semantic_service("multilingual", **kwargs)


def create_portuguese_service(**kwargs) -> SemanticProcessingService:
    """Create service optimized for Portuguese language."""
    return create_semantic_service("portuguese", **kwargs)
