"""
Response Generation Service for RAG System
Generates contextual responses with precise source attribution
"""

import logging
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import asyncio
from collections import Counter

from backend.src.services.semantic_processing_service import SemanticProcessingService


logger = logging.getLogger(__name__)


class ResponseGenerationService:
    """Service for generating context-aware responses with source attribution"""
    
    def __init__(self):
        self.semantic_service = SemanticProcessingService()
        
        # Response templates for different query types
        self.response_templates = {
            "definition": "Com base nos documentos analisados, {concept} pode ser definido como: {definition}",
            "procedure": "O procedimento descrito nos documentos indica que: {steps}",
            "comparison": "Analisando os documentos, as principais diferenças são: {differences}",
            "factual": "Segundo a documentação: {facts}",
            "default": "Com base na análise dos documentos: {content}"
        }
    
    async def generate_response(
        self,
        query: str,
        retrieved_chunks: List[Dict],
        query_analysis: Dict,
        max_tokens: int = 2000
    ) -> Dict:
        """
        Generate contextual response with source attribution
        
        Args:
            query: Original user query
            retrieved_chunks: List of retrieved document chunks
            query_analysis: Analysis of the query intent and complexity
            max_tokens: Maximum tokens for response generation
            
        Returns:
            Dict with response text, citations, and metadata
        """
        try:
            if not retrieved_chunks:
                return {
                    "response": "Não foram encontradas informações relevantes para responder à sua pergunta.",
                    "citations": [],
                    "confidence_score": 0.0,
                    "source_count": 0
                }
            
            # 1. Prepare context from chunks
            context_data = self._prepare_context(retrieved_chunks, max_tokens)
            
            # 2. Generate response based on query type
            response_text = await self._generate_contextual_response(
                query=query,
                context_data=context_data,
                query_analysis=query_analysis
            )
            
            # 3. Add source citations
            response_with_citations, citations = self._add_source_citations(
                response_text, context_data["chunks"]
            )
            
            # 4. Calculate confidence score
            confidence_score = self._calculate_response_confidence(
                response_text, retrieved_chunks, query_analysis
            )
            
            return {
                "response": response_with_citations,
                "citations": citations,
                "confidence_score": confidence_score,
                "source_count": len(retrieved_chunks),
                "context_tokens_used": context_data["total_tokens"],
                "response_type": self._determine_response_type(query_analysis)
            }
            
        except Exception as e:
            logger.error(f"Response generation failed: {e}")
            raise
    
    def _prepare_context(self, chunks: List[Dict], max_tokens: int) -> Dict:
        """Prepare and optimize context from retrieved chunks"""
        
        # Sort chunks by relevance score
        sorted_chunks = sorted(
            chunks, 
            key=lambda x: x.get("rerank_score", x.get("similarity_score", 0)), 
            reverse=True
        )
        
        # Build context while respecting token limits
        selected_chunks = []
        total_tokens = 0
        
        for chunk in sorted_chunks:
            # Estimate tokens (rough approximation)
            chunk_tokens = len(chunk.get("expanded_content", chunk["content"]).split()) * 1.3
            
            if total_tokens + chunk_tokens <= max_tokens * 0.8:  # Leave room for response
                selected_chunks.append(chunk)
                total_tokens += chunk_tokens
            else:
                break
        
        # Create context text with source markers
        context_parts = []
        for i, chunk in enumerate(selected_chunks, 1):
            content = chunk.get("expanded_content", chunk["content"])
            
            # Clean and format content
            cleaned_content = self._clean_content(content)
            
            # Add source marker
            source_info = f"[Fonte {i}: {chunk['document_name']}"
            if chunk.get("page_number"):
                source_info += f", página {chunk['page_number']}"
            source_info += "]"
            
            context_parts.append(f"{source_info}\n{cleaned_content}")
        
        return {
            "context_text": "\n\n".join(context_parts),
            "chunks": selected_chunks,
            "total_tokens": int(total_tokens)
        }
    
    async def _generate_contextual_response(
        self,
        query: str,
        context_data: Dict,
        query_analysis: Dict
    ) -> str:
        """Generate response based on context and query analysis"""
        
        # Determine response approach based on query type
        query_intents = query_analysis.get("intents", [])
        context_text = context_data["context_text"]
        
        if "definition" in query_intents:
            response = self._generate_definition_response(query, context_text)
        elif "procedure" in query_intents:
            response = self._generate_procedure_response(query, context_text)
        elif "comparison" in query_intents:
            response = self._generate_comparison_response(query, context_text)
        elif "numerical" in query_intents or "temporal" in query_intents:
            response = self._generate_factual_response(query, context_text)
        else:
            response = self._generate_general_response(query, context_text)
        
        return response
    
    def _generate_definition_response(self, query: str, context: str) -> str:
        """Generate definition-type response"""
        
        # Extract key terms from query
        key_terms = re.findall(r'\b\w+\b', query.lower())
        concept_candidates = [term for term in key_terms if len(term) > 3]
        
        if concept_candidates:
            main_concept = concept_candidates[0]
            
            # Look for definitions in context
            definition_patterns = [
                rf"{main_concept}.*?é.*?[.!?]",
                rf"define.*?{main_concept}.*?[.!?]",
                rf"{main_concept}.*?significa.*?[.!?]"
            ]
            
            for pattern in definition_patterns:
                matches = re.findall(pattern, context, re.IGNORECASE | re.DOTALL)
                if matches:
                    definition = matches[0].strip()
                    return f"Com base nos documentos analisados, {main_concept} {definition}"
        
        # Fallback to general summary
        return self._extract_key_information(context, query)
    
    def _generate_procedure_response(self, query: str, context: str) -> str:
        """Generate procedure-type response"""
        
        # Look for step-by-step information
        step_patterns = [
            r'\d+[.)]\s*[^.]+[.!?]',
            r'primeiro.*?[.!?]',
            r'segundo.*?[.!?]',
            r'depois.*?[.!?]',
            r'em seguida.*?[.!?]'
        ]
        
        steps = []
        for pattern in step_patterns:
            matches = re.findall(pattern, context, re.IGNORECASE)
            steps.extend(matches)
        
        if steps:
            formatted_steps = "\n".join(f"• {step.strip()}" for step in steps[:5])
            return f"Segundo os documentos, o procedimento envolve os seguintes passos:\n\n{formatted_steps}"
        
        return self._extract_key_information(context, query)
    
    def _generate_comparison_response(self, query: str, context: str) -> str:
        """Generate comparison-type response"""
        
        # Look for comparative language
        comparison_patterns = [
            r'diferença.*?entre.*?[.!?]',
            r'enquanto.*?[.!?]',
            r'por outro lado.*?[.!?]',
            r'comparado.*?[.!?]'
        ]
        
        comparisons = []
        for pattern in comparison_patterns:
            matches = re.findall(pattern, context, re.IGNORECASE | re.DOTALL)
            comparisons.extend(matches)
        
        if comparisons:
            formatted_comparisons = "\n".join(f"• {comp.strip()}" for comp in comparisons[:3])
            return f"Analisando os documentos, as principais diferenças identificadas são:\n\n{formatted_comparisons}"
        
        return self._extract_key_information(context, query)
    
    def _generate_factual_response(self, query: str, context: str) -> str:
        """Generate factual/numerical response"""
        
        # Look for specific facts, numbers, dates
        fact_patterns = [
            r'\d+[%$]?',
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'\d+\s*(?:dias|meses|anos)',
            r'valor.*?\d+',
            r'prazo.*?\d+'
        ]
        
        facts = []
        for pattern in fact_patterns:
            matches = re.findall(pattern, context, re.IGNORECASE)
            facts.extend(matches)
        
        # Extract sentences containing these facts
        fact_sentences = []
        for fact in facts[:5]:
            sentences = re.findall(rf'[^.!?]*{re.escape(str(fact))}[^.!?]*[.!?]', context)
            fact_sentences.extend(sentences)
        
        if fact_sentences:
            return "Segundo a documentação:\n\n" + "\n".join(f"• {sent.strip()}" for sent in fact_sentences[:3])
        
        return self._extract_key_information(context, query)
    
    def _generate_general_response(self, query: str, context: str) -> str:
        """Generate general response"""
        return self._extract_key_information(context, query)
    
    def _extract_key_information(self, context: str, query: str) -> str:
        """Extract key information relevant to the query"""
        
        # Split context into sentences
        sentences = re.split(r'[.!?]+', context)
        
        # Score sentences based on query terms
        query_terms = set(re.findall(r'\b\w+\b', query.lower()))
        scored_sentences = []
        
        for sentence in sentences:
            if len(sentence.strip()) < 20:  # Skip very short sentences
                continue
                
            sentence_words = set(re.findall(r'\b\w+\b', sentence.lower()))
            overlap = len(query_terms.intersection(sentence_words))
            
            if overlap > 0:
                scored_sentences.append((sentence.strip(), overlap))
        
        # Sort by relevance and take top sentences
        scored_sentences.sort(key=lambda x: x[1], reverse=True)
        top_sentences = [sent[0] for sent in scored_sentences[:3]]
        
        if top_sentences:
            return "Com base na análise dos documentos:\n\n" + "\n\n".join(f"• {sent}" for sent in top_sentences)
        
        # Fallback to first part of context
        first_sentences = sentences[:2]
        return "Com base nos documentos disponíveis:\n\n" + " ".join(sent.strip() for sent in first_sentences if sent.strip())
    
    def _add_source_citations(self, response_text: str, chunks: List[Dict]) -> Tuple[str, List[Dict]]:
        """Add source citations to response"""
        
        citations = []
        for i, chunk in enumerate(chunks, 1):
            citation = {
                "id": i,
                "document_name": chunk["document_name"],
                "page_number": chunk.get("page_number"),
                "chunk_id": chunk["id"],
                "relevance_score": chunk.get("rerank_score", chunk.get("similarity_score", 0)),
                "area_classification": chunk.get("area_classification")
            }
            citations.append(citation)
        
        # Add citation markers to response
        if citations:
            citation_text = "\n\n**Fontes:**\n"
            for citation in citations:
                ref = f"[{citation['id']}] {citation['document_name']}"
                if citation['page_number']:
                    ref += f", página {citation['page_number']}"
                if citation['area_classification']:
                    ref += f" ({citation['area_classification']})"
                citation_text += f"{ref}\n"
            
            response_with_citations = response_text + citation_text
        else:
            response_with_citations = response_text
        
        return response_with_citations, citations
    
    def _clean_content(self, content: str) -> str:
        """Clean and format content"""
        # Remove excessive whitespace
        content = re.sub(r'\s+', ' ', content)
        
        # Remove source markers from expanded content
        content = re.sub(r'\[Contexto [^:]+\]:\s*', '', content)
        content = re.sub(r'\[Conteúdo principal\]:\s*', '', content)
        
        return content.strip()
    
    def _calculate_response_confidence(
        self, 
        response: str, 
        chunks: List[Dict], 
        query_analysis: Dict
    ) -> float:
        """Calculate confidence score for the response"""
        
        if not chunks:
            return 0.0
        
        # Base confidence from chunk scores
        chunk_scores = [
            chunk.get("rerank_score", chunk.get("similarity_score", 0)) 
            for chunk in chunks
        ]
        base_confidence = sum(chunk_scores) / len(chunk_scores) if chunk_scores else 0
        
        # Confidence boost for specific response types
        if query_analysis.get("needs_precise_answer"):
            # Lower confidence for definition/factual queries (harder to get right)
            confidence_modifier = 0.9
        else:
            confidence_modifier = 1.0
        
        # Response length factor
        response_length = len(response.split())
        if response_length < 10:
            length_factor = 0.8  # Very short responses might be incomplete
        elif response_length > 200:
            length_factor = 0.9  # Very long responses might be verbose
        else:
            length_factor = 1.0
        
        final_confidence = base_confidence * confidence_modifier * length_factor
        return min(final_confidence, 1.0)
    
    def _determine_response_type(self, query_analysis: Dict) -> str:
        """Determine the type of response generated"""
        intents = query_analysis.get("intents", [])
        
        if "definition" in intents:
            return "definition"
        elif "procedure" in intents:
            return "procedure"
        elif "comparison" in intents:
            return "comparison"
        elif "numerical" in intents or "temporal" in intents:
            return "factual"
        else:
            return "general"
    
    async def generate_insights(
        self, 
        query: str, 
        retrieved_chunks: List[Dict]
    ) -> Dict:
        """Generate cross-document insights"""
        
        if not retrieved_chunks:
            return {
                "insights": "Não há dados suficientes para gerar insights.",
                "key_themes": [],
                "document_coverage": {}
            }
        
        # Group chunks by document
        doc_groups = {}
        for chunk in retrieved_chunks:
            doc_id = chunk["document_id"]
            if doc_id not in doc_groups:
                doc_groups[doc_id] = []
            doc_groups[doc_id].append(chunk)
        
        # Extract key themes across documents
        all_content = " ".join(
            chunk.get("expanded_content", chunk["content"]) 
            for chunk in retrieved_chunks
        )
        
        key_themes = self._extract_themes(all_content)
        
        # Generate comparative insights
        insights = []
        
        if len(doc_groups) > 1:
            insights.append(f"A análise abrange {len(doc_groups)} documentos diferentes.")
            
            # Find common themes
            common_themes = []
            for theme in key_themes[:5]:
                theme_docs = sum(
                    1 for doc_chunks in doc_groups.values()
                    if any(theme.lower() in chunk["content"].lower() for chunk in doc_chunks)
                )
                if theme_docs > 1:
                    common_themes.append(f"{theme} (presente em {theme_docs} documentos)")
            
            if common_themes:
                insights.append(f"Temas recorrentes: {', '.join(common_themes[:3])}")
        
        # Document coverage analysis
        document_coverage = {
            doc_chunks[0]["document_name"]: {
                "chunks_found": len(doc_chunks),
                "avg_relevance": sum(
                    chunk.get("rerank_score", chunk.get("similarity_score", 0)) 
                    for chunk in doc_chunks
                ) / len(doc_chunks),
                "pages_covered": len(set(
                    chunk.get("page_number") for chunk in doc_chunks 
                    if chunk.get("page_number")
                ))
            }
            for doc_chunks in doc_groups.values()
        }
        
        return {
            "insights": " ".join(insights) if insights else "Análise disponível nos resultados.",
            "key_themes": key_themes[:10],
            "document_coverage": document_coverage
        }
    
    def _extract_themes(self, content: str) -> List[str]:
        """Extract key themes from content"""
        
        # Simple keyword extraction (could be enhanced with NLP)
        words = re.findall(r'\b\w+\b', content.lower())
        
        # Filter out common words
        stop_words = {
            "o", "a", "os", "as", "de", "da", "do", "das", "dos", "em", "no", "na",
            "para", "por", "com", "como", "que", "qual", "onde", "quando", "porque",
            "este", "esta", "isso", "ser", "ter", "fazer", "dizer", "muito", "mais"
        }
        
        meaningful_words = [w for w in words if w not in stop_words and len(w) > 3]
        
        # Count frequency and return most common
        word_counts = Counter(meaningful_words)
        return [word for word, count in word_counts.most_common(20)]