"""
Vector Storage Service for RAG System
Handles vector similarity search and embedding operations
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from backend.src.database.connection import get_db_session
from backend.src.services.semantic_processing_service import SemanticProcessingService


logger = logging.getLogger(__name__)


class VectorStorageService:
    """Service for vector similarity search and embedding operations"""
    
    def __init__(self):
        self.semantic_service = SemanticProcessingService()
    
    async def similarity_search(
        self,
        query_text: str,
        similarity_threshold: float = 0.7,
        max_results: int = 10,
        document_ids: Optional[List[str]] = None,
        area_filter: Optional[str] = None,
        confidence_threshold: float = 0.0
    ) -> List[Dict]:
        """
        Perform vector similarity search on document chunks
        
        Args:
            query_text: Natural language query
            similarity_threshold: Minimum similarity score (0-1)
            max_results: Maximum number of results to return
            document_ids: Filter by specific document IDs
            area_filter: Filter by area classification
            confidence_threshold: Minimum confidence score for chunks
            
        Returns:
            List of matching chunks with similarity scores and metadata
        """
        try:
            # Generate query embedding
            query_embedding = await self.semantic_service.generate_embedding(query_text)
            
            async with get_db_session() as session:
                # Build dynamic query based on filters
                query_parts = [
                    "SELECT dc.id, dc.content, dc.document_id, dc.page_number,",
                    "dc.chunk_index, dc.confidence_score, dc.area_classification,",
                    "dc.metadata, d.name as document_name,",
                    "(1 - (dc.embedding <=> :query_embedding))::float as similarity_score",
                    "FROM document_chunks dc",
                    "JOIN documents d ON dc.document_id = d.id",
                    "WHERE (1 - (dc.embedding <=> :query_embedding)) > :similarity_threshold",
                    "AND dc.confidence_score >= :confidence_threshold"
                ]
                
                params = {
                    "query_embedding": query_embedding.tolist(),
                    "similarity_threshold": similarity_threshold,
                    "confidence_threshold": confidence_threshold,
                    "max_results": max_results
                }
                
                # Add document filter
                if document_ids:
                    query_parts.append("AND dc.document_id = ANY(:document_ids)")
                    params["document_ids"] = document_ids
                
                # Add area filter
                if area_filter:
                    query_parts.append("AND dc.area_classification = :area_filter")
                    params["area_filter"] = area_filter
                
                # Add ordering and limit
                query_parts.extend([
                    "ORDER BY dc.embedding <=> :query_embedding",
                    "LIMIT :max_results"
                ])
                
                query = text(" ".join(query_parts))
                result = await session.execute(query, params)
                
                chunks = []
                for row in result.fetchall():
                    chunks.append({
                        "id": str(row[0]),
                        "content": row[1],
                        "document_id": str(row[2]),
                        "page_number": row[3],
                        "chunk_index": row[4],
                        "confidence_score": row[5],
                        "area_classification": row[6],
                        "metadata": row[7],
                        "document_name": row[8],
                        "similarity_score": row[9]
                    })
                
                logger.info(f"Found {len(chunks)} similar chunks for query")
                return chunks
                
        except Exception as e:
            logger.error(f"Similarity search failed: {e}")
            raise
    
    async def hybrid_search(
        self,
        query_text: str,
        max_results: int = 10,
        document_ids: Optional[List[str]] = None,
        semantic_weight: float = 0.7,
        keyword_weight: float = 0.3
    ) -> List[Dict]:
        """
        Perform hybrid search combining semantic similarity and keyword matching
        
        Args:
            query_text: Natural language query
            max_results: Maximum number of results
            document_ids: Filter by specific document IDs
            semantic_weight: Weight for semantic similarity (0-1)
            keyword_weight: Weight for keyword matching (0-1)
            
        Returns:
            List of chunks ranked by combined score
        """
        try:
            # Normalize weights
            total_weight = semantic_weight + keyword_weight
            if total_weight > 0:
                semantic_weight = semantic_weight / total_weight
                keyword_weight = keyword_weight / total_weight
            
            # Generate query embedding
            query_embedding = await self.semantic_service.generate_embedding(query_text)
            
            async with get_db_session() as session:
                # Hybrid search query combining vector similarity and text search
                query_parts = [
                    "WITH semantic_search AS (",
                    "  SELECT dc.id, dc.content, dc.document_id, dc.page_number,",
                    "  dc.chunk_index, dc.confidence_score, dc.area_classification,",
                    "  dc.metadata, d.name as document_name,",
                    "  (1 - (dc.embedding <=> :query_embedding))::float as semantic_score",
                    "  FROM document_chunks dc",
                    "  JOIN documents d ON dc.document_id = d.id",
                    "  WHERE (1 - (dc.embedding <=> :query_embedding)) > 0.5"
                ]
                
                if document_ids:
                    query_parts.append("  AND dc.document_id = ANY(:document_ids)")
                
                query_parts.extend([
                    "),",
                    "keyword_search AS (",
                    "  SELECT dc.id,",
                    "  ts_rank_cd(to_tsvector('portuguese', dc.content), plainto_tsquery('portuguese', :query_text))::float as keyword_score",
                    "  FROM document_chunks dc",
                    "  WHERE to_tsvector('portuguese', dc.content) @@ plainto_tsquery('portuguese', :query_text)"
                ])
                
                if document_ids:
                    query_parts.append("  AND dc.document_id = ANY(:document_ids)")
                
                query_parts.extend([
                    ")",
                    "SELECT s.id, s.content, s.document_id, s.page_number,",
                    "s.chunk_index, s.confidence_score, s.area_classification,",
                    "s.metadata, s.document_name, s.semantic_score,",
                    "COALESCE(k.keyword_score, 0) as keyword_score,",
                    "(:semantic_weight * s.semantic_score + :keyword_weight * COALESCE(k.keyword_score, 0)) as combined_score",
                    "FROM semantic_search s",
                    "LEFT JOIN keyword_search k ON s.id = k.id",
                    "ORDER BY combined_score DESC",
                    "LIMIT :max_results"
                ])
                
                params = {
                    "query_embedding": query_embedding.tolist(),
                    "query_text": query_text,
                    "semantic_weight": semantic_weight,
                    "keyword_weight": keyword_weight,
                    "max_results": max_results
                }
                
                if document_ids:
                    params["document_ids"] = document_ids
                
                query = text(" ".join(query_parts))
                result = await session.execute(query, params)
                
                chunks = []
                for row in result.fetchall():
                    chunks.append({
                        "id": str(row[0]),
                        "content": row[1],
                        "document_id": str(row[2]),
                        "page_number": row[3],
                        "chunk_index": row[4],
                        "confidence_score": row[5],
                        "area_classification": row[6],
                        "metadata": row[7],
                        "document_name": row[8],
                        "semantic_score": row[9],
                        "keyword_score": row[10],
                        "combined_score": row[11]
                    })
                
                logger.info(f"Hybrid search returned {len(chunks)} chunks")
                return chunks
                
        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            raise
    
    async def get_chunk_neighbors(
        self,
        chunk_id: str,
        context_size: int = 2
    ) -> List[Dict]:
        """
        Get neighboring chunks for context expansion
        
        Args:
            chunk_id: ID of the reference chunk
            context_size: Number of chunks before and after to include
            
        Returns:
            List of neighboring chunks in order
        """
        try:
            async with get_db_session() as session:
                # Get the reference chunk info
                ref_query = text("""
                    SELECT document_id, chunk_index 
                    FROM document_chunks 
                    WHERE id = :chunk_id
                """)
                ref_result = await session.execute(ref_query, {"chunk_id": chunk_id})
                ref_row = ref_result.fetchone()
                
                if not ref_row:
                    return []
                
                document_id, chunk_index = ref_row
                
                # Get neighboring chunks
                neighbor_query = text("""
                    SELECT id, content, chunk_index, page_number, 
                           confidence_score, area_classification, metadata
                    FROM document_chunks
                    WHERE document_id = :document_id
                    AND chunk_index BETWEEN :start_index AND :end_index
                    ORDER BY chunk_index
                """)
                
                start_index = max(0, chunk_index - context_size)
                end_index = chunk_index + context_size
                
                result = await session.execute(neighbor_query, {
                    "document_id": document_id,
                    "start_index": start_index,
                    "end_index": end_index
                })
                
                chunks = []
                for row in result.fetchall():
                    chunks.append({
                        "id": str(row[0]),
                        "content": row[1],
                        "chunk_index": row[2],
                        "page_number": row[3],
                        "confidence_score": row[4],
                        "area_classification": row[5],
                        "metadata": row[6],
                        "is_reference": str(row[0]) == chunk_id
                    })
                
                return chunks
                
        except Exception as e:
            logger.error(f"Failed to get chunk neighbors: {e}")
            raise
    
    async def get_document_statistics(self, document_id: str) -> Dict:
        """Get statistics for a document's chunks and embeddings"""
        try:
            async with get_db_session() as session:
                query = text("""
                    SELECT 
                        COUNT(*) as total_chunks,
                        AVG(confidence_score) as avg_confidence,
                        COUNT(DISTINCT page_number) as pages_covered,
                        COUNT(DISTINCT area_classification) as areas_covered,
                        SUM(token_count) as total_tokens
                    FROM document_chunks
                    WHERE document_id = :document_id
                """)
                
                result = await session.execute(query, {"document_id": document_id})
                row = result.fetchone()
                
                if row:
                    return {
                        "total_chunks": row[0],
                        "avg_confidence": float(row[1]) if row[1] else 0,
                        "pages_covered": row[2],
                        "areas_covered": row[3],
                        "total_tokens": row[4] or 0
                    }
                return {}
                
        except Exception as e:
            logger.error(f"Failed to get document statistics: {e}")
            return {}
    
    async def update_chunk_embedding(
        self, 
        chunk_id: str, 
        new_content: str
    ) -> bool:
        """Update a chunk's content and regenerate its embedding"""
        try:
            # Generate new embedding
            new_embedding = await self.semantic_service.generate_embedding(new_content)
            
            async with get_db_session() as session:
                query = text("""
                    UPDATE document_chunks 
                    SET content = :content, 
                        embedding = :embedding,
                        token_count = :token_count,
                        updated_at = NOW()
                    WHERE id = :chunk_id
                """)
                
                token_count = len(new_content.split())
                
                await session.execute(query, {
                    "chunk_id": chunk_id,
                    "content": new_content,
                    "embedding": new_embedding.tolist(),
                    "token_count": token_count
                })
                
                await session.commit()
                logger.info(f"Updated embedding for chunk {chunk_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to update chunk embedding: {e}")
            return False