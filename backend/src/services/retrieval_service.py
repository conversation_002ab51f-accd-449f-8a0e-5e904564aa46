"""
Retrieval Service for RAG System
Advanced retrieval with query processing, reranking, and context optimization
"""

import logging
import re
from datetime import datetime
from typing import Dict, List, Optional, Set, Tuple
import numpy as np
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from backend.src.database.connection import get_db_session
from backend.src.services.vector_storage_service import VectorStorageService
from backend.src.services.semantic_processing_service import SemanticProcessingService


logger = logging.getLogger(__name__)


class RetrievalService:
    """Advanced retrieval service with query understanding and context optimization"""
    
    def __init__(self):
        self.vector_service = VectorStorageService()
        self.semantic_service = SemanticProcessingService()
        
        # Query intent patterns
        self.intent_patterns = {
            "definition": [
                r"o que é", r"define", r"definição", r"conceito", r"significa"
            ],
            "comparison": [
                r"diferença", r"comparar", r"versus", r"vs", r"melhor que"
            ],
            "procedure": [
                r"como", r"passo", r"processo", r"procedimento", r"método"
            ],
            "location": [
                r"onde", r"localização", r"local", r"endereço"
            ],
            "temporal": [
                r"quando", r"data", r"prazo", r"período", r"tempo"
            ],
            "numerical": [
                r"quanto", r"número", r"valor", r"quantidade", r"percentual"
            ]
        }
    
    async def intelligent_retrieve(
        self,
        query: str,
        max_results: int = 10,
        document_ids: Optional[List[str]] = None,
        enable_reranking: bool = True,
        expand_context: bool = True
    ) -> Dict:
        """
        Perform intelligent retrieval with query understanding and optimization
        
        Args:
            query: Natural language query
            max_results: Maximum number of results
            document_ids: Filter by specific documents
            enable_reranking: Whether to rerank results
            expand_context: Whether to include neighboring chunks
            
        Returns:
            Dict with retrieval results and metadata
        """
        start_time = datetime.utcnow()
        
        try:
            # 1. Query understanding and preprocessing
            query_analysis = await self._analyze_query(query)
            processed_query = self._preprocess_query(query)
            
            # 2. Multi-stage retrieval
            initial_results = await self._multi_stage_retrieval(
                processed_query, 
                max_results * 2,  # Get more for reranking
                document_ids,
                query_analysis
            )
            
            # 3. Reranking if enabled
            if enable_reranking and len(initial_results) > 1:
                reranked_results = await self._rerank_results(
                    query, initial_results, max_results
                )
            else:
                reranked_results = initial_results[:max_results]
            
            # 4. Context expansion if enabled
            if expand_context:
                enriched_results = await self._expand_context(reranked_results)
            else:
                enriched_results = reranked_results
            
            # 5. Calculate retrieval time
            end_time = datetime.utcnow()
            retrieval_time = (end_time - start_time).total_seconds() * 1000
            
            # 6. Prepare final response
            response = {
                "query": query,
                "query_analysis": query_analysis,
                "results": enriched_results,
                "total_results": len(enriched_results),
                "retrieval_time_ms": int(retrieval_time),
                "search_strategy": self._get_search_strategy(query_analysis),
                "confidence_score": self._calculate_retrieval_confidence(enriched_results)
            }
            
            # 7. Log retrieval for analytics
            await self._log_retrieval(query, enriched_results, retrieval_time)
            
            logger.info(f"Retrieved {len(enriched_results)} results in {retrieval_time:.1f}ms")
            return response
            
        except Exception as e:
            logger.error(f"Intelligent retrieval failed: {e}")
            raise
    
    async def _analyze_query(self, query: str) -> Dict:
        """Analyze query to understand intent and extract key information"""
        query_lower = query.lower()
        
        # Detect query intent
        detected_intents = []
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    detected_intents.append(intent)
                    break
        
        # Extract key entities and terms
        key_terms = self._extract_key_terms(query)
        
        # Determine query complexity
        complexity = self._assess_query_complexity(query)
        
        # Detect area focus
        area_focus = self._detect_area_focus(query)
        
        return {
            "intents": detected_intents,
            "key_terms": key_terms,
            "complexity": complexity,
            "area_focus": area_focus,
            "requires_multiple_sources": len(key_terms) > 3 or "comparison" in detected_intents,
            "needs_precise_answer": any(intent in detected_intents for intent in ["definition", "numerical", "temporal"])
        }
    
    def _preprocess_query(self, query: str) -> str:
        """Preprocess query for better matching"""
        # Remove common stop words that might interfere with semantic search
        stop_words = {"o", "a", "os", "as", "de", "da", "do", "em", "para", "por", "com"}
        
        # Keep original query but create cleaned version for keyword search
        words = query.lower().split()
        cleaned_words = [w for w in words if w not in stop_words and len(w) > 2]
        
        # Return original if too much was removed
        if len(cleaned_words) < len(words) * 0.5:
            return query
        
        return " ".join(cleaned_words)
    
    async def _multi_stage_retrieval(
        self,
        query: str,
        max_results: int,
        document_ids: Optional[List[str]],
        query_analysis: Dict
    ) -> List[Dict]:
        """Perform multi-stage retrieval based on query analysis"""
        
        # Choose retrieval strategy based on query analysis
        if query_analysis["complexity"] == "high" or query_analysis["requires_multiple_sources"]:
            # Use hybrid search for complex queries
            results = await self.vector_service.hybrid_search(
                query_text=query,
                max_results=max_results,
                document_ids=document_ids,
                semantic_weight=0.6,
                keyword_weight=0.4
            )
        else:
            # Use pure semantic search for simple queries
            results = await self.vector_service.similarity_search(
                query_text=query,
                similarity_threshold=0.65,
                max_results=max_results,
                document_ids=document_ids,
                area_filter=query_analysis.get("area_focus")
            )
        
        # Filter by confidence if we have enough results
        if len(results) > max_results * 0.8:
            results = [r for r in results if r.get("confidence_score", 0) > 0.7]
        
        return results
    
    async def _rerank_results(
        self, 
        query: str, 
        results: List[Dict], 
        target_count: int
    ) -> List[Dict]:
        """Rerank results using advanced scoring"""
        
        try:
            for result in results:
                # Calculate comprehensive score
                semantic_score = result.get("semantic_score", result.get("similarity_score", 0))
                keyword_score = result.get("keyword_score", 0)
                confidence_score = result.get("confidence_score", 1.0)
                
                # Length penalty for very short or very long chunks
                content_length = len(result["content"])
                length_penalty = self._calculate_length_penalty(content_length)
                
                # Position bonus for chunks with better context
                position_bonus = self._calculate_position_bonus(result)
                
                # Area relevance bonus
                area_bonus = self._calculate_area_bonus(query, result.get("area_classification"))
                
                # Combined reranking score
                rerank_score = (
                    semantic_score * 0.4 +
                    keyword_score * 0.2 +
                    confidence_score * 0.2 +
                    length_penalty * 0.1 +
                    position_bonus * 0.05 +
                    area_bonus * 0.05
                )
                
                result["rerank_score"] = rerank_score
            
            # Sort by rerank score and return top results
            reranked = sorted(results, key=lambda x: x["rerank_score"], reverse=True)
            return reranked[:target_count]
            
        except Exception as e:
            logger.error(f"Reranking failed: {e}")
            return results[:target_count]
    
    async def _expand_context(self, results: List[Dict]) -> List[Dict]:
        """Expand context by including neighboring chunks"""
        
        expanded_results = []
        
        for result in results:
            # Get the main chunk
            expanded_result = result.copy()
            
            # Get neighboring chunks for context
            neighbors = await self.vector_service.get_chunk_neighbors(
                chunk_id=result["id"],
                context_size=1  # 1 chunk before and after
            )
            
            if neighbors:
                # Find the reference chunk and its neighbors
                ref_index = next(
                    (i for i, n in enumerate(neighbors) if n.get("is_reference")), 
                    -1
                )
                
                if ref_index >= 0:
                    # Build expanded content with context
                    context_parts = []
                    
                    # Add previous chunk if available
                    if ref_index > 0:
                        prev_chunk = neighbors[ref_index - 1]
                        context_parts.append(f"[Contexto anterior]: {prev_chunk['content'][:200]}...")
                    
                    # Add main content
                    context_parts.append(f"[Conteúdo principal]: {result['content']}")
                    
                    # Add next chunk if available
                    if ref_index < len(neighbors) - 1:
                        next_chunk = neighbors[ref_index + 1]
                        context_parts.append(f"[Contexto posterior]: {next_chunk['content'][:200]}...")
                    
                    expanded_result["expanded_content"] = "\n\n".join(context_parts)
                    expanded_result["context_chunks"] = len(neighbors)
                else:
                    expanded_result["expanded_content"] = result["content"]
                    expanded_result["context_chunks"] = 1
            else:
                expanded_result["expanded_content"] = result["content"]
                expanded_result["context_chunks"] = 1
            
            expanded_results.append(expanded_result)
        
        return expanded_results
    
    def _extract_key_terms(self, query: str) -> List[str]:
        """Extract key terms from query"""
        # Remove common words and extract meaningful terms
        common_words = {
            "o", "a", "os", "as", "de", "da", "do", "das", "dos", "em", "no", "na", 
            "para", "por", "com", "como", "que", "qual", "onde", "quando", "porque"
        }
        
        words = re.findall(r'\b\w+\b', query.lower())
        key_terms = [w for w in words if w not in common_words and len(w) > 2]
        
        return key_terms[:10]  # Limit to top 10 terms
    
    def _assess_query_complexity(self, query: str) -> str:
        """Assess query complexity"""
        word_count = len(query.split())
        
        if word_count <= 3:
            return "low"
        elif word_count <= 8:
            return "medium"
        else:
            return "high"
    
    def _detect_area_focus(self, query: str) -> Optional[str]:
        """Detect if query focuses on specific areas"""
        area_keywords = {
            "processual": ["processo", "procedimento", "decisão", "sentença"],
            "penal": ["crime", "penal", "criminal", "delito"],
            "civil": ["civil", "contrato", "obrigação", "responsabilidade"],
            "administrativo": ["administrativo", "público", "servidor", "licitação"]
        }
        
        query_lower = query.lower()
        for area, keywords in area_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                return area
        
        return None
    
    def _calculate_length_penalty(self, content_length: int) -> float:
        """Calculate penalty/bonus based on content length"""
        if content_length < 50:
            return 0.7  # Too short
        elif content_length > 1000:
            return 0.8  # Too long
        else:
            return 1.0  # Good length
    
    def _calculate_position_bonus(self, result: Dict) -> float:
        """Calculate bonus based on chunk position"""
        # Prefer chunks from middle of documents (often contain key content)
        chunk_index = result.get("chunk_index", 0)
        
        if chunk_index == 0:
            return 0.9  # First chunk might be introduction
        elif chunk_index < 5:
            return 1.0  # Early chunks are often important
        else:
            return 0.95  # Later chunks might be less relevant
    
    def _calculate_area_bonus(self, query: str, area_classification: Optional[str]) -> float:
        """Calculate bonus if area matches query intent"""
        if not area_classification:
            return 1.0
        
        detected_area = self._detect_area_focus(query)
        if detected_area and detected_area == area_classification:
            return 1.2  # Bonus for area match
        
        return 1.0
    
    def _get_search_strategy(self, query_analysis: Dict) -> str:
        """Determine which search strategy was used"""
        if query_analysis["complexity"] == "high":
            return "hybrid_search"
        elif query_analysis["requires_multiple_sources"]:
            return "multi_source_semantic"
        else:
            return "semantic_search"
    
    def _calculate_retrieval_confidence(self, results: List[Dict]) -> float:
        """Calculate overall confidence in retrieval results"""
        if not results:
            return 0.0
        
        # Average of top result scores
        top_scores = [
            r.get("rerank_score", r.get("similarity_score", 0)) 
            for r in results[:3]
        ]
        
        return sum(top_scores) / len(top_scores) if top_scores else 0.0
    
    async def _log_retrieval(
        self, 
        query: str, 
        results: List[Dict], 
        retrieval_time: float
    ):
        """Log retrieval for analytics"""
        try:
            async with get_db_session() as session:
                # Generate query embedding for analytics
                query_embedding = self.semantic_service.generate_embedding(query)
                
                query_log = text("""
                    INSERT INTO rag_queries (
                        query_text, query_embedding, retrieved_chunk_ids,
                        retrieval_scores, retrieval_time_ms
                    ) VALUES (
                        :query_text, :query_embedding, :chunk_ids,
                        :scores, :retrieval_time
                    )
                """)
                
                chunk_ids = [r["id"] for r in results]
                scores = [r.get("rerank_score", r.get("similarity_score", 0)) for r in results]
                
                await session.execute(query_log, {
                    "query_text": query,
                    "query_embedding": query_embedding.tolist(),
                    "chunk_ids": chunk_ids,
                    "scores": scores,
                    "retrieval_time": int(retrieval_time)
                })
                
                await session.commit()
                
        except Exception as e:
            logger.error(f"Failed to log retrieval: {e}")