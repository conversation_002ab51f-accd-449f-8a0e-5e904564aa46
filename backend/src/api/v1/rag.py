"""
RAG API Endpoints
Natural language query interface for document retrieval and Q&A
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from backend.src.services.retrieval_service import RetrievalService
from backend.src.services.document_indexing_service import DocumentIndexingService
from backend.src.services.response_generation_service import ResponseGenerationService


logger = logging.getLogger(__name__)
router = APIRouter()


class RAGQueryRequest(BaseModel):
    """Request model for RAG queries"""
    query: str = Field(..., description="Natural language question")
    document_ids: Optional[List[str]] = Field(None, description="Filter by specific document IDs")
    max_results: int = Field(10, ge=1, le=50, description="Maximum number of chunks to retrieve")
    enable_reranking: bool = Field(True, description="Enable advanced result reranking")
    expand_context: bool = Field(True, description="Include neighboring chunks for context")
    generate_response: bool = Field(True, description="Generate synthesized response")


class RAGQueryResponse(BaseModel):
    """Response model for RAG queries"""
    query: str
    response: Optional[str] = None
    sources: List[Dict]
    confidence_score: float
    retrieval_time_ms: int
    generation_time_ms: Optional[int] = None
    total_results: int
    search_strategy: str


class DocumentIndexRequest(BaseModel):
    """Request model for document indexing"""
    document_id: str
    force_reindex: bool = Field(False, description="Force reindexing if already exists")


@router.post("/query", response_model=RAGQueryResponse)
async def query_documents(request: RAGQueryRequest):
    """
    Query documents using natural language
    
    This endpoint provides Google NotebookLM-like functionality:
    - Natural language queries in Portuguese
    - Intelligent retrieval with hybrid search
    - Context-aware response generation
    - Precise source attribution
    """
    start_time = datetime.utcnow()
    
    try:
        # Initialize services
        retrieval_service = RetrievalService()
        
        # Perform intelligent retrieval
        retrieval_result = await retrieval_service.intelligent_retrieve(
            query=request.query,
            max_results=request.max_results,
            document_ids=request.document_ids,
            enable_reranking=request.enable_reranking,
            expand_context=request.expand_context
        )
        
        response_text = None
        generation_time = None
        
        # Generate response if requested
        if request.generate_response and retrieval_result["results"]:
            response_service = ResponseGenerationService()
            
            generation_start = datetime.utcnow()
            response_result = await response_service.generate_response(
                query=request.query,
                retrieved_chunks=retrieval_result["results"],
                query_analysis=retrieval_result["query_analysis"]
            )
            generation_end = datetime.utcnow()
            
            response_text = response_result["response"]
            generation_time = int((generation_end - generation_start).total_seconds() * 1000)
        
        # Format sources with citations
        sources = []
        for i, result in enumerate(retrieval_result["results"], 1):
            source = {
                "citation_id": i,
                "content": result.get("expanded_content", result["content"]),
                "document_name": result["document_name"],
                "page_number": result.get("page_number"),
                "confidence_score": result.get("confidence_score", 1.0),
                "similarity_score": result.get("rerank_score", result.get("similarity_score", 0)),
                "area_classification": result.get("area_classification"),
                "reference": f"[{i}] {result['document_name']}"
            }
            
            if result.get("page_number"):
                source["reference"] += f", página {result['page_number']}"
            
            sources.append(source)
        
        return RAGQueryResponse(
            query=request.query,
            response=response_text,
            sources=sources,
            confidence_score=retrieval_result["confidence_score"],
            retrieval_time_ms=retrieval_result["retrieval_time_ms"],
            generation_time_ms=generation_time,
            total_results=retrieval_result["total_results"],
            search_strategy=retrieval_result["search_strategy"]
        )
        
    except Exception as e:
        logger.error(f"RAG query failed: {e}")
        raise HTTPException(status_code=500, detail=f"Query processing failed: {str(e)}")


@router.get("/search/{document_id}")
async def search_document(
    document_id: str,
    query: str = Query(..., description="Search query"),
    max_results: int = Query(10, ge=1, le=50)
):
    """Search within a specific document"""
    try:
        retrieval_service = RetrievalService()
        
        result = await retrieval_service.intelligent_retrieve(
            query=query,
            max_results=max_results,
            document_ids=[document_id],
            enable_reranking=True,
            expand_context=False
        )
        
        return {
            "document_id": document_id,
            "query": query,
            "results": result["results"],
            "total_results": result["total_results"],
            "retrieval_time_ms": result["retrieval_time_ms"]
        }
        
    except Exception as e:
        logger.error(f"Document search failed: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.post("/index-document")
async def index_document(request: DocumentIndexRequest):
    """Index a document for RAG retrieval"""
    try:
        indexing_service = DocumentIndexingService()
        
        # Get document chunks (this would typically come from your existing PDF processing)
        # For now, we'll assume chunks are already processed and stored
        
        # Check current indexing status
        status = await indexing_service.get_indexing_status(request.document_id)
        
        if status and status["embeddings_created"] and not request.force_reindex:
            return {
                "status": "already_indexed",
                "document_id": request.document_id,
                "message": "Document already indexed. Use force_reindex=true to reindex."
            }
        
        # TODO: Integrate with existing PDF processing pipeline
        # This would typically get chunks from your current processing system
        
        return {
            "status": "indexing_required",
            "document_id": request.document_id,
            "message": "Integration with existing PDF processing pipeline needed"
        }
        
    except Exception as e:
        logger.error(f"Document indexing failed: {e}")
        raise HTTPException(status_code=500, detail=f"Indexing failed: {str(e)}")


@router.get("/document/{document_id}/status")
async def get_indexing_status(document_id: str):
    """Get indexing status for a document"""
    try:
        indexing_service = DocumentIndexingService()
        status = await indexing_service.get_indexing_status(document_id)
        
        if not status:
            return {
                "document_id": document_id,
                "status": "not_indexed",
                "indexed": False
            }
        
        return {
            "document_id": document_id,
            "status": "indexed" if status["embeddings_created"] else "indexing",
            "indexed": status["embeddings_created"],
            "chunks_count": status["chunks_count"],
            "indexing_started_at": status["indexing_started_at"],
            "indexing_completed_at": status["indexing_completed_at"],
            "error": status["indexing_error"]
        }
        
    except Exception as e:
        logger.error(f"Failed to get indexing status: {e}")
        raise HTTPException(status_code=500, detail=f"Status check failed: {str(e)}")


@router.get("/insights")
async def get_cross_document_insights(
    query: str = Query(..., description="Insight query"),
    document_ids: Optional[List[str]] = Query(None, description="Filter by document IDs"),
    max_results: int = Query(20, ge=5, le=100)
):
    """Get insights across multiple documents"""
    try:
        retrieval_service = RetrievalService()
        response_service = ResponseGenerationService()
        
        # Retrieve relevant chunks from multiple documents
        retrieval_result = await retrieval_service.intelligent_retrieve(
            query=query,
            max_results=max_results,
            document_ids=document_ids,
            enable_reranking=True,
            expand_context=True
        )
        
        # Generate cross-document insights
        if retrieval_result["results"]:
            insights_result = await response_service.generate_insights(
                query=query,
                retrieved_chunks=retrieval_result["results"]
            )
            
            return {
                "query": query,
                "insights": insights_result["insights"],
                "key_themes": insights_result.get("key_themes", []),
                "document_coverage": insights_result.get("document_coverage", {}),
                "sources": retrieval_result["results"][:10],  # Top 10 sources
                "confidence_score": retrieval_result["confidence_score"]
            }
        else:
            return {
                "query": query,
                "insights": "Não foram encontradas informações relevantes para gerar insights.",
                "sources": [],
                "confidence_score": 0.0
            }
        
    except Exception as e:
        logger.error(f"Cross-document insights failed: {e}")
        raise HTTPException(status_code=500, detail=f"Insights generation failed: {str(e)}")


@router.get("/analytics")
async def get_rag_analytics():
    """Get RAG system analytics and performance metrics"""
    try:
        # This would typically query your analytics tables
        # For now, return placeholder data
        
        return {
            "total_queries_today": 0,
            "avg_retrieval_time_ms": 0,
            "avg_confidence_score": 0,
            "most_queried_documents": [],
            "popular_query_types": [],
            "system_performance": {
                "status": "healthy",
                "indexed_documents": 0,
                "total_chunks": 0
            }
        }
        
    except Exception as e:
        logger.error(f"Analytics query failed: {e}")
        raise HTTPException(status_code=500, detail=f"Analytics failed: {str(e)}")


# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check for RAG system"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "retrieval": "operational",
            "indexing": "operational",
            "response_generation": "operational"
        }
    }