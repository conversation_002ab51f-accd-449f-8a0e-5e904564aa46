# Sistema RAG - Guia de Configuração

## Implementação Completa do Sistema RAG NotebookLM-like

Este guia descreve como configurar e utilizar o novo sistema RAG que foi implementado para proporcionar funcionalidade similar ao Google NotebookLM.

## ✅ Componentes Implementados

### 1. **Infraestrutura de Banco de Dados**
- ✅ **pgvector Extension**: `database/migrations/enable_pgvector.sql`
- ✅ **Schema RAG**: `database/migrations/create_rag_tables.sql`
  - Tabelas: `document_chunks`, `rag_queries`, `document_indexing_status`
  - Indexes vetoriais HNSW para busca eficiente
  - Funções SQL para similaridade vetorial

### 2. **Serviços Core**
- ✅ **DocumentIndexingService**: Indexação de documentos com embeddings
- ✅ **VectorStorageService**: Busca por similaridade e operações vetoriais
- ✅ **RetrievalService**: Recuperação inteligente com análise de queries
- ✅ **ResponseGenerationService**: Geração de respostas com citações

### 3. **API Endpoints**
- ✅ **POST /api/v1/rag/query**: Consulta em linguagem natural
- ✅ **GET /api/v1/rag/search/{document_id}**: Busca em documento específico
- ✅ **POST /api/v1/rag/index-document**: Indexação de documentos
- ✅ **GET /api/v1/rag/insights**: Insights cross-document
- ✅ **GET /api/v1/rag/analytics**: Métricas do sistema

### 4. **Integração com Pipeline Existente**
- ✅ **RAGPDFIntegrationService**: Integração com processamento PDF atual

## 🚀 Configuração Passo a Passo

### Passo 1: Configurar Banco de Dados

1. **Habilitar pgvector no Supabase**:
```sql
-- Execute no SQL Editor do Supabase
\i database/migrations/enable_pgvector.sql
```

2. **Criar tabelas RAG**:
```sql
-- Execute no SQL Editor do Supabase
\i database/migrations/create_rag_tables.sql
```

### Passo 2: Configurar Variáveis de Ambiente

Adicione ao seu `.env`:
```env
# OpenAI para geração de respostas (opcional)
OPENAI_API_KEY=sk-your-openai-key

# Configurações RAG
RAG_ENABLED=true
RAG_MAX_CHUNKS_PER_QUERY=10
RAG_SIMILARITY_THRESHOLD=0.7
RAG_ENABLE_CACHING=true
```

### Passo 3: Instalar Dependências

```bash
poetry add openai
poetry add numpy
poetry add asyncio
```

### Passo 4: Testar Endpoints

1. **Verificar saúde do sistema**:
```bash
curl http://localhost:8000/api/v1/rag/health
```

2. **Fazer uma consulta de teste**:
```bash
curl -X POST http://localhost:8000/api/v1/rag/query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "O que fala sobre responsabilidade civil?",
    "max_results": 5,
    "generate_response": true
  }'
```

## 📋 Funcionalidades Principais

### 1. **Consulta em Linguagem Natural**
```python
# Exemplo de uso
response = await rag_service.query_documents({
    "query": "Quais são os prazos para recursos?",
    "document_ids": ["doc-123", "doc-456"],  # Opcional
    "max_results": 10,
    "generate_response": True
})
```

**Retorna**:
- Resposta sintetizada com citações
- Lista de trechos relevantes com scores de similaridade
- Referências precisas (página, documento)
- Métricas de confiança e tempo de resposta

### 2. **Busca Híbrida Inteligente**
- **Busca Semântica**: Usando embeddings sentence-transformers
- **Busca por Palavras-chave**: PostgreSQL text search
- **Análise de Intent**: Detecta tipo de pergunta (definição, comparação, etc.)
- **Reranking**: Pontuação multi-fator para relevância

### 3. **Geração de Respostas Contextualizada**
- **Com OpenAI**: Respostas sofisticadas usando GPT-3.5-turbo
- **Template Fallback**: Sistema de backup sem dependências externas
- **Citações Precisas**: Formato [1], [2] referenciando trechos
- **Controle de Confiança**: Scores baseados em evidências

### 4. **Insights Cross-Document**
```python
# Análise entre múltiplos documentos
insights = await rag_service.get_insights(
    query="Tendências em jurisprudência trabalhista",
    document_ids=None  # Todos os documentos
)
```

**Recursos**:
- Identificação de temas recorrentes
- Padrões e tendências
- Cobertura por documento
- Síntese de informações complementares

## 🔧 Integração com Sistema Existente

### Indexar Documentos Existentes

```python
# Integração com pipeline PDF atual
integration_service = RAGPDFIntegrationService()

# Processar e indexar novo PDF
result = await integration_service.process_and_index_pdf(
    file_path="/path/to/document.pdf",
    document_id="doc-123",
    enable_rag_indexing=True
)

# Reindexar documento existente
result = await integration_service.reindex_existing_document(
    document_id="existing-doc-456",
    force_reindex=True
)
```

### Batch Processing

```python
# Indexar múltiplos documentos em lote
result = await integration_service.batch_index_existing_documents(
    document_ids=["doc1", "doc2", "doc3"],
    max_concurrent=3
)
```

## 📊 Monitoramento e Analytics

### Métricas Disponíveis
- **Performance**: Tempo de retrieval, geração, tokens usados
- **Qualidade**: Scores de confiança, satisfação do usuário
- **Uso**: Queries populares, documentos mais acessados
- **Sistema**: Status de indexação, estatísticas de chunks

### Endpoints de Monitoramento
```bash
# Analytics gerais
GET /api/v1/rag/analytics

# Status de documento específico
GET /api/v1/rag/document/{doc_id}/status

# Saúde do sistema
GET /api/v1/rag/health
```

## 🎯 Benefícios vs. Google NotebookLM

| Funcionalidade | NotebookLM | Nosso Sistema |
|----------------|------------|---------------|
| **Consulta Natural** | ✅ | ✅ |
| **Citações Precisas** | ✅ | ✅ |
| **Multi-documentos** | ✅ | ✅ |
| **Busca Híbrida** | ✅ | ✅ |
| **Insights Cross-doc** | ✅ | ✅ |
| **API Programática** | ❌ | ✅ |
| **Controle de Dados** | ❌ | ✅ |
| **Personalização** | ❌ | ✅ |
| **Integração Local** | ❌ | ✅ |

## 🔄 Próximos Passos

### Melhorias Sugeridas
1. **Cache de Embeddings**: Implementar cache Redis para embeddings frequentes
2. **Streaming Responses**: Respostas em tempo real
3. **Fine-tuning**: Ajustar modelo para domínio jurídico específico
4. **Interface Web**: Frontend similar ao NotebookLM
5. **Exportação**: Geração de relatórios baseados em consultas

### Performance Tuning
1. **Otimizar Indexes**: Ajustar parâmetros HNSW
2. **Batch Embeddings**: Processar múltiplos chunks simultaneamente
3. **Connection Pooling**: Otimizar conexões com banco
4. **Caching Strategy**: Implementar cache em múltiplas camadas

## 📞 Suporte

O sistema foi projetado para ser:
- **Auto-documentado**: Swagger UI em `/docs`
- **Observável**: Logs detalhados e métricas
- **Testável**: Endpoints de health check
- **Escalável**: Arquitetura assíncrona

Para dúvidas ou problemas, consulte os logs da aplicação ou use os endpoints de diagnóstico disponíveis.