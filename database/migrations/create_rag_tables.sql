-- RAG System Database Schema
-- Create tables for vector storage and document indexing

-- Document chunks table with vector embeddings
CREATE TABLE IF NOT EXISTS document_chunks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    page_number INTEGER,
    confidence_score FLOAT DEFAULT 1.0,
    area_classification TEXT,
    token_count INTEGER,
    
    -- Vector embedding (384 dimensions for sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2)
    embedding vector(384),
    
    -- Metadata JSON for flexible storage
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique chunks per document
    UNIQUE(document_id, chunk_index)
);

-- Create indexes for efficient vector similarity search
CREATE INDEX IF NOT EXISTS document_chunks_embedding_idx 
ON document_chunks USING hnsw (embedding vector_cosine_ops);

-- Create indexes for metadata searches
CREATE INDEX IF NOT EXISTS document_chunks_document_id_idx ON document_chunks(document_id);
CREATE INDEX IF NOT EXISTS document_chunks_page_number_idx ON document_chunks(page_number);
CREATE INDEX IF NOT EXISTS document_chunks_area_classification_idx ON document_chunks(area_classification);
CREATE INDEX IF NOT EXISTS document_chunks_confidence_score_idx ON document_chunks(confidence_score);

-- GIN index for metadata JSONB searches
CREATE INDEX IF NOT EXISTS document_chunks_metadata_gin_idx ON document_chunks USING gin(metadata);

-- RAG queries table for analytics and caching
CREATE TABLE IF NOT EXISTS rag_queries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    query_text TEXT NOT NULL,
    query_embedding vector(384),
    
    -- Retrieved chunks and their scores
    retrieved_chunk_ids UUID[],
    retrieval_scores FLOAT[],
    
    -- Generated response
    response_text TEXT,
    response_confidence FLOAT,
    source_chunks_used UUID[],
    
    -- Performance metrics
    retrieval_time_ms INTEGER,
    generation_time_ms INTEGER,
    total_tokens_used INTEGER,
    
    -- Analytics
    user_satisfaction INTEGER CHECK (user_satisfaction BETWEEN 1 AND 5),
    feedback TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for query analytics
CREATE INDEX IF NOT EXISTS rag_queries_user_id_idx ON rag_queries(user_id);
CREATE INDEX IF NOT EXISTS rag_queries_created_at_idx ON rag_queries(created_at);
CREATE INDEX IF NOT EXISTS rag_queries_query_embedding_idx 
ON rag_queries USING hnsw (query_embedding vector_cosine_ops);

-- Query performance view for analytics
CREATE OR REPLACE VIEW rag_query_analytics AS
SELECT 
    DATE_TRUNC('day', created_at) as query_date,
    COUNT(*) as total_queries,
    AVG(retrieval_time_ms) as avg_retrieval_time,
    AVG(generation_time_ms) as avg_generation_time,
    AVG(total_tokens_used) as avg_tokens_used,
    AVG(response_confidence) as avg_confidence,
    AVG(user_satisfaction) as avg_satisfaction
FROM rag_queries
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY query_date DESC;

-- Document indexing status table
CREATE TABLE IF NOT EXISTS document_indexing_status (
    document_id UUID PRIMARY KEY REFERENCES documents(id) ON DELETE CASCADE,
    chunks_count INTEGER DEFAULT 0,
    embeddings_created BOOLEAN DEFAULT FALSE,
    indexing_started_at TIMESTAMP WITH TIME ZONE,
    indexing_completed_at TIMESTAMP WITH TIME ZONE,
    indexing_error TEXT,
    last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Update trigger for document_indexing_status
CREATE OR REPLACE FUNCTION update_indexing_status_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_indexing_status_timestamp_trigger
    BEFORE UPDATE ON document_indexing_status
    FOR EACH ROW
    EXECUTE FUNCTION update_indexing_status_timestamp();

-- Function to get similar chunks using vector similarity
CREATE OR REPLACE FUNCTION get_similar_chunks(
    query_embedding vector(384),
    similarity_threshold float DEFAULT 0.7,
    max_chunks integer DEFAULT 10,
    document_ids uuid[] DEFAULT NULL
)
RETURNS TABLE (
    chunk_id uuid,
    content text,
    document_id uuid,
    page_number integer,
    similarity_score float,
    metadata jsonb
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        dc.id,
        dc.content,
        dc.document_id,
        dc.page_number,
        (1 - (dc.embedding <=> query_embedding))::float as similarity,
        dc.metadata
    FROM document_chunks dc
    WHERE 
        (document_ids IS NULL OR dc.document_id = ANY(document_ids))
        AND (1 - (dc.embedding <=> query_embedding)) > similarity_threshold
    ORDER BY dc.embedding <=> query_embedding
    LIMIT max_chunks;
END;
$$ LANGUAGE plpgsql;