#!/usr/bin/env python3
"""
Script para converter dados com SUBTEMAS_IDENTIFICADOS para formato multilabel
com colunas binárias esperado pelo sistema existente.
"""

import pandas as pd
import os
import sys
from datetime import datetime

def extrair_subtemas_unicos(df: pd.DataFrame) -> list:
    """Extrai lista de subtemas únicos dos dados"""
    subtemas_set = set()
    
    for _, row in df.iterrows():
        subtemas_str = str(row.get('SUBTEMAS_IDENTIFICADOS', ''))
        if subtemas_str and subtemas_str != 'nan':
            subtemas = [s.strip() for s in subtemas_str.split(',') if s.strip()]
            for subtema in subtemas:
                subtemas_set.add(subtema)
    
    return sorted(list(subtemas_set))

def converter_para_formato_multilabel(df: pd.DataFrame) -> pd.DataFrame:
    """Converte dados para formato multilabel com colunas binárias"""
    
    # Extrair subtemas únicos
    subtemas = extrair_subtemas_unicos(df)
    print(f"📋 Subtemas encontrados: {len(subtemas)}")
    for subtema in subtemas:
        print(f"  - {subtema}")
    
    # Criar cópia do DataFrame
    df_resultado = df.copy()
    
    # Adicionar coluna de texto analisado
    if 'Assunto Inteiro Teor' in df.columns:
        df_resultado['TEXTO_ANALISADO'] = df['Assunto Inteiro Teor']
    elif 'Teor' in df.columns:
        df_resultado['TEXTO_ANALISADO'] = df['Teor']
    elif 'Assunto Resumido' in df.columns:
        df_resultado['TEXTO_ANALISADO'] = df['Assunto Resumido']
    else:
        # Usar a primeira coluna de texto disponível
        text_columns = [col for col in df.columns if df[col].dtype == 'object' and col not in ['MPRJ', 'Noticiado', 'SUBTEMAS_IDENTIFICADOS']]
        if text_columns:
            df_resultado['TEXTO_ANALISADO'] = df[text_columns[0]]
        else:
            df_resultado['TEXTO_ANALISADO'] = ""
    
    # Adicionar colunas binárias para cada subtema
    for subtema in subtemas:
        # Criar nome da coluna no formato esperado
        nome_coluna = f"SUBTEMA_{subtema.replace(' ', '_').replace('(', '').replace(')', '').replace('-', '_').replace(',', '').upper()}"
        
        # Inicializar coluna com zeros
        df_resultado[nome_coluna] = 0
        
        # Marcar casos que contêm o subtema
        for idx, row in df.iterrows():
            subtemas_str = str(row.get('SUBTEMAS_IDENTIFICADOS', ''))
            if subtema in subtemas_str:
                df_resultado.loc[idx, nome_coluna] = 1
    
    print(f"✅ Conversão concluída. Adicionadas {len(subtemas)} colunas binárias.")
    return df_resultado

def main():
    """Função principal"""
    if len(sys.argv) != 2:
        print("Uso: python scripts/converter_para_formato_multilabel.py <arquivo_csv>")
        sys.exit(1)
    
    arquivo_entrada = sys.argv[1]
    
    if not os.path.exists(arquivo_entrada):
        print(f"❌ Arquivo não encontrado: {arquivo_entrada}")
        sys.exit(1)
    
    print("🔄 Convertendo dados para formato multilabel")
    print(f"📄 Arquivo de entrada: {arquivo_entrada}")
    
    # Carregar dados
    try:
        df = pd.read_csv(arquivo_entrada, encoding='utf-8')
        print(f"✅ Dados carregados: {len(df)} registros")
    except Exception as e:
        print(f"❌ Erro ao carregar dados: {e}")
        sys.exit(1)
    
    # Converter para formato multilabel
    df_convertido = converter_para_formato_multilabel(df)
    
    # Gerar nome do arquivo de saída
    nome_base = os.path.splitext(os.path.basename(arquivo_entrada))[0]
    arquivo_saida = f"data/output/{nome_base}_formato_multilabel.csv"
    
    # Salvar arquivo convertido
    try:
        df_convertido.to_csv(arquivo_saida, index=False, encoding='utf-8')
        print(f"✅ Arquivo convertido salvo: {arquivo_saida}")
        
        # Mostrar estatísticas
        print(f"\n📊 Estatísticas:")
        print(f"  - Total de registros: {len(df_convertido)}")
        print(f"  - Total de colunas: {len(df_convertido.columns)}")
        
        # Mostrar colunas de subtemas criadas
        colunas_subtemas = [col for col in df_convertido.columns if col.startswith('SUBTEMA_')]
        print(f"  - Colunas de subtemas: {len(colunas_subtemas)}")
        
        # Mostrar estatísticas por subtema
        print(f"\n📈 Distribuição por subtema:")
        for coluna in sorted(colunas_subtemas):
            total = df_convertido[coluna].sum()
            percentual = (total / len(df_convertido)) * 100
            nome_subtema = coluna.replace('SUBTEMA_', '').replace('_', ' ')
            print(f"  - {nome_subtema}: {total} casos ({percentual:.1f}%)")
        
        return arquivo_saida
        
    except Exception as e:
        print(f"❌ Erro ao salvar arquivo: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
