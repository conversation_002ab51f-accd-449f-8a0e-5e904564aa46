#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para gerar um relatório tático a partir dos casos classificados como positivos.
"""

import sys
import os

# Garantir que o diretório raiz do projeto esteja no PYTHONPATH
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importação absoluta
from src.reports.gerador_relatorio import main as gerar_relatorio_main

if __name__ == "__main__":
    gerar_relatorio_main() 