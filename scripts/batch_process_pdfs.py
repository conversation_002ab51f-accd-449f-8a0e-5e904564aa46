#!/usr/bin/env python3
"""
CLI script for PDF batch processing.

This script provides a command-line interface for processing multiple PDF files
using the Simple Class batch processing system.

Usage:
    python scripts/batch_process_pdfs.py /path/to/pdfs EDUCACAO --output /path/to/output
    python scripts/batch_process_pdfs.py /path/to/pdfs SAUDE --workers 8 --chunk-size 1500
"""

import argparse
import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.api.services.batch_processing_service import BatchProcessingService
from src.api.models.batch_processing import BatchProcessingRequest, JobStatus
from src.utils.config_utils import carregar_configuracao

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class BatchProcessingCLI:
    """Command-line interface for batch PDF processing."""
    
    def __init__(self):
        """Initialize the CLI."""
        self.service = BatchProcessingService()
        self.config = carregar_configuracao()
    
    async def process_pdfs(self, 
                          folder_path: str,
                          area: str,
                          output_dir: Optional[str] = None,
                          chunk_size: int = 1000,
                          overlap: int = 200,
                          max_subtemas: int = 3,
                          parallel_workers: int = 4,
                          show_progress: bool = True) -> bool:
        """
        Process PDFs in the specified folder.
        
        Args:
            folder_path: Path to folder containing PDF files
            area: Classification area (EDUCACAO, SAUDE, MEIO_AMBIENTE)
            output_dir: Output directory for results
            chunk_size: Size of text chunks in tokens
            overlap: Overlap between chunks in tokens
            max_subtemas: Maximum number of subtemas per PDF
            parallel_workers: Number of parallel workers
            show_progress: Whether to show progress updates
            
        Returns:
            True if processing succeeded, False otherwise
        """
        try:
            # Validate folder path
            folder = Path(folder_path)
            if not folder.exists():
                logger.error(f"Folder does not exist: {folder_path}")
                return False
            
            if not folder.is_dir():
                logger.error(f"Path is not a directory: {folder_path}")
                return False
            
            # Count PDF files
            pdf_files = list(folder.rglob("*.pdf"))
            if not pdf_files:
                logger.error(f"No PDF files found in: {folder_path}")
                return False
            
            logger.info(f"Found {len(pdf_files)} PDF files to process")
            
            # Configure output directory
            if output_dir:
                self.service.csv_exporter.output_dir = Path(output_dir)
                self.service.csv_exporter.output_dir.mkdir(parents=True, exist_ok=True)
            
            # Create batch processing request
            request = BatchProcessingRequest(
                folder_path=folder_path,
                area=area,
                max_subtemas=max_subtemas,
                chunk_size=chunk_size,
                overlap=overlap,
                parallel_workers=parallel_workers
            )
            
            # Start batch processing
            logger.info(f"Starting batch processing for area: {area}")
            job_id = await self.service.start_batch_processing(
                request=request,
                user_id="cli-user",
                progress_callback=self._create_progress_callback() if show_progress else None
            )
            
            logger.info(f"Job started with ID: {job_id}")
            
            # Monitor progress
            if show_progress:
                await self._monitor_progress(job_id)
            else:
                await self._wait_for_completion(job_id)
            
            # Get final results
            final_status = self.service.get_job_status(job_id)
            if final_status.status == JobStatus.COMPLETED:
                results = self.service.get_job_results(job_id)
                if results:
                    self._print_results_summary(results)
                    logger.info(f"Results saved to: {results.csv_output_path}")
                    return True
                else:
                    logger.error("Failed to get job results")
                    return False
            else:
                logger.error(f"Job failed with status: {final_status.status}")
                if final_status.error_message:
                    logger.error(f"Error: {final_status.error_message}")
                return False
                
        except Exception as e:
            logger.error(f"Error processing PDFs: {e}")
            return False
    
    async def _monitor_progress(self, job_id: str) -> None:
        """Monitor job progress with visual updates."""
        logger.info("Monitoring progress (Ctrl+C to stop monitoring)...")
        
        try:
            while True:
                status = self.service.get_job_status(job_id)
                if not status:
                    logger.error("Job not found")
                    break
                
                if status.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                    break
                
                # Print progress
                progress_percent = status.progress * 100
                progress_bar = self._create_progress_bar(status.progress)
                
                print(f"\r{progress_bar} {progress_percent:.1f}% - {status.current_file} "
                      f"({status.processed_files}/{status.total_files})", end="", flush=True)
                
                await asyncio.sleep(1)
            
            print()  # New line after progress
            
        except KeyboardInterrupt:
            logger.info("Progress monitoring stopped by user")
    
    async def _wait_for_completion(self, job_id: str) -> None:
        """Wait for job completion without progress display."""
        while True:
            status = self.service.get_job_status(job_id)
            if not status:
                logger.error("Job not found")
                break
            
            if status.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                break
            
            await asyncio.sleep(2)
    
    def _create_progress_callback(self):
        """Create a progress callback function."""
        async def progress_callback(job_id, progress, current_file, processed_files, total_files, status):
            # Progress is handled by monitoring loop
            pass
        
        return progress_callback
    
    def _create_progress_bar(self, progress: float, width: int = 40) -> str:
        """Create a visual progress bar."""
        filled = int(width * progress)
        bar = "█" * filled + "░" * (width - filled)
        return f"[{bar}]"
    
    def _print_results_summary(self, results) -> None:
        """Print a summary of processing results."""
        logger.info("=" * 50)
        logger.info("BATCH PROCESSING RESULTS")
        logger.info("=" * 50)
        logger.info(f"Total PDFs processed: {results.total_pdfs}")
        logger.info(f"Successfully processed: {results.successful_pdfs}")
        logger.info(f"Failed: {len(results.failed_pdfs)}")
        logger.info(f"Success rate: {results.successful_pdfs / max(results.total_pdfs, 1) * 100:.1f}%")
        logger.info(f"Total processing time: {results.total_processing_time:.2f} seconds")
        logger.info(f"Average time per PDF: {results.total_processing_time / max(results.total_pdfs, 1):.2f} seconds")
        
        if results.failed_pdfs:
            logger.info(f"Failed files: {', '.join(results.failed_pdfs)}")
        
        logger.info("=" * 50)
    
    def list_areas(self) -> None:
        """List available classification areas."""
        areas = ["EDUCACAO", "SAUDE", "MEIO_AMBIENTE"]
        logger.info("Available classification areas:")
        for area in areas:
            logger.info(f"  - {area}")
    
    def list_subtemas(self, area: str) -> None:
        """List available subtemas for an area."""
        subtemas = self.config.get('subtemas', {}).get(area, {})
        if subtemas:
            logger.info(f"Available subtemas for {area}:")
            for subtema, info in subtemas.items():
                description = info.get('descricao', 'No description')
                logger.info(f"  - {subtema}: {description}")
        else:
            logger.warning(f"No subtemas found for area: {area}")


def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="Process PDF files using Simple Class batch processing",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s /path/to/pdfs EDUCACAO
  %(prog)s /path/to/pdfs SAUDE --output /path/to/output
  %(prog)s /path/to/pdfs MEIO_AMBIENTE --workers 8 --chunk-size 1500
  %(prog)s --list-areas
  %(prog)s --list-subtemas EDUCACAO
        """
    )
    
    parser.add_argument(
        "folder_path",
        nargs="?",
        help="Path to folder containing PDF files"
    )
    
    parser.add_argument(
        "area",
        nargs="?",
        choices=["EDUCACAO", "SAUDE", "MEIO_AMBIENTE"],
        help="Classification area"
    )
    
    parser.add_argument(
        "--output", "-o",
        type=str,
        help="Output directory for results (default: data/output)"
    )
    
    parser.add_argument(
        "--chunk-size",
        type=int,
        default=1000,
        help="Size of text chunks in tokens (default: 1000)"
    )
    
    parser.add_argument(
        "--overlap",
        type=int,
        default=200,
        help="Overlap between chunks in tokens (default: 200)"
    )
    
    parser.add_argument(
        "--max-subtemas",
        type=int,
        default=3,
        help="Maximum number of subtemas per PDF (default: 3)"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=4,
        help="Number of parallel workers (default: 4)"
    )
    
    parser.add_argument(
        "--no-progress",
        action="store_true",
        help="Disable progress display"
    )
    
    parser.add_argument(
        "--list-areas",
        action="store_true",
        help="List available classification areas"
    )
    
    parser.add_argument(
        "--list-subtemas",
        type=str,
        help="List available subtemas for an area"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create CLI instance
    cli = BatchProcessingCLI()
    
    # Handle list operations
    if args.list_areas:
        cli.list_areas()
        return
    
    if args.list_subtemas:
        cli.list_subtemas(args.list_subtemas)
        return
    
    # Validate required arguments
    if not args.folder_path or not args.area:
        parser.error("folder_path and area are required unless using --list-areas or --list-subtemas")
    
    # Run batch processing
    success = asyncio.run(cli.process_pdfs(
        folder_path=args.folder_path,
        area=args.area,
        output_dir=args.output,
        chunk_size=args.chunk_size,
        overlap=args.overlap,
        max_subtemas=args.max_subtemas,
        parallel_workers=args.workers,
        show_progress=not args.no_progress
    ))
    
    if success:
        logger.info("Batch processing completed successfully")
        sys.exit(0)
    else:
        logger.error("Batch processing failed")
        sys.exit(1)


if __name__ == "__main__":
    main()