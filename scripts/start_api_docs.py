#!/usr/bin/env python3
"""
Script para iniciar a API Simple Class com documentação melhorada.

Este script inicia a API FastAPI com configurações otimizadas para desenvolvimento
e produção, incluindo documentação automática aprimorada.
"""

import os
import sys
import uvicorn
import argparse
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def main():
    """Main function to start the API server."""
    parser = argparse.ArgumentParser(description="Start Simple Class API with enhanced documentation")
    parser.add_argument(
        "--host", 
        default="0.0.0.0", 
        help="Host to bind the server (default: 0.0.0.0)"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000, 
        help="Port to bind the server (default: 8000)"
    )
    parser.add_argument(
        "--reload", 
        action="store_true", 
        help="Enable auto-reload for development"
    )
    parser.add_argument(
        "--log-level", 
        default="info", 
        choices=["critical", "error", "warning", "info", "debug", "trace"],
        help="Log level (default: info)"
    )
    parser.add_argument(
        "--workers", 
        type=int, 
        default=1, 
        help="Number of worker processes (default: 1)"
    )
    parser.add_argument(
        "--env", 
        choices=["development", "production"], 
        default="development",
        help="Environment mode (default: development)"
    )
    
    args = parser.parse_args()
    
    # Set environment variables based on mode
    if args.env == "development":
        os.environ["ENVIRONMENT"] = "development"
        print("🔧 Starting in DEVELOPMENT mode")
        print("📚 Documentation available at:")
        print(f"   • Swagger UI: http://{args.host}:{args.port}/docs")
        print(f"   • ReDoc: http://{args.host}:{args.port}/redoc")
        print(f"   • OpenAPI JSON: http://{args.host}:{args.port}/openapi.json")
        print()
        print("🧪 Test credentials:")
        print("   • Email: <EMAIL>")
        print("   • Password: test123")
        print()
    else:
        os.environ["ENVIRONMENT"] = "production"
        print("🚀 Starting in PRODUCTION mode")
    
    # Configure uvicorn settings
    config = {
        "app": "src.api.main:app",
        "host": args.host,
        "port": args.port,
        "log_level": args.log_level,
        "access_log": True,
        "use_colors": True,
    }
    
    if args.env == "development":
        config.update({
            "reload": args.reload,
            "reload_dirs": [str(project_root / "src")],
            "reload_includes": ["*.py"],
        })
    else:
        config.update({
            "workers": args.workers,
            "loop": "uvloop",
            "http": "httptools",
        })
    
    print(f"🌐 Starting server on http://{args.host}:{args.port}")
    print("Press CTRL+C to stop the server")
    print("-" * 50)
    
    try:
        uvicorn.run(**config)
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
