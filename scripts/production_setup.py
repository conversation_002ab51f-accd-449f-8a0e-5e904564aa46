#!/usr/bin/env python3
"""
Script de configuração para produção da Simple Class API.

Este script configura o ambiente de produção com otimizações de segurança,
performance e monitoramento.
"""

import os
import sys
import secrets
import subprocess
from pathlib import Path
from typing import Dict, List

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class ProductionSetup:
    """Production setup manager."""
    
    def __init__(self):
        self.project_root = project_root
        self.env_file = self.project_root / ".env.production"
        
    def generate_secret_key(self) -> str:
        """Generate a secure secret key."""
        return secrets.token_urlsafe(32)
    
    def create_production_env(self):
        """Create production environment file."""
        print("🔧 Criando configuração de produção...")
        
        secret_key = self.generate_secret_key()
        
        env_content = f"""# Simple Class API - Production Configuration

# API Settings
API_SECRET_KEY={secret_key}
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
PASSWORD_RESET_EXPIRE_MINUTES=15

# Environment
ENVIRONMENT=production

# CORS Settings (configure for your domain)
CORS_ORIGINS=https://yourdomain.com,https://api.yourdomain.com

# Supabase (REQUIRED for production)
SUPABASE_URL=your-supabase-url
SUPABASE_KEY=your-supabase-anon-key
SUPABASE_SERVICE_KEY=your-supabase-service-key

# External APIs (REQUIRED for full functionality)
TOGETHER_API_KEY=your-together-ai-key
ANTHROPIC_API_KEY=your-anthropic-key
FIREWORKS_API_KEY=your-fireworks-key

# Rate Limiting
RATE_LIMIT_REQUESTS=50
RATE_LIMIT_WINDOW=3600

# File Upload Limits
MAX_FILE_SIZE=52428800  # 50MB

# Logging
LOG_LEVEL=INFO
LOG_FILE=/var/log/simple-class-api/app.log

# Security
SECURE_COOKIES=true
HTTPS_ONLY=true
"""
        
        with open(self.env_file, 'w') as f:
            f.write(env_content)
        
        print(f"✅ Arquivo de produção criado: {self.env_file}")
        print("⚠️  IMPORTANTE: Configure as variáveis de ambiente antes de usar em produção!")
    
    def create_systemd_service(self):
        """Create systemd service file."""
        print("🔧 Criando arquivo de serviço systemd...")
        
        service_content = f"""[Unit]
Description=Simple Class API
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory={self.project_root}
Environment=PATH={self.project_root}/.venv/bin
EnvironmentFile={self.env_file}
ExecStart={self.project_root}/.venv/bin/uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --workers 4
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=3

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths={self.project_root}/logs {self.project_root}/temp

[Install]
WantedBy=multi-user.target
"""
        
        service_file = self.project_root / "simple-class-api.service"
        with open(service_file, 'w') as f:
            f.write(service_content)
        
        print(f"✅ Arquivo de serviço criado: {service_file}")
        print("📋 Para instalar o serviço:")
        print(f"   sudo cp {service_file} /etc/systemd/system/")
        print("   sudo systemctl daemon-reload")
        print("   sudo systemctl enable simple-class-api")
        print("   sudo systemctl start simple-class-api")
    
    def create_nginx_config(self):
        """Create nginx configuration."""
        print("🔧 Criando configuração do nginx...")
        
        nginx_content = """server {
    listen 80;
    server_name your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL Configuration (configure with your certificates)
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    # API proxy
    location / {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # File upload
        client_max_body_size 50M;
    }
    
    # Static files (if any)
    location /static/ {
        alias /path/to/static/files/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check
    location /health {
        access_log off;
        proxy_pass http://127.0.0.1:8000/health;
    }
}
"""
        
        nginx_file = self.project_root / "nginx-simple-class-api.conf"
        with open(nginx_file, 'w') as f:
            f.write(nginx_content)
        
        print(f"✅ Configuração do nginx criada: {nginx_file}")
        print("📋 Para instalar:")
        print(f"   sudo cp {nginx_file} /etc/nginx/sites-available/simple-class-api")
        print("   sudo ln -s /etc/nginx/sites-available/simple-class-api /etc/nginx/sites-enabled/")
        print("   sudo nginx -t")
        print("   sudo systemctl reload nginx")
    
    def create_docker_files(self):
        """Create Docker configuration files."""
        print("🔧 Criando arquivos Docker...")
        
        # Dockerfile
        dockerfile_content = """FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV POETRY_NO_INTERACTION=1
ENV POETRY_VENV_IN_PROJECT=1
ENV POETRY_CACHE_DIR=/tmp/poetry_cache

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    build-essential \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Set work directory
WORKDIR /app

# Copy poetry files
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN poetry install --only=main && rm -rf $POETRY_CACHE_DIR

# Copy project
COPY . .

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Run the application
CMD ["poetry", "run", "uvicorn", "src.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
"""
        
        dockerfile = self.project_root / "Dockerfile"
        with open(dockerfile, 'w') as f:
            f.write(dockerfile_content)
        
        # Docker Compose
        compose_content = """version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
    env_file:
      - .env.production
    volumes:
      - ./logs:/app/logs
      - ./temp:/app/temp
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-simple-class-api.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
    restart: unless-stopped

volumes:
  logs:
  temp:
"""
        
        compose_file = self.project_root / "docker-compose.prod.yml"
        with open(compose_file, 'w') as f:
            f.write(compose_content)
        
        print(f"✅ Dockerfile criado: {dockerfile}")
        print(f"✅ Docker Compose criado: {compose_file}")
    
    def create_monitoring_config(self):
        """Create monitoring configuration."""
        print("🔧 Criando configuração de monitoramento...")
        
        # Health check script
        health_check_content = """#!/bin/bash
# Health check script for Simple Class API

API_URL="http://localhost:8000"
LOG_FILE="/var/log/simple-class-api/health.log"

# Function to log with timestamp
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Check API health
response=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/health")

if [ "$response" = "200" ]; then
    log_message "API Health Check: OK"
    exit 0
else
    log_message "API Health Check: FAILED (HTTP $response)"
    exit 1
fi
"""
        
        health_check_file = self.project_root / "scripts" / "health_check.sh"
        with open(health_check_file, 'w') as f:
            f.write(health_check_content)
        
        # Make executable
        os.chmod(health_check_file, 0o755)
        
        print(f"✅ Script de health check criado: {health_check_file}")
        print("📋 Para configurar monitoramento:")
        print("   # Adicionar ao crontab:")
        print(f"   */5 * * * * {health_check_file}")
    
    def show_production_checklist(self):
        """Show production deployment checklist."""
        print("\n" + "="*60)
        print("🚀 CHECKLIST DE PRODUÇÃO")
        print("="*60)
        
        checklist = [
            "□ Configurar variáveis de ambiente no .env.production",
            "□ Configurar certificados SSL",
            "□ Configurar Supabase para produção",
            "□ Configurar chaves de API externas",
            "□ Instalar e configurar nginx",
            "□ Configurar firewall (UFW/iptables)",
            "□ Configurar backup automático",
            "□ Configurar monitoramento e logs",
            "□ Testar todos os endpoints",
            "□ Configurar domínio e DNS",
            "□ Configurar rate limiting",
            "□ Revisar configurações de segurança"
        ]
        
        for item in checklist:
            print(f"  {item}")
        
        print("\n📋 Comandos úteis:")
        print("  # Verificar status do serviço:")
        print("    sudo systemctl status simple-class-api")
        print("  # Ver logs:")
        print("    sudo journalctl -u simple-class-api -f")
        print("  # Reiniciar serviço:")
        print("    sudo systemctl restart simple-class-api")
    
    def run(self):
        """Run production setup."""
        print("🚀 CONFIGURAÇÃO DE PRODUÇÃO - SIMPLE CLASS API")
        print("="*60)
        
        self.create_production_env()
        self.create_systemd_service()
        self.create_nginx_config()
        self.create_docker_files()
        self.create_monitoring_config()
        
        self.show_production_checklist()
        
        print("\n✅ Configuração de produção concluída!")
        print("⚠️  IMPORTANTE: Revise e configure todas as variáveis antes de usar em produção!")


def main():
    """Main function."""
    setup = ProductionSetup()
    setup.run()


if __name__ == "__main__":
    main()
