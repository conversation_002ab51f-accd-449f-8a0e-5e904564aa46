#!/usr/bin/env python3
"""
Script para gerar relatório detalhado da classificação completa de educação.
Inclui análise de subtemas, tipos de unidade e CRE.
"""

import pandas as pd
import os
import sys
from datetime import datetime
from collections import Counter
import re

def carregar_dados_classificacao(arquivo_csv: str) -> pd.DataFrame:
    """Carrega dados da classificação completa"""
    try:
        df = pd.read_csv(arquivo_csv, encoding='utf-8')
        print(f"✅ Dados carregados: {len(df)} registros")
        return df
    except Exception as e:
        print(f"❌ Erro ao carregar dados: {e}")
        return None

def analisar_subtemas(df: pd.DataFrame) -> dict:
    """Analisa distribuição de subtemas"""
    subtemas_counter = Counter()
    total_registros = len(df)
    
    for idx, row in df.iterrows():
        subtemas_str = str(row.get('SUBTEMAS_IDENTIFICADOS', ''))
        if subtemas_str and subtemas_str != 'nan':
            # Dividir por vírgula e limpar
            subtemas = [s.strip() for s in subtemas_str.split(',') if s.strip()]
            for subtema in subtemas:
                subtemas_counter[subtema] += 1
    
    # Converter para dict com percentuais
    resultado = {}
    for subtema, count in subtemas_counter.items():
        resultado[subtema] = {
            'total': count,
            'percentual': (count / total_registros * 100) if total_registros > 0 else 0
        }
    
    return resultado

def analisar_tipos_unidade(df: pd.DataFrame) -> dict:
    """Analisa distribuição por tipo de unidade"""
    tipos_counter = df['TIPO_UNIDADE'].value_counts()
    total_registros = len(df)
    
    resultado = {}
    for tipo, count in tipos_counter.items():
        if tipo and str(tipo) != 'nan':
            resultado[tipo] = {
                'total': count,
                'percentual': (count / total_registros * 100) if total_registros > 0 else 0
            }
    
    return resultado

def analisar_cre(df: pd.DataFrame) -> dict:
    """Analisa distribuição por CRE (apenas municipais)"""
    municipais = df[df['TIPO_UNIDADE'] == 'Municipal']
    cre_counter = municipais['CRE'].value_counts()
    total_municipais = len(municipais)
    
    resultado = {}
    for cre, count in cre_counter.items():
        if cre and str(cre) != 'nan' and cre != 'Não se aplica':
            resultado[cre] = {
                'total': count,
                'percentual': (count / total_municipais * 100) if total_municipais > 0 else 0
            }
    
    return resultado

def obter_exemplos_subtema(df: pd.DataFrame, subtema: str, max_exemplos: int = 3) -> list:
    """Obtém exemplos de casos para um subtema específico"""
    exemplos = []
    
    for idx, row in df.iterrows():
        subtemas_str = str(row.get('SUBTEMAS_IDENTIFICADOS', ''))
        if subtema in subtemas_str:
            resumo = str(row.get('Assunto Resumido', ''))[:150]
            noticiado = str(row.get('Noticiado', ''))
            mprj = str(row.get('MPRJ', ''))
            
            exemplos.append({
                'mprj': mprj,
                'noticiado': noticiado,
                'resumo': resumo + "..." if len(resumo) == 150 else resumo
            })
            
            if len(exemplos) >= max_exemplos:
                break
    
    return exemplos

def gerar_relatorio_markdown(df: pd.DataFrame, arquivo_saida: str) -> bool:
    """Gera relatório em formato Markdown"""
    try:
        # Análises
        subtemas_stats = analisar_subtemas(df)
        tipos_stats = analisar_tipos_unidade(df)
        cre_stats = analisar_cre(df)
        
        # Estatísticas gerais
        total_registros = len(df)
        registros_com_subtemas = len(df[df['SUBTEMAS_IDENTIFICADOS'].notna() & (df['SUBTEMAS_IDENTIFICADOS'] != '')])
        taxa_classificacao = (registros_com_subtemas / total_registros * 100) if total_registros > 0 else 0
        
        # Gerar conteúdo do relatório
        conteudo = f"""# Relatório de Classificação Multilabel Completa - Educação

## Resumo Executivo

- **Total de registros analisados:** {total_registros:,}
- **Registros classificados:** {registros_com_subtemas:,}
- **Taxa de classificação:** {taxa_classificacao:.1f}%
- **Data de geração:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Método:** Llama 3.3 70B via Fireworks AI

## Distribuição por Tipo de Unidade

"""
        
        # Tipos de unidade
        for tipo, stats in sorted(tipos_stats.items(), key=lambda x: x[1]['total'], reverse=True):
            conteudo += f"### {tipo}\n"
            conteudo += f"- **Total:** {stats['total']:,} casos ({stats['percentual']:.1f}%)\n\n"
        
        # CRE (apenas municipais)
        if cre_stats:
            conteudo += "## Distribuição por CRE (Escolas Municipais)\n\n"
            municipais_total = len(df[df['TIPO_UNIDADE'] == 'Municipal'])
            conteudo += f"**Total de escolas municipais:** {municipais_total:,}\n\n"
            
            for cre, stats in sorted(cre_stats.items(), key=lambda x: x[1]['total'], reverse=True):
                conteudo += f"### {cre}\n"
                conteudo += f"- **Total:** {stats['total']:,} casos ({stats['percentual']:.1f}% das municipais)\n\n"
        
        # Subtemas
        conteudo += "## Distribuição por Subtemas\n\n"
        conteudo += "### Ranking de Subtemas Identificados\n\n"
        
        for i, (subtema, stats) in enumerate(sorted(subtemas_stats.items(), key=lambda x: x[1]['total'], reverse=True), 1):
            conteudo += f"{i}. **{subtema}**: {stats['total']:,} casos ({stats['percentual']:.1f}%)\n"
        
        conteudo += "\n## Análise Detalhada por Subtemas\n\n"
        
        # Detalhes por subtema
        for subtema, stats in sorted(subtemas_stats.items(), key=lambda x: x[1]['total'], reverse=True):
            if stats['total'] > 0:
                conteudo += f"### {subtema}\n"
                conteudo += f"- **Frequência:** {stats['total']:,} casos ({stats['percentual']:.1f}%)\n"
                
                # Exemplos
                exemplos = obter_exemplos_subtema(df, subtema, 3)
                if exemplos:
                    conteudo += "- **Exemplos:**\n"
                    for exemplo in exemplos:
                        conteudo += f"  - **{exemplo['mprj']}** - {exemplo['noticiado']}: {exemplo['resumo']}\n"
                
                conteudo += "\n"
        
        # Metodologia
        conteudo += """## Metodologia

- **Classificação:** LLM Llama 3.3 70B via Fireworks AI
- **Fonte:** Dados de ouvidorias de educação do MPRJ
- **Período:** Dados de 2024-2025
- **Dimensões analisadas:** 
  1. **Subtemas (Multilabel):** Múltiplas categorias por caso
  2. **Tipo de Unidade (Unilabel):** Municipal/Estadual/Privada
  3. **CRE (Unilabel):** 1ª-11ª CRE para municipais

## Vantagens da Classificação LLM

- **Compreensão contextual:** Entende o significado além de palavras-chave
- **Flexibilidade:** Adapta-se a variações de linguagem e sinônimos
- **Precisão:** Maior acurácia na identificação de subtemas relevantes
- **Multilabel:** Identifica múltiplos subtemas por caso quando apropriado
- **Classificação unilabel:** Determina tipo de unidade e CRE com precisão

## Observações

- Cada caso pode ser classificado em múltiplos subtemas
- Classificação baseada em análise semântica do texto completo
- Definições funcionais específicas para gestão pública educacional
- CRE identificada apenas quando mencionada explicitamente no texto

---
*Relatório gerado automaticamente pelo Simple Class - Classificação Completa Multilabel*
"""
        
        # Salvar arquivo
        with open(arquivo_saida, 'w', encoding='utf-8') as f:
            f.write(conteudo)
        
        print(f"✅ Relatório salvo: {arquivo_saida}")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao gerar relatório: {e}")
        return False

def main():
    """Função principal"""
    if len(sys.argv) != 2:
        print("Uso: python scripts/gerar_relatorio_educacao_completo.py <arquivo_csv>")
        sys.exit(1)
    
    arquivo_csv = sys.argv[1]
    
    if not os.path.exists(arquivo_csv):
        print(f"❌ Arquivo não encontrado: {arquivo_csv}")
        sys.exit(1)
    
    print("📊 Gerando Relatório de Classificação Completa - Educação")
    print(f"📄 Arquivo: {arquivo_csv}")
    print()
    
    # Carregar dados
    df = carregar_dados_classificacao(arquivo_csv)
    if df is None:
        sys.exit(1)
    
    # Gerar relatório
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    arquivo_saida = f"data/output/relatorio_educacao_completo_{timestamp}.md"
    
    if not os.path.exists('data/output'):
        os.makedirs('data/output')
    
    sucesso = gerar_relatorio_markdown(df, arquivo_saida)
    
    if sucesso:
        print(f"\n🎉 Relatório gerado com sucesso!")
        print(f"📁 Localização: {arquivo_saida}")
    else:
        print("\n❌ Falha ao gerar relatório")
        sys.exit(1)

if __name__ == "__main__":
    main()
