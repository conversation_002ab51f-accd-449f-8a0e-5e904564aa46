#!/usr/bin/env python3
"""
Script para gerar relatório de análise criminal de denúncias de ouvidorias
com identificação de possíveis crimes e repercussão criminal.
"""

import pandas as pd
import os
import sys
import logging
from typing import Dict, List, Optional
from anthropic import Anthropic
import yaml
from datetime import datetime

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def carregar_configuracao() -> Dict:
    """Carrega configurações do arquivo config.yml"""
    try:
        with open('config/config.yml', 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        return config
    except Exception as e:
        logging.error(f"Erro ao carregar configuração: {e}")
        return {}

def carregar_variaveis_ambiente() -> Dict:
    """Carrega variáveis de ambiente do arquivo .env"""
    env_vars = {}
    try:
        with open('.env', 'r') as file:
            for linha in file:
                linha = linha.strip()
                if linha and not linha.startswith('#'):
                    chave, valor = linha.split('=', 1)
                    env_vars[chave] = valor
    except FileNotFoundError:
        logging.warning("Arquivo .env não encontrado")
    except Exception as e:
        logging.error(f"Erro ao carregar .env: {e}")
    
    return env_vars

def carregar_dados_multilabel(arquivo_csv: str) -> Optional[pd.DataFrame]:
    """Carrega dados multilabel do CSV"""
    try:
        df = pd.read_csv(arquivo_csv, encoding='utf-8')
        logging.info(f"Arquivo carregado: {len(df)} registros")
        
        # Verificar se tem a coluna SUBTEMAS_IDENTIFICADOS
        if 'SUBTEMAS_IDENTIFICADOS' not in df.columns:
            logging.error("Coluna SUBTEMAS_IDENTIFICADOS não encontrada")
            return None
            
        return df
    except Exception as e:
        logging.error(f"Erro ao carregar arquivo: {e}")
        return None

def obter_subtemas_unicos(df: pd.DataFrame) -> List[str]:
    """Obtém lista de subtemas únicos da coluna SUBTEMAS_IDENTIFICADOS"""
    subtemas_unicos = set()
    
    for _, row in df.iterrows():
        subtemas_str = str(row.get('SUBTEMAS_IDENTIFICADOS', ''))
        if subtemas_str and subtemas_str != 'nan' and subtemas_str.strip():
            subtemas = [s.strip() for s in subtemas_str.split(',') if s.strip()]
            subtemas_unicos.update(subtemas)
    
    return list(subtemas_unicos)

def analisar_estatisticas_criminais(df: pd.DataFrame, subtemas_lista: List[str]) -> Dict:
    """Analisa estatísticas focando em aspectos criminais"""
    estatisticas = {}
    total_registros = len(df)
    
    for subtema in subtemas_lista:
        # Filtrar casos que contêm este subtema
        casos_positivos = []
        
        for idx, row in df.iterrows():
            subtemas_str = str(row.get('SUBTEMAS_IDENTIFICADOS', ''))
            if subtemas_str and subtemas_str != 'nan' and subtemas_str.strip():
                subtemas = [s.strip() for s in subtemas_str.split(',') if s.strip()]
                if subtema in subtemas:
                    casos_positivos.append(idx)
        
        df_filtrado = df.loc[casos_positivos]
        num_casos = len(df_filtrado)
        percentual = (num_casos / total_registros * 100) if total_registros > 0 else 0
        
        estatisticas[subtema] = {
            'total': num_casos,
            'percentual': percentual,
            'casos': df_filtrado
        }
    
    return estatisticas

def carregar_prompt_criminal() -> Optional[str]:
    """Carrega template de prompt para análise criminal"""
    template_criminal = """
# PROMPT PARA ANÁLISE CRIMINAL DE NOTÍCIAS DE FATO

Analise o arquivo CSV contendo notícias de fato do Ministério Público e identifique APENAS os casos que podem ter repercussão criminal.

**DADOS PARA ANÁLISE:**
- Área: {area}
- Total analisado: {total_relatos} de {total_universo} notícias de fato
- Método: Análise sistemática de classificação multilabel por subtemas

**NOTÍCIAS DE FATO:**
{todos_relatos}

**INSTRUÇÕES:**

1. **Examine cada registro** na coluna "Assunto Inteiro Teor"

2. **Identifique apenas condutas que possam configurar crime** segundo o Código Penal, ECA, ou leis especiais

3. **Agrupe os casos** por tipo de crime similar

4. **Para cada grupo de crimes**, crie uma seção com:
   - **Título**: Nome do grupo de crimes (ex: "CRIMES DE VIOLÊNCIA FÍSICA CONTRA MENORES")
   - **Resumo**: Breve descrição do padrão criminal identificado (2-3 linhas)
   - **Lista de casos** no formato:
     - MPRJ (número do processo) - Nome da Instituição
     - Teor (descrição resumida do fato)
     - Tipificação (artigo do CP/lei especial aplicável)
     - Suspeito (cargo/função do possível autor)

5. **Considere como possíveis crimes APENAS quando houver elementos concretos**:
   - Agressões físicas com contato corporal (maus-tratos, lesão corporal)
   - Violência sexual com ato libidinoso (estupro de vulnerável, importunação)
   - Peculato/desvio comprovado de recursos (merenda, transporte, verbas)
   - Abandono real de incapaz (deixar criança sozinha em risco)
   - Discriminação dolosa grave contra pessoa com deficiência
   - Falsificações documentais comprovadas
   - Concussão (cobrança indevida em escola pública)
   - Prevaricação (omissão deliberada e dolosa de autoridade)

6. **NÃO INCLUA**:
   - Análises sobre o que não é crime
   - Justificativas jurídicas extensas
   - Recomendações ou sugestões
   - Estatísticas gerais
   - Casos que são apenas irregularidades administrativas
   - Condutas rudes ou inadequadas que não configuram crime
   - Gritos, discussões ou conflitos verbais simples
   - Problemas de gestão ou organização escolar
   - Reclamações sobre qualidade de serviços sem dolo

7. **Formato do relatório**:
```markdown
# RELATÓRIO DE ANÁLISE CRIMINAL - NOTÍCIAS DE FATO MPRJ
## ÁREA: EDUCAÇÃO

### INTRODUÇÃO
Este relatório apresenta a análise criminal de {total_relatos} notícias de fato da área de educação, de um universo total de {total_universo} notícias recebidas pelo Ministério Público. A análise utilizou metodologia de classificação multilabel por subtemas para identificar condutas com potencial repercussão criminal. Foram identificados [X] casos que configuram possíveis crimes, organizados por tipo penal para facilitar a atuação ministerial.

---

## 1. [NOME DO GRUPO DE CRIMES]

### RESUMO DA SEÇÃO
[Breve descrição do padrão criminal - 2-3 linhas]

### CASOS IDENTIFICADOS:

**MPRJ [número]** - [Nome da Instituição]
- **Teor:** [descrição resumida do fato]
- **Tipificação:** [artigo e nome do crime]
- **Suspeito:** [cargo/função do possível autor]

**MPRJ [número]** - [Nome da Instituição]
- **Teor:** [descrição resumida do fato]
- **Tipificação:** [artigo e nome do crime]
- **Suspeito:** [cargo/função do possível autor]

---

## 2. [PRÓXIMO GRUPO DE CRIMES]
[Repetir estrutura]
```

**IMPORTANTE**:
- Analise CADA registro individualmente com RIGOR JURÍDICO
- Só inclua casos com indícios CONCRETOS de crime (não meras irregularidades)
- Use o número MPRJ real do arquivo
- Transcreva o teor de forma resumida mas fiel ao original
- Se não houver informação sobre suspeito ou instituição, indique "A identificar"
- SEJA CONSERVADOR: na dúvida, NÃO inclua o caso
- Gritos, discussões ou condutas rudes NÃO são crimes por si só
"""

    return template_criminal

def gerar_prompt_criminal(df: pd.DataFrame, estatisticas: Dict, area: str) -> Optional[str]:
    """Gera prompt para análise criminal"""
    template = carregar_prompt_criminal()
    if not template:
        return None
    
    # Formatar estatísticas
    estatisticas_formatadas = ""
    lista_subtemas = []
    
    # Ordenar por número de casos (decrescente)
    estatisticas_ordenadas = sorted(estatisticas.items(), key=lambda x: x[1]['total'], reverse=True)
    
    for nome_subtema, stats in estatisticas_ordenadas:
        if stats['total'] > 0:
            lista_subtemas.append(nome_subtema)
            estatisticas_formatadas += f"\n### {nome_subtema}\n"
            estatisticas_formatadas += f"- Total de casos: {stats['total']} ({stats['percentual']:.1f}%)\n"
            
            # Adicionar exemplos (primeiros 2 casos)
            if len(stats['casos']) > 0:
                coluna_texto = 'Assunto Resumido' if 'Assunto Resumido' in stats['casos'].columns else 'Assunto Inteiro Teor'
                exemplos = stats['casos'][coluna_texto].head(2).tolist()
                if exemplos:
                    estatisticas_formatadas += "- Exemplos:\n"
                    for i, exemplo in enumerate(exemplos, 1):
                        texto_exemplo = exemplo[:150] + "..." if len(exemplo) > 150 else exemplo
                        estatisticas_formatadas += f"  {i}. {texto_exemplo}\n"
    
    # Coletar todos os relatos relevantes
    todos_relatos = ""
    
    # Usar coluna 'Assunto Inteiro Teor' se disponível, senão 'Assunto Resumido'
    coluna_texto = 'Assunto Inteiro Teor' if 'Assunto Inteiro Teor' in df.columns else 'Assunto Resumido'
    
    # Filtrar apenas registros com subtemas identificados
    df_com_subtemas = df[df['SUBTEMAS_IDENTIFICADOS'].notna() & (df['SUBTEMAS_IDENTIFICADOS'] != '')]

    # Usar todas as notícias para análise criminal completa
    df_limitado = df_com_subtemas

    for idx, row in df_limitado.iterrows():
        relato_texto = row[coluna_texto] if pd.notna(row[coluna_texto]) else row['Assunto Resumido']
        # Limitar tamanho do relato
        relato_texto = relato_texto[:300] + "..." if len(str(relato_texto)) > 300 else relato_texto

        subtemas_caso = row['SUBTEMAS_IDENTIFICADOS']
        mprj = row.get('MPRJ', 'N/A')
        noticiado = row.get('Noticiado', 'N/A')

        todos_relatos += f"### DENÚNCIA #{idx + 1}\n"
        todos_relatos += f"**MPRJ:** {mprj}\n"
        todos_relatos += f"**Noticiado:** {noticiado}\n"
        todos_relatos += f"**Subtemas:** {subtemas_caso}\n"
        todos_relatos += f"**Relato:** {relato_texto}\n"
        todos_relatos += "---\n\n"
    
    # Substituir variáveis no template
    prompt = template.format(
        area=area,
        area_maiuscula=area.upper(),
        total_relatos=len(df_limitado),
        total_universo=len(df),
        num_subtemas=len(lista_subtemas),
        lista_subtemas=", ".join(lista_subtemas),
        estatisticas_formatadas=estatisticas_formatadas,
        todos_relatos=todos_relatos
    )
    
    return prompt

def gerar_relatorio_com_claude(prompt: str, model_name: str, api_key: str) -> Optional[str]:
    """Gera relatório criminal usando Claude Sonnet 4"""
    try:
        cliente = Anthropic(api_key=api_key)
        
        # System prompt especializado para análise criminal
        system_prompt = """Você é um promotor de justiça especializado em identificação de crimes em notícias de fato. Sua tarefa é:

1. Analisar cada relato individualmente
2. Identificar APENAS condutas que configuram crime
3. Agrupar casos por tipo criminal similar
4. Criar tabelas organizadas por grupo de crimes
5. Usar linguagem jurídica precisa

Foque exclusivamente em:
- Identificação de tipos penais concretos
- Agrupamento por similaridade criminal
- Formato tabular organizado
- Informações objetivas (MPRJ, teor, tipificação, suspeito, instituição)

NÃO inclua análises, recomendações ou estatísticas gerais."""
        
        # Fazer a chamada para a API
        resposta = cliente.messages.create(
            model=model_name,
            max_tokens=20000,
            temperature=0.2,  # Mais baixa para análise criminal precisa
            system=system_prompt,
            messages=[
                {"role": "user", "content": prompt}
            ]
        )
        
        # Extrair o conteúdo da resposta
        relatorio = resposta.content[0].text
        return relatorio
    except Exception as e:
        logging.error(f"Erro ao gerar relatório criminal com Claude: {e}")
        return None

def salvar_relatorio(relatorio: str, nome_arquivo: str) -> bool:
    """Salva relatório em arquivo markdown"""
    try:
        if not os.path.exists('data/output'):
            os.makedirs('data/output')
        
        caminho_completo = os.path.join('data/output', nome_arquivo)
        with open(caminho_completo, 'w', encoding='utf-8') as file:
            file.write(relatorio)
        
        logging.info(f"Relatório salvo em {caminho_completo}")
        return True
    except Exception as e:
        logging.error(f"Erro ao salvar relatório: {e}")
        return False

def main():
    """Função principal"""
    if len(sys.argv) != 2:
        print("Uso: python scripts/gerar_relatorio_criminal.py <arquivo_csv>")
        sys.exit(1)
    
    arquivo_csv = sys.argv[1]
    
    # Verificar se arquivo existe
    if not os.path.exists(arquivo_csv):
        logging.error(f"Arquivo não encontrado: {arquivo_csv}")
        sys.exit(1)
    
    # Carregar configurações
    config = carregar_configuracao()
    env_vars = carregar_variaveis_ambiente()
    
    # Configurações
    report_model = config.get('REPORT_MODEL', 'claude-3-5-sonnet-20241022')
    area = config.get('AREA_POLITICA_PUBLICA', 'Educação')
    api_key = env_vars.get('ANTHROPIC_API_KEY')
    
    if not api_key:
        logging.error("ANTHROPIC_API_KEY não encontrada no arquivo .env")
        sys.exit(1)
    
    print(f"📊 Arquivo: {arquivo_csv}")
    print(f"🏛️ Área: {area}")
    print(f"⚖️ Modelo: {report_model}")
    print(f"🔍 Análise: Repercussão Criminal")
    print()
    
    # Carregar dados
    df = carregar_dados_multilabel(arquivo_csv)
    if df is None:
        sys.exit(1)
    
    # Obter subtemas únicos
    subtemas_lista = obter_subtemas_unicos(df)
    if not subtemas_lista:
        logging.error("Nenhum subtema encontrado")
        sys.exit(1)
    
    # Analisar estatísticas
    estatisticas = analisar_estatisticas_criminais(df, subtemas_lista)
    
    # Gerar prompt
    prompt = gerar_prompt_criminal(df, estatisticas, area)
    if not prompt:
        logging.error("Erro ao gerar prompt")
        sys.exit(1)
    
    # Gerar relatório
    print("🔍 Gerando análise criminal com Claude Sonnet 4...")
    relatorio = gerar_relatorio_com_claude(prompt, report_model, api_key)
    if not relatorio:
        logging.error("Erro ao gerar relatório")
        sys.exit(1)
    
    # Salvar relatório
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nome_arquivo = f"relatorio_criminal_{area.lower()}_{timestamp}.md"
    
    if salvar_relatorio(relatorio, nome_arquivo):
        print(f"✅ Relatório criminal gerado: data/output/{nome_arquivo}")
    else:
        print("❌ Erro ao salvar relatório")
        sys.exit(1)

if __name__ == "__main__":
    main()
