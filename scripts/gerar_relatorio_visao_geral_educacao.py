#!/usr/bin/env python3
"""
Script para gerar relatório de visão geral tático para dados de educação
com classificação multilabel no formato SUBTEMAS_IDENTIFICADOS.
"""

import pandas as pd
import os
import sys
import logging
from typing import Dict, List, Optional
from anthropic import Anthropic
import yaml
from datetime import datetime

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def carregar_configuracao() -> Dict:
    """Carrega configurações do arquivo config.yml"""
    try:
        with open('config/config.yml', 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        return config
    except Exception as e:
        logging.error(f"Erro ao carregar configuração: {e}")
        return {}

def carregar_variaveis_ambiente() -> Dict:
    """Carrega variáveis de ambiente do arquivo .env"""
    env_vars = {}
    try:
        with open('.env', 'r') as file:
            for linha in file:
                linha = linha.strip()
                if linha and not linha.startswith('#'):
                    chave, valor = linha.split('=', 1)
                    # Remover aspas se existirem
                    valor = valor.strip().strip('"').strip("'")
                    env_vars[chave] = valor
    except FileNotFoundError:
        logging.warning("Arquivo .env não encontrado")
    except Exception as e:
        logging.error(f"Erro ao carregar .env: {e}")
    
    return env_vars

def carregar_dados_educacao(arquivo_csv: str) -> Optional[pd.DataFrame]:
    """Carrega dados de educação do CSV"""
    try:
        df = pd.read_csv(arquivo_csv, encoding='utf-8')
        logging.info(f"Arquivo carregado: {len(df)} registros")
        return df
    except Exception as e:
        logging.error(f"Erro ao carregar arquivo CSV {arquivo_csv}: {e}")
        return None

def extrair_subtemas_identificados(df: pd.DataFrame) -> Dict:
    """
    Extrai estatísticas dos subtemas da coluna SUBTEMAS_IDENTIFICADOS
    """
    estatisticas = {}

    # Filtrar apenas registros com subtemas identificados
    df_com_subtemas = df[df['SUBTEMAS_IDENTIFICADOS'].notna() & (df['SUBTEMAS_IDENTIFICADOS'] != '')]
    total_registros_com_subtemas = len(df_com_subtemas)

    # Contar ocorrências de cada subtema
    contadores_subtemas = {}

    for idx, row in df.iterrows():
        subtemas_str = row.get('SUBTEMAS_IDENTIFICADOS', '')
        if pd.notna(subtemas_str) and subtemas_str.strip():
            # Dividir por vírgula e limpar espaços
            subtemas = [s.strip() for s in subtemas_str.split(',') if s.strip()]

            for subtema in subtemas:
                if subtema not in contadores_subtemas:
                    contadores_subtemas[subtema] = []
                contadores_subtemas[subtema].append(idx)

    # Criar estatísticas formatadas
    for subtema, indices in contadores_subtemas.items():
        num_casos = len(indices)
        # Calcular percentual baseado apenas nos registros COM subtemas
        percentual = (num_casos / total_registros_com_subtemas * 100) if total_registros_com_subtemas > 0 else 0

        # Obter casos para este subtema
        casos_subtema = df.iloc[indices]

        estatisticas[subtema] = {
            'total': num_casos,
            'percentual': percentual,
            'casos': casos_subtema,
            'indices': indices
        }

    return estatisticas

def carregar_prompt_visao_geral() -> Optional[str]:
    """Carrega o prompt de visão geral do arquivo de configuração"""
    try:
        with open('config/prompts/report_prompts.yml', 'r', encoding='utf-8') as file:
            prompts = yaml.safe_load(file)
        return prompts.get('overview_report', '')
    except Exception as e:
        logging.error(f"Erro ao carregar prompt: {e}")
        return None

def gerar_prompt_visao_geral(df: pd.DataFrame, estatisticas: Dict, area: str) -> Optional[str]:
    """
    Gera prompt para relatório de visão geral tático
    """
    template = carregar_prompt_visao_geral()
    if not template:
        return None
    
    # Formatar estatísticas
    estatisticas_formatadas = ""
    lista_subtemas = []
    
    # Ordenar por número de casos (decrescente)
    estatisticas_ordenadas = sorted(estatisticas.items(), key=lambda x: x[1]['total'], reverse=True)
    
    for nome_subtema, stats in estatisticas_ordenadas:
        if stats['total'] > 0:
            lista_subtemas.append(nome_subtema)
            estatisticas_formatadas += f"\n### {nome_subtema}\n"
            estatisticas_formatadas += f"- Total de casos: {stats['total']} ({stats['percentual']:.1f}%)\n"
            
            # Adicionar exemplos (primeiros 2 casos)
            if len(stats['casos']) > 0:
                # Tentar usar RELATO se disponível, senão usar ASSUNTO_COMUNICACAO
                coluna_exemplo = 'RELATO' if 'RELATO' in stats['casos'].columns else 'ASSUNTO_COMUNICACAO'
                if coluna_exemplo in stats['casos'].columns:
                    exemplos = stats['casos'][coluna_exemplo].dropna().head(2).tolist()
                    if exemplos:
                        estatisticas_formatadas += "- Exemplos:\n"
                        for i, exemplo in enumerate(exemplos, 1):
                            texto_exemplo = str(exemplo)[:150] + "..." if len(str(exemplo)) > 150 else str(exemplo)
                            estatisticas_formatadas += f"  {i}. {texto_exemplo}\n"
    
    # Coletar todos os relatos relevantes
    todos_relatos = ""
    
    # Usar coluna 'TEXTO_ANALISADO' se disponível, senão 'RELATO'
    if 'TEXTO_ANALISADO' in df.columns:
        coluna_texto = 'TEXTO_ANALISADO'
    elif 'RELATO' in df.columns:
        coluna_texto = 'RELATO'
    else:
        coluna_texto = 'ASSUNTO_COMUNICACAO'
    
    # Filtrar apenas registros com subtemas identificados
    df_com_subtemas = df[df['SUBTEMAS_IDENTIFICADOS'].notna() & (df['SUBTEMAS_IDENTIFICADOS'] != '')]
    
    for idx, row in df_com_subtemas.iterrows():
        relato_texto = row[coluna_texto] if pd.notna(row[coluna_texto]) else ""
        subtemas_caso = row['SUBTEMAS_IDENTIFICADOS']
        
        todos_relatos += f"### RELATO #{idx + 1}\n"
        todos_relatos += f"**Subtemas:** {subtemas_caso}\n"
        todos_relatos += f"**Relato:** {relato_texto}\n"
        todos_relatos += "---\n\n"
    
    # Substituir variáveis no template
    prompt = template.format(
        area=area,
        area_maiuscula=area.upper(),
        total_relatos=len(df_com_subtemas),
        num_subtemas=len(lista_subtemas),
        lista_subtemas=", ".join(lista_subtemas),
        estatisticas_formatadas=estatisticas_formatadas,
        todos_relatos=todos_relatos
    )
    
    return prompt

def gerar_relatorio_com_claude(prompt: str, model_name: str, api_key: str) -> Optional[str]:
    """
    Gera relatório usando Claude Sonnet 4
    """
    try:
        cliente = Anthropic(api_key=api_key)
        
        # Fazer a chamada para a API
        resposta = cliente.messages.create(
            model=model_name,
            max_tokens=20000,
            temperature=0.3,
            system="Você é um analista especializado em análise tática de denúncias para o Ministério Público. Sua tarefa é analisar rigorosamente os dados apresentados e identificar padrões sistêmicos, causas raiz e recomendações práticas, sempre distinguindo claramente entre evidências (fatos relatados) e interpretações (hipóteses para investigação). Use linguagem formal, objetiva e analítica.",
            messages=[
                {"role": "user", "content": prompt}
            ]
        )
        
        # Extrair o conteúdo da resposta
        relatorio = resposta.content[0].text
        return relatorio
    except Exception as e:
        logging.error(f"Erro ao gerar relatório com Claude: {e}")
        return None

def salvar_relatorio(relatorio: str, nome_arquivo: str) -> bool:
    """
    Salva relatório em arquivo markdown
    """
    try:
        if not os.path.exists('data/output'):
            os.makedirs('data/output')
            
        caminho_completo = os.path.join('data/output', nome_arquivo)
        
        with open(caminho_completo, 'w', encoding='utf-8') as file:
            file.write(relatorio)
        logging.info(f"Relatório salvo em {caminho_completo}")
        return True
    except Exception as e:
        logging.error(f"Erro ao salvar relatório: {e}")
        return False

def main():
    """Função principal"""
    
    # Verificar argumentos
    if len(sys.argv) != 2:
        print("Uso: python scripts/gerar_relatorio_visao_geral_educacao.py <arquivo_csv>")
        sys.exit(1)
    
    arquivo_csv = sys.argv[1]
    
    if not os.path.exists(arquivo_csv):
        logging.error(f"Arquivo não encontrado: {arquivo_csv}")
        sys.exit(1)
    
    # Carregar variáveis de ambiente
    env_vars = carregar_variaveis_ambiente()
    api_key = env_vars.get("ANTHROPIC_API_KEY")
    
    if not api_key:
        logging.error("ERRO: Variável de ambiente ANTHROPIC_API_KEY não encontrada")
        sys.exit(1)
    
    # Carregar configurações
    config = carregar_configuracao()
    report_model = config.get('REPORT_MODEL', 'claude-3-5-sonnet-20241022')
    area = config.get('AREA_POLITICA_PUBLICA', 'Educação')
    
    print(f"📊 Arquivo: {arquivo_csv}")
    print(f"🏫 Área: {area}")
    print(f"🤖 Modelo: {report_model}")
    print()
    print("=== GERANDO RELATÓRIO DE VISÃO GERAL TÁTICO - EDUCAÇÃO ===")
    
    # Carregar dados
    df = carregar_dados_educacao(arquivo_csv)
    if df is None:
        sys.exit(1)
    
    # Extrair estatísticas dos subtemas
    estatisticas = extrair_subtemas_identificados(df)
    
    if not estatisticas:
        logging.warning("Nenhum subtema encontrado nos dados")
        sys.exit(1)
    
    # Gerar prompt
    prompt = gerar_prompt_visao_geral(df, estatisticas, area)
    if not prompt:
        logging.error("Falha ao gerar prompt")
        sys.exit(1)
    
    # Gerar relatório
    logging.info("Gerando relatório de visão geral tático...")
    relatorio = gerar_relatorio_com_claude(prompt, report_model, api_key)
    if not relatorio:
        logging.error("Falha ao gerar relatório")
        sys.exit(1)
    
    # Salvar relatório
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nome_arquivo = f"relatorio_tatico_visao_geral_educacao_{timestamp}.md"
    
    if salvar_relatorio(relatorio, nome_arquivo):
        print(f"✅ Relatório de visão geral tático gerado com sucesso!")
        print(f"📄 Arquivo: data/output/{nome_arquivo}")
        
        # Mostrar estatísticas resumidas
        print(f"\n📈 Estatísticas:")
        print(f"   • Total de relatos analisados: {len(df)}")
        print(f"   • Relatos com subtemas identificados: {len([r for r in df['SUBTEMAS_IDENTIFICADOS'] if pd.notna(r) and r.strip()])}")
        print(f"   • Número de subtemas únicos: {len(estatisticas)}")
        print(f"   • Subtemas principais:")
        
        # Mostrar top 5 subtemas
        estatisticas_ordenadas = sorted(estatisticas.items(), key=lambda x: x[1]['total'], reverse=True)
        for i, (subtema, stats) in enumerate(estatisticas_ordenadas[:5], 1):
            print(f"     {i}. {subtema}: {stats['total']} casos ({stats['percentual']:.1f}%)")
    else:
        print("❌ Falha ao salvar relatório")
        sys.exit(1)

if __name__ == "__main__":
    main()
