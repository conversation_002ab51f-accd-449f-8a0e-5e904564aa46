#!/usr/bin/env python3
"""
Script de deploy automatizado para Simple Class API.

Este script automatiza o processo de deploy da API em diferentes ambientes.
"""

import os
import sys
import subprocess
import argparse
import json
import time
from pathlib import Path
from typing import Dict, List, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class DeployManager:
    """Deploy manager for Simple Class API."""
    
    def __init__(self, environment: str = "development"):
        self.environment = environment
        self.project_root = project_root
        self.is_production = environment == "production"
        
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp."""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def run_command(self, command: List[str], description: str) -> bool:
        """Run command and return success status."""
        self.log(f"Running: {description}")
        try:
            result = subprocess.run(command, check=True, capture_output=True, text=True)
            self.log(f"✅ {description} - Success")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"❌ {description} - Failed: {e}", "ERROR")
            if e.stderr:
                self.log(f"Error output: {e.stderr}", "ERROR")
            return False
    
    def check_prerequisites(self) -> bool:
        """Check deployment prerequisites."""
        self.log("Checking prerequisites...")
        
        checks = [
            (["python", "--version"], "Python installation"),
            (["poetry", "--version"], "Poetry installation"),
            (["git", "status"], "Git repository status")
        ]
        
        for command, description in checks:
            if not self.run_command(command, description):
                return False
        
        # Check environment file
        env_file = self.project_root / f".env.{self.environment}"
        if self.environment != "development":
            if not env_file.exists():
                self.log(f"❌ Environment file not found: {env_file}", "ERROR")
                return False
            self.log(f"✅ Environment file found: {env_file}")
        
        return True
    
    def install_dependencies(self) -> bool:
        """Install project dependencies."""
        self.log("Installing dependencies...")
        
        if self.is_production:
            return self.run_command(
                ["poetry", "install", "--only=main"],
                "Installing production dependencies"
            )
        else:
            return self.run_command(
                ["poetry", "install"],
                "Installing all dependencies"
            )
    
    def run_tests(self) -> bool:
        """Run tests before deployment."""
        if self.environment == "development":
            self.log("Skipping tests in development mode")
            return True
        
        self.log("Running tests...")
        
        test_commands = [
            (["poetry", "run", "pytest", "tests/test_auth.py", "-v"], "Authentication tests"),
            (["poetry", "run", "pytest", "tests/test_classification.py::TestClassificationEndpoints::test_classify_text_success", "-v"], "Classification tests")
        ]
        
        for command, description in test_commands:
            if not self.run_command(command, description):
                self.log("❌ Tests failed - aborting deployment", "ERROR")
                return False
        
        return True
    
    def build_application(self) -> bool:
        """Build application for deployment."""
        self.log("Building application...")
        
        # Create necessary directories
        directories = ["logs", "temp", "data/uploads"]
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            self.log(f"✅ Directory ready: {directory}")
        
        # Set proper permissions
        if self.is_production:
            for directory in directories:
                dir_path = self.project_root / directory
                os.chmod(dir_path, 0o755)
        
        return True
    
    def deploy_docker(self) -> bool:
        """Deploy using Docker."""
        self.log("Deploying with Docker...")
        
        commands = [
            (["docker", "build", "-t", "simple-class-api", "."], "Building Docker image"),
            (["docker-compose", "-f", "docker-compose.prod.yml", "down"], "Stopping existing containers"),
            (["docker-compose", "-f", "docker-compose.prod.yml", "up", "-d"], "Starting new containers")
        ]
        
        for command, description in commands:
            if not self.run_command(command, description):
                return False
        
        # Wait for containers to be ready
        self.log("Waiting for containers to be ready...")
        time.sleep(10)
        
        # Health check
        return self.run_command(
            ["curl", "-f", "http://localhost:8000/health"],
            "Health check"
        )
    
    def deploy_systemd(self) -> bool:
        """Deploy using systemd service."""
        self.log("Deploying with systemd...")
        
        service_file = self.project_root / "simple-class-api.service"
        if not service_file.exists():
            self.log("❌ Systemd service file not found. Run production_setup.py first.", "ERROR")
            return False
        
        commands = [
            (["sudo", "systemctl", "stop", "simple-class-api"], "Stopping existing service"),
            (["sudo", "systemctl", "daemon-reload"], "Reloading systemd"),
            (["sudo", "systemctl", "start", "simple-class-api"], "Starting service"),
            (["sudo", "systemctl", "enable", "simple-class-api"], "Enabling service")
        ]
        
        for command, description in commands:
            if not self.run_command(command, description):
                return False
        
        # Wait for service to be ready
        time.sleep(5)
        
        # Check service status
        return self.run_command(
            ["sudo", "systemctl", "is-active", "simple-class-api"],
            "Service status check"
        )
    
    def deploy_development(self) -> bool:
        """Deploy for development."""
        self.log("Starting development server...")
        
        # Just verify the application can start
        return self.run_command(
            ["poetry", "run", "python", "-c", "from src.api.main import app; print('✅ Application loads successfully')"],
            "Application startup test"
        )
    
    def post_deploy_checks(self) -> bool:
        """Run post-deployment checks."""
        self.log("Running post-deployment checks...")
        
        if self.environment == "development":
            return True
        
        # Health check
        health_check = self.run_command(
            ["curl", "-f", "http://localhost:8000/health"],
            "API health check"
        )
        
        if not health_check:
            return False
        
        # Test authentication endpoint
        auth_check = self.run_command(
            ["curl", "-f", "-X", "POST", "http://localhost:8000/api/v1/auth/login", 
             "-H", "Content-Type: application/json",
             "-d", '{"email": "<EMAIL>", "password": "test123"}'],
            "Authentication endpoint check"
        )
        
        return auth_check
    
    def rollback(self) -> bool:
        """Rollback deployment."""
        self.log("Rolling back deployment...", "WARNING")
        
        if self.environment == "development":
            self.log("No rollback needed for development")
            return True
        
        # Stop current deployment
        if self.is_production:
            self.run_command(
                ["sudo", "systemctl", "stop", "simple-class-api"],
                "Stopping failed deployment"
            )
        
        self.log("❌ Deployment rolled back", "ERROR")
        return False
    
    def deploy(self) -> bool:
        """Main deployment function."""
        self.log(f"Starting deployment to {self.environment}")
        
        # Check prerequisites
        if not self.check_prerequisites():
            return False
        
        # Install dependencies
        if not self.install_dependencies():
            return False
        
        # Run tests
        if not self.run_tests():
            return False
        
        # Build application
        if not self.build_application():
            return False
        
        # Deploy based on environment
        if self.environment == "development":
            success = self.deploy_development()
        elif self.environment == "production":
            # Choose deployment method
            if (self.project_root / "docker-compose.prod.yml").exists():
                success = self.deploy_docker()
            else:
                success = self.deploy_systemd()
        else:
            self.log(f"❌ Unknown environment: {self.environment}", "ERROR")
            return False
        
        if not success:
            self.rollback()
            return False
        
        # Post-deployment checks
        if not self.post_deploy_checks():
            self.rollback()
            return False
        
        self.log(f"🎉 Deployment to {self.environment} completed successfully!")
        return True


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Deploy Simple Class API")
    parser.add_argument(
        "--environment", "-e",
        choices=["development", "staging", "production"],
        default="development",
        help="Deployment environment"
    )
    parser.add_argument(
        "--skip-tests",
        action="store_true",
        help="Skip running tests"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force deployment even if checks fail"
    )
    
    args = parser.parse_args()
    
    # Create deploy manager
    deploy_manager = DeployManager(args.environment)
    
    # Override test skipping
    if args.skip_tests:
        deploy_manager.run_tests = lambda: True
    
    # Run deployment
    success = deploy_manager.deploy()
    
    if success:
        print("\n" + "="*60)
        print("🎉 DEPLOYMENT SUCCESSFUL!")
        print("="*60)
        
        if args.environment == "development":
            print("🚀 Start the API with:")
            print("   poetry run python scripts/start_api_docs.py --reload")
            print("\n📚 Documentation:")
            print("   http://localhost:8000/docs")
        else:
            print("🔍 Check deployment status:")
            print("   curl http://localhost:8000/health")
            print("\n📊 Monitor logs:")
            if args.environment == "production":
                print("   sudo journalctl -u simple-class-api -f")
        
        sys.exit(0)
    else:
        print("\n" + "="*60)
        print("❌ DEPLOYMENT FAILED!")
        print("="*60)
        print("Check the logs above for details.")
        sys.exit(1)


if __name__ == "__main__":
    main()
