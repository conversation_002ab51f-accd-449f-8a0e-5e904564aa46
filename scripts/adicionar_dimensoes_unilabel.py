#!/usr/bin/env python3
"""
Script para adicionar as dimensões TIPO_UNIDADE e CRE ao arquivo já classificado
"""

import pandas as pd
import requests
import os
import time
from tqdm import tqdm
from datetime import datetime

def carregar_env():
    """Carrega variáveis de ambiente do .env"""
    try:
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✅ Arquivo .env carregado")
    except FileNotFoundError:
        print("❌ Arquivo .env não encontrado")

def classificar_tipo_unidade_cre(texto_resumido, texto_completo, noticiado, api_key):
    """
    Classifica TIPO_UNIDADE e CRE usando Fireworks AI
    """
    prompt = f"""Classifique este relato de educação em 2 dimensões:

TIPO DE UNIDADE (escolha uma):
A = Municipal (escola municipal, EDI, creche municipal, CRE)
B = Estadual (escola estadual, SEEDUC, FAETEC, colégio estadual)  
C = Privada (escola particular, centro educacional privado)

CRE (só para Municipal):
1ª CRE, 2ª CRE, 3ª CRE, 4ª CRE, 5ª CRE, 6ª CRE, 7ª CRE, 8ª CRE, 9ª CRE, 10ª CRE, 11ª CRE, Não se aplica

INSTITUIÇÃO NOTICIADA: {noticiado}
RESUMO: {texto_resumido}
TEXTO COMPLETO: {texto_completo}

Responda EXATAMENTE assim:
TIPO_UNIDADE: [A/B/C]
CRE: [CRE específica ou Não se aplica]"""

    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }

    data = {
        'model': 'accounts/fireworks/models/llama-v3p3-70b-instruct',
        'prompt': prompt,
        'max_tokens': 50,
        'temperature': 0.1
    }

    try:
        response = requests.post('https://api.fireworks.ai/inference/v1/completions', 
                               headers=headers, json=data)
        
        if response.status_code == 200:
            result = response.json()
            response_text = result['choices'][0]['text'].strip()
            
            # Extrair informações
            tipo_unidade = ""
            cre = ""
            
            lines = response_text.split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('TIPO_UNIDADE:'):
                    tipo_part = line.replace('TIPO_UNIDADE:', '').strip().upper()
                    if tipo_part == 'A':
                        tipo_unidade = 'Municipal'
                    elif tipo_part == 'B':
                        tipo_unidade = 'Estadual'
                    elif tipo_part == 'C':
                        tipo_unidade = 'Privada'
                
                elif line.startswith('CRE:'):
                    cre = line.replace('CRE:', '').strip()
            
            return tipo_unidade, cre
        else:
            print(f"❌ Erro na API: {response.status_code}")
            return "", ""
            
    except Exception as e:
        print(f"❌ Erro na classificação: {e}")
        return "", ""

def main():
    """Função principal"""
    
    # Carregar variáveis de ambiente
    carregar_env()
    api_key = os.environ.get('FIREWORKS_API_KEY')
    
    if not api_key:
        print("❌ FIREWORKS_API_KEY não encontrada")
        return
    
    # Carregar arquivo já classificado
    arquivo_entrada = "data/output/ouvidorias_educacao_fireworks_multilabel.csv"
    
    try:
        df = pd.read_csv(arquivo_entrada, encoding='utf-8')
        print(f"✅ Arquivo carregado: {len(df)} registros")
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo: {e}")
        return
    
    # Adicionar colunas para as novas dimensões
    df['TIPO_UNIDADE'] = ''
    df['CRE'] = ''
    
    print("🔥 Adicionando dimensões TIPO_UNIDADE e CRE...")
    
    # Processar cada registro
    for idx, row in tqdm(df.iterrows(), total=len(df), desc="Classificando dimensões unilabel"):
        texto_resumido = str(row.get('Assunto Resumido', ''))
        texto_completo = str(row.get('Assunto Inteiro Teor', ''))
        noticiado = str(row.get('Noticiado', ''))
        
        # Classificar
        tipo_unidade, cre = classificar_tipo_unidade_cre(
            texto_resumido, texto_completo, noticiado, api_key
        )
        
        # Armazenar resultados
        df.at[idx, 'TIPO_UNIDADE'] = tipo_unidade
        df.at[idx, 'CRE'] = cre
        
        # Pausa para evitar rate limiting
        time.sleep(0.5)
    
    # Salvar resultado
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    arquivo_saida = f"data/output/ouvidorias_educacao_classificacao_completa_{timestamp}.csv"
    
    df.to_csv(arquivo_saida, index=False, encoding='utf-8')
    
    # Estatísticas
    total_registros = len(df)
    tipo_unidade_counts = df['TIPO_UNIDADE'].value_counts()
    cre_counts = df['CRE'].value_counts()
    
    print(f"""
🎉 Classificação completa finalizada!

📄 Arquivo salvo: {arquivo_saida}

📊 Estatísticas TIPO_UNIDADE:""")
    
    for tipo, count in tipo_unidade_counts.items():
        if tipo:
            print(f"   • {tipo}: {count} ({count/total_registros*100:.1f}%)")
    
    print(f"""
📊 Estatísticas CRE:""")
    
    for cre, count in cre_counts.items():
        if cre and cre != 'Não se aplica':
            print(f"   • {cre}: {count}")
    
    print(f"""
✅ Processamento concluído com sucesso!
🔥 Método: Llama 3.3 70B - Classificação Completa (3 dimensões)
""")

if __name__ == "__main__":
    main()
