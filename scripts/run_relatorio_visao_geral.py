#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para gerar um relatório executivo de visão geral analisando múltiplos subtemas
de uma área de política pública configurada.
"""

import sys
import os

# Garantir que o diretório raiz do projeto esteja no PYTHONPATH
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importação absoluta
from src.reports.gerador_relatorio_visao_geral import main as gerar_relatorio_visao_geral_main

if __name__ == "__main__":
    gerar_relatorio_visao_geral_main()