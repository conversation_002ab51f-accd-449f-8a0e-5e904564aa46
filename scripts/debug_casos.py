#!/usr/bin/env python3
"""
Script para debugar casos específicos de detecção de nomes.
"""

import pandas as pd
from pathlib import Path
import sys
import re

# Adiciona o diretório scripts ao path
sys.path.append(str(Path(__file__).parent))

from anonimiza import criar_analyzer_customizado
from presidio_anonymizer import AnonymizerEngine

def debug_casos():
    """Debug detalhado dos casos específicos."""
    
    # Carrega dados
    arquivo_entrada = Path('data/input/ouvidorias.csv')
    df = pd.read_csv(arquivo_entrada)
    
    print("=== DEBUG: Casos Específicos ===\n")
    
    # Configura o analyzer
    analyzer = criar_analyzer_customizado()
    
    # Casos específicos
    casos = [
        {
            'linha': 3,
            'texto': df.iloc[2]['Teor'],
            'nome_esperado': 'JACIRA FRANCISCA'
        },
        {
            'linha': 132,
            'texto': df.iloc[131]['Teor'],
            'nome_esperado': 'meu sogro'
        },
        {
            'linha': 69,
            'texto': df.iloc[68]['Teor'],
            'nome_esperado': 'Keilane Vieira Caxias'
        }
    ]
    
    for caso in casos:
        print(f"=== CASO: Linha {caso['linha']} ===")
        texto = str(caso['texto'])
        print(f"Texto completo:\n{texto}\n")
        print(f"Nome esperado: {caso['nome_esperado']}\n")
        
        # Testa padrões específicos
        print("--- Testando Padrões ---")
        
        # 1. Padrão "meu nome e"
        pattern1 = re.compile(r'\b(?:meu\s+)?nome\s+(?:é|e)\s+([A-Z][a-záàáâãéêíóôõúç]+(?:\s+[A-Z][a-záàáâãéêíóôõúç]+)*)', re.IGNORECASE)
        matches1 = pattern1.finditer(texto)
        print("1. Padrão 'meu nome é/e':")
        for match in matches1:
            print(f"   Encontrado: '{match.group(1)}' na posição {match.start(1)}-{match.end(1)}")
        
        # 2. Padrão nomes com idade
        pattern2 = re.compile(r'\b([A-Z][a-záàáâãéêíóôõúç]+(?:\s+[A-Z][a-záàáâãéêíóôõúç]+)+),?\s+\d+\s+anos?\b', re.IGNORECASE)
        matches2 = pattern2.finditer(texto)
        print("2. Padrão 'Nome, XX anos':")
        for match in matches2:
            print(f"   Encontrado: '{match.group(1)}' na posição {match.start(1)}-{match.end(1)}")
        
        # 3. Padrão referências familiares
        pattern3 = re.compile(r'\b(?:meu|minha)\s+(?:sogro|sogra|pai|mãe|avô|avó|genitor|genitora)\b', re.IGNORECASE)
        matches3 = pattern3.finditer(texto)
        print("3. Padrão 'meu/minha + familiar':")
        for match in matches3:
            print(f"   Encontrado: '{match.group()}' na posição {match.start()}-{match.end()}")
        
        # 4. Padrão nomes em maiúscula
        pattern4 = re.compile(r'\b([A-Z]{2,}(?:\s+[A-Z]{2,})*(?:\s+[A-Z][a-záàáâãéêíóôõúç]+)*)\b')
        matches4 = pattern4.finditer(texto)
        print("4. Padrão 'NOMES EM MAIÚSCULA':")
        for match in matches4:
            nome = match.group(1).strip()
            if len(nome) > 4:
                print(f"   Encontrado: '{nome}' na posição {match.start(1)}-{match.end(1)}")
        
        # 5. Análise do Presidio
        print("\n--- Análise Presidio ---")
        resultados = analyzer.analyze(
            text=texto,
            language='pt',
            entities=["PERSON", "PERSON_CONTEXTUAL"]
        )
        
        print(f"Entidades detectadas: {len(resultados)}")
        for i, resultado in enumerate(resultados):
            entidade = texto[resultado.start:resultado.end]
            print(f"  {i+1}. '{entidade}' ({resultado.entity_type}, score: {resultado.score:.2f}, pos: {resultado.start}-{resultado.end})")
        
        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    debug_casos() 