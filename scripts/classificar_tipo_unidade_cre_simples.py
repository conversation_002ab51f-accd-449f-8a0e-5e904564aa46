#!/usr/bin/env python3
"""
Script simples para adicionar TIPO_UNIDADE e CRE baseado em regras
"""

import pandas as pd
import re
from datetime import datetime

def classificar_tipo_unidade(noticiado):
    """
    Classifica tipo de unidade baseado no campo Noticiado
    """
    noticiado_upper = str(noticiado).upper()
    
    # Palavras-chave para Municipal
    municipal_keywords = [
        'ESCOLA MUNICIPAL', 'EDI ', 'CRECHE', 'CRE', 'SME', 
        'SECRETARIA MUNICIPAL', 'PREFEITURA', 'MUNICIPAL'
    ]
    
    # Palavras-chave para Estadual  
    estadual_keywords = [
        'COLÉGIO ESTADUAL', 'ESCOL<PERSON> ESTADUAL', 'SEEDUC', 'FAETEC',
        'CECIERJ', 'CEDERJ', 'ESTADUAL'
    ]
    
    # Palavras-chave para Privada
    privada_keywords = [
        'ESCOLA ', 'COLÉGIO ', 'CENTRO EDUCACIONAL', 'INSTITUTO',
        'MAPLE BEAR', 'CASA MATERNAL', 'CRECHE ESCOLA'
    ]
    
    # Verificar Municipal primeiro (mais específico)
    for keyword in municipal_keywords:
        if keyword in noticiado_upper:
            return 'Municipal'
    
    # Verificar Estadual
    for keyword in estadual_keywords:
        if keyword in noticiado_upper:
            return 'Estadual'
    
    # Verificar se é privada (por exclusão e palavras-chave)
    for keyword in privada_keywords:
        if keyword in noticiado_upper:
            # Se não foi classificada como municipal ou estadual, é privada
            return 'Privada'
    
    return 'Não identificado'

def classificar_cre(texto_completo, tipo_unidade):
    """
    Classifica CRE baseado em menções explícitas no texto
    """
    if tipo_unidade != 'Municipal':
        return 'Não se aplica'
    
    texto_upper = str(texto_completo).upper()
    
    # Buscar menções explícitas de CRE
    cre_patterns = [
        r'(\d+)ª\s*CRE',
        r'(\d+)°\s*CRE', 
        r'(\d+)\s*CRE',
        r'CRE\s*(\d+)',
        r'PRIMEIRA\s*CRE',
        r'SEGUNDA\s*CRE',
        r'TERCEIRA\s*CRE',
        r'QUARTA\s*CRE',
        r'QUINTA\s*CRE',
        r'SEXTA\s*CRE',
        r'SÉTIMA\s*CRE',
        r'SETIMA\s*CRE',
        r'OITAVA\s*CRE',
        r'NONA\s*CRE',
        r'DÉCIMA\s*CRE',
        r'DECIMA\s*CRE'
    ]
    
    for pattern in cre_patterns:
        matches = re.findall(pattern, texto_upper)
        if matches:
            if pattern.startswith(r'(\d+)'):
                numero = matches[0]
                if numero.isdigit() and 1 <= int(numero) <= 11:
                    return f'{numero}ª CRE'
            else:
                # Mapear nomes por extenso
                nome_para_numero = {
                    'PRIMEIRA': '1ª',
                    'SEGUNDA': '2ª', 
                    'TERCEIRA': '3ª',
                    'QUARTA': '4ª',
                    'QUINTA': '5ª',
                    'SEXTA': '6ª',
                    'SÉTIMA': '7ª',
                    'SETIMA': '7ª',
                    'OITAVA': '8ª',
                    'NONA': '9ª',
                    'DÉCIMA': '10ª',
                    'DECIMA': '10ª'
                }
                
                for nome, numero in nome_para_numero.items():
                    if nome in pattern.upper():
                        return f'{numero} CRE'
    
    return 'Não se aplica'

def main():
    """Função principal"""
    
    # Carregar arquivo já classificado
    arquivo_entrada = "data/output/ouvidorias_educacao_fireworks_multilabel.csv"
    
    try:
        df = pd.read_csv(arquivo_entrada, encoding='utf-8')
        print(f"✅ Arquivo carregado: {len(df)} registros")
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo: {e}")
        return
    
    # Adicionar colunas para as novas dimensões
    df['TIPO_UNIDADE'] = ''
    df['CRE'] = ''
    
    print("🔥 Classificando TIPO_UNIDADE e CRE...")
    
    # Processar cada registro
    for idx, row in df.iterrows():
        noticiado = row.get('Noticiado', '')
        texto_completo = str(row.get('Assunto Inteiro Teor', '')) + ' ' + str(row.get('Assunto Resumido', ''))
        
        # Classificar tipo de unidade
        tipo_unidade = classificar_tipo_unidade(noticiado)
        df.at[idx, 'TIPO_UNIDADE'] = tipo_unidade
        
        # Classificar CRE
        cre = classificar_cre(texto_completo, tipo_unidade)
        df.at[idx, 'CRE'] = cre
    
    # Salvar resultado
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    arquivo_saida = f"data/output/ouvidorias_educacao_classificacao_completa_{timestamp}.csv"
    
    df.to_csv(arquivo_saida, index=False, encoding='utf-8')
    
    # Estatísticas
    total_registros = len(df)
    tipo_unidade_counts = df['TIPO_UNIDADE'].value_counts()
    cre_counts = df['CRE'].value_counts()
    
    print(f"""
🎉 Classificação completa finalizada!

📄 Arquivo salvo: {arquivo_saida}

📊 Estatísticas TIPO_UNIDADE:""")
    
    for tipo, count in tipo_unidade_counts.items():
        if tipo:
            print(f"   • {tipo}: {count} ({count/total_registros*100:.1f}%)")
    
    print(f"""
📊 Estatísticas CRE:""")
    
    for cre, count in cre_counts.items():
        if cre and cre != 'Não se aplica':
            print(f"   • {cre}: {count}")
    
    print(f"""
✅ Processamento concluído com sucesso!
📋 Método: Classificação baseada em regras + Llama 3.3 70B (subtemas)
""")

if __name__ == "__main__":
    main()
