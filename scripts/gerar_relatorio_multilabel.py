#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para gerar relatórios baseados em classificação multilabel.

Este script permite gerar relatórios de diferentes tipos:
1. Relatório de visão geral da área completa
2. Relatórios individuais por subtema
3. Relatórios combinados de subtemas específicos

Uso:
    python scripts/gerar_relatorio_multilabel.py --tipo visao_geral
    python scripts/gerar_relatorio_multilabel.py --tipo subtema --subtema ONCOLOGIA
    python scripts/gerar_relatorio_multilabel.py --tipo todos
"""

import sys
import os
import argparse
import logging

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.reports.gerador_relatorio_multilabel import (
    gerar_relatorio_area_completa,
    gerar_relatorio_subtema_especifico,
    carregar_dados_multilabel,
    obter_colunas_subtemas,
    analisar_estatisticas_subtemas
)
from src.utils.config_utils import carregar_configuracao, carregar_variaveis_ambiente

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def listar_subtemas_disponiveis(arquivo_csv: str) -> None:
    """
    Lista os subtemas disponíveis no arquivo CSV.
    
    Args:
        arquivo_csv (str): Caminho para o arquivo CSV
    """
    df = carregar_dados_multilabel(arquivo_csv)
    if df is None:
        return
    
    colunas_subtemas = obter_colunas_subtemas(df)
    estatisticas = analisar_estatisticas_subtemas(df, colunas_subtemas)
    
    print("\n=== SUBTEMAS DISPONÍVEIS ===")
    print(f"{'Subtema':<30} {'Casos':<8} {'%':<6}")
    print("-" * 50)
    
    for nome_subtema, stats in sorted(estatisticas.items(), key=lambda x: x[1]['total'], reverse=True):
        if stats['total'] > 0:
            codigo_subtema = nome_subtema.upper().replace(' ', '_')
            print(f"{codigo_subtema:<30} {stats['total']:<8} {stats['percentual']:<6.1f}")
    
    print(f"\nTotal de registros: {len(df)}")

def gerar_relatorio_visao_geral(arquivo_csv: str, area: str, api_key: str, model_name: str) -> bool:
    """
    Gera relatório de visão geral da área.
    
    Args:
        arquivo_csv (str): Caminho para o arquivo CSV
        area (str): Nome da área
        api_key (str): Chave API
        model_name (str): Nome do modelo
        
    Returns:
        bool: True se sucesso
    """
    print(f"\n=== GERANDO RELATÓRIO DE VISÃO GERAL - {area.upper()} ===")
    sucesso = gerar_relatorio_area_completa(arquivo_csv, area, api_key, model_name)
    
    if sucesso:
        print(f"✅ Relatório de visão geral gerado: data/output/relatorio_visao_geral_{area.lower()}_multilabel.md")
    else:
        print("❌ Falha ao gerar relatório de visão geral")
    
    return sucesso

def gerar_relatorio_subtema(arquivo_csv: str, subtema: str, api_key: str, model_name: str) -> bool:
    """
    Gera relatório para um subtema específico.
    
    Args:
        arquivo_csv (str): Caminho para o arquivo CSV
        subtema (str): Nome do subtema
        api_key (str): Chave API
        model_name (str): Nome do modelo
        
    Returns:
        bool: True se sucesso
    """
    print(f"\n=== GERANDO RELATÓRIO INDIVIDUAL - {subtema} ===")
    sucesso = gerar_relatorio_subtema_especifico(arquivo_csv, subtema, api_key, model_name)
    
    if sucesso:
        print(f"✅ Relatório individual gerado: data/output/relatorio_{subtema.lower()}_multilabel.md")
    else:
        print(f"❌ Falha ao gerar relatório para subtema {subtema}")
    
    return sucesso

def gerar_todos_relatorios(arquivo_csv: str, area: str, api_key: str, model_name: str, min_casos: int = 3) -> None:
    """
    Gera todos os relatórios possíveis.
    
    Args:
        arquivo_csv (str): Caminho para o arquivo CSV
        area (str): Nome da área
        api_key (str): Chave API
        model_name (str): Nome do modelo
        min_casos (int): Mínimo de casos para gerar relatório individual
    """
    print(f"\n=== GERANDO TODOS OS RELATÓRIOS - {area.upper()} ===")
    
    # 1. Relatório de visão geral
    print("\n1. Relatório de Visão Geral...")
    gerar_relatorio_visao_geral(arquivo_csv, area, api_key, model_name)
    
    # 2. Relatórios individuais por subtema
    print(f"\n2. Relatórios Individuais (mínimo {min_casos} casos)...")
    
    df = carregar_dados_multilabel(arquivo_csv)
    if df is None:
        return
    
    colunas_subtemas = obter_colunas_subtemas(df)
    estatisticas = analisar_estatisticas_subtemas(df, colunas_subtemas)
    
    relatorios_gerados = 0
    for nome_subtema, stats in estatisticas.items():
        if stats['total'] >= min_casos:
            subtema_codigo = nome_subtema.upper().replace(' ', '_')
            sucesso = gerar_relatorio_subtema(arquivo_csv, subtema_codigo, api_key, model_name)
            if sucesso:
                relatorios_gerados += 1
    
    print(f"\n✅ Total de relatórios gerados: {relatorios_gerados + 1}")  # +1 para visão geral

def main():
    """Função principal."""
    parser = argparse.ArgumentParser(description='Gerador de Relatórios Multilabel')
    parser.add_argument('--tipo', choices=['visao_geral', 'subtema', 'todos', 'listar'], 
                       default='visao_geral', help='Tipo de relatório a gerar')
    parser.add_argument('--subtema', help='Nome do subtema (ex: ONCOLOGIA)')
    parser.add_argument('--arquivo', default='data/output/relatos_classificados_multilabel.csv',
                       help='Caminho para arquivo CSV com classificação multilabel')
    parser.add_argument('--min-casos', type=int, default=3,
                       help='Mínimo de casos para gerar relatório individual')
    
    args = parser.parse_args()
    
    # Verificar se arquivo existe
    if not os.path.exists(args.arquivo):
        print(f"❌ Arquivo não encontrado: {args.arquivo}")
        print("\nPrimeiro execute a classificação multilabel:")
        print("python scripts/executar_multilabel.py --area SAUDE")
        return
    
    # Listar subtemas disponíveis
    if args.tipo == 'listar':
        listar_subtemas_disponiveis(args.arquivo)
        return
    
    # Carregar configurações
    env_vars = carregar_variaveis_ambiente()
    api_key = env_vars.get("ANTHROPIC_API_KEY")
    
    if not api_key:
        print("❌ ERRO: Variável de ambiente ANTHROPIC_API_KEY não encontrada")
        return
    
    config = carregar_configuracao()
    if not config:
        print("❌ ERRO: Não foi possível carregar configurações")
        return
    
    report_model = config.get('REPORT_MODEL', 'claude-3-5-sonnet-20241022')
    area = config.get('AREA_POLITICA_PUBLICA', 'SAUDE')
    
    print(f"📊 Arquivo: {args.arquivo}")
    print(f"🏥 Área: {area}")
    print(f"🤖 Modelo: {report_model}")
    
    # Executar ação solicitada
    if args.tipo == 'visao_geral':
        gerar_relatorio_visao_geral(args.arquivo, area, api_key, report_model)
    
    elif args.tipo == 'subtema':
        if not args.subtema:
            print("❌ ERRO: Para tipo 'subtema', especifique --subtema")
            print("\nUse --tipo listar para ver subtemas disponíveis")
            return
        gerar_relatorio_subtema(args.arquivo, args.subtema, api_key, report_model)
    
    elif args.tipo == 'todos':
        gerar_todos_relatorios(args.arquivo, area, api_key, report_model, args.min_casos)

if __name__ == "__main__":
    main() 