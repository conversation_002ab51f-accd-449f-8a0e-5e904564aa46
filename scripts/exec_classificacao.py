import pandas as pd
import re
from datetime import datetime

def classificar_tipo_unidade(noticiado):
    noticiado_upper = str(noticiado).upper()
    
    municipal_keywords = ['ESCOLA MUNICIPAL', 'EDI ', 'CRECHE', 'CRE', 'SME', 'SECRETARIA MUNICIPAL', 'PREFEITURA', 'MUN<PERSON><PERSON><PERSON>']
    estadual_keywords = ['COLÉGIO ESTADUAL', 'ESCOLA ESTADUAL', 'SEEDUC', 'FAETEC', 'CECIERJ', 'CEDERJ', 'ESTADUAL']
    
    for keyword in municipal_keywords:
        if keyword in noticiado_upper:
            return 'Municipal'
    
    for keyword in estadual_keywords:
        if keyword in noticiado_upper:
            return 'Estadual'
    
    return 'Privada'

def classificar_cre(texto_completo, tipo_unidade):
    if tipo_unidade != 'Municipal':
        return 'Não se aplica'
    
    texto_upper = str(texto_completo).upper()
    
    cre_patterns = [r'(\d+)ª\s*CRE', r'(\d+)°\s*CRE', r'(\d+)\s*CRE', r'CRE\s*(\d+)', r'SETIMA\s*CRE', r'SÉTIMA\s*CRE']
    
    for pattern in cre_patterns:
        matches = re.findall(pattern, texto_upper)
        if matches:
            if pattern.startswith(r'(\d+)'):
                numero = matches[0]
                if numero.isdigit() and 1 <= int(numero) <= 11:
                    return f'{numero}ª CRE'
            elif 'SETIMA' in pattern or 'SÉTIMA' in pattern:
                return '7ª CRE'
    
    return 'Não se aplica'

print("🔥 Iniciando classificação completa...")

df = pd.read_csv("data/output/ouvidorias_educacao_fireworks_multilabel.csv", encoding='utf-8')
print(f"✅ Arquivo carregado: {len(df)} registros")

df['TIPO_UNIDADE'] = ''
df['CRE'] = ''

for idx, row in df.iterrows():
    noticiado = row.get('Noticiado', '')
    texto_completo = str(row.get('Assunto Inteiro Teor', '')) + ' ' + str(row.get('Assunto Resumido', ''))
    
    tipo_unidade = classificar_tipo_unidade(noticiado)
    df.at[idx, 'TIPO_UNIDADE'] = tipo_unidade
    
    cre = classificar_cre(texto_completo, tipo_unidade)
    df.at[idx, 'CRE'] = cre

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
arquivo_saida = f"data/output/ouvidorias_educacao_classificacao_completa_{timestamp}.csv"
df.to_csv(arquivo_saida, index=False, encoding='utf-8')

total_registros = len(df)
tipo_unidade_counts = df['TIPO_UNIDADE'].value_counts()
cre_counts = df['CRE'].value_counts()

print(f"""
🎉 Classificação completa finalizada!

📄 Arquivo salvo: {arquivo_saida}

📊 Estatísticas TIPO_UNIDADE:""")

for tipo, count in tipo_unidade_counts.items():
    if tipo:
        print(f"   • {tipo}: {count} ({count/total_registros*100:.1f}%)")

print(f"""
📊 Estatísticas CRE:""")

for cre, count in cre_counts.items():
    if cre and cre != 'Não se aplica':
        print(f"   • {cre}: {count}")

print(f"""
✅ Processamento concluído com sucesso!
📋 Método: Classificação baseada em regras + Llama 3.3 70B (subtemas)
""")
