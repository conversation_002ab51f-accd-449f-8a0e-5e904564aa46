#!/usr/bin/env python3
"""
Script para debugar a resposta do modelo Fireworks AI
"""

import requests
import os

# Carregar .env manualmente
with open('.env', 'r') as f:
    for line in f:
        line = line.strip()
        if line and not line.startswith('#') and '=' in line:
            key, value = line.split('=', 1)
            os.environ[key] = value

api_key = os.environ['FIREWORKS_API_KEY']
url = 'https://api.fireworks.ai/inference/v1/completions'

# Teste com um caso real simples
prompt = '''Você é um classificador especializado em análise de relatos da área de EDUCAÇÃO.

TAREFA: Classifique este relato em 3 dimensões:

RELATO: Em marco de 2024 o meu filho Arthur de 10 anos autista relatou que o estagiario que o mediava na escola introduziu o dedo no anus dele! A escola e a setima CRE nao ofereceram nenhum tipo de apoio psicologico.

FORMATO DE RESPOSTA:
SUBTEMAS: [números separados por vírgula]
TIPO_UNIDADE: [A, B ou C]  
CRE: [1ª CRE, 2ª CRE, etc]

RESPOSTA:'''

headers = {
    'Authorization': f'Bearer {api_key}',
    'Content-Type': 'application/json'
}

data = {
    'model': 'accounts/fireworks/models/llama-v3p3-70b-instruct',
    'prompt': prompt,
    'max_tokens': 100,
    'temperature': 0.1
}

response = requests.post(url, headers=headers, json=data)
print('Status:', response.status_code)
if response.status_code == 200:
    result = response.json()
    resposta = result['choices'][0]['text']
    print('Resposta completa:')
    print(repr(resposta))
    print()
    print('Resposta formatada:')
    print(resposta)
    print()
    
    # Testar parsing
    print('=== TESTE DE PARSING ===')
    lines = resposta.split('\n')
    for i, line in enumerate(lines):
        print(f'Linha {i}: {repr(line)}')
        
    # Tentar extrair informações
    identified_subtemas = []
    tipo_unidade = ""
    cre = ""
    
    for line in lines:
        line = line.strip()
        print(f'Processando linha: {repr(line)}')
        
        if line.startswith('SUBTEMAS:'):
            subtemas_part = line.replace('SUBTEMAS:', '').strip().upper()
            print(f'Subtemas encontrados: {repr(subtemas_part)}')
            
        elif line.startswith('TIPO_UNIDADE:'):
            tipo_part = line.replace('TIPO_UNIDADE:', '').strip().upper()
            print(f'Tipo unidade encontrado: {repr(tipo_part)}')
            
        elif line.startswith('CRE:'):
            cre_part = line.replace('CRE:', '').strip()
            print(f'CRE encontrado: {repr(cre_part)}')
    
else:
    print('Erro:', response.json())
