#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script principal para processamento completo do arquivo ouvidorias.csv.

Este script permite executar todos os processos de análise, geração de relatórios
e anonimização especificando qual coluna do arquivo contém o texto a ser processado.
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# Adicionar o diretório raiz ao PYTHONPATH
sys.path.insert(0, str(Path(__file__).parent.parent))

def verificar_arquivo_entrada():
    """Verifica se o arquivo de entrada existe."""
    arquivo_entrada = "data/input/ouvidorias.csv"
    if not os.path.exists(arquivo_entrada):
        print(f"❌ ERRO: Arquivo '{arquivo_entrada}' não encontrado.")
        print("\nPor favor, certifique-se de que o arquivo está no local correto:")
        print(f"  📁 {os.path.abspath(arquivo_entrada)}")
        return False
    return True

def mostrar_colunas_disponiveis():
    """Mostra as colunas disponíveis no arquivo ouvidorias.csv."""
    try:
        import pandas as pd
        df = pd.read_csv("data/input/ouvidorias.csv")
        print("\n📋 COLUNAS DISPONÍVEIS NO ARQUIVO:")
        print("=" * 40)
        for i, coluna in enumerate(df.columns, 1):
            print(f"{i:2d}. {coluna}")
        print("=" * 40)
        print(f"Total: {len(df.columns)} colunas | {len(df)} registros")
        return df.columns.tolist()
    except Exception as e:
        print(f"❌ Erro ao ler arquivo: {e}")
        return []

def executar_analise_individual(coluna_texto):
    """Executa análise individual."""
    print("\n🔍 EXECUTANDO ANÁLISE INDIVIDUAL")
    print("=" * 50)
    
    cmd = [
        sys.executable, 
        "src/analisadores/analisador_relatos.py", 
        coluna_texto
    ]
    
    try:
        resultado = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
        if resultado.returncode == 0:
            print("✅ Análise individual concluída com sucesso!")
            return True
        else:
            print(f"❌ Erro na análise individual: {resultado.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ Timeout na análise individual (1 hora)")
        return False
    except Exception as e:
        print(f"❌ Erro ao executar análise individual: {e}")
        return False

def executar_comparacao_metodos(coluna_texto):
    """Executa comparação entre métodos."""
    print("\n📊 EXECUTANDO COMPARAÇÃO DE MÉTODOS")
    print("=" * 50)
    
    cmd = [
        sys.executable,
        "src/analisadores/run_comparacao.py",
        coluna_texto
    ]
    
    try:
        resultado = subprocess.run(cmd, capture_output=True, text=True, timeout=7200)  # 2 horas
        if resultado.returncode == 0:
            print("✅ Comparação de métodos concluída com sucesso!")
            return True
        else:
            print(f"❌ Erro na comparação: {resultado.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ Timeout na comparação (2 horas)")
        return False
    except Exception as e:
        print(f"❌ Erro ao executar comparação: {e}")
        return False

def executar_relatorio_geral(coluna_texto):
    """Executa geração de relatório geral."""
    print("\n📄 EXECUTANDO GERAÇÃO DE RELATÓRIO GERAL")
    print("=" * 50)
    
    cmd = [
        sys.executable,
        "scripts/run_relatorio.py"
    ]
    
    try:
        resultado = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 min
        if resultado.returncode == 0:
            print("✅ Relatório geral gerado com sucesso!")
            return True
        else:
            print(f"❌ Erro na geração de relatório: {resultado.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ Timeout na geração de relatório (30 min)")
        return False
    except Exception as e:
        print(f"❌ Erro ao executar geração de relatório: {e}")
        return False

def executar_anonimizacao(coluna_texto):
    """Executa anonimização dos dados."""
    print("\n🔒 EXECUTANDO ANONIMIZAÇÃO")
    print("=" * 50)
    
    cmd = [
        sys.executable,
        "scripts/anonimiza.py",
        coluna_texto
    ]
    
    try:
        resultado = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 min
        if resultado.returncode == 0:
            print("✅ Anonimização concluída com sucesso!")
            return True
        else:
            print(f"❌ Erro na anonimização: {resultado.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ Timeout na anonimização (30 min)")
        return False
    except Exception as e:
        print(f"❌ Erro ao executar anonimização: {e}")
        return False

def main():
    """Função principal."""
    parser = argparse.ArgumentParser(
        description="Processamento completo do arquivo ouvidorias.csv",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
EXEMPLOS DE USO:

1. Mostrar colunas disponíveis:
   python scripts/processar_ouvidorias.py --mostrar-colunas

2. Executar análise individual apenas:
   python scripts/processar_ouvidorias.py Teor --individual

3. Executar análise completa (todos os processos):
   python scripts/processar_ouvidorias.py Teor --completo

4. Executar processos específicos:
   python scripts/processar_ouvidorias.py Teor --comparacao --relatorio
        """
    )
    
    # Argumentos principais
    parser.add_argument(
        "coluna_texto",
        nargs="?",
        help="Nome da coluna que contém o texto a ser processado"
    )
    
    # Opções de execução
    parser.add_argument(
        "--mostrar-colunas",
        action="store_true",
        help="Mostrar colunas disponíveis no arquivo e sair"
    )
    
    parser.add_argument(
        "--individual",
        action="store_true",
        help="Executar apenas análise individual"
    )
    
    parser.add_argument(
        "--comparacao",
        action="store_true",
        help="Executar comparação entre métodos"
    )
    
    parser.add_argument(
        "--relatorio",
        action="store_true",
        help="Executar geração de relatório"
    )
    
    parser.add_argument(
        "--anonimizacao",
        action="store_true",
        help="Executar anonimização dos dados"
    )
    
    parser.add_argument(
        "--completo",
        action="store_true",
        help="Executar todos os processos (análise, comparação, relatório e anonimização)"
    )
    
    args = parser.parse_args()
    
    # Cabeçalho
    print("🏛️ " + "=" * 58)
    print("  PROCESSADOR DE OUVIDORIAS - SISTEMA COMPLETO")
    print("=" * 60)
    print("📁 Arquivo de entrada: data/input/ouvidorias.csv")
    print("📂 Diretório de saída: data/output/")
    print()
    
    # Verificar arquivo de entrada
    if not verificar_arquivo_entrada():
        sys.exit(1)
    
    # Mostrar colunas disponíveis
    colunas_disponiveis = mostrar_colunas_disponiveis()
    
    if args.mostrar_colunas:
        print("\n💡 Use o nome exato da coluna como parâmetro para processar.")
        print("   Exemplo: python scripts/processar_ouvidorias.py 'Teor' --individual")
        sys.exit(0)
    
    # Validar coluna especificada
    if not args.coluna_texto:
        print("❌ ERRO: Especifique a coluna a ser processada.")
        print("💡 Use --mostrar-colunas para ver as opções disponíveis.")
        sys.exit(1)
    
    if args.coluna_texto not in colunas_disponiveis:
        print(f"❌ ERRO: Coluna '{args.coluna_texto}' não encontrada.")
        print("💡 Use --mostrar-colunas para ver as opções disponíveis.")
        sys.exit(1)
    
    print(f"🎯 Coluna selecionada: '{args.coluna_texto}'")
    
    # Determinar quais processos executar
    processos = []
    
    if args.completo:
        processos = ["individual", "comparacao", "relatorio", "anonimizacao"]
    else:
        if args.individual:
            processos.append("individual")
        if args.comparacao:
            processos.append("comparacao")
        if args.relatorio:
            processos.append("relatorio")
        if args.anonimizacao:
            processos.append("anonimizacao")
    
    if not processos:
        print("❌ ERRO: Especifique pelo menos um processo para executar.")
        print("💡 Use --help para ver as opções disponíveis.")
        sys.exit(1)
    
    print(f"🚀 Processos a executar: {', '.join(processos)}")
    print()
    
    # Executar processos
    sucessos = 0
    total = len(processos)
    
    for processo in processos:
        if processo == "individual":
            if executar_analise_individual(args.coluna_texto):
                sucessos += 1
        elif processo == "comparacao":
            if executar_comparacao_metodos(args.coluna_texto):
                sucessos += 1
        elif processo == "relatorio":
            if executar_relatorio_geral(args.coluna_texto):
                sucessos += 1
        elif processo == "anonimizacao":
            if executar_anonimizacao(args.coluna_texto):
                sucessos += 1
    
    # Relatório final
    print("\n" + "=" * 60)
    print("📊 RELATÓRIO FINAL")
    print("=" * 60)
    print(f"✅ Processos executados com sucesso: {sucessos}/{total}")
    print(f"❌ Processos com falha: {total - sucessos}/{total}")
    
    if sucessos == total:
        print("\n🎉 TODOS OS PROCESSOS CONCLUÍDOS COM SUCESSO!")
        print("📂 Verifique os resultados em: data/output/")
    else:
        print(f"\n⚠️  ALGUNS PROCESSOS FALHARAM.")
        print("🔍 Verifique os logs acima para mais detalhes.")
    
    print("=" * 60)

if __name__ == "__main__":
    main() 