#!/usr/bin/env python3
"""
Script to check storage bucket configuration.
"""

import os
import sys
import psycopg2
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv

def check_storage_bucket():
    """Check storage bucket in database."""
    print("📦 Checking storage bucket configuration...")
    
    # Load environment variables
    load_dotenv()
    
    # Get database credentials
    host = os.getenv("SUPABASE_DB_HOST")
    port = os.getenv("SUPABASE_DB_PORT", "5432")
    database = os.getenv("SUPABASE_DB_NAME")
    user = os.getenv("SUPABASE_DB_USER")
    password = os.getenv("SUPABASE_DB_PASSWORD")
    
    try:
        # Create connection
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=user,
            password=password,
            sslmode='require'
        )
        
        cursor = conn.cursor()
        
        # Check storage tables
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'storage'
            ORDER BY table_name;
        """)
        
        storage_tables = cursor.fetchall()
        print(f"📋 Storage tables: {[table[0] for table in storage_tables]}")
        
        # Check if buckets table exists
        if ('buckets',) in storage_tables:
            # Check all buckets
            cursor.execute("""
                SELECT id, name, public, file_size_limit, allowed_mime_types, created_at
                FROM storage.buckets
                ORDER BY created_at;
            """)
            
            buckets = cursor.fetchall()
            print(f"\n📦 Buckets in database ({len(buckets)} total):")
            
            for bucket in buckets:
                print(f"   - ID: {bucket[0]}")
                print(f"     Name: {bucket[1]}")
                print(f"     Public: {bucket[2]}")
                print(f"     Size limit: {bucket[3]} bytes")
                print(f"     MIME types: {bucket[4]}")
                print(f"     Created: {bucket[5]}")
                print()
            
            # Check specifically for our bucket
            cursor.execute("""
                SELECT * FROM storage.buckets WHERE id = 'simple-class-files';
            """)
            
            our_bucket = cursor.fetchone()
            if our_bucket:
                print("✅ Our bucket 'simple-class-files' exists in database")
            else:
                print("❌ Our bucket 'simple-class-files' NOT found in database")
                
                # Try to create it
                print("🔧 Attempting to create bucket...")
                try:
                    cursor.execute("""
                        INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
                        VALUES (
                            'simple-class-files',
                            'simple-class-files',
                            false,
                            52428800,
                            ARRAY['text/csv', 'application/pdf', 'text/plain', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
                        );
                    """)
                    conn.commit()
                    print("✅ Bucket created successfully!")
                except Exception as e:
                    print(f"❌ Failed to create bucket: {e}")
                    conn.rollback()
        
        else:
            print("❌ Storage buckets table not found")
        
        # Check storage policies
        cursor.execute("""
            SELECT schemaname, tablename, policyname, cmd
            FROM pg_policies 
            WHERE schemaname = 'storage'
            ORDER BY tablename, policyname;
        """)
        
        storage_policies = cursor.fetchall()
        print(f"\n🔐 Storage policies ({len(storage_policies)} total):")
        
        for policy in storage_policies:
            print(f"   - {policy[1]}.{policy[2]} ({policy[3]})")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Storage check failed: {e}")
        return False

def test_supabase_api_access():
    """Test Supabase API access to storage."""
    print("\n🔗 Testing Supabase API access...")
    
    from supabase import create_client, Client
    
    # Load environment variables
    load_dotenv()
    
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_KEY")
    
    try:
        supabase: Client = create_client(url, key)
        
        # Try to list buckets via API
        buckets = supabase.storage.list_buckets()
        print(f"📦 Buckets via API: {[bucket.name for bucket in buckets]}")
        
        # Try to access our specific bucket
        try:
            files = supabase.storage.from_('simple-class-files').list()
            print("✅ Can access 'simple-class-files' bucket via API")
            print(f"   Files in bucket: {len(files)}")
        except Exception as e:
            print(f"❌ Cannot access 'simple-class-files' bucket via API: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Supabase API test failed: {e}")
        return False

def main():
    """Main function."""
    print("📦 Storage Bucket Check")
    print("=" * 40)
    
    # Check database
    db_ok = check_storage_bucket()
    
    # Test API access
    api_ok = test_supabase_api_access()
    
    print("\n" + "=" * 40)
    print("📊 Results:")
    print(f"   Database: {'✅ OK' if db_ok else '❌ FAILED'}")
    print(f"   API Access: {'✅ OK' if api_ok else '❌ FAILED'}")

if __name__ == "__main__":
    main()
