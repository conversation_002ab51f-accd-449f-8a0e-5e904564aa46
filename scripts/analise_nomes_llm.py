#!/usr/bin/env python3
"""
Script para análise de nomes usando LLM e comparação com o script de anonimização.

Identifica nomes de pessoas na coluna "Teor" do arquivo ouvidorias.csv usando
análise de linguagem natural e compara com a detecção do script anonimiza.py.
"""

import pandas as pd
import re
from pathlib import Path
from typing import List, Dict, Tuple
import sys
import os

# Adiciona o diretório scripts ao path para importar o módulo anonimiza
sys.path.append(str(Path(__file__).parent))

# Importa funções do script de anonimização
from anonimiza import criar_analyzer_customizado, anonimizar_relato
from presidio_anonymizer import AnonymizerEngine


def analisar_nomes_com_llm(texto: str) -> List[Dict[str, any]]:
    """
    Analisa um texto usando heurísticas de LLM para identificar nomes de pessoas.
    
    Args:
        texto: Texto para análise
        
    Returns:
        Lista de dicionários com informações sobre nomes encontrados
    """
    if pd.isna(texto) or not str(texto).strip():
        return []
    
    texto_str = str(texto).upper()
    nomes_encontrados = []
    
    # Padrões para identificar nomes de pessoas
    padroes_nomes = [
        # Padrão 1: Contextos seguidos de nomes próprios
        r'\b(?:MÃE|PAI|FILHO|FILHA|ESPOSO|ESPOSA|MARIDO|VIZINHO|VIZINHA|AMIGO|AMIGA|SENHORA?|DONA|PACIENTE|RESPONSÁVEL|ACOMPANHANTE|IDOSA?|JOVEM|PESSOA|INDIVÍDUO|CIDADÃO|CIDADÃ|COMUNICANTE|NOTICIANTE|VÍTIMA|DENUNCIANTE)\s+([A-ZÁÀÁÂÃÉÊÍÓÔÕÚÇ][A-ZÁÀÁÂÃÉÊÍÓÔÕÚÇ\s\.]{2,50}?)(?=\s*[,\.!?\n]|$)',
        
        # Padrão 2: "Nome é/e" seguido de nome
        r'\b(?:MEU\s+)?NOME\s+[EÉ]?\s+([A-ZÁÀÁÂÃÉÊÍÓÔÕÚÇ][A-ZÁÀÁÂÃÉÊÍÓÔÕÚÇ\s\.]{2,50}?)(?=\s*[,\.!?\n]|$)',
        
        # Padrão 3: "Chamado/chamada" seguido de nome
        r'\b(?:CHAMADA?|CONHECIDA?)\s+([A-ZÁÀÁÂÃÉÊÍÓÔÕÚÇ][A-ZÁÀÁÂÃÉÊÍÓÔÕÚÇ\s\.]{2,50}?)(?=\s*[,\.!?\n]|$)',
        
        # Padrão 4: Nomes em contextos médicos
        r'\b(?:DR\.?|DRA\.?|DOUTOR|DOUTORA|MÉDICA?|ENFERMEIRA?)\s+([A-ZÁÀÁÂÃÉÊÍÓÔÕÚÇ][A-ZÁÀÁÂÃÉÊÍÓÔÕÚÇ\s\.]{2,50}?)(?=\s*[,\.!?\n\(]|$)',
        
        # Padrão 5: Nomes com idade
        r'\b([A-ZÁÀÁÂÃÉÊÍÓÔÕÚÇ][A-ZÁÀÁÂÃÉÊÍÓÔÕÚÇ\s\.]{2,50}?),?\s*(?:DE\s+)?(\d{1,3})\s+ANOS?\b',
        
        # Padrão 6: Nomes em contextos familiares específicos
        r'\b(?:SUA|SEU|MINHA|MEU)\s+(?:MÃE|PAI|FILHO|FILHA|ESPOSO|ESPOSA|MARIDO|VIZINHO|VIZINHA|AMIGO|AMIGA|TIA|TIO|AVÔ|AVÓ|NETO|NETA)\s+([A-ZÁÀÁÂÃÉÊÍÓÔÕÚÇ][A-ZÁÀÁÂÃÉÊÍÓÔÕÚÇ\s\.]{2,50}?)(?=\s*[,\.!?\n]|$)',
    ]
    
    # Termos que indicam que NÃO é um nome de pessoa
    exclusoes = {
        'HOSPITAL', 'CLÍNICA', 'CENTRO', 'POSTO', 'UPA', 'UPS', 'EMERGÊNCIA', 
        'PRONTO SOCORRO', 'AMBULATÓRIO', 'MATERNIDADE', 'LABORATÓRIO',
        'SANTA CASA', 'HCPA', 'HUPE', 'INSTITUTO', 'FUNDAÇÃO', 'SECRETARIA',
        'MINISTÉRIO', 'PREFEITURA', 'GOVERNO', 'ESTADO', 'MUNICÍPIO',
        'JUIZ', 'JUÍZA', 'PROMOTOR', 'PROMOTORA', 'DEFENSOR', 'DEFENSORA',
        'DELEGADO', 'DELEGADA', 'MINISTRO', 'MINISTRA', 'DESEMBARGADOR',
        'DESEMBARGADORA', 'PROCURADOR', 'PROCURADORA', 'CONSELHEIRO',
        'CONSELHEIRA', 'VEREADOR', 'VEREADORA', 'DEPUTADO', 'DEPUTADA',
        'SENADOR', 'SENADORA', 'PREFEITO', 'PREFEITA', 'GOVERNADOR',
        'GOVERNADORA', 'PRESIDENTE', 'SECRETÁRIO', 'SECRETÁRIA',
        'SISTEMA', 'SISREG', 'SER', 'SUS', 'MPRJ', 'RIO', 'JANEIRO',
        'BRASIL', 'BRASILEIRO', 'BRASILEIRA', 'CARIOCA', 'FLUMINENSE'
    }
    
    for i, padrao in enumerate(padroes_nomes, 1):
        matches = re.finditer(padrao, texto_str, re.IGNORECASE)
        
        for match in matches:
            nome_candidato = match.group(1).strip()
            
            # Remove pontuação no final
            nome_candidato = re.sub(r'[,\.!?]+$', '', nome_candidato).strip()
            
            # Verifica se não é uma exclusão
            if any(excl in nome_candidato for excl in exclusoes):
                continue
                
            # Verifica se tem pelo menos 2 palavras ou é um nome composto válido
            palavras = nome_candidato.split()
            if len(palavras) >= 1 and len(nome_candidato) >= 3:
                # Verifica se não é só números ou caracteres especiais
                if re.search(r'[A-ZÁÀÁÂÃÉÊÍÓÔÕÚÇ]', nome_candidato):
                    nomes_encontrados.append({
                        'nome': nome_candidato,
                        'padrao': f'Padrão {i}',
                        'posicao_inicio': match.start(1),
                        'posicao_fim': match.end(1),
                        'contexto': match.group(0)[:100] + '...' if len(match.group(0)) > 100 else match.group(0)
                    })
    
    return nomes_encontrados


def comparar_deteccoes(linha_idx: int, texto: str, analyzer, anonymizer) -> Dict[str, any]:
    """
    Compara a detecção de nomes entre LLM e Presidio.
    
    Args:
        linha_idx: Índice da linha
        texto: Texto para análise
        analyzer: Analyzer do Presidio
        anonymizer: Anonymizer do Presidio
        
    Returns:
        Dicionário com resultados da comparação
    """
    # Análise com LLM
    nomes_llm = analisar_nomes_com_llm(texto)
    
    # Análise com Presidio (script atual)
    texto_anonimizado = anonimizar_relato(texto, analyzer, anonymizer)
    nomes_presidio_detectados = texto != texto_anonimizado
    
    # Análise detalhada do Presidio
    if pd.isna(texto) or not str(texto).strip():
        resultados_presidio = []
    else:
        resultados_presidio = analyzer.analyze(
            text=str(texto),
            language='pt',
            entities=["PERSON", "PERSON_CONTEXTUAL"]
        )
    
    return {
        'linha': linha_idx,
        'texto_original': texto[:200] + '...' if len(str(texto)) > 200 else texto,
        'nomes_llm': nomes_llm,
        'nomes_presidio': len(resultados_presidio),
        'presidio_detectou': nomes_presidio_detectados,
        'texto_anonimizado': texto_anonimizado[:200] + '...' if len(str(texto_anonimizado)) > 200 else texto_anonimizado,
        'resultados_presidio': resultados_presidio
    }


def main():
    """Função principal para análise comparativa."""
    print("=== Análise de Nomes com LLM vs Presidio ===\n")
    
    # Carrega o arquivo
    arquivo_entrada = Path('data/input/ouvidorias.csv')
    if not arquivo_entrada.exists():
        print(f"Erro: Arquivo {arquivo_entrada} não encontrado!")
        return
    
    print(f"Carregando dados de {arquivo_entrada}...")
    df = pd.read_csv(arquivo_entrada)
    
    # Verifica se a coluna Teor existe
    if 'Teor' not in df.columns:
        print(f"Erro: Coluna 'Teor' não encontrada no arquivo!")
        print(f"Colunas disponíveis: {', '.join(df.columns)}")
        return
    
    print(f"Analisando {len(df)} registros na coluna 'Teor'...\n")
    
    # Configura o Presidio
    print("Configurando Presidio...")
    analyzer = criar_analyzer_customizado()
    anonymizer = AnonymizerEngine()
    
    # Análise linha por linha
    resultados = []
    linhas_com_nomes_llm = []
    linhas_com_nomes_presidio = []
    
    for idx, row in df.iterrows():
        texto = row['Teor']
        resultado = comparar_deteccoes(idx, texto, analyzer, anonymizer)
        resultados.append(resultado)
        
        if resultado['nomes_llm']:
            linhas_com_nomes_llm.append(idx)
        
        if resultado['presidio_detectou']:
            linhas_com_nomes_presidio.append(idx)
        
        # Progresso
        if (idx + 1) % 20 == 0:
            print(f"Processadas: {idx + 1}/{len(df)} linhas...")
    
    # Relatório de resultados
    print(f"\n=== RELATÓRIO DE ANÁLISE ===")
    print(f"Total de registros analisados: {len(df)}")
    print(f"Linhas com nomes detectados pelo LLM: {len(linhas_com_nomes_llm)}")
    print(f"Linhas com nomes detectados pelo Presidio: {len(linhas_com_nomes_presidio)}")
    
    # Linhas onde LLM detectou mas Presidio não
    llm_nao_presidio = set(linhas_com_nomes_llm) - set(linhas_com_nomes_presidio)
    print(f"Linhas onde LLM detectou mas Presidio NÃO: {len(llm_nao_presidio)}")
    
    # Linhas onde Presidio detectou mas LLM não
    presidio_nao_llm = set(linhas_com_nomes_presidio) - set(linhas_com_nomes_llm)
    print(f"Linhas onde Presidio detectou mas LLM NÃO: {len(presidio_nao_llm)}")
    
    # Linhas onde ambos detectaram
    ambos_detectaram = set(linhas_com_nomes_llm) & set(linhas_com_nomes_presidio)
    print(f"Linhas onde AMBOS detectaram: {len(ambos_detectaram)}")
    
    # Exemplos detalhados
    print(f"\n=== EXEMPLOS DETALHADOS ===")
    
    # Casos onde LLM detectou mas Presidio não
    if llm_nao_presidio:
        print(f"\n--- Casos onde LLM detectou mas Presidio NÃO ({len(llm_nao_presidio)} casos) ---")
        for i, linha_idx in enumerate(list(llm_nao_presidio)[:5]):  # Mostra apenas 5 exemplos
            resultado = resultados[linha_idx]
            print(f"\nLinha {linha_idx + 1}:")
            print(f"Texto: {resultado['texto_original']}")
            print(f"Nomes detectados pelo LLM:")
            for nome_info in resultado['nomes_llm']:
                print(f"  - {nome_info['nome']} ({nome_info['padrao']})")
                print(f"    Contexto: {nome_info['contexto']}")
            print(f"Presidio detectou: {resultado['nomes_presidio']} entidades")
    
    # Casos onde Presidio detectou mas LLM não
    if presidio_nao_llm:
        print(f"\n--- Casos onde Presidio detectou mas LLM NÃO ({len(presidio_nao_llm)} casos) ---")
        for i, linha_idx in enumerate(list(presidio_nao_llm)[:5]):  # Mostra apenas 5 exemplos
            resultado = resultados[linha_idx]
            print(f"\nLinha {linha_idx + 1}:")
            print(f"Texto: {resultado['texto_original']}")
            print(f"LLM detectou: {len(resultado['nomes_llm'])} nomes")
            print(f"Presidio detectou: {resultado['nomes_presidio']} entidades")
            print(f"Texto anonimizado: {resultado['texto_anonimizado']}")
    
    # Casos onde ambos detectaram
    if ambos_detectaram:
        print(f"\n--- Casos onde AMBOS detectaram ({len(ambos_detectaram)} casos) ---")
        for i, linha_idx in enumerate(list(ambos_detectaram)[:3]):  # Mostra apenas 3 exemplos
            resultado = resultados[linha_idx]
            print(f"\nLinha {linha_idx + 1}:")
            print(f"Texto: {resultado['texto_original']}")
            print(f"Nomes detectados pelo LLM:")
            for nome_info in resultado['nomes_llm']:
                print(f"  - {nome_info['nome']} ({nome_info['padrao']})")
            print(f"Presidio detectou: {resultado['nomes_presidio']} entidades")
            print(f"Texto anonimizado: {resultado['texto_anonimizado']}")
    
    # Salva relatório detalhado
    arquivo_relatorio = Path('data/output/analise_nomes_comparativa.csv')
    arquivo_relatorio.parent.mkdir(parents=True, exist_ok=True)
    
    # Prepara dados para CSV
    dados_csv = []
    for resultado in resultados:
        nomes_llm_str = '; '.join([f"{n['nome']} ({n['padrao']})" for n in resultado['nomes_llm']])
        dados_csv.append({
            'linha': resultado['linha'] + 1,
            'texto_original': resultado['texto_original'],
            'nomes_llm_count': len(resultado['nomes_llm']),
            'nomes_llm_detalhes': nomes_llm_str,
            'presidio_count': resultado['nomes_presidio'],
            'presidio_detectou': resultado['presidio_detectou'],
            'texto_anonimizado': resultado['texto_anonimizado']
        })
    
    df_relatorio = pd.DataFrame(dados_csv)
    df_relatorio.to_csv(arquivo_relatorio, index=False)
    
    print(f"\n=== RESUMO FINAL ===")
    print(f"Relatório detalhado salvo em: {arquivo_relatorio}")
    print(f"Taxa de detecção LLM: {(len(linhas_com_nomes_llm)/len(df))*100:.1f}%")
    print(f"Taxa de detecção Presidio: {(len(linhas_com_nomes_presidio)/len(df))*100:.1f}%")
    print(f"Concordância entre métodos: {(len(ambos_detectaram)/len(df))*100:.1f}%")
    
    # Recomendações
    print(f"\n=== RECOMENDAÇÕES ===")
    if len(llm_nao_presidio) > len(presidio_nao_llm):
        print("- O LLM detectou mais casos que o Presidio")
        print("- Considere melhorar os padrões contextuais no script de anonimização")
    elif len(presidio_nao_llm) > len(llm_nao_presidio):
        print("- O Presidio detectou mais casos que o LLM")
        print("- Os padrões atuais do Presidio parecem mais abrangentes")
    else:
        print("- Ambos os métodos têm performance similar")
        print("- Considere combinar ambas as abordagens para melhor cobertura")


if __name__ == "__main__":
    main() 