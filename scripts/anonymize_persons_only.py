#!/usr/bin/env python3
"""
Anonymize only person names in the entire ouvidorias_educacao.csv file.

This script processes the complete input file and anonymizes only PERSON entities,
leaving all other information (addresses, schools, etc.) intact.
"""

import sys
import os
import pandas as pd
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from src.tools.anonymization_tool import AnonymizationTool


def anonymize_persons_only(text):
    """
    Anonymize only person names in the given text.
    
    Args:
        text: Text to process
        
    Returns:
        Text with only person names anonymized
    """
    if not text or not isinstance(text, str) or not text.strip():
        return text
    
    try:
        # Initialize anonymizer
        anonymizer = AnonymizationTool()
        
        # Get analysis results
        results = anonymizer.analyzer.analyze(text=text, language="pt")
        
        # Filter only PERSON entities
        person_entities = [r for r in results if r.entity_type == "PERSON"]
        
        if not person_entities:
            return text
        
        # Anonymize only person entities
        anonymized_result = anonymizer.anonymizer.anonymize(
            text=text,
            analyzer_results=person_entities
        )
        
        return anonymized_result.text
        
    except Exception as e:
        print(f"⚠️ Error anonymizing text: {e}")
        return text


def process_full_dataset():
    """Process the complete dataset anonymizing only person names."""
    
    print("🔥 Anonymizing Person Names in Complete Dataset")
    print("=" * 60)
    
    # Load input data
    input_file = "data/input/ouvidorias_educacao.csv"
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False
    
    print(f"📄 Loading complete dataset from: {input_file}")
    try:
        df = pd.read_csv(input_file, encoding='utf-8', sep=';')
        print(f"✅ Loaded {len(df)} records")
        print(f"📊 Columns: {list(df.columns)}")
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return False
    
    # Columns to process
    text_columns = ['Assunto Resumido', 'Assunto Inteiro Teor', 'Noticiado']
    available_columns = [col for col in text_columns if col in df.columns]
    
    print(f"📝 Processing columns: {available_columns}")
    
    if not available_columns:
        print("❌ No text columns found for processing")
        return False
    
    # Initialize global anonymizer (for efficiency)
    print("🔧 Initializing anonymization tool...")
    global_anonymizer = AnonymizationTool()
    
    # Process each column
    total_persons_found = 0
    
    for col in available_columns:
        print(f"\n🔍 Processing column: {col}")
        
        # Create anonymized column
        anonymized_col = f"{col}_PERSONS_ANONYMIZED"
        column_persons = 0
        
        # Process each row
        for idx, value in df[col].items():
            if pd.notna(value) and str(value).strip():
                try:
                    # Analyze for person entities
                    results = global_anonymizer.analyzer.analyze(text=str(value), language="pt")
                    person_entities = [r for r in results if r.entity_type == "PERSON"]
                    
                    if person_entities:
                        # Anonymize only persons
                        anonymized_result = global_anonymizer.anonymizer.anonymize(
                            text=str(value),
                            analyzer_results=person_entities
                        )
                        df.at[idx, anonymized_col] = anonymized_result.text
                        column_persons += len(person_entities)
                    else:
                        # No persons found, keep original
                        df.at[idx, anonymized_col] = value
                        
                except Exception as e:
                    print(f"⚠️ Error processing row {idx}: {e}")
                    df.at[idx, anonymized_col] = value
            else:
                # Empty or NaN value
                df.at[idx, anonymized_col] = value
            
            # Progress indicator
            if (idx + 1) % 25 == 0:
                print(f"   📊 Processed: {idx + 1}/{len(df)} rows")
        
        total_persons_found += column_persons
        print(f"✅ Column {col}: {column_persons} person entities anonymized")
    
    # Save result
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"data/output/ouvidorias_educacao_persons_anonymized_{timestamp}.csv"
    
    print(f"\n💾 Saving anonymized dataset...")
    df.to_csv(output_file, index=False, encoding='utf-8', sep=';')
    
    # Statistics
    print(f"\n📊 ANONYMIZATION SUMMARY")
    print("=" * 40)
    print(f"✅ Total records processed: {len(df)}")
    print(f"👤 Total person names anonymized: {total_persons_found}")
    print(f"📄 Output file: {output_file}")
    
    # Show sample comparisons
    print(f"\n📋 SAMPLE COMPARISONS:")
    print("-" * 50)
    
    for col in available_columns[:2]:  # Show first 2 columns
        anonymized_col = f"{col}_PERSONS_ANONYMIZED"
        if anonymized_col in df.columns:
            # Find a row with differences
            for idx in range(min(10, len(df))):
                original = str(df.at[idx, col])
                anonymized = str(df.at[idx, anonymized_col])
                
                if original != anonymized and len(original) > 50:
                    print(f"\n🔸 {col} (Row {idx + 1}):")
                    print(f"   Original: {original[:100]}...")
                    print(f"   Anonymized: {anonymized[:100]}...")
                    break
    
    return True


def main():
    """Main function."""
    
    print("🚀 Person Names Anonymization - Complete Dataset")
    print("=" * 70)
    
    success = process_full_dataset()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Person names anonymization completed successfully!")
        print("✅ All person names in the dataset have been anonymized")
        print("✅ Other information (addresses, schools, etc.) preserved")
        print("✅ Ready for further processing or analysis")
    else:
        print("❌ Anonymization failed")
        print("🔧 Check input file and dependencies")
    
    return success


if __name__ == "__main__":
    main()
