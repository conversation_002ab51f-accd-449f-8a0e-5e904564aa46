#!/usr/bin/env python3
"""
Script para classificação completa de dados de educação com três dimensões:
1. MULTILABEL: Subtemas de educação (múltiplas categorias)
2. UNILABEL: TIPO_UNIDADE (Estadual/Municipal/Privada)
3. UNILABEL: CRE (1ª-11ª CRE/Não se aplica)

Usa Llama 3.3 70B via Fireworks AI para todas as classificações.
"""

import pandas as pd
import os
import sys
import logging
import json
import time
from typing import Dict, List, Optional, Tuple
from fireworks.client import Fireworks
import yaml
from datetime import datetime

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def carregar_configuracao() -> Dict:
    """Carrega configurações do arquivo config.yml"""
    try:
        with open('config/config.yml', 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        return config
    except Exception as e:
        logging.error(f"Erro ao carregar configuração: {e}")
        return {}

def carregar_variaveis_ambiente() -> Dict:
    """Carrega variáveis de ambiente do arquivo .env"""
    env_vars = {}
    try:
        with open('.env', 'r') as file:
            for linha in file:
                linha = linha.strip()
                if linha and not linha.startswith('#'):
                    chave, valor = linha.split('=', 1)
                    env_vars[chave] = valor
    except FileNotFoundError:
        logging.warning("Arquivo .env não encontrado")
    except Exception as e:
        logging.error(f"Erro ao carregar .env: {e}")
    
    return env_vars

def carregar_dados_educacao(arquivo_csv: str) -> Optional[pd.DataFrame]:
    """Carrega dados de educação do CSV"""
    try:
        df = pd.read_csv(arquivo_csv, sep=';', encoding='utf-8')
        logging.info(f"Arquivo carregado: {len(df)} registros")
        return df
    except Exception as e:
        logging.error(f"Erro ao carregar arquivo CSV {arquivo_csv}: {e}")
        return None

def criar_prompt_classificacao_completa() -> str:
    """
    Cria prompt para classificação completa (multilabel + unilabel)
    """
    return """# SISTEMA DE CLASSIFICAÇÃO COMPLETA - EDUCAÇÃO

Você é um especialista em classificação de denúncias educacionais. Sua tarefa é classificar cada relato em TRÊS DIMENSÕES:

## DIMENSÃO 1: SUBTEMAS (MULTILABEL - múltiplas categorias possíveis)
Classifique em um ou mais dos seguintes subtemas:

1. **Educação Especial - Falta de Mediador**: Ausência de mediadores escolares para PcD
2. **Educação Especial - Exclusão e Discriminação**: Recusa de matrícula, isolamento, tratamento diferenciado
3. **Bullying**: Agressões, humilhações, cyberbullying entre estudantes
4. **Gestão e Casos Pontuais (Violência e maus tratos)**: Agressões por profissionais, abuso
5. **Gestão e Casos Pontuais (Irregularidades Administrativas)**: Problemas de gestão, documentação
6. **Controle Interno**: Falhas de supervisão, omissão, falta de protocolos
7. **Alimentação Escolar**: Problemas na merenda, qualidade, distribuição
8. **Infraestrutura**: Problemas físicos, acessibilidade, equipamentos
9. **Transporte**: Transporte escolar, acessibilidade no transporte
10. **Qualificação Profissional**: Profissionais sem formação adequada
11. **Educação de Jovens e Adultos (sistema prisional)**: EJA específica do sistema prisional

## DIMENSÃO 2: TIPO_UNIDADE (UNILABEL - apenas uma categoria)
Classifique baseado na instituição noticiada:

- **Municipal**: Escolas municipais, EDI, creches municipais, CRE
- **Estadual**: Escolas estaduais, SEEDUC, FAETEC, colégios estaduais
- **Privada**: Escolas particulares, centros educacionais privados

## DIMENSÃO 3: CRE (UNILABEL - apenas uma categoria)
Aplicável APENAS para instituições municipais. Para estaduais e privadas, use "Não se aplica":

- **1ª CRE** a **11ª CRE**: Se mencionado explicitamente no texto
- **Não se aplica**: Para escolas estaduais, privadas, ou quando não há menção específica

## INSTRUÇÕES DE CLASSIFICAÇÃO:

1. **Leia todo o relato** (resumo + teor completo)
2. **Identifique TODOS os subtemas** relevantes (pode ser mais de um)
3. **Determine o tipo de unidade** baseado na instituição noticiada
4. **Identifique a CRE** apenas se for escola municipal E houver menção explícita

## FORMATO DE RESPOSTA:
Responda EXATAMENTE neste formato JSON:

```json
{
    "subtemas": ["subtema1", "subtema2"],
    "tipo_unidade": "Municipal/Estadual/Privada",
    "cre": "1ª CRE/2ª CRE/.../11ª CRE/Não se aplica"
}
```

## EXEMPLOS:

**Exemplo 1:**
Relato: "Escola Municipal João Silva. Falta mediador para aluno autista. 7ª CRE informou que não tem previsão."

```json
{
    "subtemas": ["Educação Especial - Falta de Mediador"],
    "tipo_unidade": "Municipal",
    "cre": "7ª CRE"
}
```

**Exemplo 2:**
Relato: "Colégio Estadual Paulo Freire. Professor agrediu aluno com deficiência. SEEDUC foi omissa."

```json
{
    "subtemas": ["Gestão e Casos Pontuais (Violência e maus tratos)", "Educação Especial - Exclusão e Discriminação", "Controle Interno"],
    "tipo_unidade": "Estadual",
    "cre": "Não se aplica"
}
```

**IMPORTANTE:**
- Seja preciso na classificação
- Use os nomes EXATOS dos subtemas listados
- Para CRE, só classifique se houver menção explícita
- Sempre responda no formato JSON especificado"""

def classificar_com_llama(
    texto_resumido: str,
    texto_completo: str,
    noticiado: str,
    cliente_fireworks: Fireworks,
    model_name: str
) -> Optional[Dict]:
    """
    Classifica um relato usando Llama 3.3 70B
    """
    prompt_base = criar_prompt_classificacao_completa()
    
    # Preparar texto para análise
    texto_para_analise = f"""
## DADOS DO RELATO:

**Instituição Noticiada:** {noticiado}

**Resumo:** {texto_resumido}

**Teor Completo:** {texto_completo}

---

Classifique este relato nas três dimensões solicitadas e responda no formato JSON especificado.
"""
    
    prompt_completo = prompt_base + "\n\n" + texto_para_analise
    
    try:
        resposta = cliente_fireworks.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "system", "content": "Você é um especialista em classificação de denúncias educacionais. Responda sempre no formato JSON especificado."},
                {"role": "user", "content": prompt_completo}
            ],
            max_tokens=1000,
            temperature=0.1
        )
        
        resposta_texto = resposta.choices[0].message.content.strip()
        
        # Extrair JSON da resposta
        inicio_json = resposta_texto.find('{')
        fim_json = resposta_texto.rfind('}') + 1
        
        if inicio_json != -1 and fim_json > inicio_json:
            json_str = resposta_texto[inicio_json:fim_json]
            resultado = json.loads(json_str)
            
            # Validar estrutura
            if 'subtemas' in resultado and 'tipo_unidade' in resultado and 'cre' in resultado:
                return resultado
            else:
                logging.warning(f"Estrutura JSON inválida: {resultado}")
                return None
        else:
            logging.warning(f"JSON não encontrado na resposta: {resposta_texto}")
            return None
            
    except json.JSONDecodeError as e:
        logging.error(f"Erro ao decodificar JSON: {e}")
        return None
    except Exception as e:
        logging.error(f"Erro na classificação: {e}")
        return None

def processar_classificacao_completa(
    df: pd.DataFrame,
    cliente_fireworks: Fireworks,
    model_name: str
) -> pd.DataFrame:
    """
    Processa classificação completa de todos os registros
    """
    # Adicionar colunas para resultados
    df['SUBTEMAS_IDENTIFICADOS'] = ''
    df['TIPO_UNIDADE'] = ''
    df['CRE'] = ''
    df['METODO_CLASSIFICACAO'] = 'Llama3.3_70B_Completo'
    
    total_registros = len(df)
    classificados = 0
    
    for idx, row in df.iterrows():
        logging.info(f"Processando registro {idx + 1}/{total_registros}")
        
        # Preparar dados
        texto_resumido = str(row.get('Assunto Resumido', ''))
        texto_completo = str(row.get('Assunto Inteiro Teor', ''))
        noticiado = str(row.get('Noticiado', ''))
        
        # Classificar
        resultado = classificar_com_llama(
            texto_resumido, texto_completo, noticiado,
            cliente_fireworks, model_name
        )
        
        if resultado:
            # Processar subtemas
            subtemas = resultado.get('subtemas', [])
            if isinstance(subtemas, list):
                df.at[idx, 'SUBTEMAS_IDENTIFICADOS'] = ', '.join(subtemas)
            else:
                df.at[idx, 'SUBTEMAS_IDENTIFICADOS'] = str(subtemas)
            
            # Processar tipo de unidade
            df.at[idx, 'TIPO_UNIDADE'] = resultado.get('tipo_unidade', '')
            
            # Processar CRE
            df.at[idx, 'CRE'] = resultado.get('cre', '')
            
            classificados += 1
        else:
            logging.warning(f"Falha na classificação do registro {idx + 1}")
        
        # Pausa para evitar rate limiting
        time.sleep(1)
    
    logging.info(f"Classificação concluída: {classificados}/{total_registros} registros")
    return df

def main():
    """Função principal"""
    
    # Verificar argumentos
    if len(sys.argv) != 2:
        print("Uso: python scripts/classificar_educacao_completo.py <arquivo_csv>")
        sys.exit(1)
    
    arquivo_csv = sys.argv[1]
    
    if not os.path.exists(arquivo_csv):
        logging.error(f"Arquivo não encontrado: {arquivo_csv}")
        sys.exit(1)
    
    # Carregar variáveis de ambiente
    env_vars = carregar_variaveis_ambiente()
    fireworks_api_key = env_vars.get("FIREWORKS_API_KEY")
    
    if not fireworks_api_key:
        logging.error("ERRO: Variável de ambiente FIREWORKS_API_KEY não encontrada")
        sys.exit(1)
    
    # Carregar configurações
    config = carregar_configuracao()
    model_name = "accounts/fireworks/models/llama-v3p3-70b-instruct"
    
    print(f"📊 Arquivo: {arquivo_csv}")
    print(f"🤖 Modelo: {model_name}")
    print()
    print("=== CLASSIFICAÇÃO COMPLETA - EDUCAÇÃO ===")
    print("📋 Dimensões:")
    print("   1. SUBTEMAS (Multilabel)")
    print("   2. TIPO_UNIDADE (Unilabel)")
    print("   3. CRE (Unilabel)")
    print()
    
    # Carregar dados
    df = carregar_dados_educacao(arquivo_csv)
    if df is None:
        sys.exit(1)
    
    # Inicializar cliente Fireworks
    try:
        cliente_fireworks = Fireworks(api_key=fireworks_api_key)
        logging.info("Cliente Fireworks inicializado com sucesso")
    except Exception as e:
        logging.error(f"Erro ao inicializar cliente Fireworks: {e}")
        sys.exit(1)
    
    # Processar classificação
    df_classificado = processar_classificacao_completa(df, cliente_fireworks, model_name)
    
    # Salvar resultado
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"data/output/ouvidorias_educacao_classificacao_completa_{timestamp}.csv"
    
    if not os.path.exists('data/output'):
        os.makedirs('data/output')
    
    df_classificado.to_csv(output_file, index=False, encoding='utf-8')
    
    # Estatísticas finais
    total_registros = len(df_classificado)
    registros_com_subtemas = len(df_classificado[df_classificado['SUBTEMAS_IDENTIFICADOS'] != ''])
    
    print(f"✅ Classificação completa finalizada!")
    print(f"📄 Arquivo salvo: {output_file}")
    print(f"\n📈 Estatísticas:")
    print(f"   • Total de registros: {total_registros}")
    print(f"   • Registros classificados: {registros_com_subtemas}")
    print(f"   • Taxa de sucesso: {(registros_com_subtemas/total_registros*100):.1f}%")
    
    # Estatísticas por dimensão
    print(f"\n📊 Distribuição por TIPO_UNIDADE:")
    tipo_unidade_counts = df_classificado['TIPO_UNIDADE'].value_counts()
    for tipo, count in tipo_unidade_counts.items():
        if tipo:
            print(f"   • {tipo}: {count} ({count/total_registros*100:.1f}%)")
    
    print(f"\n📊 Distribuição por CRE (apenas municipais):")
    municipais = df_classificado[df_classificado['TIPO_UNIDADE'] == 'Municipal']
    if len(municipais) > 0:
        cre_counts = municipais['CRE'].value_counts()
        for cre, count in cre_counts.items():
            if cre and cre != 'Não se aplica':
                print(f"   • {cre}: {count} ({count/len(municipais)*100:.1f}%)")

if __name__ == "__main__":
    main()
