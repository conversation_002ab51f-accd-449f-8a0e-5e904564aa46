#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para executar diferentes métodos de análise de relatos e comparar resultados.
"""

import argparse
import os
import sys
import subprocess
import time
from colorama import Fore, Style, init

# Garantir que o diretório raiz do projeto esteja no PYTHONPATH
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Inicializar colorama para funcionar em todos os terminais
init()

def main():
    parser = argparse.ArgumentParser(
        description="Executa diferentes abordagens de análise de relatos.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    
    # Argumentos comuns
    parser.add_argument('arquivo_entrada', help='Caminho para o arquivo CSV de entrada')
    
    # Criar subcomandos
    subparsers = parser.add_subparsers(dest='comando', help='Comando a ser executado')
    
    # Comando: Individual (original)
    parser_individual = subparsers.add_parser('individual', help='Processa registros individualmente')
    parser_individual.add_argument('--saida', default='data/output/relatos_classificados_individual.csv', 
                              help='Caminho para o arquivo CSV de saída')
    
    # Comando: Lote
    parser_lote = subparsers.add_parser('lote', help='Processa registros em lotes')
    parser_lote.add_argument('--tamanho', type=int, default=5, 
                           help='Tamanho do lote (padrão: 5)')
    parser_lote.add_argument('--saida', default='data/output/relatos_classificados_lote.csv',
                           help='Caminho para o arquivo CSV de saída')
    
    # Comando: Embeddings
    parser_embeddings = subparsers.add_parser('embeddings', help='Usa embeddings e similaridade')
    parser_embeddings.add_argument('--limiar', type=float, default=0.2, 
                                 help='Limiar de similaridade (padrão: 0.2)')
    parser_embeddings.add_argument('--saida', default='data/output/relatos_classificados_embeddings.csv',
                                 help='Caminho para o arquivo CSV de saída')
    
    # Comando: Comparação
    parser_comparar = subparsers.add_parser('comparar', 
        help='Compara as três abordagens e gera relatórios')
    parser_comparar.add_argument('--diretorio', default='data/output',
                               help='Diretório para salvar resultados da comparação (padrão: data/output)')
    parser_comparar.add_argument('--tamanho', type=int, default=5,
                               help='Tamanho do lote para método em lotes (padrão: 5)')
    parser_comparar.add_argument('--limiar', type=float, default=0.2,
                               help='Limiar de similaridade para método de embeddings (padrão: 0.2)')
    
    args = parser.parse_args()
    
    # Verificar se arquivo de entrada existe
    if not os.path.exists(args.arquivo_entrada):
        print(f"ERRO: Arquivo de entrada '{args.arquivo_entrada}' não encontrado.")
        return 1
    
    # Executar o comando escolhido
    if args.comando == 'individual':
        from src.analisadores.analisador_relatos import main as main_individual
        main_individual(args.arquivo_entrada, args.saida)
        
    elif args.comando == 'lote':
        from src.analisadores.analisador_lote import main as main_lote
        main_lote(args.arquivo_entrada, args.saida, args.tamanho)
        
    elif args.comando == 'embeddings':
        from src.analisadores.analisador_embeddings import main as main_embeddings
        main_embeddings(args.arquivo_entrada, args.saida, args.limiar)
        
    elif args.comando == 'comparar':
        from src.analisadores.run_comparacao import executar_comparacao
        executar_comparacao(
            args.arquivo_entrada,
            args.diretorio, 
            args.tamanho,
            args.limiar
        )
    else:
        parser.print_help()
        return 1
    
    return 0

def limpar_tela():
    """Limpa a tela do terminal"""
    os.system('cls' if os.name == 'nt' else 'clear')

def exibir_cabecalho():
    """Exibe o cabeçalho do programa"""
    print(Fore.CYAN + """
    ==================================================
            SISTEMA DE ANÁLISE DE RELATOS
    ==================================================
    """ + Style.RESET_ALL)

def exibir_menu():
    """Exibe as opções do menu principal"""
    print(Fore.WHITE + "\n Escolha um método de análise:\n" + Style.RESET_ALL)
    print(Fore.YELLOW + " [1]" + Style.RESET_ALL + " Método Individual (uma chamada LLM por registro)")
    print(Fore.YELLOW + " [2]" + Style.RESET_ALL + " Método em Lote (múltiplos registros por chamada LLM)")
    print(Fore.YELLOW + " [3]" + Style.RESET_ALL + " Método com Embeddings (similaridade semântica)")
    print(Fore.YELLOW + " [4]" + Style.RESET_ALL + " Gerar Relatório Tático Detalhado (Claude 3.7)")
    print("     Gera um relatório tático completo no formato markdown a partir dos")
    print("     casos classificados como positivos, com análise estatística,")
    print("     problemas sistêmicos, causas e recomendações.")
    print(Fore.YELLOW + " [5]" + Style.RESET_ALL + " Executar Comparação (todos os métodos)")
    print(Fore.YELLOW + " [0]" + Style.RESET_ALL + " Sair\n")

def executar_comando(cmd, verbose=True):
    """Executa um comando no sistema operacional"""
    if verbose:
        print(Fore.BLUE + f"\nExecutando: {' '.join(cmd)}\n" + Style.RESET_ALL)
    
    try:
        resultado = subprocess.run(cmd, check=True)
        if verbose:
            print(Fore.GREEN + "Comando executado com sucesso!" + Style.RESET_ALL)
        return resultado.returncode == 0
    except subprocess.CalledProcessError as e:
        if verbose:
            print(Fore.RED + f"Erro ao executar comando: {e}" + Style.RESET_ALL)
        return False
    except Exception as e:
        if verbose:
            print(Fore.RED + f"Erro inesperado: {e}" + Style.RESET_ALL)
        return False

def aguardar_confirmacao():
    """Aguarda confirmação do usuário para continuar"""
    input(Fore.CYAN + "\nPressione Enter para continuar..." + Style.RESET_ALL)

def executar_analisador_individual():
    """Executa o analisador de relatos individual"""
    print(Fore.CYAN + "\nExecutando analisador de relatos (método individual)...\n" + Style.RESET_ALL)
    executar_comando(["python", "scripts/run.py", "data/input/TS3 NF Control Page 2.csv", "data/output/relatos_classificados_individual.csv"])
    aguardar_confirmacao()

def executar_analisador_lote():
    """Executa o analisador de relatos em lote"""
    print(Fore.CYAN + "\nExecutando analisador de relatos (método em lote)...\n" + Style.RESET_ALL)
    executar_comando(["python", "scripts/run_testes.py", "data/input/TS3 NF Control Page 2.csv", "lote"])
    aguardar_confirmacao()

def executar_analisador_embeddings():
    """Executa o analisador de relatos com embeddings"""
    print(Fore.CYAN + "\nExecutando analisador de relatos (método com embeddings)...\n" + Style.RESET_ALL)
    executar_comando(["python", "scripts/run_testes.py", "data/input/TS3 NF Control Page 2.csv", "embeddings"])
    aguardar_confirmacao()

def executar_gerador_relatorio():
    """Executa o gerador de relatórios táticos"""
    print(Fore.CYAN + "\nGerando relatório tático a partir dos casos classificados como positivos...\n" + Style.RESET_ALL)
    
    # Verificar se a variável de ambiente ANTHROPIC_API_KEY está configurada
    if not os.environ.get('ANTHROPIC_API_KEY'):
        print(Fore.RED + "\nErro: Variável de ambiente ANTHROPIC_API_KEY não configurada!" + Style.RESET_ALL)
        print(Fore.YELLOW + "Configure a chave API com: export ANTHROPIC_API_KEY='sua-chave-api'" + Style.RESET_ALL)
        aguardar_confirmacao()
        return
    
    executar_comando(["python", "scripts/run_relatorio.py"])
    
    # Verificar se o relatório foi gerado
    arquivos_relatorio = [f for f in os.listdir('data/output') if f.startswith('relatorio_') and f.endswith('.md')]
    
    if arquivos_relatorio:
        caminho_relatorio = os.path.join('data/output', arquivos_relatorio[0])
        print(Fore.GREEN + f"\nRelatório gerado com sucesso em: {caminho_relatorio}" + Style.RESET_ALL)
        
        # Perguntar se o usuário deseja visualizar o relatório
        resposta = input(Fore.CYAN + "\nDeseja visualizar o relatório agora? (s/n): " + Style.RESET_ALL).lower()
        if resposta.startswith('s'):
            # Abrir o relatório (depende do sistema operacional)
            if os.name == 'nt':  # Windows
                os.system(f'start {caminho_relatorio}')
            elif os.name == 'posix':  # Linux/Mac
                os.system(f'open {caminho_relatorio}' if sys.platform == 'darwin' else f'xdg-open {caminho_relatorio}')
    else:
        print(Fore.RED + "\nNão foi possível encontrar o relatório gerado." + Style.RESET_ALL)
    
    aguardar_confirmacao()

def executar_comparacao():
    """Executa a comparação de todos os métodos"""
    print(Fore.CYAN + "\nIniciando comparação de todos os métodos de análise...\n" + Style.RESET_ALL)
    executar_comando(["python", "scripts/run_testes.py", "data/input/TS3 NF Control Page 2.csv", "comparar"])
    aguardar_confirmacao()

def menu_principal():
    """Exibe e gerencia o menu principal"""
    while True:
        limpar_tela()
        exibir_cabecalho()
        exibir_menu()
        
        try:
            opcao = input(Fore.WHITE + " Escolha uma opção: " + Style.RESET_ALL)
            
            if opcao == "1":
                executar_analisador_individual()
            elif opcao == "2":
                executar_analisador_lote()
            elif opcao == "3":
                executar_analisador_embeddings()
            elif opcao == "4":
                executar_gerador_relatorio()
            elif opcao == "5":
                executar_comparacao()
            elif opcao == "0":
                limpar_tela()
                print(Fore.GREEN + "\nObrigado por utilizar o Sistema de Análise de Relatos!" + Style.RESET_ALL)
                break
            else:
                print(Fore.RED + "\nOpção inválida! Por favor, escolha uma opção válida." + Style.RESET_ALL)
                aguardar_confirmacao()
        except KeyboardInterrupt:
            limpar_tela()
            print(Fore.YELLOW + "\n\nOperação cancelada pelo usuário." + Style.RESET_ALL)
            print(Fore.GREEN + "Obrigado por utilizar o Sistema de Análise de Relatos!" + Style.RESET_ALL)
            break
        except Exception as e:
            print(Fore.RED + f"\nErro inesperado: {e}" + Style.RESET_ALL)
            aguardar_confirmacao()

if __name__ == "__main__":
    # Se argumentos foram passados, executar o modo de linha de comando
    if len(sys.argv) > 1:
        sys.exit(main())
    else:
        # Caso contrário, executar a interface de menu
        menu_principal() 