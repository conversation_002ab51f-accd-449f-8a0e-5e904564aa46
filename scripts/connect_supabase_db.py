#!/usr/bin/env python3
"""
Script to connect directly to Supabase PostgreSQL database and execute setup.
"""

import os
import sys
import psycopg2
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv

def connect_to_supabase():
    """Connect to Supabase PostgreSQL database."""
    print("🔗 Connecting to Supabase PostgreSQL...")
    
    # Load environment variables
    load_dotenv()
    
    # Get database credentials
    host = os.getenv("SUPABASE_DB_HOST")
    port = os.getenv("SUPABASE_DB_PORT", "5432")
    database = os.getenv("SUPABASE_DB_NAME")
    user = os.getenv("SUPABASE_DB_USER")
    password = os.getenv("SUPABASE_DB_PASSWORD")
    
    print(f"📍 Host: {host}")
    print(f"🔌 Port: {port}")
    print(f"🗄️  Database: {database}")
    print(f"👤 User: {user}")
    
    try:
        # Create connection
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=user,
            password=password,
            sslmode='require'  # Supabase requires SSL
        )
        
        print("✅ Connected to Supabase PostgreSQL successfully!")
        return conn
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return None

def execute_setup_sql(conn):
    """Execute the setup SQL script."""
    print("\n🗄️  Executing setup SQL...")
    
    # Read SQL setup file
    sql_file = project_root / "database" / "supabase_setup.sql"
    if not sql_file.exists():
        print(f"❌ SQL file not found: {sql_file}")
        return False
    
    with open(sql_file, 'r', encoding='utf-8') as f:
        sql_content = f.read()
    
    try:
        cursor = conn.cursor()
        
        # Execute the entire SQL script
        cursor.execute(sql_content)
        conn.commit()
        
        print("✅ SQL script executed successfully!")
        
        # Verify tables were created
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('files', 'processing_jobs', 'reports')
            ORDER BY table_name;
        """)
        
        tables = cursor.fetchall()
        print(f"📋 Created tables: {[table[0] for table in tables]}")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ SQL execution failed: {e}")
        conn.rollback()
        return False

def test_database_setup(conn):
    """Test the database setup."""
    print("\n🧪 Testing database setup...")
    
    try:
        cursor = conn.cursor()
        
        # Test each table
        tables = ['files', 'processing_jobs', 'reports']
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table};")
            count = cursor.fetchone()[0]
            print(f"   ✅ Table '{table}': {count} records")
        
        # Test indexes
        cursor.execute("""
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename IN ('files', 'processing_jobs', 'reports')
            ORDER BY indexname;
        """)
        
        indexes = cursor.fetchall()
        print(f"📊 Created indexes: {len(indexes)} total")
        
        # Test RLS policies
        cursor.execute("""
            SELECT tablename, policyname 
            FROM pg_policies 
            WHERE tablename IN ('files', 'processing_jobs', 'reports')
            ORDER BY tablename, policyname;
        """)
        
        policies = cursor.fetchall()
        print(f"🔐 RLS policies: {len(policies)} total")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def create_storage_bucket_sql(conn):
    """Create storage bucket using SQL."""
    print("\n📦 Setting up storage bucket...")
    
    try:
        cursor = conn.cursor()
        
        # Check if bucket exists
        cursor.execute("""
            SELECT id FROM storage.buckets WHERE id = 'simple-class-files';
        """)
        
        if cursor.fetchone():
            print("✅ Storage bucket 'simple-class-files' already exists")
        else:
            # Create bucket
            cursor.execute("""
                INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
                VALUES (
                    'simple-class-files',
                    'simple-class-files',
                    false,
                    52428800,  -- 50MB limit
                    ARRAY['text/csv', 'application/pdf', 'text/plain', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
                );
            """)
            
            # Create storage policies
            cursor.execute("""
                CREATE POLICY "Users can upload their own files" ON storage.objects
                FOR INSERT WITH CHECK (
                    bucket_id = 'simple-class-files' 
                    AND auth.uid()::text = (storage.foldername(name))[1]
                );
            """)
            
            cursor.execute("""
                CREATE POLICY "Users can view their own files" ON storage.objects
                FOR SELECT USING (
                    bucket_id = 'simple-class-files' 
                    AND auth.uid()::text = (storage.foldername(name))[1]
                );
            """)
            
            cursor.execute("""
                CREATE POLICY "Users can update their own files" ON storage.objects
                FOR UPDATE USING (
                    bucket_id = 'simple-class-files' 
                    AND auth.uid()::text = (storage.foldername(name))[1]
                );
            """)
            
            cursor.execute("""
                CREATE POLICY "Users can delete their own files" ON storage.objects
                FOR DELETE USING (
                    bucket_id = 'simple-class-files' 
                    AND auth.uid()::text = (storage.foldername(name))[1]
                );
            """)
            
            conn.commit()
            print("✅ Storage bucket and policies created successfully!")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Storage setup failed: {e}")
        conn.rollback()
        return False

def main():
    """Main setup function."""
    print("🚀 Supabase Direct Database Setup")
    print("=" * 50)
    
    # Connect to database
    conn = connect_to_supabase()
    if not conn:
        return
    
    try:
        # Execute setup SQL
        sql_ok = execute_setup_sql(conn)
        
        # Setup storage
        storage_ok = create_storage_bucket_sql(conn)
        
        # Test setup
        if sql_ok:
            test_ok = test_database_setup(conn)
        else:
            test_ok = False
        
        print("\n" + "=" * 50)
        print("📊 Setup Results:")
        print(f"   Database Tables: {'✅ OK' if sql_ok else '❌ FAILED'}")
        print(f"   Storage Bucket: {'✅ OK' if storage_ok else '❌ FAILED'}")
        print(f"   Tests: {'✅ OK' if test_ok else '❌ FAILED'}")
        
        if sql_ok and storage_ok and test_ok:
            print("\n🎉 Supabase setup completed successfully!")
            print("   Your database is ready for the Simple Class API")
        else:
            print("\n⚠️  Setup incomplete. Check errors above.")
    
    finally:
        conn.close()
        print("\n🔌 Database connection closed")

if __name__ == "__main__":
    main()
