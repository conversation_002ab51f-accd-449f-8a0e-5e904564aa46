#!/usr/bin/env python3
"""
Script to verify which database we're connecting to.
"""

import os
import sys
import psycopg2
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv

def verify_connection():
    """Verify database connection and show details."""
    print("🔍 Verifying database connection...")
    
    # Load environment variables
    load_dotenv()
    
    # Get database credentials
    host = os.getenv("SUPABASE_DB_HOST")
    port = os.getenv("SUPABASE_DB_PORT", "5432")
    database = os.getenv("SUPABASE_DB_NAME")
    user = os.getenv("SUPABASE_DB_USER")
    password = os.getenv("SUPABASE_DB_PASSWORD")
    
    print(f"📍 Connecting to:")
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   Database: {database}")
    print(f"   User: {user}")
    print(f"   Password: {'*' * len(password) if password else 'None'}")
    
    try:
        # Create connection
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=user,
            password=password,
            sslmode='require'
        )
        
        cursor = conn.cursor()
        
        # Get database info
        cursor.execute("SELECT current_database(), current_user, version();")
        db_info = cursor.fetchone()
        
        print(f"\n✅ Connected successfully!")
        print(f"   Current database: {db_info[0]}")
        print(f"   Current user: {db_info[1]}")
        print(f"   PostgreSQL version: {db_info[2]}")
        
        # Check if this is a Supabase database
        cursor.execute("""
            SELECT schema_name 
            FROM information_schema.schemata 
            WHERE schema_name IN ('auth', 'storage', 'realtime', 'supabase_functions')
            ORDER BY schema_name;
        """)
        
        supabase_schemas = cursor.fetchall()
        print(f"\n🔍 Supabase schemas found: {[schema[0] for schema in supabase_schemas]}")
        
        if supabase_schemas:
            print("✅ This appears to be a Supabase database")
        else:
            print("❌ This does NOT appear to be a Supabase database")
        
        # Check our tables
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('files', 'processing_jobs', 'reports')
            ORDER BY table_name;
        """)
        
        our_tables = cursor.fetchall()
        print(f"📋 Our tables found: {[table[0] for table in our_tables]}")
        
        # Check all public tables
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name;
        """)
        
        all_tables = cursor.fetchall()
        print(f"📊 All public tables: {[table[0] for table in all_tables]}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def main():
    """Main function."""
    print("🔍 Database Connection Verification")
    print("=" * 50)
    
    success = verify_connection()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Verification completed")
    else:
        print("❌ Verification failed")

if __name__ == "__main__":
    main()
