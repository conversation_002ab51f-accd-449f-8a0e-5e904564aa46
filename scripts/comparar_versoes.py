#!/usr/bin/env python3
"""
Script para comparar a versão original vs aprimorada do anonimizador.
"""

import pandas as pd
from pathlib import Path
import sys

# Adiciona o diretório scripts ao path
sys.path.append(str(Path(__file__).parent))

from anonimiza import criar_analyzer_customizado, anonimizar_relato
from presidio_anonymizer import AnonymizerEngine

def comparar_versoes():
    """Compara a detecção da versão aprimorada com casos específicos."""
    
    # Carrega dados
    arquivo_entrada = Path('data/input/ouvidorias.csv')
    df = pd.read_csv(arquivo_entrada)
    
    print("=== Comparação: Versão Aprimorada ===")
    print("Testando casos específicos que foram perdidos na análise anterior...\n")
    
    # Configura o analyzer aprimorado
    analyzer = criar_analyzer_customizado()
    anonymizer = AnonymizerEngine()
    
    # Casos específicos para testar (baseados na análise anterior)
    casos_teste = [
        {
            'linha': 3,
            'texto': df.iloc[2]['Teor'],  # Linha 3 (índice 2)
            'nomes_esperados': ['JACIRA FRANCISCA LIRA']
        },
        {
            'linha': 132,
            'texto': df.iloc[131]['Teor'],  # Linha 132 (índice 131)
            'nomes_esperados': ['meu sogro um senhor']
        },
        {
            'linha': 8,
            'texto': df.iloc[7]['Teor'],  # Linha 8 (índice 7)
            'nomes_esperados': ['WALTER']
        },
        {
            'linha': 25,
            'texto': df.iloc[24]['Teor'],  # Linha 25 (índice 24)
            'nomes_esperados': ['um senhor de 80 anos']
        },
        {
            'linha': 69,
            'texto': df.iloc[68]['Teor'],  # Linha 69 (índice 68)
            'nomes_esperados': ['Keilane Vieira Caxias']
        }
    ]
    
    melhorias = 0
    total_casos = len(casos_teste)
    
    for caso in casos_teste:
        print(f"--- Linha {caso['linha']} ---")
        print(f"Texto: {str(caso['texto'])[:150]}...")
        
        # Testa anonimização
        texto_original = str(caso['texto'])
        texto_anonimizado = anonimizar_relato(texto_original, analyzer, anonymizer)
        
        # Verifica se houve anonimização
        houve_anonimizacao = texto_original != texto_anonimizado
        
        print(f"Houve anonimização: {'SIM' if houve_anonimizacao else 'NÃO'}")
        
        if houve_anonimizacao:
            print(f"Resultado: {texto_anonimizado[:150]}...")
            melhorias += 1
        
        # Analisa entidades detectadas
        resultados = analyzer.analyze(
            text=texto_original,
            language='pt',
            entities=["PERSON", "PERSON_CONTEXTUAL"]
        )
        
        print(f"Entidades detectadas: {len(resultados)}")
        for i, resultado in enumerate(resultados):
            entidade = texto_original[resultado.start:resultado.end]
            print(f"  {i+1}. '{entidade}' ({resultado.entity_type}, score: {resultado.score:.2f})")
        
        print()
    
    print(f"=== Resumo ===")
    print(f"Casos testados: {total_casos}")
    print(f"Casos com melhoria: {melhorias}")
    print(f"Taxa de melhoria: {(melhorias/total_casos)*100:.1f}%")
    
    # Compara com arquivo original se existir
    arquivo_original = Path('data/output/ouvidorias_anonimizado.csv')
    arquivo_aprimorado = Path('data/output/ouvidorias_anonimizado_aprimorado.csv')
    
    if arquivo_original.exists() and arquivo_aprimorado.exists():
        print("\n=== Comparação de Arquivos ===")
        
        df_original = pd.read_csv(arquivo_original)
        df_aprimorado = pd.read_csv(arquivo_aprimorado)
        
        # Conta diferenças
        diferencas = 0
        for i in range(len(df_original)):
            if df_original.iloc[i]['Teor'] != df_aprimorado.iloc[i]['Teor']:
                diferencas += 1
        
        print(f"Registros com diferenças: {diferencas}")
        print(f"Percentual de diferenças: {(diferencas/len(df_original))*100:.1f}%")

if __name__ == "__main__":
    comparar_versoes() 