#!/usr/bin/env python3
"""
Script para classificação multilabel de dados de educação usando Fireworks AI
Usa as funções de classificação como módulos Python com Llama 4 Scout
"""

import pandas as pd
import yaml
import os
import time
import requests
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any
from tqdm import tqdm

# Carregar variáveis de ambiente do .env se existir
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Arquivo .env carregado")
except ImportError:
    print("⚠️ python-dotenv não instalado, carregando .env manualmente")
    # Carregar .env manualmente
    try:
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✅ Arquivo .env carregado manualmente")
    except FileNotFoundError:
        print("❌ Arquivo .env não encontrado")

def load_education_definitions():
    """Carrega definições de subtemas de educação"""
    config_path = Path("config/definicoes_subtemas.yml")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        definitions = yaml.safe_load(f)
    
    return definitions.get('EDUCACAO', {})

def load_education_subtemas():
    """Carrega lista de subtemas de educação"""
    config_path = Path("config/settings.yml")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    return config.get('subtemas', {}).get('EDUCACAO', [])

def classify_multilabel_single_fireworks(
    text: str,
    subtemas: List[str],
    definitions: Dict[str, Any],
    api_key: str,
    model_name: str = "accounts/fireworks/models/llama-v3p3-70b-instruct"
) -> List[str]:
    """
    Classifica um texto em múltiplos subtemas usando Fireworks AI
    """
    try:
        # Criar lista numerada com definições
        subtemas_with_definitions = []
        for i, subtema in enumerate(subtemas):
            definition = definitions.get(subtema, {}).get('definicao', f"Casos relacionados a {subtema.lower()}")
            subtemas_with_definitions.append(f"{i+1}. {subtema}: {definition}")

        formatted_subtemas = "\n".join(subtemas_with_definitions)

        prompt = f"""Você é um classificador especializado em análise de relatos da área de EDUCAÇÃO.

TAREFA:
Analise o relato abaixo e identifique quais subtemas de EDUCAÇÃO estão presentes. Você pode selecionar até 3 subtemas mais relevantes.

SUBTEMAS DISPONÍVEIS COM DEFINIÇÕES:
{formatted_subtemas}

RELATO:
{text}

CRITÉRIOS DE CLASSIFICAÇÃO:
- Leia cada palavra-chave e contexto do relato
- Compare com as definições específicas de cada subtema
- "Educação Especial - Falta de Mediador" deve ser usado para: ausência de mediador, falta de profissional de apoio, aluno sem acompanhante
- "Educação Especial - Exclusão e Discriminação" deve ser usado para: exclusão, discriminação, segregação, preconceito
- "Alimentação Escolar" deve ser usado para: merenda, comida estragada, cardápio, nutrição
- "Bullying" deve ser usado para: agressão entre alunos, intimidação, humilhação, cyberbullying
- "Infraestrutura" deve ser usado para: prédio, salas, banheiros, acessibilidade, manutenção
- "Transporte" deve ser usado para: ônibus escolar, van, condução, transporte público escolar
- Selecione apenas subtemas que realmente se aplicam ao conteúdo

INSTRUÇÕES:
1. Leia cuidadosamente o relato
2. Identifique palavras-chave e contexto
3. Compare com as definições específicas dos subtemas
4. Selecione até 3 subtemas mais relevantes
5. Responda APENAS com os números dos subtemas separados por vírgula
6. Se nenhum subtema for relevante, responda "NENHUM"

EXEMPLOS DE RESPOSTA:
- "1,6,9" (para subtemas 1, 6 e 9)
- "5" (para apenas o subtema 5)
- "NENHUM" (se não houver subtemas relevantes)

RESPOSTA:"""

        # Fazer chamada para Fireworks AI
        url = "https://api.fireworks.ai/inference/v1/completions"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model_name,
            "prompt": prompt,
            "max_tokens": 50,
            "temperature": 0.1,
            "stop": ["\n"]
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        response_text = result['choices'][0]['text'].strip().upper()

        # Interpretar a resposta
        identified_subtemas = []

        if response_text == "NENHUM":
            return identified_subtemas

        # Extrair números da resposta
        try:
            numbers = [int(num.strip()) for num in response_text.split(',') if num.strip().isdigit()]

            # Converter números para subtemas (ajustar para índice 0)
            for num in numbers[:3]:  # Máximo 3 subtemas
                if 1 <= num <= len(subtemas):
                    identified_subtemas.append(subtemas[num - 1])

        except (ValueError, IndexError) as e:
            print(f"⚠️ Erro ao interpretar resposta '{response_text}': {e}")

        return identified_subtemas

    except Exception as e:
        print(f"❌ Erro ao classificar texto: {e}")
        return []

def process_csv_with_custom_parser(file_path: str) -> pd.DataFrame:
    """
    Processa CSV usando pandas com separador correto
    """
    try:
        # Usar pandas diretamente com separador de ponto e vírgula
        df = pd.read_csv(file_path, sep=';', encoding='utf-8', on_bad_lines='skip')
        print(f"✅ Arquivo carregado: {len(df)} registros")
        print(f"📋 Colunas: {list(df.columns)}")
        return df

    except Exception as e:
        print(f"❌ Erro ao carregar CSV: {e}")
        # Tentar com encoding diferente
        try:
            df = pd.read_csv(file_path, sep=';', encoding='latin-1', on_bad_lines='skip')
            print(f"✅ Arquivo carregado com latin-1: {len(df)} registros")
            return df
        except Exception as e2:
            print(f"❌ Erro com latin-1: {e2}")
            return pd.DataFrame(columns=['MPRJ', 'Tema Original', 'Noticiado', 'Assunto Resumido', 'Assunto Inteiro Teor'])

def generate_fireworks_multilabel_report(df: pd.DataFrame, output_path: str, subtema_counts: Dict[str, int]):
    """Gera relatório de classificação multilabel com Fireworks AI"""
    
    total_cases = len(df)
    classified_cases = sum(1 for _, row in df.iterrows() if row['SUBTEMAS_IDENTIFICADOS'])
    
    # Gera relatório
    report = f"""# Relatório de Classificação Multilabel Fireworks AI - Educação

## Resumo Executivo

- **Total de registros analisados:** {total_cases}
- **Casos classificados:** {classified_cases}
- **Taxa de classificação:** {(classified_cases/total_cases)*100:.1f}%
- **Método:** Classificação com Fireworks AI (Llama 4 Maverick)
- **Data de geração:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Distribuição por Subtemas

### Ranking de Subtemas Identificados

"""
    
    # Ordena subtemas por frequência
    sorted_subtemas = sorted(subtema_counts.items(), key=lambda x: x[1], reverse=True)
    
    for i, (subtema, count) in enumerate(sorted_subtemas, 1):
        percentage = (count/total_cases)*100
        report += f"{i}. **{subtema}**: {count} casos ({percentage:.1f}%)\n"
    
    report += f"""

## Análise Detalhada

### Top 5 Subtemas Mais Frequentes

"""
    
    # Detalhes dos top 5
    for i, (subtema, count) in enumerate(sorted_subtemas[:5], 1):
        percentage = (count/total_cases)*100
        
        # Pega exemplos deste subtema
        examples = []
        for _, row in df.iterrows():
            subtemas_str = str(row['SUBTEMAS_IDENTIFICADOS'])
            if subtema in subtemas_str:
                # Pega primeiras 200 caracteres do texto
                text_sample = str(row['Assunto Inteiro Teor'])[:200] + "..." if len(str(row['Assunto Inteiro Teor'])) > 200 else str(row['Assunto Inteiro Teor'])
                examples.append(text_sample)
                if len(examples) >= 2:  # Máximo 2 exemplos
                    break
        
        report += f"""
#### {i}. {subtema}
- **Frequência:** {count} casos ({percentage:.1f}%)
- **Exemplos:**
"""
        for j, example in enumerate(examples, 1):
            report += f"  {j}. {example}\n"
    
    report += f"""

## Metodologia

- **Classificação:** Fireworks AI Llama 4 Maverick
- **Fonte:** Dados de ouvidorias de educação do MPRJ
- **Período:** Dados de 2024-2025
- **Subtemas analisados:** {len(subtema_counts)} diferentes categorias
- **Prompt:** Especializado para área de educação com definições funcionais

## Vantagens da Classificação LLM

- **Compreensão contextual:** Entende o significado além de palavras-chave
- **Flexibilidade:** Adapta-se a variações de linguagem e sinônimos
- **Precisão:** Maior acurácia na identificação de subtemas relevantes
- **Multilabel:** Identifica múltiplos subtemas por caso quando apropriado

## Observações

- Cada caso pode ser classificado em múltiplos subtemas (máximo 3)
- Classificação baseada em análise semântica do texto completo
- Definições funcionais específicas para gestão pública educacional

---
*Relatório gerado automaticamente pelo Simple Class - Classificação Fireworks AI Multilabel*
"""
    
    # Salva relatório
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📄 Relatório Fireworks AI salvo em: {output_path}")

def main():
    """Função principal"""
    print("🔥 Processando classificação multilabel Fireworks AI para educação...")

    # Verificar API key
    api_key = os.getenv("FIREWORKS_API_KEY")
    if not api_key or api_key == "your_fireworks_api_key_here":
        print("❌ FIREWORKS_API_KEY não configurada")
        print("Configure a variável de ambiente no arquivo .env")
        return 1

    print(f"✅ API Key configurada: {api_key[:20]}...")
    
    # Carrega configurações
    print("📚 Carregando definições de subtemas...")
    definitions = load_education_definitions()
    subtemas = load_education_subtemas()
    print(f"✅ {len(subtemas)} subtemas carregados")
    
    # Processa arquivo
    input_file = "data/input/ouvidorias_educacao.csv"
    print(f"📁 Processando arquivo: {input_file}")
    
    try:
        df = process_csv_with_custom_parser(input_file)
        print(f"✅ Arquivo carregado: {len(df)} registros")
        
        # Classifica cada registro usando Fireworks AI
        print("🔥 Executando classificação multilabel com Fireworks AI...")

        subtemas_list = []
        subtema_counts = {subtema: 0 for subtema in subtemas}

        for _, row in tqdm(df.iterrows(), total=len(df), desc="Classificando com Fireworks AI"):
            text = str(row.get('Assunto Inteiro Teor', ''))

            # Classificação Fireworks AI
            identified_subtemas = classify_multilabel_single_fireworks(
                text, subtemas, definitions, api_key
            )

            # Contar ocorrências
            for subtema in identified_subtemas:
                if subtema in subtema_counts:
                    subtema_counts[subtema] += 1

            # Armazenar resultado
            result_str = ", ".join(identified_subtemas) if identified_subtemas else ""
            subtemas_list.append(result_str)

            # Pausa para evitar rate limiting
            time.sleep(0.5)

        # Adiciona colunas de resultado
        df['SUBTEMAS_IDENTIFICADOS'] = subtemas_list
        df['TOTAL_SUBTEMAS'] = [len(s.split(", ")) if s else 0 for s in subtemas_list]
        df['METODO_CLASSIFICACAO'] = 'Fireworks_AI'
        
        # Salva resultado
        output_file = "data/output/ouvidorias_educacao_fireworks_multilabel.csv"
        df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"💾 Classificação Fireworks AI salva em: {output_file}")

        # Gera relatório
        report_file = "data/output/relatorio_educacao_fireworks_multilabel.md"
        generate_fireworks_multilabel_report(df, report_file, subtema_counts)

        # Estatísticas finais
        total_cases = len(df)
        classified_cases = sum(1 for s in subtemas_list if s)
        total_classifications = sum(len(s.split(", ")) if s else 0 for s in subtemas_list)

        print(f"""
🎉 Processamento Fireworks AI concluído!

📊 Estatísticas:
   • Total de casos: {total_cases}
   • Casos classificados: {classified_cases} ({(classified_cases/total_cases)*100:.1f}%)
   • Total de classificações: {total_classifications}
   • Média de subtemas por caso: {total_classifications/total_cases:.1f}

📁 Arquivos gerados:
   • {output_file}
   • {report_file}

🔥 Método: Fireworks AI Llama 3.3 70B Instruct
""")
        
    except Exception as e:
        print(f"❌ Erro no processamento: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
