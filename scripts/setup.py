#!/usr/bin/env python3
"""
Script de configuração e inicialização da Simple Class API.

Este script configura o ambiente, verifica dependências e inicializa a API
com as configurações apropriadas para desenvolvimento ou produção.
"""

import os
import sys
import subprocess
import argparse
import json
import shutil
from pathlib import Path
from typing import Dict, List, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class Colors:
    """ANSI color codes for terminal output."""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


def print_colored(message: str, color: str = Colors.ENDC):
    """Print colored message to terminal."""
    print(f"{color}{message}{Colors.ENDC}")


def print_header(title: str):
    """Print formatted header."""
    print_colored(f"\n{'='*60}", Colors.HEADER)
    print_colored(f"  {title}", Colors.HEADER + Colors.BOLD)
    print_colored(f"{'='*60}", Colors.HEADER)


def run_command(command: List[str], description: str, check: bool = True) -> bool:
    """Run a command and return success status."""
    print_colored(f"🔄 {description}...", Colors.OKBLUE)
    try:
        result = subprocess.run(command, check=check, capture_output=True, text=True)
        if result.returncode == 0:
            print_colored(f"✅ {description} - Sucesso", Colors.OKGREEN)
            return True
        else:
            print_colored(f"❌ {description} - Falhou", Colors.FAIL)
            if result.stderr:
                print_colored(f"Erro: {result.stderr}", Colors.FAIL)
            return False
    except subprocess.CalledProcessError as e:
        print_colored(f"❌ {description} - Erro: {e}", Colors.FAIL)
        return False
    except FileNotFoundError:
        print_colored(f"❌ {description} - Comando não encontrado", Colors.FAIL)
        return False


def check_python_version() -> bool:
    """Check if Python version is compatible."""
    print_colored("🐍 Verificando versão do Python...", Colors.OKBLUE)
    
    version = sys.version_info
    if version.major == 3 and version.minor >= 11:
        print_colored(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK", Colors.OKGREEN)
        return True
    else:
        print_colored(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requer Python 3.11+", Colors.FAIL)
        return False


def check_poetry() -> bool:
    """Check if Poetry is installed."""
    return run_command(["poetry", "--version"], "Verificando Poetry", check=False)


def check_git() -> bool:
    """Check if Git is installed."""
    return run_command(["git", "--version"], "Verificando Git", check=False)


def install_dependencies() -> bool:
    """Install project dependencies using Poetry."""
    return run_command(["poetry", "install"], "Instalando dependências")


def create_env_file() -> bool:
    """Create .env file from template if it doesn't exist."""
    env_file = project_root / ".env"
    env_example = project_root / ".env.example"
    
    if env_file.exists():
        print_colored("✅ Arquivo .env já existe", Colors.OKGREEN)
        return True
    
    if env_example.exists():
        shutil.copy(env_example, env_file)
        print_colored("✅ Arquivo .env criado a partir do .env.example", Colors.OKGREEN)
        print_colored("⚠️  Configure as variáveis de ambiente no arquivo .env", Colors.WARNING)
        return True
    else:
        # Create basic .env file
        env_content = """# Simple Class API Configuration

# API Settings
API_SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=30

# CORS Settings
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Supabase (opcional para desenvolvimento)
SUPABASE_URL=
SUPABASE_KEY=
SUPABASE_SERVICE_KEY=

# External APIs (opcional para desenvolvimento)
TOGETHER_API_KEY=
ANTHROPIC_API_KEY=
FIREWORKS_API_KEY=

# Environment
ENVIRONMENT=development
"""
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print_colored("✅ Arquivo .env básico criado", Colors.OKGREEN)
        print_colored("⚠️  Configure as variáveis de ambiente conforme necessário", Colors.WARNING)
        return True


def check_directories() -> bool:
    """Create necessary directories."""
    directories = [
        "data/input",
        "data/output", 
        "logs",
        "temp",
        "docs",
        "tests"
    ]
    
    print_colored("📁 Verificando diretórios...", Colors.OKBLUE)
    
    for directory in directories:
        dir_path = project_root / directory
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print_colored(f"✅ Diretório criado: {directory}", Colors.OKGREEN)
        else:
            print_colored(f"✅ Diretório existe: {directory}", Colors.OKGREEN)
    
    return True


def run_tests() -> bool:
    """Run basic tests to verify installation."""
    print_colored("🧪 Executando testes básicos...", Colors.OKBLUE)
    
    # Test import
    try:
        import src.api.main
        print_colored("✅ Importação da API - OK", Colors.OKGREEN)
    except ImportError as e:
        print_colored(f"❌ Erro na importação: {e}", Colors.FAIL)
        return False
    
    # Run quick tests
    return run_command(
        ["poetry", "run", "pytest", "tests/test_auth.py::TestAuthEndpoints::test_login_success", "-v"],
        "Teste de autenticação",
        check=False
    )


def show_configuration_info():
    """Show configuration information."""
    print_header("INFORMAÇÕES DE CONFIGURAÇÃO")
    
    print_colored("📋 Configuração atual:", Colors.OKBLUE)
    print_colored(f"  • Diretório do projeto: {project_root}", Colors.ENDC)
    print_colored(f"  • Python: {sys.version}", Colors.ENDC)
    
    # Check .env file
    env_file = project_root / ".env"
    if env_file.exists():
        print_colored("  • Arquivo .env: ✅ Configurado", Colors.OKGREEN)
    else:
        print_colored("  • Arquivo .env: ❌ Não encontrado", Colors.FAIL)
    
    print_colored("\n🚀 Para iniciar a API:", Colors.OKBLUE)
    print_colored("  poetry run python scripts/start_api_docs.py --reload", Colors.ENDC)
    
    print_colored("\n📚 Documentação:", Colors.OKBLUE)
    print_colored("  • Swagger UI: http://localhost:8000/docs", Colors.ENDC)
    print_colored("  • ReDoc: http://localhost:8000/redoc", Colors.ENDC)
    
    print_colored("\n🧪 Credenciais de teste:", Colors.OKBLUE)
    print_colored("  • Email: <EMAIL>", Colors.ENDC)
    print_colored("  • Senha: test123", Colors.ENDC)


def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description="Setup Simple Class API")
    parser.add_argument("--skip-tests", action="store_true", help="Skip running tests")
    parser.add_argument("--skip-deps", action="store_true", help="Skip installing dependencies")
    parser.add_argument("--production", action="store_true", help="Setup for production")
    
    args = parser.parse_args()
    
    print_header("SIMPLE CLASS API - CONFIGURAÇÃO")
    
    success = True
    
    # Check system requirements
    if not check_python_version():
        success = False
    
    if not check_poetry():
        print_colored("❌ Poetry não encontrado. Instale: https://python-poetry.org/docs/", Colors.FAIL)
        success = False
    
    if not check_git():
        print_colored("⚠️  Git não encontrado. Recomendado para desenvolvimento", Colors.WARNING)
    
    if not success:
        print_colored("\n❌ Requisitos do sistema não atendidos", Colors.FAIL)
        sys.exit(1)
    
    # Setup project
    if not args.skip_deps:
        if not install_dependencies():
            success = False
    
    if not create_env_file():
        success = False
    
    if not check_directories():
        success = False
    
    # Run tests
    if not args.skip_tests and success:
        if not run_tests():
            print_colored("⚠️  Alguns testes falharam, mas a configuração pode continuar", Colors.WARNING)
    
    # Show final status
    if success:
        print_header("CONFIGURAÇÃO CONCLUÍDA")
        print_colored("🎉 Simple Class API configurada com sucesso!", Colors.OKGREEN)
        show_configuration_info()
    else:
        print_header("CONFIGURAÇÃO COM PROBLEMAS")
        print_colored("❌ Alguns problemas foram encontrados durante a configuração", Colors.FAIL)
        print_colored("Verifique os erros acima e tente novamente", Colors.FAIL)
        sys.exit(1)


if __name__ == "__main__":
    main()
