#!/usr/bin/env python3
"""
Script to setup Supabase database and storage.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from supabase import create_client, Client
from dotenv import load_dotenv

def setup_database():
    """Check database setup and provide instructions."""
    print("🗄️  Checking database setup...")

    # Load environment variables
    load_dotenv()

    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_KEY")

    try:
        supabase: Client = create_client(url, key)

        # Test if tables exist
        tables_to_check = ["files", "processing_jobs", "reports"]
        existing_tables = []

        for table in tables_to_check:
            try:
                supabase.table(table).select("count").limit(1).execute()
                existing_tables.append(table)
                print(f"   ✅ Table '{table}' exists")
            except Exception:
                print(f"   ❌ Table '{table}' not found")

        if len(existing_tables) == len(tables_to_check):
            print("✅ All database tables exist")
            return True
        else:
            print(f"⚠️  {len(tables_to_check) - len(existing_tables)} tables missing")
            print("\n📝 Manual setup required:")
            print("   1. Go to your Supabase dashboard")
            print("   2. Navigate to SQL Editor")
            print("   3. Copy and paste the content from 'database/supabase_setup.sql'")
            print("   4. Execute the SQL script")
            return False

    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False

def setup_storage():
    """Setup storage bucket."""
    print("\n📦 Setting up storage...")
    
    # Load environment variables
    load_dotenv()
    
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_KEY")
    
    try:
        supabase: Client = create_client(url, key)
        
        bucket_name = "simple-class-files"
        
        # Check if bucket already exists
        buckets = supabase.storage.list_buckets()
        bucket_exists = any(bucket.name == bucket_name for bucket in buckets)
        
        if bucket_exists:
            print(f"✅ Bucket '{bucket_name}' already exists")
            return True
        
        # Create bucket
        try:
            supabase.storage.create_bucket(bucket_name, {"public": False})
            print(f"✅ Created bucket '{bucket_name}'")
            return True
        except Exception as e:
            print(f"❌ Failed to create bucket: {e}")
            print("   Please create it manually in the Supabase dashboard")
            return False
        
    except Exception as e:
        print(f"❌ Storage setup failed: {e}")
        return False

def test_setup():
    """Test the setup by creating a test record."""
    print("\n🧪 Testing setup...")
    
    # Load environment variables
    load_dotenv()
    
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_KEY")
    
    try:
        supabase: Client = create_client(url, key)
        
        # Test database access
        try:
            supabase.table("files").select("count").execute()
            print("✅ Database tables accessible")
        except Exception as e:
            print(f"❌ Database test failed: {e}")
            return False
        
        # Test storage access
        try:
            buckets = supabase.storage.list_buckets()
            bucket_names = [bucket.name for bucket in buckets]
            if "simple-class-files" in bucket_names:
                print("✅ Storage bucket accessible")
            else:
                print("❌ Storage bucket not found")
                return False
        except Exception as e:
            print(f"❌ Storage test failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Setup test failed: {e}")
        return False

def main():
    """Main setup function."""
    print("🚀 Supabase Setup Script")
    print("=" * 40)
    
    # Setup database
    db_ok = setup_database()
    
    # Setup storage
    storage_ok = setup_storage()
    
    # Test setup
    if db_ok and storage_ok:
        test_ok = test_setup()
    else:
        test_ok = False
    
    print("\n" + "=" * 40)
    print("📊 Setup Results:")
    print(f"   Database: {'✅ OK' if db_ok else '❌ FAILED'}")
    print(f"   Storage: {'✅ OK' if storage_ok else '❌ FAILED'}")
    print(f"   Tests: {'✅ OK' if test_ok else '❌ FAILED'}")
    
    if db_ok and storage_ok and test_ok:
        print("\n🎉 Supabase setup completed successfully!")
        print("   Your API is ready to use with real Supabase backend")
    else:
        print("\n⚠️  Setup incomplete. Please check the errors above.")
        print("   You may need to run some steps manually in the Supabase dashboard")

if __name__ == "__main__":
    main()
