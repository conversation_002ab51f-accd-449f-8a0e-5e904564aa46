#!/usr/bin/env python3
"""
Script para classificação multilabel de dados de saúde usando Fireworks AI
Usa as funções de classificação como módulos Python com Llama 4 Scout
"""

import pandas as pd
import yaml
import os
import time
import requests
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any
from tqdm import tqdm

# Carregar variáveis de ambiente do .env se existir
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Arquivo .env carregado")
except ImportError:
    print("⚠️ python-dotenv não instalado, carregando .env manualmente")
    # Carregar .env manualmente
    try:
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✅ Arquivo .env carregado manualmente")
    except FileNotFoundError:
        print("❌ Arquivo .env não encontrado")

def load_saude_definitions():
    """Carrega definições de subtemas de saúde"""
    try:
        with open('config/definicoes_subtemas.yml', 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
            return data.get('SAUDE', {})
    except FileNotFoundError:
        print("⚠️  Arquivo de definições não encontrado, usando definições básicas")
        return {}

def load_saude_subtemas():
    """Carrega lista de subtemas de saúde"""
    try:
        with open('config/config.yml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            return config.get('SUBTEMAS_SAUDE', [])
    except FileNotFoundError:
        print("⚠️  Arquivo de configuração não encontrado")
        return []

def classify_multilabel_single_fireworks(
    text: str,
    subtemas: List[str],
    definitions: Dict[str, Any],
    api_key: str,
    model_name: str = "accounts/fireworks/models/llama-v3p3-70b-instruct"
) -> List[str]:
    """
    Classifica um texto em múltiplos subtemas usando Fireworks AI
    """
    try:
        # Criar lista numerada com definições
        subtemas_with_definitions = []
        for i, subtema in enumerate(subtemas):
            definition = definitions.get(subtema, {}).get('definicao', f"Casos relacionados a {subtema.lower()}")
            subtemas_with_definitions.append(f"{i+1}. {subtema}: {definition}")

        formatted_subtemas = "\n".join(subtemas_with_definitions)

        prompt = f"""Você é um classificador especializado em análise de relatos da área de SAÚDE.

TAREFA:
Analise o relato abaixo e identifique quais subtemas de SAÚDE estão presentes. Você pode selecionar até 3 subtemas mais relevantes.

SUBTEMAS DISPONÍVEIS COM DEFINIÇÕES:
{formatted_subtemas}

RELATO:
{text}

CRITÉRIOS DE CLASSIFICAÇÃO:
- Leia cada palavra-chave e contexto do relato
- Compare com as definições específicas de cada subtema
- "Regulação em Saúde" deve ser usado para: filas de espera, SISREG, SER, demoras no agendamento, transferências, problemas de regulação
- "Diagnose" deve ser usado para: exames, laudos, resultados, procedimentos diagnósticos
- "Auditoria" deve ser usado para: irregularidades administrativas, má gestão, problemas de controle
- Selecione apenas subtemas que realmente se aplicam ao conteúdo

INSTRUÇÕES:
1. Leia cuidadosamente o relato
2. Identifique palavras-chave e contexto
3. Compare com as definições específicas dos subtemas
4. Selecione até 3 subtemas mais relevantes
5. Responda APENAS com os números dos subtemas separados por vírgula
6. Se nenhum subtema for relevante, responda "NENHUM"

EXEMPLOS DE RESPOSTA:
- "1,6,9" (para subtemas 1, 6 e 9)
- "5" (para apenas o subtema 5)
- "NENHUM" (se não houver subtemas relevantes)

RESPOSTA:"""

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model_name,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": 50,
            "temperature": 0.1
        }

        response = requests.post(
            "https://api.fireworks.ai/inference/v1/chat/completions",
            headers=headers,
            json=data
        )

        if response.status_code == 200:
            result = response.json()
            response_text = result['choices'][0]['message']['content'].strip()
            
            # Processar resposta
            if response_text.upper() == "NENHUM":
                return []
            
            # Extrair números
            try:
                numbers = [int(num.strip()) for num in response_text.split(',') if num.strip().isdigit()]
                identified_subtemas = []
                for num in numbers[:3]:  # Máximo 3 subtemas
                    if 1 <= num <= len(subtemas):
                        identified_subtemas.append(subtemas[num - 1])
                return identified_subtemas
            except:
                return []
        else:
            print(f"Erro na API: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"Erro na classificação: {e}")
        return []

def process_csv_with_custom_parser(file_path: str) -> pd.DataFrame:
    """
    Processa CSV usando pandas com separador correto
    """
    try:
        # Primeiro tentar com vírgula como separador
        df = pd.read_csv(file_path, sep=',', encoding='utf-8', on_bad_lines='skip')
        print(f"✅ Arquivo carregado: {len(df)} registros")
        print(f"📋 Colunas: {list(df.columns)}")

        # Verificar se temos as colunas esperadas
        if 'Teor' not in df.columns and 'Assunto Inteiro Teor' not in df.columns:
            print("⚠️ Tentando com ponto e vírgula como separador...")
            df = pd.read_csv(file_path, sep=';', encoding='utf-8', on_bad_lines='skip')
            print(f"✅ Arquivo carregado: {len(df)} registros")
            print(f"📋 Colunas: {list(df.columns)}")

        return df
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo: {e}")
        return None

def generate_fireworks_multilabel_report(df: pd.DataFrame, output_path: str, subtema_counts: Dict[str, int]):
    """Gera relatório de classificação multilabel com Fireworks AI"""
    
    total_cases = len(df)
    classified_cases = sum(1 for _, row in df.iterrows() if row['SUBTEMAS_IDENTIFICADOS'])
    
    # Gera relatório
    report = f"""# Relatório de Classificação Multilabel Fireworks AI - Saúde

## Resumo Executivo

- **Total de casos analisados:** {total_cases:,}
- **Casos classificados:** {classified_cases:,} ({classified_cases/total_cases*100:.1f}%)
- **Casos sem classificação:** {total_cases - classified_cases:,} ({(total_cases - classified_cases)/total_cases*100:.1f}%)
- **Data de processamento:** {datetime.now().strftime('%d/%m/%Y %H:%M')}

## Distribuição por Subtema

| Subtema | Casos | Percentual |
|---------|-------|------------|"""

    for subtema, count in sorted(subtema_counts.items(), key=lambda x: x[1], reverse=True):
        if count > 0:
            percentage = (count / total_cases) * 100
            report += f"\n| {subtema} | {count:,} | {percentage:.1f}% |"

    report += f"""

## Metodologia

- **Classificação:** Fireworks AI Llama 3.3 70B Instruct
- **Fonte:** Dados de ouvidorias de saúde do MPRJ
- **Período:** Dados de 2024-2025
- **Subtemas analisados:** {len(subtema_counts)} diferentes categorias
- **Prompt:** Especializado para área de saúde com definições funcionais

## Vantagens da Classificação LLM

- **Compreensão contextual:** Entende o significado além de palavras-chave
- **Flexibilidade:** Adapta-se a variações de linguagem e sinônimos
- **Precisão:** Maior acurácia na identificação de subtemas relevantes
- **Multilabel:** Identifica múltiplos subtemas por caso quando apropriado

## Observações

Este relatório foi gerado automaticamente pelo sistema de classificação multilabel.
Os dados refletem a análise automatizada dos relatos de ouvidoria na área de saúde.
"""

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📄 Relatório Fireworks AI salvo em: {output_path}")

def main():
    """Função principal"""
    print("🔥 Processando classificação multilabel Fireworks AI para saúde...")

    # Verificar API key
    api_key = os.getenv("FIREWORKS_API_KEY")
    if not api_key or api_key == "your_fireworks_api_key_here":
        print("❌ FIREWORKS_API_KEY não configurada")
        print("Configure a variável de ambiente no arquivo .env")
        return 1

    print(f"✅ API Key configurada: {api_key[:20]}...")
    
    # Carrega configurações
    print("📚 Carregando definições de subtemas...")
    definitions = load_saude_definitions()
    subtemas = load_saude_subtemas()
    print(f"✅ {len(subtemas)} subtemas carregados")
    
    # Processa arquivo
    input_file = "data/input/ouvidorias_saude.csv"
    print(f"📁 Processando arquivo: {input_file}")
    
    try:
        df = process_csv_with_custom_parser(input_file)
        print(f"✅ Arquivo carregado: {len(df)} registros")
        
        # Classifica cada registro usando Fireworks AI
        print("🔥 Executando classificação multilabel com Fireworks AI...")

        subtemas_list = []
        subtema_counts = {subtema: 0 for subtema in subtemas}

        for _, row in tqdm(df.iterrows(), total=len(df), desc="Classificando com Fireworks AI"):
            # Usar a coluna correta dependendo do arquivo
            text = str(row.get('Teor', row.get('Assunto Inteiro Teor', '')))

            # Classificação Fireworks AI
            identified_subtemas = classify_multilabel_single_fireworks(
                text, subtemas, definitions, api_key
            )

            # Contar ocorrências
            for subtema in identified_subtemas:
                if subtema in subtema_counts:
                    subtema_counts[subtema] += 1

            # Armazenar resultado
            result_str = ", ".join(identified_subtemas) if identified_subtemas else ""
            subtemas_list.append(result_str)

            # Pausa para evitar rate limiting
            time.sleep(0.5)

        # Adiciona colunas de resultado
        df['SUBTEMAS_IDENTIFICADOS'] = subtemas_list
        df['TOTAL_SUBTEMAS'] = [len(s.split(", ")) if s else 0 for s in subtemas_list]
        df['METODO_CLASSIFICACAO'] = 'Fireworks_AI'
        
        # Salva resultado
        output_file = "data/output/ouvidorias_saude_fireworks_multilabel.csv"
        df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"💾 Classificação Fireworks AI salva em: {output_file}")

        # Gera relatório
        report_file = "data/output/relatorio_saude_fireworks_multilabel.md"
        generate_fireworks_multilabel_report(df, report_file, subtema_counts)

        # Estatísticas finais
        total_cases = len(df)
        classified_cases = sum(1 for s in subtemas_list if s)
        total_classifications = sum(len(s.split(", ")) if s else 0 for s in subtemas_list)

        print(f"\n🎉 Processamento Fireworks AI concluído!")
        print(f"\n📊 Estatísticas:")
        print(f"   • Total de casos: {total_cases}")
        print(f"   • Casos classificados: {classified_cases} ({classified_cases/total_cases*100:.1f}%)")
        print(f"   • Total de classificações: {total_classifications}")
        print(f"   • Média de subtemas por caso: {total_classifications/total_cases:.1f}")

        print(f"\n📁 Arquivos gerados:")
        print(f"   • {output_file}")
        print(f"   • {report_file}")

        print(f"\n🔥 Método: Fireworks AI Llama 3.3 70B Instruct")

    except Exception as e:
        print(f"❌ Erro durante o processamento: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
