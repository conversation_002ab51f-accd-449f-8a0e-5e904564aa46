#!/usr/bin/env python3
"""
Script para anonimizar dados usando Presidio com heurísticas contextuais aprimoradas.

Combina o poder do Presidio com detecção contextual inteligente de nomes,
preservando instituições e autoridades públicas. Versão aprimorada baseada
em análise comparativa com LLM.
"""

import pandas as pd
from presidio_analyzer import AnalyzerEngine, RecognizerResult
from presidio_anonymizer import AnonymizerEngine
from presidio_analyzer.nlp_engine import NlpEngineProvider
import argparse
from pathlib import Path
import re
from typing import List
from presidio_analyzer import EntityRecognizer

# Configuração do parser de argumentos
parser = argparse.ArgumentParser(description='Anonimiza relatos preservando contexto')
parser.add_argument(
    '--coluna',
    type=str,
    default='Teor',
    help='Nome da coluna a ser anonimizada (padrão: Teor)'
)

# Listas de termos que indicam contexto específico - APRIMORADAS
CONTEXTOS_NOME = [
    r'\bmãe\b', r'\bpai\b', r'\bfilho\b', r'\bfilha\b',
    r'\besposo\b', r'\besposa\b', r'\bmarido\b', r'\besposa\b',
    r'\bvizinho\b', r'\bvizinha\b', r'\bamigo\b', r'\bamiga\b',
    r'\bsenhora?\b', r'\bdona\b', r'\bnome\b', r'\bchamada?\b',
    r'\bconhecida?\b', r'\bpaciente\b', r'\bresponsável\b',
    r'\bacompanhante\b', r'\bidosa?\b', r'\bjovem\b',
    r'\bpessoa\b', r'\bindivíduo\b', r'\bcidadão\b', r'\bcidadã\b',
    # NOVOS CONTEXTOS baseados na análise LLM
    r'\bsogro\b', r'\bsogra\b', r'\bgenitor\b', r'\bgenitora\b',
    r'\bvítima\b', r'\bnoticiante\b', r'\bcomunicante\b',
    r'\bdenunciante\b', r'\bdemandante\b', r'\binformante\b',
    r'\brelata\b', r'\binforma\b', r'\bconta\b', r'\bnarra\b',
    r'\bsenhor\b', r'\bsenhora\b', r'\bhomem\b', r'\bmulher\b',
    r'\bcunhado\b', r'\bcunhada\b', r'\btio\b', r'\btia\b',
    r'\bavô\b', r'\bavó\b', r'\bneto\b', r'\bneta\b'
]

# Padrões para detectar referências com idade - NOVO
PADROES_IDADE = [
    r'\b(?:senhor|senhora|homem|mulher|idoso|idosa|pessoa)\s+de\s+\d+\s+anos?\b',
    r'\b\d+\s+anos?\b',
    r'\b(?:anos?|idade)\b'
]

# Instituições que devem ser preservadas - EXPANDIDAS
INSTITUICOES_PRESERVAR = {
    'hospital', 'clínica', 'centro de saúde', 'posto de saúde', 'ups', 'upa',
    'emergência', 'pronto socorro', 'pronto atendimento', 'ambulatório',
    'maternidade', 'laboratório', 'centro médico', 'policlínica',
    'santa casa', 'hcpa', 'grupo hospitalar', 'instituto', 'fundação',
    # NOVOS - baseados na análise
    'hélio', 'pelegrino', 'pedro ernesto', 'miguel couto', 'andaraí',
    'bonsucesso', 'santa maria', 'jorge vaistman', 'getúlio vargas',
    'into', 'inca', 'hupe', 'caps', 'sisreg', 'ser', 'sus',
    'centro carioca', 'rio imagem', 'piquet carneiro'
}

# Termos médicos que NÃO são nomes - NOVO
TERMOS_MEDICOS = {
    'retina', 'policitemia', 'vera', 'alzheimer', 'câncer', 'cancer',
    'tumor', 'cirurgia', 'operação', 'tratamento', 'consulta',
    'exame', 'biopsia', 'endoscopia', 'colonoscopia', 'tomografia',
    'ressonância', 'ultrassom', 'raio-x', 'mamografia'
}

# Autoridades que devem ser preservadas
AUTORIDADES_PRESERVAR = {
    'juiz', 'juíza', 'promotor', 'promotora', 'defensor', 'defensora',
    'delegado', 'delegada', 'ministro', 'ministra', 'desembargador',
    'desembargadora', 'procurador', 'procuradora', 'conselheiro',
    'conselheira', 'vereador', 'vereadora', 'deputado', 'deputada',
    'senador', 'senadora', 'prefeito', 'prefeita', 'governador',
    'governadora', 'presidente', 'secretário', 'secretária',
    'doutor', 'doutora', 'dr', 'dra', 'médico', 'médica'
}


class ContextualNameRecognizer(EntityRecognizer):
    """
    Custom recognizer aprimorado que detecta nomes baseado em contexto.
    Versão melhorada baseada na análise comparativa com LLM.
    """
    
    def __init__(self):
        super().__init__(
            supported_entities=["PERSON_CONTEXTUAL"],
            name="contextual_name_recognizer"
        )
        self.contextos_pattern = re.compile(
            '|'.join(CONTEXTOS_NOME), 
            re.IGNORECASE
        )
        self.idade_pattern = re.compile(
            '|'.join(PADROES_IDADE),
            re.IGNORECASE
        )
    
    def analyze(self, text: str, entities: List[str] = None, nlp_artifacts=None) -> List[RecognizerResult]:
        """
        Detecta nomes baseado em contextos aprimorados.
        """
        _ = entities, nlp_artifacts
        results = []
        
        # 1. PADRÃO ORIGINAL: Contextos seguidos de nomes
        results.extend(self._detectar_nomes_por_contexto(text))
        
        # 2. NOVO: Padrão "Meu nome é/e Nome"
        results.extend(self._detectar_padrao_meu_nome(text))
        
        # 3. NOVO: Nomes com idade (ex: "João Silva, 65 anos")
        results.extend(self._detectar_nomes_com_idade(text))
        
        # 4. NOVO: Referências familiares (ex: "meu sogro um senhor")
        results.extend(self._detectar_referencias_familiares(text))
        
        # 5. NOVO: Nomes em contexto de relato (ex: "relata que Maria")
        results.extend(self._detectar_nomes_em_relatos(text))
        
        return results
    
    def _detectar_nomes_por_contexto(self, text: str) -> List[RecognizerResult]:
        """Detecção original por contexto."""
        results = []
        
        for match in self.contextos_pattern.finditer(text):
            start = match.end()
            
            # Pula espaços e pontuação
            while start < len(text) and text[start] in ' ,:;-':
                start += 1
            
            # Busca nome completo
            end = start
            palavras_nome = []
            while end < len(text):
                while end < len(text) and text[end] in ' ,-':
                    end += 1
                if end >= len(text):
                    break
                
                palavra_start = end
                while end < len(text) and text[end] not in ' ,.;:!?\n':
                    end += 1
                
                palavra = text[palavra_start:end]
                
                if (palavra and palavra[0].isupper() and len(palavra) > 1) or palavra.upper() in ['DE', 'DA', 'DO', 'DOS', 'DAS', 'B.']:
                    palavras_nome.append(palavra)
                else:
                    break
                
                if len(palavras_nome) >= 5:
                    break
            
            if len(palavras_nome) >= 1:
                nome_completo = ' '.join(palavras_nome)
                
                if self._eh_nome_valido(nome_completo):
                    nome_end = start + len(nome_completo)
                    results.append(RecognizerResult(
                        entity_type="PERSON_CONTEXTUAL",
                        start=start,
                        end=nome_end,
                        score=0.85
                    ))
        
        return results
    
    def _detectar_padrao_meu_nome(self, text: str) -> List[RecognizerResult]:
        """Detecta padrão 'Meu nome é/e Nome'."""
        results = []
        
        # Padrão mais flexível para "meu nome é/e"
        pattern = re.compile(r'\b(?:meu\s+)?nome\s+(?:é|e)\s+([A-Z][a-záàáâãéêíóôõúç]+(?:\s+[A-Z][a-záàáâãéêíóôõúç]+)*)', re.IGNORECASE)
        for match in pattern.finditer(text):
            nome = match.group(1).strip()
            if len(nome) > 2 and self._eh_nome_valido(nome):
                results.append(RecognizerResult(
                    entity_type="PERSON_CONTEXTUAL",
                    start=match.start(1),
                    end=match.end(1),
                    score=0.95
                ))
        
        return results
    
    def _detectar_nomes_com_idade(self, text: str) -> List[RecognizerResult]:
        """Detecta nomes seguidos de idade."""
        results = []
        
        # Padrão: Nome Sobrenome, XX anos
        pattern = re.compile(r'\b([A-Z][a-záàáâãéêíóôõúç]+(?:\s+[A-Z][a-záàáâãéêíóôõúç]+)+),?\s+\d+\s+anos?\b', re.IGNORECASE)
        for match in pattern.finditer(text):
            nome = match.group(1).strip()
            if self._eh_nome_valido(nome):
                results.append(RecognizerResult(
                    entity_type="PERSON_CONTEXTUAL",
                    start=match.start(1),
                    end=match.end(1),
                    score=0.90
                ))
        
        return results
    
    def _detectar_referencias_familiares(self, text: str) -> List[RecognizerResult]:
        """Detecta referências familiares como 'meu sogro um senhor'."""
        results = []
        
        # Padrões como "meu sogro um senhor de X anos"
        pattern = re.compile(r'\b(?:meu|minha)\s+(?:sogro|sogra|pai|mãe|avô|avó)\s+(?:um|uma)\s+(?:senhor|senhora)\s+de\s+\d+\s+anos?\b', re.IGNORECASE)
        for match in pattern.finditer(text):
            # Marca toda a expressão como referência pessoal
            results.append(RecognizerResult(
                entity_type="PERSON_CONTEXTUAL",
                start=match.start(),
                end=match.end(),
                score=0.80
            ))
        
        # NOVO: Padrão mais simples para "meu sogro", "minha mãe", etc.
        pattern2 = re.compile(r'\b(?:meu|minha)\s+(?:sogro|sogra|pai|mãe|avô|avó|genitor|genitora)\b', re.IGNORECASE)
        for match in pattern2.finditer(text):
            results.append(RecognizerResult(
                entity_type="PERSON_CONTEXTUAL",
                start=match.start(),
                end=match.end(),
                score=0.75
            ))
        
        return results
    
    def _detectar_nomes_em_relatos(self, text: str) -> List[RecognizerResult]:
        """Detecta nomes em contexto de relatos."""
        results = []
        
        # Padrões como "relata que Maria", "informa que João"
        pattern = re.compile(r'\b(?:relata|informa|conta|narra|diz)\s+que\s+([A-Z][a-záàáâãéêíóôõúç]+(?:\s+[A-Z][a-záàáâãéêíóôõúç]+)*)', re.IGNORECASE)
        for match in pattern.finditer(text):
            nome = match.group(1).strip()
            if len(nome) > 2 and self._eh_nome_valido(nome):
                results.append(RecognizerResult(
                    entity_type="PERSON_CONTEXTUAL",
                    start=match.start(1),
                    end=match.end(1),
                    score=0.85
                ))
        
        # NOVO: Detecta nomes próprios isolados em contextos específicos
        # Como "JACIRA FRANCISCA", "WALTER", etc.
        pattern2 = re.compile(r'\b([A-Z]{2,}(?:\s+[A-Z]{2,})*(?:\s+[A-Z][a-záàáâãéêíóôõúç]+)*)\b')
        for match in pattern2.finditer(text):
            nome = match.group(1).strip()
            if len(nome) > 4 and self._eh_nome_valido(nome) and self._tem_contexto_pessoa(text, match.start(), match.end()):
                results.append(RecognizerResult(
                    entity_type="PERSON_CONTEXTUAL",
                    start=match.start(1),
                    end=match.end(1),
                    score=0.80
                ))
        
        return results
    
    def _tem_contexto_pessoa(self, text: str, start: int, end: int) -> bool:
        """Verifica se há contexto que indica que é uma pessoa."""
        # Pega contexto ao redor (50 caracteres antes e depois)
        contexto_inicio = max(0, start - 50)
        contexto_fim = min(len(text), end + 50)
        contexto = text[contexto_inicio:contexto_fim].lower()
        
        # Palavras que indicam contexto de pessoa
        indicadores_pessoa = [
            'anos', 'idade', 'senhor', 'senhora', 'mãe', 'pai', 'filho', 'filha',
            'sogro', 'sogra', 'avô', 'avó', 'genitor', 'genitora', 'paciente',
            'vítima', 'pessoa', 'idoso', 'idosa', 'chamado', 'nome'
        ]
        
        return any(indicador in contexto for indicador in indicadores_pessoa)
    
    def _eh_nome_valido(self, nome: str) -> bool:
        """Verifica se é um nome válido (não instituição ou termo médico)."""
        nome_lower = nome.lower()
        
        # Verifica se não é instituição
        for instituicao in INSTITUICOES_PRESERVAR:
            if instituicao in nome_lower:
                return False
        
        # Verifica se não é termo médico
        for termo in TERMOS_MEDICOS:
            if termo in nome_lower:
                return False
        
        # NOVO: Lista de palavras que NÃO são nomes
        palavras_nao_nomes = {
            'fico', 'vou', 'sou', 'estou', 'tenho', 'posso', 'devo', 'quero',
            'preciso', 'gostaria', 'solicito', 'peço', 'aguardo', 'espero',
            'devido', 'cita', 'conta', 'relata', 'informa', 'narra',
            'pacientes', 'hospital', 'tratamento', 'cirurgia', 'exame'
        }
        
        # Verifica se é uma palavra que não é nome
        if nome_lower in palavras_nao_nomes:
            return False
        
        # Verifica se não é muito curto ou muito longo
        if len(nome) < 3 or len(nome) > 100:
            return False
        
        # NOVO: Verifica se tem pelo menos uma letra maiúscula (indicativo de nome próprio)
        if not any(c.isupper() for c in nome):
            return False
        
        return True


def criar_analyzer_customizado():
    """
    Cria um AnalyzerEngine com configuração customizada.
    """
    # Configura o NLP engine
    configuration = {
        "nlp_engine_name": "spacy",
        "models": [{"lang_code": "pt", "model_name": "pt_core_news_lg"}],
    }
    
    # Cria o provider
    provider = NlpEngineProvider(nlp_configuration=configuration)
    nlp_engine = provider.create_engine()
    
    # Cria o analyzer
    analyzer = AnalyzerEngine(
        nlp_engine=nlp_engine,
        supported_languages=["pt"]
    )
    
    # Adiciona nosso recognizer customizado aprimorado
    analyzer.registry.add_recognizer(ContextualNameRecognizer())
    
    return analyzer


def deve_preservar_entidade(texto: str, inicio: int, fim: int) -> bool:
    """
    Verifica se uma entidade deve ser preservada - APRIMORADA.
    """
    entidade = texto[inicio:fim].lower()
    contexto = texto[max(0, inicio-50):min(len(texto), fim+50)].lower()
    
    # Preserva instituições
    for instituicao in INSTITUICOES_PRESERVAR:
        if instituicao in entidade or instituicao in contexto:
            return True
    
    # Preserva autoridades
    for autoridade in AUTORIDADES_PRESERVAR:
        if autoridade in contexto:
            return True
    
    # Preserva termos médicos
    for termo in TERMOS_MEDICOS:
        if termo in entidade:
            return True
    
    # Preserva se é muito curto (provavelmente não é nome)
    if len(entidade.strip()) < 3:
        return True
    
    # NOVO: Preserva palavras comuns que não são nomes
    palavras_preservar = {
        'fico', 'vou', 'sou', 'estou', 'tenho', 'posso', 'devo', 'quero',
        'preciso', 'gostaria', 'solicito', 'peço', 'aguardo', 'espero'
    }
    
    if entidade.strip() in palavras_preservar:
        return True
    
    return False


def anonimizar_relato(texto: str, analyzer: AnalyzerEngine, anonymizer: AnonymizerEngine) -> str:
    """
    Anonimiza um relato preservando contexto importante.
    """
    if pd.isna(texto) or not str(texto).strip():
        return texto
    
    texto_str = str(texto)
    
    # Analisa o texto para encontrar entidades
    resultados = analyzer.analyze(
        text=texto_str,
        language='pt',
        entities=["PERSON", "PERSON_CONTEXTUAL"]
    )
    
    # Filtra entidades que devem ser preservadas
    resultados_filtrados = []
    for resultado in resultados:
        if not deve_preservar_entidade(texto_str, resultado.start, resultado.end):
            resultados_filtrados.append(resultado)
    
    # Remove sobreposições (mantém o de maior score)
    resultados_filtrados = _remover_sobreposicoes(resultados_filtrados)
    
    # Anonimiza apenas as entidades filtradas
    if resultados_filtrados:
        resultado_anonimizado = anonymizer.anonymize(
            text=texto_str,
            analyzer_results=resultados_filtrados
        )
        return resultado_anonimizado.text
    
    return texto_str


def _remover_sobreposicoes(resultados: List[RecognizerResult]) -> List[RecognizerResult]:
    """Remove resultados sobrepostos, mantendo o de maior score."""
    if not resultados:
        return resultados
    
    # Ordena por posição
    resultados_ordenados = sorted(resultados, key=lambda x: x.start)
    
    filtrados = []
    for resultado in resultados_ordenados:
        # Verifica se sobrepõe com algum já adicionado
        sobrepoe = False
        for existente in filtrados:
            if (resultado.start < existente.end and resultado.end > existente.start):
                # Há sobreposição - mantém o de maior score
                if resultado.score > existente.score:
                    filtrados.remove(existente)
                else:
                    sobrepoe = True
                break
        
        if not sobrepoe:
            filtrados.append(resultado)
    
    return filtrados


def main():
    args = parser.parse_args()
    
    # Carrega o arquivo
    arquivo_entrada = Path('data/input/ouvidorias.csv')
    if not arquivo_entrada.exists():
        print(f"Erro: Arquivo {arquivo_entrada} não encontrado!")
        return
    
    print(f"Carregando dados de {arquivo_entrada}...")
    df = pd.read_csv(arquivo_entrada)
    
    # Verifica se a coluna existe
    if args.coluna not in df.columns:
        print(f"Erro: Coluna '{args.coluna}' não encontrada no arquivo!")
        print(f"Colunas disponíveis: {', '.join(df.columns)}")
        return
    
    print(f"Iniciando anonimização da coluna '{args.coluna}'...")
    print("Usando Presidio com heurísticas contextuais APRIMORADAS...")
    print("Melhorias baseadas em análise comparativa com LLM:")
    print("- Detecção de referências familiares (sogro, genitor, etc.)")
    print("- Padrões com idade (Nome, XX anos)")
    print("- Contextos de relato (relata que, informa que)")
    print("- Filtros aprimorados para termos médicos")
    print("- Remoção de sobreposições")
    
    # Configura o Presidio
    analyzer = criar_analyzer_customizado()
    anonymizer = AnonymizerEngine()
    
    # Aplica anonimização
    total = len(df)
    anonimizados = 0
    
    for idx, row in df.iterrows():
        texto_original = row[args.coluna]
        texto_anonimizado = anonimizar_relato(texto_original, analyzer, anonymizer)
        
        if texto_original != texto_anonimizado:
            anonimizados += 1
            if anonimizados <= 5:  # Mostra apenas os primeiros 5 exemplos
                print(f"\nExemplo {anonimizados}:")
                print(f"Original: {str(texto_original)[:200]}...")
                print(f"Anonimizado: {texto_anonimizado[:200]}...")
        
        df.at[idx, args.coluna] = texto_anonimizado
        
        if (idx + 1) % 50 == 0:
            print(f"Processados: {idx + 1}/{total} registros...")
    
    # Salva o resultado
    arquivo_saida = Path('data/output/ouvidorias_anonimizado_aprimorado.csv')
    arquivo_saida.parent.mkdir(parents=True, exist_ok=True)
    df.to_csv(arquivo_saida, index=False)
    
    # Estatísticas finais
    print(f"\n=== Resultado da Anonimização APRIMORADA ===")
    print(f"Total de registros: {total}")
    print(f"Registros com anonimização: {anonimizados}")
    print(f"Taxa de anonimização: {(anonimizados/total)*100:.1f}%")
    print(f"Arquivo salvo em: {arquivo_saida}")
    print("\nAbordagem híbrida aprimorada:")
    print("- Presidio para detecção base de nomes")
    print("- Heurísticas contextuais expandidas")
    print("- Filtros inteligentes para falsos positivos")
    print("- Preservação de instituições e autoridades")
    print("- Remoção de sobreposições")


if __name__ == "__main__":
    main()