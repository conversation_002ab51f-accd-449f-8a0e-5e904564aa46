#!/usr/bin/env python3
"""
Script para gerar relatórios específicos por subtema da educação.
Cria um relatório detalhado para cada subtema identificado.
"""

import pandas as pd
import os
import sys
from datetime import datetime
from collections import Counter
import re
from anthropic import Anthropic
from typing import Optional
import logging

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.config_utils import carregar_configuracao, carregar_variaveis_ambiente

def carregar_dados_classificacao(arquivo_csv: str) -> pd.DataFrame:
    """Carrega dados da classificação completa"""
    try:
        df = pd.read_csv(arquivo_csv, encoding='utf-8')
        print(f"✅ Dados carregados: {len(df)} registros")
        return df
    except Exception as e:
        print(f"❌ Erro ao carregar dados: {e}")
        return None

def extrair_subtemas_unicos(df: pd.DataFrame) -> list:
    """Extrai lista de subtemas únicos dos dados"""
    subtemas_set = set()
    
    for _, row in df.iterrows():
        subtemas_str = str(row.get('SUBTEMAS_IDENTIFICADOS', ''))
        if subtemas_str and subtemas_str != 'nan':
            subtemas = [s.strip() for s in subtemas_str.split(',') if s.strip()]
            for subtema in subtemas:
                subtemas_set.add(subtema)
    
    return sorted(list(subtemas_set))

def filtrar_casos_por_subtema(df: pd.DataFrame, subtema: str) -> pd.DataFrame:
    """Filtra casos que contêm um subtema específico"""
    casos_filtrados = []
    
    for idx, row in df.iterrows():
        subtemas_str = str(row.get('SUBTEMAS_IDENTIFICADOS', ''))
        if subtema in subtemas_str:
            casos_filtrados.append(row)
    
    return pd.DataFrame(casos_filtrados)

def analisar_coocorrencias(df_subtema: pd.DataFrame, subtema_principal: str) -> dict:
    """Analisa quais outros subtemas aparecem junto com o subtema principal"""
    coocorrencias = Counter()
    total_casos = len(df_subtema)
    
    for _, row in df_subtema.iterrows():
        subtemas_str = str(row.get('SUBTEMAS_IDENTIFICADOS', ''))
        subtemas = [s.strip() for s in subtemas_str.split(',') if s.strip()]
        
        for subtema in subtemas:
            if subtema != subtema_principal:
                coocorrencias[subtema] += 1
    
    # Converter para dict com percentuais
    resultado = {}
    for subtema, count in coocorrencias.items():
        resultado[subtema] = {
            'total': count,
            'percentual': (count / total_casos * 100) if total_casos > 0 else 0
        }
    
    return resultado

def identificar_tipo_unidade(noticiado: str) -> str:
    """Identifica tipo de unidade baseado no campo Noticiado"""
    noticiado_lower = noticiado.lower()
    
    if any(palavra in noticiado_lower for palavra in ['escola municipal', 'creche municipal', 'edi ', 'ciep', 'get ']):
        return 'Municipal'
    elif any(palavra in noticiado_lower for palavra in ['colégio estadual', 'escola estadual', 'faetec', 'seeduc']):
        return 'Estadual'
    elif any(palavra in noticiado_lower for palavra in ['escola particular', 'colégio particular', 'centro educacional', 'maple bear', 'creche escola']):
        return 'Privada'
    else:
        return 'Não identificado'

def analisar_tipos_unidade_subtema(df_subtema: pd.DataFrame) -> dict:
    """Analisa distribuição por tipo de unidade para um subtema"""
    tipos_counter = Counter()
    
    for _, row in df_subtema.iterrows():
        noticiado = str(row.get('Noticiado', ''))
        tipo = identificar_tipo_unidade(noticiado)
        tipos_counter[tipo] += 1
    
    total_casos = len(df_subtema)
    resultado = {}
    
    for tipo, count in tipos_counter.items():
        resultado[tipo] = {
            'total': count,
            'percentual': (count / total_casos * 100) if total_casos > 0 else 0
        }
    
    return resultado

def gerar_relatorio_subtema(df: pd.DataFrame, subtema: str, pasta_saida: str) -> bool:
    """Gera relatório específico para um subtema"""
    try:
        # Filtrar casos do subtema
        df_subtema = filtrar_casos_por_subtema(df, subtema)
        total_casos = len(df_subtema)
        
        if total_casos == 0:
            print(f"⚠️  Nenhum caso encontrado para o subtema: {subtema}")
            return False
        
        # Análises
        coocorrencias = analisar_coocorrencias(df_subtema, subtema)
        tipos_unidade = analisar_tipos_unidade_subtema(df_subtema)
        
        # Nome do arquivo
        nome_arquivo = re.sub(r'[^\w\s-]', '', subtema).strip()
        nome_arquivo = re.sub(r'[-\s]+', '_', nome_arquivo)
        arquivo_saida = os.path.join(pasta_saida, f"relatorio_{nome_arquivo.lower()}.md")
        
        # Gerar conteúdo
        conteudo = f"""# Relatório Específico: {subtema}

## Resumo Executivo

- **Subtema:** {subtema}
- **Total de casos:** {total_casos:,}
- **Percentual do total:** {(total_casos / len(df) * 100):.1f}%
- **Data de geração:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Distribuição por Tipo de Unidade

"""
        
        for tipo, stats in sorted(tipos_unidade.items(), key=lambda x: x[1]['total'], reverse=True):
            conteudo += f"### {tipo}\n"
            conteudo += f"- **Total:** {stats['total']:,} casos ({stats['percentual']:.1f}%)\n\n"
        
        # Coocorrências
        if coocorrencias:
            conteudo += "## Subtemas Relacionados (Coocorrências)\n\n"
            conteudo += "Outros subtemas que aparecem frequentemente junto com este:\n\n"
            
            for subtema_rel, stats in sorted(coocorrencias.items(), key=lambda x: x[1]['total'], reverse=True)[:10]:
                conteudo += f"- **{subtema_rel}**: {stats['total']:,} casos ({stats['percentual']:.1f}%)\n"
            
            conteudo += "\n"
        
        # Casos exemplares
        conteudo += "## Casos Exemplares\n\n"
        
        for i, (_, row) in enumerate(df_subtema.head(10).iterrows(), 1):
            numero_mprj = str(row.get('NUMERO_MPRJ', ''))
            orgao = str(row.get('ORGAO_RESPONSAVEL_DOC', ''))
            # Tentar usar RELATO primeiro, depois ASSUNTO_COMUNICACAO
            resumo = str(row.get('RELATO', ''))
            if not resumo or resumo == 'nan':
                resumo = str(row.get('ASSUNTO_COMUNICACAO', ''))
            resumo = resumo[:200] if resumo else ''
            
            conteudo += f"### Caso {i}: {numero_mprj}\n"
            conteudo += f"- **Órgão:** {orgao}\n"
            conteudo += f"- **Resumo:** {resumo}{'...' if len(resumo) > 200 else ''}\n\n"
        
        # Metodologia
        conteudo += """## Metodologia

- **Classificação:** LLM Llama 3.3 70B via Fireworks AI
- **Fonte:** Dados de ouvidorias de educação do MPRJ
- **Período:** Dados de 2024-2025
- **Critério de seleção:** Casos que contêm o subtema específico na classificação multilabel

## Observações

- Este relatório foca especificamente no subtema selecionado
- Casos podem aparecer em múltiplos subtemas (classificação multilabel)
- Análise baseada em compreensão semântica do texto completo
- Coocorrências indicam padrões de problemas relacionados

---
*Relatório gerado automaticamente pelo Simple Class - Análise por Subtema*
"""
        
        # Salvar arquivo
        with open(arquivo_saida, 'w', encoding='utf-8') as f:
            f.write(conteudo)
        
        print(f"✅ Relatório do subtema '{subtema}' salvo: {arquivo_saida}")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao gerar relatório do subtema '{subtema}': {e}")
        return False

def main():
    """Função principal"""
    if len(sys.argv) != 2:
        print("Uso: python scripts/gerar_relatorios_por_subtema.py <arquivo_csv>")
        sys.exit(1)
    
    arquivo_csv = sys.argv[1]
    
    if not os.path.exists(arquivo_csv):
        print(f"❌ Arquivo não encontrado: {arquivo_csv}")
        sys.exit(1)
    
    print("📊 Gerando Relatórios Específicos por Subtema - Educação")
    print(f"📄 Arquivo: {arquivo_csv}")
    print()
    
    # Carregar dados
    df = carregar_dados_classificacao(arquivo_csv)
    if df is None:
        sys.exit(1)
    
    # Extrair subtemas únicos
    subtemas = extrair_subtemas_unicos(df)
    print(f"📋 Subtemas identificados: {len(subtemas)}")
    
    # Criar pasta de saída
    pasta_saida = "data/output/relatorios_subtemas"
    if not os.path.exists(pasta_saida):
        os.makedirs(pasta_saida)
    
    # Gerar relatório para cada subtema
    sucessos = 0
    for subtema in subtemas:
        print(f"📝 Gerando relatório: {subtema}")
        if gerar_relatorio_subtema(df, subtema, pasta_saida):
            sucessos += 1
    
    print(f"\n🎉 Relatórios gerados com sucesso: {sucessos}/{len(subtemas)}")
    print(f"📁 Localização: {pasta_saida}/")

if __name__ == "__main__":
    main()
