# Scripts de Configuração e Deploy - Simple Class API

Este diretório contém scripts para configuração, inicialização e deploy da Simple Class API.

## 📁 Estrutura dos Scripts

```
scripts/
├── setup.py              # Configuração inicial do projeto
├── start_api_docs.py      # Inicialização da API com documentação
├── production_setup.py    # Configuração para ambiente de produção
├── deploy.py             # Deploy automatizado
├── health_check.sh       # Script de monitoramento
└── README.md             # Esta documentação
```

## 🔧 Scripts de Configuração

### `setup.py` - Configuração Inicial

Configura o ambiente de desenvolvimento completo.

```bash
# Configuração básica
python scripts/setup.py

# Pular testes durante configuração
python scripts/setup.py --skip-tests

# Pular instalação de dependências
python scripts/setup.py --skip-deps

# Configuração para produção
python scripts/setup.py --production
```

**O que faz:**
- ✅ Verifica versão do Python (3.11+)
- ✅ Verifica instalação do Poetry
- ✅ Instala dependências
- ✅ Cria arquivo `.env` se não existir
- ✅ Cria diretórios necessários
- ✅ Executa testes básicos
- ✅ Mostra informações de configuração

### `production_setup.py` - Configuração de Produção

Prepara o ambiente para deploy em produção.

```bash
python scripts/production_setup.py
```

**O que cria:**
- 🔐 `.env.production` com configurações seguras
- 🔧 `simple-class-api.service` (systemd)
- 🌐 `nginx-simple-class-api.conf` (nginx)
- 🐳 `Dockerfile` e `docker-compose.prod.yml`
- 📊 `health_check.sh` para monitoramento
- 📋 Checklist de produção

## 🚀 Scripts de Execução

### `start_api_docs.py` - Inicialização da API

Inicia a API com configurações otimizadas e documentação.

```bash
# Desenvolvimento com auto-reload
python scripts/start_api_docs.py --reload

# Produção com múltiplos workers
python scripts/start_api_docs.py --env production --workers 4

# Configurações customizadas
python scripts/start_api_docs.py --host 0.0.0.0 --port 8080 --log-level debug
```

**Opções disponíveis:**
- `--host`: Host para bind (padrão: 0.0.0.0)
- `--port`: Porta (padrão: 8000)
- `--reload`: Auto-reload para desenvolvimento
- `--log-level`: Nível de log (debug, info, warning, error)
- `--workers`: Número de workers (produção)
- `--env`: Ambiente (development, production)

### `deploy.py` - Deploy Automatizado

Automatiza o processo de deploy em diferentes ambientes.

```bash
# Deploy para desenvolvimento
python scripts/deploy.py --environment development

# Deploy para produção
python scripts/deploy.py --environment production

# Deploy pulando testes
python scripts/deploy.py --environment production --skip-tests

# Deploy forçado
python scripts/deploy.py --environment production --force
```

**Processo de deploy:**
1. ✅ Verifica pré-requisitos
2. ✅ Instala dependências
3. ✅ Executa testes
4. ✅ Constrói aplicação
5. ✅ Deploy (Docker ou systemd)
6. ✅ Verificações pós-deploy
7. ✅ Rollback automático se falhar

## 📊 Scripts de Monitoramento

### `health_check.sh` - Verificação de Saúde

Script para monitoramento contínuo da API.

```bash
# Executar verificação manual
./scripts/health_check.sh

# Adicionar ao crontab para monitoramento automático
*/5 * * * * /path/to/scripts/health_check.sh
```

**Funcionalidades:**
- 🏥 Verifica endpoint `/health`
- 📝 Log com timestamp
- 🚨 Exit codes para alertas
- 📊 Compatível com sistemas de monitoramento

## 🛠️ Makefile - Comandos Simplificados

Use o Makefile para comandos mais simples:

```bash
# Configuração inicial
make setup

# Iniciar API
make run

# Testes
make test

# Deploy
make deploy-prod

# Ver todos os comandos
make help
```

## 🔧 Configuração de Ambiente

### Desenvolvimento

```bash
# 1. Configuração inicial
python scripts/setup.py

# 2. Iniciar API
python scripts/start_api_docs.py --reload

# 3. Acessar documentação
# http://localhost:8000/docs
```

### Produção

```bash
# 1. Configurar produção
python scripts/production_setup.py

# 2. Configurar variáveis de ambiente
# Editar .env.production

# 3. Deploy
python scripts/deploy.py --environment production

# 4. Verificar status
curl http://localhost:8000/health
```

## 📋 Variáveis de Ambiente

### Desenvolvimento (`.env`)

```bash
# Mínimo necessário para desenvolvimento
API_SECRET_KEY=dev-secret-key
ENVIRONMENT=development
```

### Produção (`.env.production`)

```bash
# Configuração completa para produção
API_SECRET_KEY=secure-random-key
ENVIRONMENT=production
SUPABASE_URL=your-supabase-url
SUPABASE_KEY=your-supabase-key
TOGETHER_API_KEY=your-together-key
ANTHROPIC_API_KEY=your-anthropic-key
```

## 🐳 Deploy com Docker

### Desenvolvimento

```bash
# Construir imagem
docker build -t simple-class-api .

# Executar container
docker run -p 8000:8000 --env-file .env simple-class-api
```

### Produção

```bash
# Usar Docker Compose
docker-compose -f docker-compose.prod.yml up -d

# Verificar logs
docker-compose -f docker-compose.prod.yml logs -f

# Parar containers
docker-compose -f docker-compose.prod.yml down
```

## 🔍 Troubleshooting

### Problemas Comuns

**1. Erro de permissão nos scripts**
```bash
chmod +x scripts/*.py scripts/*.sh
```

**2. Poetry não encontrado**
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

**3. Python 3.11+ não encontrado**
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install python3.11

# macOS
brew install python@3.11
```

**4. Dependências faltando**
```bash
poetry install --no-dev  # Produção
poetry install           # Desenvolvimento
```

### Logs e Debugging

```bash
# Logs da aplicação
tail -f logs/app.log

# Logs do systemd (produção)
sudo journalctl -u simple-class-api -f

# Logs do Docker
docker-compose -f docker-compose.prod.yml logs -f api

# Debug da configuração
python -c "from src.api.config import settings; print(settings.dict())"
```

## 📈 Monitoramento

### Health Checks

```bash
# Verificação básica
curl http://localhost:8000/health

# Verificação com detalhes
curl http://localhost:8000/health?detailed=true

# Verificação de autenticação
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "test123"}'
```

### Métricas

- **Tempo de resposta**: Monitorado automaticamente
- **Rate limiting**: Configurável via variáveis de ambiente
- **Uso de recursos**: Logs de performance
- **Erros**: Logs estruturados com níveis

## 🔐 Segurança

### Checklist de Segurança

- ✅ Chaves secretas seguras (`API_SECRET_KEY`)
- ✅ HTTPS em produção (`HTTPS_ONLY=true`)
- ✅ Rate limiting configurado
- ✅ CORS restritivo
- ✅ Logs de segurança
- ✅ Validação de entrada
- ✅ Tokens JWT com expiração

### Configurações Recomendadas

```bash
# Produção
API_SECRET_KEY=<32-char-random-string>
ACCESS_TOKEN_EXPIRE_MINUTES=30
HTTPS_ONLY=true
SECURE_COOKIES=true
RATE_LIMIT_REQUESTS=50
```

## 📞 Suporte

Para problemas com os scripts:

1. **Verificar logs**: Sempre verificar os logs primeiro
2. **Executar com debug**: Usar `--log-level debug`
3. **Verificar pré-requisitos**: Python 3.11+, Poetry, Git
4. **Consultar documentação**: README principal do projeto
5. **Abrir issue**: GitHub Issues para bugs

---

**Os scripts estão prontos para uso em desenvolvimento e produção!** 🚀
