#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script principal para processamento de relatos usando o método individual.
"""

import argparse
import os
import sys
import logging

# Garantir que o diretório raiz do projeto esteja no PYTHONPATH
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importações absolutas
from src.analisadores.analisador_relatos import main as analisar_main

# Configuração do logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Processa um arquivo CSV para classificar relatos com LLM.")
    parser.add_argument(
        "input_csv",
        type=str,
        help="Caminho para o arquivo CSV de entrada (ex: data/input/dados.csv)"
    )
    parser.add_argument(
        "output_csv",
        type=str,
        help="Caminho para salvar o arquivo CSV processado (ex: data/output/resultado.csv)"
    )

    args = parser.parse_args()

    # Validação simples dos caminhos
    if not os.path.exists(args.input_csv):
        logging.error(f"Arquivo de entrada especificado não encontrado: {args.input_csv}")
        print(f"Erro: Arquivo de entrada não encontrado em '{args.input_csv}'")
    elif not args.output_csv.endswith('.csv'):
        logging.warning(f"O nome do arquivo de saída '{args.output_csv}' não termina com .csv. Será salvo como está.")
        analisar_main(args.input_csv, args.output_csv)
    else:
        analisar_main(args.input_csv, args.output_csv) 