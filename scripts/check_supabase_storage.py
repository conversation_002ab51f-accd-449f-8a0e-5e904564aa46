#!/usr/bin/env python3
"""
<PERSON>ript to check Supabase storage configuration directly in the database.
"""

import os
import sys
import psycopg2
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv

def check_storage_in_db():
    """Check storage configuration directly in database."""
    print("🔍 Checking storage configuration in database...")
    
    # Load environment variables
    load_dotenv()
    
    # Get database credentials
    host = os.getenv("SUPABASE_DB_HOST")
    port = os.getenv("SUPABASE_DB_PORT", "5432")
    database = os.getenv("SUPABASE_DB_NAME")
    user = os.getenv("SUPABASE_DB_USER")
    password = os.getenv("SUPABASE_DB_PASSWORD")
    
    try:
        # Create connection
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=user,
            password=password,
            sslmode='require'
        )
        
        cursor = conn.cursor()
        
        # Check if storage schema exists
        cursor.execute("""
            SELECT schema_name 
            FROM information_schema.schemata 
            WHERE schema_name = 'storage';
        """)
        
        storage_schema = cursor.fetchone()
        if storage_schema:
            print("✅ Storage schema exists")
        else:
            print("❌ Storage schema not found")
            return False
        
        # Check storage tables
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'storage'
            ORDER BY table_name;
        """)
        
        storage_tables = cursor.fetchall()
        print(f"📋 Storage tables: {[table[0] for table in storage_tables]}")
        
        # Check if buckets table exists and has our bucket
        if ('buckets',) in storage_tables:
            cursor.execute("""
                SELECT id, name, public, file_size_limit, allowed_mime_types
                FROM storage.buckets;
            """)
            
            buckets = cursor.fetchall()
            print(f"📦 Buckets in database: {len(buckets)}")
            
            for bucket in buckets:
                print(f"   - ID: {bucket[0]}, Name: {bucket[1]}, Public: {bucket[2]}")
        
        # Check storage policies
        cursor.execute("""
            SELECT schemaname, tablename, policyname, cmd, qual
            FROM pg_policies 
            WHERE schemaname = 'storage'
            ORDER BY tablename, policyname;
        """)
        
        storage_policies = cursor.fetchall()
        print(f"🔐 Storage policies: {len(storage_policies)}")
        
        for policy in storage_policies:
            print(f"   - {policy[1]}.{policy[2]} ({policy[3]})")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False

def main():
    """Main function."""
    print("🔍 Supabase Storage Check")
    print("=" * 40)
    
    success = check_storage_in_db()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ Storage check completed")
    else:
        print("❌ Storage check failed")

if __name__ == "__main__":
    main()
