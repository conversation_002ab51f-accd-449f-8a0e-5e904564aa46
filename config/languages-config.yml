# Language configuration for anonymization
# Based on official Inova-MPRJ/Anonymizer configuration

nlp_engine_name: spacy
models:
  - lang_code: pt
    model_name: pt_core_news_lg
  - lang_code: en  
    model_name: en_core_web_lg

# Portuguese language configuration
pt:
  # spaCy model for Portuguese
  model_name: pt_core_news_lg
  
  # Supported entities for Portuguese
  entities:
    - PERSON
    - EMAIL_ADDRESS
    - PHONE_NUMBER
    - CPF
    - ESCOLA
    - ENDEREÇO
  
  # Context words for better recognition
  context:
    person:
      - nome
      - pessoa
      - chamado
      - filho
      - filha
      - neto
      - neta
      - pai
      - mãe
      - genitor
      - genitora
      - sogro
      - sogra
      - avô
      - avó
      - senhor
      - senhora
      - vítima
      - paciente
      - comunicante
      - denunciante
      - noticiante
      - informante
      - demandante
    
    email:
      - email
      - correio eletrônico
      - endereço de email
      - e-mail
      - contato
    
    phone:
      - telefone
      - celular
      - número de telefone
      - contato
      - fone
      - tel
    
    cpf:
      - cpf
      - cadastro
      - pessoa física
      - documento
      - identificação
    
    escola:
      - escola
      - colégio
      - universidade
      - faculdade
      - instituto
      - centro educacional
      - campus
      - educação
      - ensino
      - estudante
      - aluno
      - professor
      - matrícula
      - municipal
      - estadual
      - federal
      - particular
      - educacional
      - pedagógico
      - edi
      - ciep
      - faetec
      - seeduc
      - cre
      - coordenadoria
    
    endereco:
      - endereço
      - localizado
      - situada
      - rua
      - avenida
      - bairro
      - cidade
      - cep
      - número
      - apartamento
      - sala
      - bloco
      - condomínio
      - residencial
      - fazenda
      - sítio
      - rodovia
      - estrada
      - praça
      - largo
      - alameda
      - travessa
      - km
      - quilômetro
      - lote
      - quadra
      - localizada

# English language configuration (fallback)
en:
  model_name: en_core_web_lg
  entities:
    - PERSON
    - EMAIL_ADDRESS
    - PHONE_NUMBER
  
  context:
    person:
      - name
      - person
      - called
      - son
      - daughter
      - father
      - mother
      - victim
      - patient
    email:
      - email
      - electronic mail
      - email address
      - contact
    phone:
      - phone
      - telephone
      - phone number
      - contact
