{"input_data_schema": {"type": "object", "description": "Schema para dados de entrada do sistema", "properties": {"file_path": {"type": "string", "description": "Caminho para o arquivo CSV de entrada"}, "column_name": {"type": "string", "description": "Nome da coluna que contém o texto a ser classificado"}, "encoding": {"type": "string", "enum": ["utf-8", "latin-1", "cp1252"], "description": "Encoding do arquivo CSV"}}, "required": ["file_path", "column_name"]}, "classification_result_schema": {"type": "object", "description": "Schema para resultados de classificação", "properties": {"text": {"type": "string", "description": "Texto original classificado"}, "classification": {"type": "integer", "enum": [0, 1], "description": "Resultado da classificação (0=negativo, 1=positivo)"}, "confidence": {"type": "number", "minimum": 0, "maximum": 1, "description": "Confiança da classificação"}, "model_used": {"type": "string", "description": "Modelo usado para classificação"}, "timestamp": {"type": "string", "format": "date-time", "description": "Timestamp da classificação"}}, "required": ["text", "classification", "model_used"]}, "multilabel_result_schema": {"type": "object", "description": "Schema para resultados de classificação multilabel", "properties": {"text": {"type": "string", "description": "Texto original classificado"}, "area": {"type": "string", "enum": ["SAUDE", "EDUCACAO", "MEIO_AMBIENTE"], "description": "Área de classificação"}, "subtemas": {"type": "array", "items": {"type": "string"}, "maxItems": 3, "description": "Lista de subtemas identificados"}, "confidence_scores": {"type": "object", "description": "Scores de confiança por subtema"}, "model_used": {"type": "string", "description": "Modelo usado para classificação"}, "timestamp": {"type": "string", "format": "date-time", "description": "Timestamp da classificação"}}, "required": ["text", "area", "subtemas", "model_used"]}, "report_schema": {"type": "object", "description": "Schema para relatórios gerados", "properties": {"report_type": {"type": "string", "enum": ["tactical", "overview", "multilabel", "comparison"], "description": "Tipo de relatório"}, "subject": {"type": "string", "description": "<PERSON><PERSON><PERSON> analisado"}, "total_cases": {"type": "integer", "minimum": 0, "description": "Total de casos analisados"}, "positive_cases": {"type": "integer", "minimum": 0, "description": "Casos positivos encontrados"}, "content": {"type": "string", "description": "Conteúdo do relatório em markdown"}, "model_used": {"type": "string", "description": "Modelo usado para gerar o relatório"}, "generated_at": {"type": "string", "format": "date-time", "description": "Timestamp de geração"}, "metadata": {"type": "object", "description": "Metadados adicionais do relatório"}}, "required": ["report_type", "content", "model_used", "generated_at"]}, "workflow_state_schema": {"type": "object", "description": "Schema para estado de workflows", "properties": {"workflow_id": {"type": "string", "description": "ID único do workflow"}, "current_step": {"type": "string", "description": "Passo atual do workflow"}, "status": {"type": "string", "enum": ["running", "completed", "failed", "paused"], "description": "Status do workflow"}, "input_data": {"type": "object", "description": "Dados de entrada do workflow"}, "intermediate_results": {"type": "object", "description": "Resultados intermediários"}, "final_result": {"type": "object", "description": "Resultado final do workflow"}, "execution_log": {"type": "array", "items": {"type": "string"}, "description": "Log de execução"}, "errors": {"type": "array", "items": {"type": "string"}, "description": "Lista de erros"}, "started_at": {"type": "string", "format": "date-time", "description": "Timestamp de início"}, "completed_at": {"type": "string", "format": "date-time", "description": "Timestamp de conclusão"}}, "required": ["workflow_id", "current_step", "status", "started_at"]}, "api_response_schema": {"type": "object", "description": "Schema padrão para respostas da API", "properties": {"success": {"type": "boolean", "description": "Indica se a operação foi bem-sucedida"}, "data": {"type": "object", "description": "<PERSON><PERSON> da resposta"}, "message": {"type": "string", "description": "Mensagem descritiva"}, "error": {"type": "object", "properties": {"code": {"type": "string", "description": "Código do erro"}, "message": {"type": "string", "description": "Mensagem de erro"}, "details": {"type": "object", "description": "Detalhes adicionais do erro"}}, "description": "Informações de erro (se aplicável)"}, "timestamp": {"type": "string", "format": "date-time", "description": "Timestamp da resposta"}}, "required": ["success", "timestamp"]}}