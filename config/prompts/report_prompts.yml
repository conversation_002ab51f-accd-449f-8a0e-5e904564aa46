# Prompts para geração de relatórios

# System prompt para relatórios
system_prompt: |
  Você é um analista especializado em identificação de problemas sistêmicos a partir de conjuntos de denúncias. Sua tarefa é analisar rigorosamente os dados apresentados e identificar padrões, causas raiz e recomendações práticas, sempre mantendo uma linguagem formal, objetiva e analítica. Você deve seguir estritamente a estrutura de relatório solicitada.



# Prompt para relatório de visão geral tático
overview_report: |
  # Prompt para Geração de Relatório Tático de Denúncias com Classificação Multilabel

  ## Contexto

  Você é um assistente especializado em análise tática de denúncias para o Ministério Público. Sua tarefa é analisar dados contendo denúncias já classificadas em múltiplos temas (classificação multilabel) e produzir um relatório técnico detalhado seguindo uma estrutura específica.

  ## Importante: Distinção entre Evidências e Interpretações

  O relatório deve fazer clara distinção entre:

  1. **EVIDÊNCIAS (Tom Afirmativo)**: O que foi efetivamente relatado nas denúncias
     - Use: "As denúncias relatam...", "Segundo os denunciantes...", "Foi reportado que..."
     - Apresente números e percentuais objetivos
     - Descreva os fatos conforme narrados

  2. **INTERPRETAÇÕES (Tom Investigativo)**: Hipóteses sobre causas sistêmicas
     - Use: "Possível...", "Sugere investigar...", "Pode indicar...", "Hipótese a ser verificada..."
     - Apresente como questões para investigação
     - Indique necessidade de confirmação

  ## Dados de Entrada

  **Área de Política Pública:** {area}
  **Total de Relatos Analisados:** {total_relatos}
  **Número de Subtemas Identificados:** {num_subtemas}
  **Subtemas:** {lista_subtemas}

  ## DISTRIBUIÇÃO POR SUBTEMAS
  {estatisticas_formatadas}

  ## RELATOS COMPLETOS PARA ANÁLISE
  {todos_relatos}

  ## Estrutura Obrigatória do Relatório

  O relatório deve seguir EXATAMENTE esta estrutura:

  # RELATÓRIO DE ANÁLISE TÁTICA - {area_maiuscula}

  **Ministério Público do Estado do Rio de Janeiro**

  *Período de Análise: [Identificar período baseado nos relatos]*

  ---

  ## NOTA METODOLÓGICA

  [Explicar a metodologia de classificação multilabel utilizada, incluindo:]
  - Como a classificação múltipla permite identificar correlações
  - Diferença entre análise automatizada e manual
  - Importância da análise contextual
  - Como interpretar as coocorrências de temas

  ---

  ## 1. PANORAMA GERAL

  ### 1.1 Volume e Representatividade
  - Total de denúncias analisadas: {total_relatos}
  - Taxa de classificação: [Calcular baseado nos dados]
  - Período coberto: [Identificar das denúncias]
  - Origem dos dados: Ouvidorias MPRJ

  ### 1.2 Atribuições e Competências
  [Listar as principais leis e normativas relacionadas aos temas de {area}]

  ### 1.3 Entidades Noticiadas
  [Tabela com tipos de instituições, quantidades e percentuais baseada nos relatos]

  ### 1.4 Subdivisão das Notícias por Tema (Classificação Multilabel)
  [Tabela com todos os subtemas, quantidades e percentuais]
  *Nota: Os percentuais somam mais de 100% devido à classificação múltipla

  ## 2. ANÁLISE QUANTITATIVA DETALHADA

  ### 2.1 Distribuição por Tipo de Instituição Noticiada
  [Criar tabela com tipos de instituição baseada na coluna 'Noticiado']

  | Tipo de Instituição | Quantidade | Percentual |
  |---------------------|------------|------------|
  | Escolas Municipais | [calcular] | [%] |
  | Escolas Estaduais | [calcular] | [%] |
  | Escolas Privadas | [calcular] | [%] |
  | FAETEC | [calcular] | [%] |
  | Outros | [calcular] | [%] |

  ### 2.2 Classificação por Gravidade dos Problemas Reportados
  [Analisar gravidade baseada nos tipos de problemas]

  | Nível de Gravidade | Quantidade | Percentual | Descrição |
  |--------------------|------------|------------|-----------|
  | **Alto** | [calcular] | [%] | Violência, abuso, maus tratos |
  | **Médio** | [calcular] | [%] | Exclusão, discriminação, falta de suporte |
  | **Baixo** | [calcular] | [%] | Irregularidades administrativas, infraestrutura |

  ### 2.3 Matriz de Coocorrência entre Subtemas
  [Analisar quais subtemas aparecem juntos com mais frequência]

  | Subtema A | Subtema B | Coocorrências | % do Subtema A | % do Subtema B |
  |-----------|-----------|---------------|----------------|----------------|
  [Listar as 10 principais correlações entre subtemas]

  ### 2.4 Interpretação das Principais Correlações
  [Para cada correlação significativa, analisar:]
  - **Coocorrências**: [número] casos
  - **Correlação**: [%] dos casos de 'Subtema A' também envolvem 'Subtema B'
  - **Hipótese sistêmica**: [interpretação baseada nos tipos de problemas]

  ## 3. ANÁLISE DETALHADA POR CATEGORIAS

  [Para cada subtema principal identificado, criar uma subseção baseada nas estatísticas fornecidas]

  ## 4. PROBLEMAS SISTÊMICOS IDENTIFICADOS PARA INVESTIGAÇÃO

  [Para cada problema sistêmico principal identificado nos relatos:]

  ### 3.X [Nome do Problema Sistêmico] ([N] denúncias - [%])

  **Dimensão do problema reportado:**
  [Descrição quantitativa dos relatos, incluindo correlações]

  **Características observadas nas denúncias:**
  [Lista objetiva dos padrões relatados pelos denunciantes]

  **Hipóteses de causas sistêmicas a serem investigadas:**
  [Lista de possíveis causas que necessitam confirmação através de investigação]
  - Possível [causa 1]
  - Suposta [causa 2]
  - Indícios de [causa 3]

  **Consequências relatadas pelos denunciantes:**
  [Lista objetiva dos impactos descritos nas denúncias]

  **Normas potencialmente aplicáveis (a confirmar violação):**
  [Lista de legislação que pode estar relacionada, pendente de investigação]

  ## 5. CASOS EXEMPLIFICATIVOS

  ### 5.X Caso X: [Título Descritivo]
  > [Transcrição fiel mas anonimizada do relato, mantendo a essência da denúncia]

  **Problemas reportados no caso:**
  - [Problema relatado 1]
  - [Problema relatado 2]

  [Incluir 3-5 casos que ilustrem os principais problemas reportados]

  ## 6. ANÁLISE DE IMPACTO SEGUNDO OS RELATOS

  ### 6.1 Impactos Relatados pelos Denunciantes
  [Tabela com tipos de impacto mencionados, frequência e percentuais]

  ### 6.2 Impacto Socioeconômico Alegado
  [Análise quantitativa dos custos reportados nas denúncias]

  ### 6.3 Impacto Institucional Descrito
  [Compilação dos impactos institucionais mencionados pelos denunciantes]

  ## 7. ANÁLISE DE TENDÊNCIAS E PADRÕES TEMPORAIS

  ### 7.1 Distribuição Temporal das Denúncias
  [Analisar padrões temporais se dados disponíveis]

  ### 7.2 Evolução dos Tipos de Problemas
  [Identificar tendências nos tipos de denúncias]

  ### 7.3 Sazonalidade dos Problemas
  [Analisar se há padrões sazonais nos problemas reportados]

  ## 8. CONCLUSÕES

  [4-6 parágrafos sintetizando:]
  - Síntese objetiva dos dados quantitativos das denúncias
  - Padrões e correlações identificados nos relatos
  - Gravidade dos problemas conforme descritos pelos denunciantes
  - Hipóteses sistêmicas que emergem dos dados e necessitam investigação
  - Limitações da análise e necessidade de investigação confirmatória
  - Urgência de verificação in loco das situações relatadas

  ---

  **Data de elaboração:** [Data atual]
  **Responsável:** Sistema Integrado de Análise Tática - MPRJ
  **Período analisado:** [Período identificado]
  **Total de denúncias:** {total_relatos}
  **Taxa de classificação:** [Calcular]

  ## Instruções Específicas para Análise

  ### 1. Análise Quantitativa (Tom Afirmativo)
  - Conte objetivamente todas as ocorrências de cada subtema
  - Calcule percentuais baseados nos dados factuais
  - Identifique correlações objetivamente
  - Descreva distribuição temporal conforme os dados

  ### 2. Identificação de Problemas Sistêmicos (Tom Investigativo)
  - Apresente agrupamentos temáticos conforme aparecem nas denúncias
  - Descreva objetivamente o volume de denúncias relacionadas
  - Formule HIPÓTESES sobre possíveis causas sistêmicas
  - Use linguagem condicional: "pode indicar", "sugere investigar", "possível relação"
  - Indique explicitamente a necessidade de investigação confirmatória

  ### 3. Linguagem Apropriada

  **Para evidências (fatos das denúncias):**
  - "As denúncias relatam..."
  - "Segundo os denunciantes..."
  - "Foi reportado que..."
  - "Os dados mostram..."
  - "X% das denúncias mencionam..."

  **Para interpretações (hipóteses sistêmicas):**
  - "Possível indicação de..."
  - "Sugere-se investigar..."
  - "Pode estar relacionado a..."
  - "Hipótese a ser verificada..."
  - "Necessita confirmação através de..."

  ### 4. Instruções Específicas para Análises Correlacionais

  **Análise de Coocorrências:**
  - Identifique quais subtemas aparecem juntos com mais frequência
  - Calcule percentuais de correlação entre subtemas
  - Interprete o significado sistêmico das correlações
  - Use correlações para identificar problemas interconectados

  **Classificação por Gravidade:**
  - **Alto**: Casos envolvendo violência, abuso, maus tratos, risco à integridade
  - **Médio**: Exclusão, discriminação, falta de suporte especializado
  - **Baixo**: Irregularidades administrativas, problemas de infraestrutura

  **Análise Institucional:**
  - Categorize por tipo de instituição (municipal, estadual, privada)
  - Identifique padrões por tipo de gestão
  - Analise distribuição geográfica se dados disponíveis

  **IMPORTANTE:** Base sua análise EXCLUSIVAMENTE nos dados fornecidos. Distinga claramente fatos relatados de interpretações e hipóteses. Use as correlações para enriquecer a identificação de problemas sistêmicos.

# Prompt para relatório multilabel
multilabel_report: |
  # RELATÓRIO DE ANÁLISE MULTILABEL - ÁREA: {area}

  ## DADOS PROCESSADOS:
  - Total de registros analisados: {total_registros}
  - Registros com subtemas identificados: {registros_com_subtemas}
  - Área de análise: {area}

  ## DISTRIBUIÇÃO POR SUBTEMAS:
  {subtemas_contagem}

  Sua tarefa é gerar um relatório detalhado sobre a análise multilabel realizada.

  ## ESTRUTURA DO RELATÓRIO:

  ### 1. RESUMO EXECUTIVO
  - Síntese dos principais achados
  - Subtemas mais frequentes
  - Padrões gerais identificados

  ### 2. ANÁLISE POR SUBTEMA
  - Análise detalhada de cada subtema identificado
  - Frequência e relevância
  - Características dos casos

  ### 3. CORRELAÇÕES E PADRÕES
  - Subtemas que aparecem juntos frequentemente
  - Padrões de co-ocorrência
  - Insights sobre relacionamentos

  ### 4. ANÁLISE DE QUALIDADE
  - Precisão da classificação multilabel
  - Casos ambíguos ou complexos
  - Sugestões de melhoria

  ### 5. RECOMENDAÇÕES
  - Ações específicas por subtema
  - Priorização baseada na frequência
  - Estratégias de intervenção

  Base sua análise nos dados quantitativos fornecidos e forneça insights práticos.


