# Prompts para classificação de textos

# Prompt para classificação individual
individual_classification: |
  Você é um classificador de textos especializado em análise de documentos. Sua tarefa é determinar se um relato específico se refere ao assunto "{subject}".

  INSTRUÇÃO:
  Analise cuidadosamente o relato abaixo e responda APENAS "SIM" ou "NAO" se o relato se refere especificamente ao assunto "{subject}".

  RELATO:
  {text}

  ASSUNTO: {subject}

  RESPOSTA:

# Prompt para classificação multilabel
multilabel_classification: |
  Você é um classificador especializado em análise de relatos da área de {area}.

  TAREFA:
  Analise o relato abaixo e identifique quais subtemas de {area} estão presentes. Você pode selecionar até 3 subtemas mais relevantes.

  SUBTEMAS DISPONÍVEIS COM DEFINIÇÕES:
  {subtemas_with_definitions}

  RELATO:
  {text}

  CRITÉRIOS DE CLASSIFICAÇÃO:
  - Leia cada palavra-chave e contexto do relato
  - Compare com as definições específicas de cada subtema
  - "Regulação em Saúde" deve ser usado para: filas de espera, SISREG, SER, demoras no agendamento, transferências, problemas de regulação
  - "Diagnose" deve ser usado para: exames, laudos, resultados, procedimentos diagnósticos
  - "Auditoria" deve ser usado para: irregularidades administrativas, má gestão, problemas de controle
  - Selecione apenas subtemas que realmente se aplicam ao conteúdo

  INSTRUÇÕES:
  1. Leia cuidadosamente o relato
  2. Identifique palavras-chave e contexto
  3. Compare com as definições específicas dos subtemas
  4. Selecione até 3 subtemas mais relevantes
  5. Responda APENAS com os números dos subtemas separados por vírgula
  6. Se nenhum subtema for relevante, responda "NENHUM"

  EXEMPLOS DE RESPOSTA:
  - "1,6,9" (para subtemas 1, 6 e 9)
  - "5" (para apenas o subtema 5)
  - "NENHUM" (se não houver subtemas relevantes)

  RESPOSTA:

# Prompt para comparação de métodos
method_comparison: |
  Você é um analista especializado em avaliação de métodos de classificação de texto.

  TAREFA:
  Compare os resultados de diferentes métodos de classificação para o mesmo conjunto de textos e forneça uma análise detalhada.

  MÉTODOS COMPARADOS:
  {methods}

  RESULTADOS:
  {results}

  CRITÉRIOS DE ANÁLISE:
  1. Precisão: Qual método teve maior taxa de acerto?
  2. Consistência: Qual método teve resultados mais estáveis?
  3. Velocidade: Qual método foi mais rápido?
  4. Casos específicos: Em que tipos de texto cada método se destacou?

  Forneça uma análise detalhada e recomendações sobre qual método usar em diferentes cenários.

# Prompt para validação de classificação
validation_prompt: |
  Você é um validador de classificações de texto.

  TAREFA:
  Analise se a classificação atribuída ao texto está correta.

  TEXTO:
  {text}

  CLASSIFICAÇÃO ATRIBUÍDA: {classification}
  ASSUNTO: {subject}

  CRITÉRIOS:
  - A classificação está correta?
  - Se não, qual seria a classificação correta?
  - Justifique sua resposta

  RESPOSTA:
