#!/usr/bin/env python3
"""
Script para testar o prompt melhorado do classificador multilabel.
Demonstra como as definições específicas melhoram a precisão da classificação.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.analisadores.analisador_multilabel import (
    classificar_relato_multilabel, 
    carregar_configuracao_yaml, 
    obter_subtemas_por_area, 
    carregar_variaveis_ambiente
)

def testar_classificador():
    """Testa o classificador com diferentes tipos de relatos."""
    
    # Carregar configurações
    config = carregar_configuracao_yaml()
    subtemas = obter_subtemas_por_area(config, 'SAUDE')
    env_vars = carregar_variaveis_ambiente()
    api_key = env_vars.get('TOGETHER_API_KEY')
    
    # Casos de teste com diferentes subtemas
    casos_teste = [
        {
            "relato": "Paciente aguardando há 8 meses por cirurgia de catarata. SISREG não tem vagas disponíveis no sistema.",
            "esperado": ["Regulação em Saúde", "Oftalmologia"]
        },
        {
            "relato": "Demora na entrega de resultado de exame de tomografia. Já faz 3 semanas que fiz o exame.",
            "esperado": ["Diagnose (laboratório e imagem)"]
        },
        {
            "relato": "Paciente com câncer de mama aguardando início da quimioterapia há 2 meses.",
            "esperado": ["Oncologia", "Regulação em Saúde"]
        },
        {
            "relato": "Irregularidades no pagamento de funcionários do hospital. Salários atrasados há 3 meses.",
            "esperado": ["Auditoria", "Planejamento, Financiamento e Gestão Financeira dos recursos do SUS"]
        },
        {
            "relato": "Paciente precisa de fisioterapia após AVC mas não consegue vaga no centro de reabilitação.",
            "esperado": ["Reabilitação", "Regulação em Saúde"]
        },
        {
            "relato": "Falta de aparelhos auditivos para distribuição gratuita pelo SUS.",
            "esperado": ["Saúde Auditiva"]
        }
    ]
    
    print("🧪 TESTE DO CLASSIFICADOR MULTILABEL COM PROMPT MELHORADO")
    print("=" * 70)
    
    for i, caso in enumerate(casos_teste, 1):
        print(f"\n📋 CASO {i}:")
        print(f"Relato: {caso['relato']}")
        print(f"Esperado: {caso['esperado']}")
        
        # Classificar
        resultado = classificar_relato_multilabel(
            caso['relato'], 
            subtemas, 
            'SAUDE', 
            'meta-llama/Llama-3.2-3B-Instruct-Turbo', 
            api_key
        )
        
        print(f"Resultado: {resultado}")
        
        # Verificar acertos
        acertos = set(resultado) & set(caso['esperado'])
        if acertos:
            print(f"✅ Acertos: {list(acertos)}")
        
        erros = set(resultado) - set(caso['esperado'])
        if erros:
            print(f"❌ Classificações extras: {list(erros)}")
            
        perdidos = set(caso['esperado']) - set(resultado)
        if perdidos:
            print(f"⚠️  Não identificados: {list(perdidos)}")
        
        print("-" * 50)

if __name__ == "__main__":
    testar_classificador() 