#!/usr/bin/env python3
"""
Exemplo de workflow LangGraph usando o PDFAnonymizationTool.

Este script demonstra como integrar o tool de anonimização de PDF
em um workflow completo de processamento de documentos.
"""

import sys
from pathlib import Path
from typing import Dict, Any
import json

# Adicionar src ao path para imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.tools.pdf_anonymization_tool import create_pdf_anonymization_tool


class DocumentProcessingState:
    """Estado do workflow de processamento de documentos."""
    
    def __init__(self):
        self.pdf_path: str = ""
        self.original_text: str = ""
        self.anonymized_text: str = ""
        self.extraction_metadata: Dict[str, Any] = {}
        self.anonymization_metadata: Dict[str, Any] = {}
        self.processing_status: str = "pending"
        self.errors: list = []


def pdf_anonymization_node(state: DocumentProcessingState) -> DocumentProcessingState:
    """
    Node LangGraph para anonimização de PDF.
    
    Args:
        state: Estado atual do workflow
        
    Returns:
        Estado atualizado com resultado da anonimização
    """
    print(f"🔄 Processando PDF: {state.pdf_path}")
    
    # Criar tool de anonimização
    pdf_tool = create_pdf_anonymization_tool()
    
    try:
        # Executar anonimização
        result = pdf_tool(state.pdf_path, language="pt")
        
        if result["success"]:
            # Atualizar estado com sucesso
            state.original_text = result["original_text"]
            state.anonymized_text = result["anonymized_text"]
            state.extraction_metadata = result["extraction_metadata"]
            state.anonymization_metadata = result["anonymization_metadata"]
            state.processing_status = "completed"
            
            print(f"✅ Anonimização concluída!")
            print(f"📄 Páginas processadas: {state.extraction_metadata['pages']}")
            print(f"🔒 Entidades anonimizadas: {state.anonymization_metadata['entities_count']}")
            
        else:
            # Tratar erro
            state.processing_status = "failed"
            state.errors.append(f"Erro na anonimização: {result['error']}")
            print(f"❌ Erro na anonimização: {result['error']}")
            
    except Exception as e:
        state.processing_status = "failed"
        state.errors.append(f"Exceção durante anonimização: {str(e)}")
        print(f"❌ Exceção: {str(e)}")
    
    return state


def text_analysis_node(state: DocumentProcessingState) -> DocumentProcessingState:
    """
    Node para análise do texto anonimizado.
    
    Args:
        state: Estado atual do workflow
        
    Returns:
        Estado atualizado com análise do texto
    """
    if state.processing_status != "completed":
        print("⏭️ Pulando análise - anonimização não foi bem-sucedida")
        return state
    
    print("🔍 Analisando texto anonimizado...")
    
    # Análise simples do texto
    text = state.anonymized_text
    
    analysis = {
        "character_count": len(text),
        "word_count": len(text.split()),
        "line_count": len(text.split('\n')),
        "entity_types": {},
        "readiness_for_llm": True
    }
    
    # Contar tipos de entidades
    for entity in state.anonymization_metadata.get("entities_found", []):
        entity_type = entity["entity_type"]
        analysis["entity_types"][entity_type] = analysis["entity_types"].get(entity_type, 0) + 1
    
    # Verificar se o texto está pronto para LLM
    if analysis["character_count"] < 50:
        analysis["readiness_for_llm"] = False
        analysis["readiness_reason"] = "Texto muito curto"
    elif analysis["word_count"] < 10:
        analysis["readiness_for_llm"] = False
        analysis["readiness_reason"] = "Poucas palavras"
    else:
        analysis["readiness_reason"] = "Texto adequado para processamento por LLM"
    
    # Adicionar análise ao estado
    state.text_analysis = analysis
    
    print(f"📊 Análise concluída:")
    print(f"   - Caracteres: {analysis['character_count']}")
    print(f"   - Palavras: {analysis['word_count']}")
    print(f"   - Linhas: {analysis['line_count']}")
    print(f"   - Pronto para LLM: {analysis['readiness_for_llm']}")
    print(f"   - Motivo: {analysis['readiness_reason']}")
    
    return state


def save_results_node(state: DocumentProcessingState) -> DocumentProcessingState:
    """
    Node para salvar resultados do processamento.
    
    Args:
        state: Estado atual do workflow
        
    Returns:
        Estado atualizado com informações de salvamento
    """
    if state.processing_status != "completed":
        print("⏭️ Pulando salvamento - processamento não foi bem-sucedido")
        return state
    
    print("💾 Salvando resultados...")
    
    # Definir caminhos de saída
    pdf_path = Path(state.pdf_path)
    output_dir = pdf_path.parent / "workflow_output"
    output_dir.mkdir(exist_ok=True)
    
    # Salvar texto anonimizado
    anonymized_path = output_dir / f"{pdf_path.stem}_anonymized.txt"
    with open(anonymized_path, 'w', encoding='utf-8') as f:
        f.write(state.anonymized_text)
    
    # Salvar relatório completo
    report_path = output_dir / f"{pdf_path.stem}_report.json"
    report = {
        "pdf_path": state.pdf_path,
        "processing_status": state.processing_status,
        "extraction_metadata": state.extraction_metadata,
        "anonymization_metadata": state.anonymization_metadata,
        "text_analysis": getattr(state, 'text_analysis', {}),
        "errors": state.errors,
        "output_files": {
            "anonymized_text": str(anonymized_path),
            "report": str(report_path)
        }
    }
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    state.output_files = report["output_files"]
    
    print(f"✅ Resultados salvos:")
    print(f"   - Texto anonimizado: {anonymized_path}")
    print(f"   - Relatório: {report_path}")
    
    return state


def run_pdf_workflow(pdf_path: str) -> DocumentProcessingState:
    """
    Executa o workflow completo de processamento de PDF.
    
    Args:
        pdf_path: Caminho para o arquivo PDF
        
    Returns:
        Estado final do workflow
    """
    print("🚀 INICIANDO WORKFLOW DE PROCESSAMENTO DE PDF")
    print("=" * 60)
    
    # Inicializar estado
    state = DocumentProcessingState()
    state.pdf_path = pdf_path
    
    # Executar nodes do workflow
    print("\n📄 STEP 1: Anonimização do PDF")
    print("-" * 30)
    state = pdf_anonymization_node(state)
    
    print("\n🔍 STEP 2: Análise do Texto")
    print("-" * 30)
    state = text_analysis_node(state)
    
    print("\n💾 STEP 3: Salvamento dos Resultados")
    print("-" * 30)
    state = save_results_node(state)
    
    print("\n✅ WORKFLOW CONCLUÍDO")
    print("=" * 60)
    print(f"Status: {state.processing_status}")
    
    if state.errors:
        print("❌ Erros encontrados:")
        for error in state.errors:
            print(f"   - {error}")
    
    return state


def main():
    """Função principal."""
    if len(sys.argv) != 2:
        print("Uso: python scripts/example_pdf_workflow.py <caminho_do_pdf>")
        print("\nExemplo:")
        print("python scripts/example_pdf_workflow.py data/test_document.pdf")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    # Verificar se o arquivo existe
    if not Path(pdf_path).exists():
        print(f"❌ Arquivo não encontrado: {pdf_path}")
        sys.exit(1)
    
    try:
        # Executar workflow
        final_state = run_pdf_workflow(pdf_path)
        
        if final_state.processing_status == "completed":
            print(f"\n🎉 Processamento bem-sucedido!")
            if hasattr(final_state, 'output_files'):
                print(f"📁 Arquivos gerados:")
                for name, path in final_state.output_files.items():
                    print(f"   - {name}: {path}")
        else:
            print(f"\n❌ Processamento falhou")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n⏹️ Workflow interrompido pelo usuário")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erro durante o workflow: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
