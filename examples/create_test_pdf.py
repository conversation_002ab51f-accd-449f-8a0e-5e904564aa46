#!/usr/bin/env python3
"""
Script para criar um PDF de teste com dados brasileiros para demonstrar
o funcionamento do PDFAnonymizationTool.
"""

import fitz  # PyMuPDF
from pathlib import Path


def create_test_pdf(output_path: str = "data/test_document.pdf"):
    """
    Cria um PDF de teste com dados brasileiros para anonimização.
    
    Args:
        output_path: Caminho onde salvar o PDF
    """
    # Criar diretório se não existir
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Criar documento PDF
    doc = fitz.open()
    
    # Página 1
    page1 = doc.new_page()
    
    # Header (será removido automaticamente)
    page1.insert_text((72, 50), "MINISTÉRIO PÚBLICO DO RIO DE JANEIRO", fontsize=12)
    page1.insert_text((72, 65), "Relatório de Ouvidoria - Documento Confidencial", fontsize=10)
    
    # Conteúdo principal
    content1 = """
RELATO DE OCORRÊNCIA

Data: 15 de março de 2024
Protocolo: 2024.001.234

O <PERSON>d<PERSON>, CPF 123.456.789-00, residente na Rua das Flores, 123, 
Copacabana, Rio de Janeiro, CEP 22071-900, relatou problemas na Escola Municipal 
Professor <PERSON> Drummond de Andrade.

Segundo o relato, sua filha Maria Silva, de 8 anos, tem enfrentado dificuldades 
com bullying na escola. O diretor da instituição, Sr. Pedro Santos, foi 
contactado mas não tomou providências adequadas.

Contato do denunciante: <EMAIL>
Telefone: (21) 98765-4321

A escola está localizada na Avenida Atlântica, 500, e atende aproximadamente 
300 alunos do ensino fundamental.
"""
    
    page1.insert_text((72, 100), content1, fontsize=11)
    
    # Footer (será removido automaticamente)
    page1.insert_text((72, 750), "Página 1 de 2 - Documento Confidencial", fontsize=8)
    
    # Página 2
    page2 = doc.new_page()
    
    # Header (mesmo da página 1)
    page2.insert_text((72, 50), "MINISTÉRIO PÚBLICO DO RIO DE JANEIRO", fontsize=12)
    page2.insert_text((72, 65), "Relatório de Ouvidoria - Documento Confidencial", fontsize=10)
    
    # Conteúdo da página 2
    content2 = """
CONTINUAÇÃO DO RELATO

Providências Tomadas:
- Contato com a Coordenadoria Regional de Educação (CRE)
- Agendamento de reunião com a direção da escola
- Orientação aos pais sobre direitos dos estudantes

Pessoas Envolvidas:
- Ana Costa (Coordenadora Pedagógica)
- Carlos Mendes (Professor da turma)
- Lucia Fernandes (Responsável pela disciplina)

Endereços Adicionais:
- Secretaria Municipal de Educação: Rua da Assembleia, 10, Centro
- CRE 2ª Região: Rua Mariz e Barros, 273, Tijuca

Documentos Anexos:
- Cópia do RG do denunciante: 12.345.678-9
- Histórico escolar da aluna
- Ata da reunião com a direção

Observações:
O caso foi encaminhado para acompanhamento pela equipe técnica.
Prazo para retorno: 30 dias úteis.
"""
    
    page2.insert_text((72, 100), content2, fontsize=11)
    
    # Footer (mesmo padrão)
    page2.insert_text((72, 750), "Página 2 de 2 - Documento Confidencial", fontsize=8)
    
    # Salvar documento
    doc.save(str(output_path))
    doc.close()
    
    print(f"✅ PDF de teste criado: {output_path}")
    print(f"📄 Páginas: 2")
    print(f"📁 Tamanho: {output_path.stat().st_size / 1024:.1f} KB")
    
    return str(output_path)


def main():
    """Função principal."""
    print("🔧 Criando PDF de teste para anonimização...")
    
    # Criar PDF de teste
    pdf_path = create_test_pdf()
    
    print(f"\n📋 CONTEÚDO DO PDF:")
    print("- Headers: 'MINISTÉRIO PÚBLICO...' e 'Relatório de Ouvidoria...'")
    print("- Footers: 'Página X de Y - Documento Confidencial'")
    print("- Dados pessoais: João Silva, Maria Silva, CPF, endereços")
    print("- Escolas: Escola Municipal Professor Carlos Drummond de Andrade")
    print("- Contatos: email, telefone")
    print("- Documentos: RG")
    
    print(f"\n🧪 Para testar a anonimização, execute:")
    print(f"python scripts/test_pdf_anonymization.py {pdf_path}")


if __name__ == "__main__":
    main()
