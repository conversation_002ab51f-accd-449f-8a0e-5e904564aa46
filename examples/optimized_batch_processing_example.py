#!/usr/bin/env python3
"""
Example script demonstrating the optimized PDF batch processing system.

This script shows how to use the new optimization features including:
- Token usage reduction
- Hierarchical classification
- Budget management
- Performance monitoring
"""

import asyncio
import tempfile
from pathlib import Path
import sys
import logging

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.api.services.optimized_batch_processing_service import OptimizedBatchProcessingService
from src.api.services.token_budget_manager import PriorityLevel
from src.api.models.batch_processing import BatchProcessingRequest

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def demonstrate_optimization():
    """Demonstrate the optimization system capabilities."""
    
    print("=" * 60)
    print("SIMPLE CLASS - OPTIMIZED BATCH PROCESSING DEMO")
    print("=" * 60)
    
    # Create optimized service
    service = OptimizedBatchProcessingService(
        enable_token_optimization=True,
        max_workers=2
    )
    
    print("\n1. SERVICE INITIALIZATION")
    print("✓ Optimized batch processing service created")
    print("✓ Token optimization enabled")
    print("✓ Hierarchical classification pipeline initialized")
    
    # Get optimization stats
    stats = service.get_optimization_stats()
    print(f"\n2. OPTIMIZATION CONFIGURATION")
    print(f"   - Optimization enabled: {stats['optimization_enabled']}")
    print(f"   - Budget daily limit: {stats['budget_config']['daily_limit']:,} tokens")
    print(f"   - Relevance threshold: {stats['filter_config']['relevance_threshold']}")
    print(f"   - Summarization threshold: {stats['summarizer_config']['similarity_threshold']}")
    
    # Create example data
    print(f"\n3. CREATING EXAMPLE DATA")
    
    # Create temporary directory with sample PDFs
    temp_dir = tempfile.mkdtemp()
    sample_pdfs = []
    
    sample_texts = [
        "A escola Municipal João da Silva precisa de melhorias urgentes na infraestrutura educacional. "
        "Os alunos relatam problemas com a qualidade do ensino e falta de recursos pedagógicos. "
        "É necessário investir em tecnologia educacional e capacitação de professores.",
        
        "O hospital regional implementou novos protocolos de saúde para melhorar o atendimento. "
        "Os pacientes agora têm acesso a tratamentos mais eficientes e seguros. "
        "A gestão hospitalar foi modernizada com sistemas digitais.",
        
        "A questão ambiental na cidade requer atenção imediata. "
        "A poluição do ar e da água está afetando a qualidade de vida dos moradores. "
        "É preciso implementar políticas sustentáveis e programas de reciclagem.",
        
        "O transporte público da cidade enfrenta diversos desafios. "
        "Os usuários reclamam da falta de pontualidade e qualidade do serviço. "
        "É necessário modernizar a frota e melhorar a gestão do sistema."
    ]
    
    for i, text in enumerate(sample_texts):
        pdf_path = Path(temp_dir) / f"documento_{i+1}.pdf"
        pdf_path.write_text(text)
        sample_pdfs.append(pdf_path)
    
    print(f"   ✓ Created {len(sample_pdfs)} sample PDF files")
    print(f"   ✓ Sample data covers multiple areas: Education, Health, Environment, Transport")
    
    # Demonstrate batch processing
    print(f"\n4. STARTING BATCH PROCESSING")
    
    request = BatchProcessingRequest(
        folder_path=str(temp_dir),
        area="EDUCACAO",
        chunk_size=500,
        overlap=100,
        parallel_workers=2
    )
    
    try:
        # Start batch processing
        job_id = await service.start_batch_processing(
            request=request,
            user_id="demo-user",
            priority=PriorityLevel.HIGH
        )
        
        print(f"   ✓ Batch processing started with job ID: {job_id}")
        
        # Monitor progress
        print(f"\n5. MONITORING PROGRESS")
        
        completed = False
        while not completed:
            status = service.get_job_status(job_id)
            if status:
                print(f"   Progress: {status.progress:.1%} - {status.current_file}")
                print(f"   Status: {status.status.value}")
                print(f"   Processed: {status.processed_files}/{status.total_files}")
                
                if status.status.value in ['completed', 'failed', 'cancelled']:
                    completed = True
                    break
                    
                await asyncio.sleep(1)
            else:
                print("   ✗ Job not found")
                break
        
        # Get final results
        if completed and status.status.value == 'completed':
            print(f"\n6. PROCESSING RESULTS")
            
            results = service.get_job_results(job_id)
            if results:
                print(f"   ✓ Processing completed successfully")
                print(f"   - Total PDFs: {results.total_pdfs}")
                print(f"   - Successful: {results.successful_pdfs}")
                print(f"   - Failed: {len(results.failed_pdfs)}")
                print(f"   - Processing time: {results.total_processing_time:.2f}s")
                print(f"   - CSV output: {results.csv_output_path}")
                
                # Show some results
                if results.pdf_results:
                    print(f"\n   SAMPLE RESULTS:")
                    for i, pdf_result in enumerate(results.pdf_results[:2]):
                        print(f"   File {i+1}: {pdf_result.filename}")
                        print(f"     - Subtemas: {pdf_result.subtemas_finais}")
                        print(f"     - Confidence: {pdf_result.confidence_scores}")
                        print(f"     - Pages: {pdf_result.total_pages}")
                        print(f"     - Chunks: {pdf_result.total_chunks}")
                        print(f"     - Time: {pdf_result.processing_time:.2f}s")
                
                # Get optimization metrics
                print(f"\n7. OPTIMIZATION METRICS")
                
                final_stats = service.get_optimization_stats()
                budget_metrics = final_stats.get('budget_metrics', {})
                
                if budget_metrics:
                    print(f"   - Tokens used: {budget_metrics.get('total_used', 0):,}")
                    print(f"   - Budget usage: {budget_metrics.get('usage_percentage', 0):.1f}%")
                    print(f"   - Estimated cost: ${budget_metrics.get('cost_estimate', 0):.4f}")
                
                # Get optimization suggestions
                suggestions = service.get_optimization_suggestions()
                if suggestions:
                    print(f"\n8. OPTIMIZATION SUGGESTIONS")
                    for i, suggestion in enumerate(suggestions[:3]):
                        print(f"   {i+1}. {suggestion.suggestion_type}:")
                        print(f"      - {suggestion.description}")
                        print(f"      - Potential savings: {suggestion.potential_savings:,} tokens")
                        print(f"      - Confidence: {suggestion.confidence:.1%}")
                else:
                    print(f"\n8. OPTIMIZATION SUGGESTIONS")
                    print("   No optimization suggestions available")
                
            else:
                print("   ✗ Could not retrieve results")
        
        else:
            print(f"\n6. PROCESSING FAILED")
            print(f"   Status: {status.status.value}")
            if status.error_message:
                print(f"   Error: {status.error_message}")
    
    except Exception as e:
        print(f"   ✗ Error during batch processing: {e}")
    
    finally:
        # Cleanup
        print(f"\n9. CLEANUP")
        try:
            import shutil
            shutil.rmtree(temp_dir)
            print("   ✓ Temporary files cleaned up")
        except Exception as e:
            print(f"   ⚠ Cleanup warning: {e}")
    
    print(f"\n" + "=" * 60)
    print("DEMONSTRATION COMPLETED")
    print("=" * 60)


async def demonstrate_optimization_comparison():
    """Demonstrate the difference between optimized and non-optimized processing."""
    
    print("\n" + "=" * 60)
    print("OPTIMIZATION COMPARISON DEMO")
    print("=" * 60)
    
    # Create sample data
    temp_dir = tempfile.mkdtemp()
    
    # Create more complex sample text
    complex_text = """
    A escola Municipal João da Silva enfrenta sérios problemas de infraestrutura educacional.
    Os alunos relatam dificuldades com a qualidade do ensino e falta de recursos pedagógicos.
    A instituição de ensino precisa de reformas urgentes na estrutura física.
    O estabelecimento educacional necessita de investimentos em tecnologia.
    A escola requer melhorias na capacitação dos professores e funcionários.
    O ambiente escolar precisa ser modernizado para atender às necessidades dos estudantes.
    A infraestrutura da escola está defasada e compromete o aprendizado.
    É necessário investir em equipamentos e materiais didáticos de qualidade.
    A gestão escolar deve ser aprimorada para melhor administração dos recursos.
    O projeto pedagógico precisa ser revisado e atualizado regularmente.
    """ * 3  # Repeat to create larger content
    
    pdf_path = Path(temp_dir) / "documento_complexo.pdf"
    pdf_path.write_text(complex_text)
    
    print(f"✓ Created complex sample document")
    print(f"✓ Document size: {len(complex_text):,} characters")
    
    # Test with optimization enabled
    print(f"\n1. PROCESSING WITH OPTIMIZATION")
    
    optimized_service = OptimizedBatchProcessingService(
        enable_token_optimization=True,
        max_workers=1
    )
    
    request = BatchProcessingRequest(
        folder_path=str(temp_dir),
        area="EDUCACAO",
        chunk_size=200,  # Smaller chunks to demonstrate optimization
        overlap=50,
        parallel_workers=1
    )
    
    try:
        # Process with optimization
        job_id_opt = await optimized_service.start_batch_processing(
            request=request,
            user_id="demo-user",
            priority=PriorityLevel.MEDIUM
        )
        
        # Wait for completion
        while True:
            status = optimized_service.get_job_status(job_id_opt)
            if status and status.status.value in ['completed', 'failed']:
                break
            await asyncio.sleep(0.5)
        
        # Get results
        opt_results = optimized_service.get_job_results(job_id_opt)
        opt_stats = optimized_service.get_optimization_stats()
        
        if opt_results:
            print(f"   ✓ Optimization processing completed")
            print(f"   - Processing time: {opt_results.total_processing_time:.2f}s")
            print(f"   - Budget usage: {opt_stats.get('budget_metrics', {}).get('usage_percentage', 0):.1f}%")
            print(f"   - Tokens used: {opt_stats.get('budget_metrics', {}).get('total_used', 0):,}")
        
        # Test without optimization
        print(f"\n2. PROCESSING WITHOUT OPTIMIZATION")
        
        basic_service = OptimizedBatchProcessingService(
            enable_token_optimization=False,
            max_workers=1
        )
        
        # Process without optimization
        job_id_basic = await basic_service.start_batch_processing(
            request=request,
            user_id="demo-user",
            priority=PriorityLevel.MEDIUM
        )
        
        # Wait for completion
        while True:
            status = basic_service.get_job_status(job_id_basic)
            if status and status.status.value in ['completed', 'failed']:
                break
            await asyncio.sleep(0.5)
        
        # Get results
        basic_results = basic_service.get_job_results(job_id_basic)
        basic_stats = basic_service.get_optimization_stats()
        
        if basic_results:
            print(f"   ✓ Basic processing completed")
            print(f"   - Processing time: {basic_results.total_processing_time:.2f}s")
            print(f"   - Optimization enabled: {basic_stats.get('optimization_enabled', False)}")
        
        # Compare results
        print(f"\n3. COMPARISON RESULTS")
        
        if opt_results and basic_results:
            time_improvement = (basic_results.total_processing_time - opt_results.total_processing_time) / basic_results.total_processing_time * 100
            
            print(f"   - Time improvement: {time_improvement:.1f}%")
            print(f"   - Optimized time: {opt_results.total_processing_time:.2f}s")
            print(f"   - Basic time: {basic_results.total_processing_time:.2f}s")
            
            if opt_stats.get('budget_metrics'):
                tokens_used = opt_stats['budget_metrics'].get('total_used', 0)
                print(f"   - Token usage (optimized): {tokens_used:,}")
                print(f"   - Estimated cost savings: Significant reduction in LLM API calls")
        
    except Exception as e:
        print(f"   ✗ Error during comparison: {e}")
    
    finally:
        # Cleanup
        try:
            import shutil
            shutil.rmtree(temp_dir)
            print(f"\n✓ Cleanup completed")
        except:
            pass


async def main():
    """Main demo function."""
    print("🚀 Starting Simple Class Optimization Demo")
    
    try:
        await demonstrate_optimization()
        await demonstrate_optimization_comparison()
        
    except KeyboardInterrupt:
        print("\n⚠ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
    
    print("\n✅ Demo completed successfully!")


if __name__ == "__main__":
    asyncio.run(main())