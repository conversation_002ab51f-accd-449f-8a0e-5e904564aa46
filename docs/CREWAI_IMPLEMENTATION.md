# 🤖 Implementação CrewAI - Simple Class

## 📋 Visão Geral

A implementação do CrewAI no projeto Simple Class introduz um sistema de agentes inteligentes que orquestram workflows complexos de processamento de texto. Esta implementação mantém total compatibilidade com a arquitetura existente enquanto adiciona capacidades avançadas de automação.

## 🏗️ Arquitetura Implementada

### Estrutura de Diretórios

```
src/
├── agents/                     # 🤖 Agentes CrewAI
│   ├── __init__.py
│   ├── base_agent.py          # Classe base para todos os agentes
│   ├── anonymization_agent.py # Agente de anonimização
│   ├── classification_agent.py # Agente de classificação
│   └── report_agent.py        # Agente de relatórios
├── crews/                      # 🔄 Workflows CrewAI
│   ├── __init__.py
│   ├── base_crew.py           # Classe base para crews
│   └── processing_crew.py     # Crew principal de processamento
└── api/
    └── routers/
        └── crew_router.py     # 🌐 API endpoints para CrewAI
```

### Diagrama de Arquitetura

```mermaid
graph TD
    A[FastAPI Router] --> B[ProcessingCrew]
    B --> C[AnonymizationAgent]
    B --> D[ClassificationAgent]
    B --> E[ReportAgent]
    
    C --> F[AnonymizationTool]
    D --> G[AnalisadorMultilabel]
    E --> H[Claude Sonnet 4]
    
    F --> I[Presidio + Recognizers BR]
    G --> J[Fireworks AI / Together AI]
    H --> K[Anthropic API]
```

## 🤖 Agentes Implementados

### 1. AnonymizationAgent

**Responsabilidade**: Anonimização de dados para conformidade LGPD

**Características**:
- Integra com `AnonymizationTool` existente
- Suporte a texto e PDF
- Reconhecedores brasileiros (CPF, escolas, endereços)
- Fallback para modo sem CrewAI

**Exemplo de Uso**:
```python
from src.agents.anonymization_agent import AnonymizationAgent

agent = AnonymizationAgent()
result = agent.execute_task({
    "text": "João Silva mora na Rua das Flores, 123",
    "language": "pt"
})

print(result["anonymized_text"])  # "<PERSON> mora na <ENDEREÇO>"
```

### 2. ClassificationAgent

**Responsabilidade**: Classificação multilabel de textos

**Características**:
- Integra com `AnalisadorMultilabel` existente
- Suporte a múltiplas áreas (EDUCACAO, SAUDE, MEIO_AMBIENTE)
- Até 3 subtemas por texto
- Configuração flexível de modelos LLM

**Exemplo de Uso**:
```python
from src.agents.classification_agent import ClassificationAgent

agent = ClassificationAgent()
result = agent.execute_task({
    "text": "Problema com infraestrutura da escola",
    "area": "EDUCACAO",
    "model_name": "meta-llama/Llama-3.3-70B-Instruct-Turbo"
})

print(result["subtemas_identificados"])  # ["Infraestrutura"]
```

### 3. ReportAgent

**Responsabilidade**: Geração de relatórios táticos com Claude Sonnet 4

**Características**:
- Integra com ferramentas de relatório existentes
- Múltiplos tipos de relatório (overview, subtema, criminal)
- Análise tática especializada
- Geração automática de insights

**Exemplo de Uso**:
```python
from src.agents.report_agent import ReportAgent
import pandas as pd

agent = ReportAgent()
df = pd.read_csv("dados_classificados.csv")

result = agent.execute_task({
    "data": df,
    "report_type": "overview",
    "area": "EDUCACAO"
})

print(result["report_content"])  # Relatório tático completo
```

## 🔄 Workflows (Crews)

### ProcessingCrew

**Workflow Principal**: Orquestra o pipeline completo de processamento

**Workflows Suportados**:

1. **`full_pipeline`**: Anonimização → Classificação → Relatórios
2. **`anonymize_classify`**: Anonimização → Classificação
3. **`classify_report`**: Classificação → Relatórios
4. **`anonymize_only`**: Apenas anonimização
5. **`classify_only`**: Apenas classificação
6. **`report_only`**: Apenas relatórios

**Exemplo de Uso**:
```python
from src.crews.processing_crew import ProcessingCrew

crew = ProcessingCrew()
result = crew.kickoff({
    "workflow_type": "full_pipeline",
    "text": "Relato de problema na escola",
    "area": "EDUCACAO"
})

print(f"Sucesso: {result['success']}")
print(f"Tempo: {result['execution_time']:.2f}s")
```

## 🌐 API Integration

### Novos Endpoints

#### Health Check
```http
GET /api/v1/crew/health
```

**Resposta**:
```json
{
  "status": "healthy",
  "crewai_available": true,
  "agents": {
    "anonymization": true,
    "classification": true,
    "report": true
  },
  "supported_workflows": ["full_pipeline", "anonymize_only", ...]
}
```

#### Listar Workflows
```http
GET /api/v1/crew/workflows
```

#### Processar Texto
```http
POST /api/v1/crew/process
Content-Type: application/json

{
  "workflow_type": "full_pipeline",
  "text": "Problema com infraestrutura escolar",
  "area": "EDUCACAO",
  "language": "pt"
}
```

#### Processar Arquivo CSV
```http
POST /api/v1/crew/process-file
Content-Type: multipart/form-data

file: arquivo.csv
workflow_type: full_pipeline
area: EDUCACAO
text_column: Teor
```

#### Status de Jobs
```http
GET /api/v1/crew/jobs/{job_id}
```

## 🔧 Configuração e Setup

### 1. Instalação de Dependências

```bash
# Adicionar CrewAI ao projeto
poetry add crewai

# Instalar dependências
poetry install
```

### 2. Configuração de API Keys

Adicione ao arquivo `.env`:
```env
# Existing keys
TOGETHER_API_KEY=your_together_key
ANTHROPIC_API_KEY=your_anthropic_key
FIREWORKS_API_KEY=your_fireworks_key

# CrewAI não requer keys adicionais
```

### 3. Verificação da Instalação

```bash
# Testar health check
curl http://localhost:8000/api/v1/crew/health

# Executar testes
poetry run pytest tests/test_crewai_integration.py -v
```

## 📊 Compatibilidade e Fallback

### Modo Fallback

A implementação funciona mesmo sem o CrewAI instalado:

- **Agentes**: Executam tarefas diretamente sem orquestração CrewAI
- **Crews**: Usam orquestração manual sequencial
- **API**: Mantém funcionalidade com avisos apropriados

### Compatibilidade com Código Existente

- ✅ **Endpoints existentes**: Mantidos sem alteração
- ✅ **Ferramentas existentes**: Reutilizadas pelos agentes
- ✅ **Configurações**: Mesmas variáveis de ambiente
- ✅ **Estrutura de dados**: Formatos compatíveis

## 🧪 Testes

### Executar Testes

```bash
# Todos os testes CrewAI
poetry run pytest tests/test_crewai_integration.py -v

# Testes da API
poetry run pytest tests/test_crew_api.py -v

# Teste específico
poetry run pytest tests/test_crewai_integration.py::TestAnonymizationAgent::test_text_anonymization_task -v
```

### Cobertura de Testes

- ✅ Inicialização de agentes
- ✅ Execução de tarefas individuais
- ✅ Workflows completos
- ✅ Tratamento de erros
- ✅ Endpoints da API
- ✅ Modo fallback

## 🚀 Exemplos de Uso

### Exemplo 1: Processamento Individual

```python
from src.crews.processing_crew import ProcessingCrew

# Inicializar crew
crew = ProcessingCrew()

# Processar texto individual
result = crew.kickoff({
    "workflow_type": "full_pipeline",
    "text": "Aluno com deficiência sem mediador na escola",
    "area": "EDUCACAO",
    "max_subtemas": 3
})

# Verificar resultados
if result["success"]:
    print("✅ Processamento concluído!")
    print(f"Tempo: {result['execution_time']:.2f}s")
    
    # Resultados de anonimização
    anon_result = result["results"]["anonymization"]
    print(f"Texto anonimizado: {anon_result['anonymized_text']}")
    
    # Resultados de classificação
    class_result = result["results"]["classification"]
    print(f"Subtemas: {class_result['subtemas_identificados']}")
    
    # Relatório gerado
    report_result = result["results"]["report"]
    print(f"Relatório: {len(report_result['report_content'])} caracteres")
```

### Exemplo 2: Processamento via API

```python
import requests

# Processar texto via API
response = requests.post("http://localhost:8000/api/v1/crew/process", json={
    "workflow_type": "classify_report",
    "text": "Falta de transporte escolar na região",
    "area": "EDUCACAO",
    "report_type": "overview"
})

result = response.json()
print(f"Sucesso: {result['success']}")
print(f"ID da execução: {result['execution_id']}")
```

### Exemplo 3: Processamento de Arquivo

```python
import requests

# Upload e processamento de CSV
with open("ouvidorias.csv", "rb") as f:
    files = {"file": ("ouvidorias.csv", f, "text/csv")}
    data = {
        "workflow_type": "full_pipeline",
        "area": "EDUCACAO",
        "text_column": "Teor"
    }
    
    response = requests.post(
        "http://localhost:8000/api/v1/crew/process-file",
        files=files,
        data=data
    )

result = response.json()
job_id = result["job_id"]

# Verificar status
status_response = requests.get(f"http://localhost:8000/api/v1/crew/jobs/{job_id}")
status = status_response.json()
print(f"Status: {status['status']}")
print(f"Progresso: {status['progress']:.1%}")
```

## 🔍 Monitoramento e Debug

### Logs

Os agentes e crews geram logs detalhados:

```python
import logging

# Configurar logging para debug
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("src.agents")
logger.setLevel(logging.DEBUG)
```

### Métricas de Performance

```python
# Verificar histórico de execuções
crew = ProcessingCrew()
history = crew.get_execution_history()

for execution in history:
    print(f"ID: {execution['execution_id']}")
    print(f"Tempo: {execution['execution_time']:.2f}s")
    print(f"Sucesso: {execution['success']}")
```

## 🛠️ Troubleshooting

### Problemas Comuns

1. **CrewAI não instalado**
   ```bash
   poetry add crewai
   ```

2. **API keys não configuradas**
   ```bash
   # Verificar .env
   cat .env | grep API_KEY
   ```

3. **Agentes não inicializando**
   ```python
   # Verificar configuração
   from src.agents.classification_agent import ClassificationAgent
   agent = ClassificationAgent()
   print(agent.validate_config())
   ```

4. **Workflows falhando**
   ```python
   # Verificar agentes disponíveis
   crew = ProcessingCrew()
   print(crew.get_available_agents())
   ```

## 📈 Próximos Passos

### Melhorias Planejadas

1. **Agentes Adicionais**:
   - ValidationAgent: Validação de qualidade
   - AuditAgent: Auditoria de processos
   - MetricsAgent: Coleta de métricas

2. **Workflows Avançados**:
   - Processamento paralelo
   - Workflows condicionais
   - Auto-retry com backoff

3. **Integração Avançada**:
   - WebSockets para status em tempo real
   - Dashboard de monitoramento
   - Métricas de performance

4. **Otimizações**:
   - Cache de resultados
   - Processamento em lote otimizado
   - Balanceamento de carga

## 📚 Referências

- [CrewAI Documentation](https://docs.crewai.com/)
- [Simple Class Architecture](./architecture.md)
- [API Documentation](./API_DOCUMENTATION.md)
- [Implementation Plan](./CREWAI_IMPLEMENTATION_PLAN.md)
- [User Guide](./CREWAI_USER_GUIDE.md)
