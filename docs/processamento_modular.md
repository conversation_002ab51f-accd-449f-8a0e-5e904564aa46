# Processamento Modular - Simple Class

## Visão Geral

O sistema de processamento modular permite usar os **tools** da nova arquitetura de forma independente, sem necessidade de implementar agentes completos. É ideal para:

- **Processamento de documentos** da pasta `data/input/`
- **Uso independente** de cada funcionalidade
- **Workflows personalizados** combinando tools
- **Transição gradual** para arquitetura multiagentes

## Scripts Disponíveis

### 1. Script Python Completo (`scripts/processar_modular.py`)

Script principal com todas as funcionalidades dos tools:

```bash
# Informações do sistema
python scripts/processar_modular.py info

# Operações com dados
python scripts/processar_modular.py data columns
python scripts/processar_modular.py data validate --input ouvidorias.csv

# Classificação
python scripts/processar_modular.py classify individual --column "Teor"
python scripts/processar_modular.py classify multilabel --column "Teor" --area SAUDE

# Relatórios
python scripts/processar_modular.py report tactical --input classificados.csv

# Anonimização
python scripts/processar_modular.py anonymize --column "Teor"

# Workflow completo
python scripts/processar_modular.py workflow --column "Teor" --anonymize
```

### 2. Script Bash Rápido (`scripts/processar.sh`)

Wrapper simplificado para uso rápido:

```bash
# Comandos rápidos
./scripts/processar.sh info                    # Informações
./scripts/processar.sh colunas                 # Ver colunas
./scripts/processar.sh classificar "Teor"      # Classificação
./scripts/processar.sh multilabel "Teor"       # Multilabel
./scripts/processar.sh relatorio               # Relatório
./scripts/processar.sh completo "Teor"         # Classificação + relatório
./scripts/processar.sh tudo "Teor"             # Tudo + anonimização
```

### 3. Comandos Make

Integração com Makefile para desenvolvimento:

```bash
# Processamento modular
make process-info          # Informações do sistema
make process-columns       # Colunas dos dados
make process-classify      # Classificação individual
make process-multilabel    # Classificação multilabel
make process-report        # Relatório
make process-workflow      # Workflow completo

# Scripts rápidos
make quick-info           # Informações rápidas
make quick-classify       # Classificação rápida
make quick-complete       # Workflow completo rápido
```

## Funcionalidades Disponíveis

### 📊 **Informações do Sistema**

```bash
# Verificar configuração, API keys, arquivos disponíveis
python scripts/processar_modular.py info
```

**Saída:**
- ✅ Status da configuração
- 🔑 API keys disponíveis
- 📁 Arquivos de entrada encontrados
- 🎯 Assunto e modelos configurados

### 📋 **Operações com Dados**

```bash
# Ver colunas disponíveis
python scripts/processar_modular.py data columns

# Validar formato dos dados
python scripts/processar_modular.py data validate

# Carregar e analisar dados
python scripts/processar_modular.py data load
```

### 🔍 **Classificação**

#### Classificação Individual (Binária)
```bash
python scripts/processar_modular.py classify individual --column "Teor"
```

**Resultado:**
- Arquivo: `data/output/{nome}_classificado_individual.csv`
- Colunas: `REFERE_ASSUNTO` (0/1), `TEXTO_ANALISADO`
- Estatísticas: Total processado, casos positivos, percentual

#### Classificação Multilabel
```bash
python scripts/processar_modular.py classify multilabel --column "Teor" --area SAUDE
```

**Resultado:**
- Arquivo: `data/output/{nome}_classificado_multilabel.csv`
- Colunas: `SUBTEMAS_IDENTIFICADOS`, colunas binárias por subtema
- Estatísticas: Contagem por subtema, co-ocorrências

### 📄 **Relatórios**

#### Relatório Tático
```bash
python scripts/processar_modular.py report tactical --input classificados.csv
```

#### Relatório de Visão Geral
```bash
python scripts/processar_modular.py report overview --input classificados.csv
```

#### Relatório Multilabel
```bash
python scripts/processar_modular.py report multilabel --input multilabel.csv
```

### 🔒 **Anonimização**

```bash
python scripts/processar_modular.py anonymize --column "Teor"
```

**Funcionalidades:**
- Detecção automática de PII (Presidio)
- Preservação de termos médicos/institucionais
- Relatório de anonimização

### 🔄 **Workflow Completo**

```bash
# Workflow básico: validação + classificação + relatório
python scripts/processar_modular.py workflow --column "Teor"

# Workflow completo: + anonimização
python scripts/processar_modular.py workflow --column "Teor" --anonymize
```

**Etapas do Workflow:**
1. ✅ Validação dos dados
2. 🔍 Classificação individual
3. 📄 Geração de relatório tático
4. 🔒 Anonimização (se solicitada)

## Detecção Automática de Arquivos

O sistema detecta automaticamente arquivos na pasta `data/input/`:

### Arquivo Único
Se há apenas um arquivo CSV, é usado automaticamente:
```bash
python scripts/processar_modular.py classify individual --column "Teor"
# Usa automaticamente o único CSV em data/input/
```

### Múltiplos Arquivos
Se há vários arquivos, o sistema pergunta qual usar:
```
Múltiplos arquivos CSV encontrados:
  1. ouvidorias.csv
  2. reclamacoes.csv
  3. denuncias.csv
Escolha um arquivo (número): 1
```

### Arquivo Específico
Especificar arquivo manualmente:
```bash
python scripts/processar_modular.py classify individual --column "Teor" --input ouvidorias.csv
```

## Estrutura de Arquivos

### Entrada
```
data/input/
├── ouvidorias.csv          # Arquivo principal
├── reclamacoes.csv         # Arquivo adicional
└── denuncias.csv           # Outro arquivo
```

### Saída
```
data/output/
├── ouvidorias_classificado_individual.csv    # Classificação individual
├── ouvidorias_classificado_multilabel.csv    # Classificação multilabel
├── ouvidorias_anonimizado.csv               # Dados anonimizados
├── relatorio_tactical_ouvidorias.md         # Relatório tático
├── relatorio_overview_ouvidorias.md         # Relatório visão geral
└── relatorio_multilabel_ouvidorias.md       # Relatório multilabel
```

## Configuração

### Variáveis de Ambiente
```bash
# .env
TOGETHER_API_KEY=your_together_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
```

### Configuração do Sistema
```yaml
# config/settings.yml
classification:
  subject: "Oftalmologia"
  batch_size: 5

models:
  llm_model: "meta-llama/Llama-4-Scout-17B-16E-Instruct"
  report_model: "claude-sonnet-4-20250514"

subtemas:
  SAUDE:
    - "Oftalmologia"
    - "Regulação em Saúde"
    - "Diagnose (laboratório e imagem)"
```

## Exemplos Práticos

### Exemplo 1: Processamento Básico
```bash
# 1. Verificar sistema
./scripts/processar.sh info

# 2. Ver colunas disponíveis
./scripts/processar.sh colunas

# 3. Classificar documentos
./scripts/processar.sh classificar "Teor"

# 4. Gerar relatório
./scripts/processar.sh relatorio
```

### Exemplo 2: Workflow Completo
```bash
# Tudo em um comando
./scripts/processar.sh completo "Teor"
```

### Exemplo 3: Multilabel Especializado
```bash
# Classificação multilabel para área específica
python scripts/processar_modular.py classify multilabel \
  --column "Teor" \
  --area SAUDE \
  --input ouvidorias.csv \
  --output resultados_saude.csv

# Relatório multilabel
python scripts/processar_modular.py report multilabel \
  --input resultados_saude.csv \
  --output relatorio_saude.md
```

### Exemplo 4: Processamento com Make
```bash
# Setup e processamento
make setup
make data-sample          # Criar dados de exemplo
make process-workflow     # Executar workflow completo
make logs                 # Ver logs
```

## Vantagens do Processamento Modular

### ✅ **Independência**
- Cada tool funciona independentemente
- Não requer implementação de agentes
- Compatível com scripts existentes

### ✅ **Flexibilidade**
- Combine tools conforme necessário
- Workflows personalizados
- Processamento sob demanda

### ✅ **Facilidade de Uso**
- Scripts simples e intuitivos
- Detecção automática de arquivos
- Feedback visual claro

### ✅ **Preparação para Agentes**
- Mesmos tools usados por agentes futuros
- Transição suave para multiagentes
- Código reutilizável

## Troubleshooting

### Problema: "Arquivo não encontrado"
```bash
# Verificar arquivos disponíveis
ls data/input/
# ou
python scripts/processar_modular.py data columns
```

### Problema: "API key não encontrada"
```bash
# Verificar configuração
python scripts/processar_modular.py info
# Editar .env com suas chaves
```

### Problema: "Coluna não encontrada"
```bash
# Ver colunas disponíveis
python scripts/processar_modular.py data columns
```

### Problema: "Erro de encoding"
O sistema detecta automaticamente encodings comuns (UTF-8, Latin-1, CP1252).

## Próximos Passos

1. **Usar processamento modular** para suas necessidades atuais
2. **Experimentar workflows** diferentes
3. **Preparar dados** para agentes futuros
4. **Implementar agentes LangGraph** quando necessário

O processamento modular oferece toda a funcionalidade necessária enquanto o sistema de agentes completo não é implementado!
