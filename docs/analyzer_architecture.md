# Arquitetura dos Analisadores - Simple Class API

## 🤖 Visão Geral dos Analisadores

Os analisadores são componentes centrais do Simple Class, responsáveis pelo processamento e análise dos dados de ouvidorias. A arquitetura dos analisadores está organizada em módulos especializados, cada um com uma função específica no pipeline de processamento.

## 📊 Estrutura dos Analisadores

```
analisadores/
├── analisador_multilabel.py      # Classificação multilabel principal
├── analisador_lote.py           # Processamento em lote
├── analisador_embeddings.py     # Análise semântica com embeddings
└── analisador_relatos.py       # Processamento específico de relatos
```

## 🎯 Funções Principais

### 1. `analisador_multilabel.py`
- **Classificação Multilabel**:
  - Suporte a até 3 subtemas por documento
  - Sistema de embeddings para análise semântica
  - Integração com Fireworks AI
  - Precisão de 96.6% em dados reais

### 2. `analisador_lote.py`
- **Processamento em Lote**:
  - Suporte a processamento paralelo
  - Sistema de queue para grandes volumes
  - Rastreamento de progresso
  - Gerenciamento de erros por lote

### 3. `analisador_embeddings.py`
- **Análise Semântica**:
  - Geração de embeddings para documentos
  - Comparação semântica de textos
  - Detecção de similaridade
  - Análise de contexto

### 4. `analisador_relatos.py`
- **Processamento de Relatos**:
  - Extração de metadados
  - Pré-processamento de texto
  - Validação de formato
  - Integração com outros analisadores

## 🔗 Integração com Outros Componentes

### 1. API e Serviços
- Integração com endpoints da API
- Uso de serviços de banco de dados
- Comunicação com ferramentas de anonimização
- Geração de relatórios

### 2. Ferramentas Especializadas
- Uso de LLMs (Fireworks AI, Claude)
- Integração com Presidio para anonimização
- Utilização de reconhecedores brasileiros
- Tratamento de PDFs e textos

## 📊 Métricas e Monitoramento

- Rastreamento de performance
- Logs detalhados por analisador
- Métricas de precisão e recall
- Monitoramento de recursos
- Sistema de health checks

## 🛠️ Melhores Práticas

1. **Separação de Responsabilidades**:
   - Cada analisador tem uma função específica
   - Código modular e reutilizável
   - Interface clara entre componentes

2. **Tratamento de Erros**:
   - Try/except em todos os níveis
   - Logs detalhados de erros
   - Sistema de fallback
   - Recuperação automática

3. **Performance**:
   - Cache de resultados
   - Processamento paralelo
   - Otimização de memória
   - Rate limiting

4. **Segurança**:
   - Validação de entrada
   - Anonimização de dados
   - Proteção contra injeção
   - Logs seguros

## 📚 Documentação

- Documentação inline completa
- Exemplos de uso
- Guia de integração
- Referência de APIs
- Best practices
