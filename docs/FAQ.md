# FAQ - Perguntas Frequentes

## 🚀 Uso Básico

### Como começar rapidamente?

**Resposta:** Use o script simples que funciona sem configuração:

```bash
# 1. Clone e instale
git clone https://github.com/daniribeiroBR/simple_class.git
cd simple_class
poetry install

# 2. Execute com dados de exemplo
poetry run python scripts/processar_simples.py workflow --column "Teor"

# 3. Veja os resultados em data/output/
```

### Preciso de API keys para usar o sistema?

**Resposta:** Não! O sistema oferece duas opções:

- **Sem API keys**: Use `scripts/processar_simples.py` com classificação baseada em palavras-chave
- **Com API keys**: Use o CLI completo `simple-class` com IA

### Como processar meus próprios dados?

**Resposta:**

1. **Coloque seu arquivo CSV** em `data/input/`
2. **Execute o processamento:**
   ```bash
   poetry run python scripts/processar_simples.py workflow --column "SuaColuna"
   ```
3. **Veja os resultados** em `data/output/`

### Que formato deve ter meu arquivo CSV?

**Resposta:**
- **Formato:** CSV padrão com cabeçalho
- **Encoding:** UTF-8 (recomendado), mas o sistema detecta automaticamente
- **Colunas:** Pelo menos uma coluna com texto para análise
- **Exemplo:**
  ```csv
  ID,Teor,Data
  1,"Texto para análise",2024-01-01
  2,"Outro texto",2024-01-02
  ```

## ⚙️ Configuração

### Como configurar API keys?

**Resposta:**

1. **Copie o template:**
   ```bash
   cp .env.example .env
   ```

2. **Edite o arquivo .env:**
   ```bash
   TOGETHER_API_KEY=sua_chave_together_ai
   ANTHROPIC_API_KEY=sua_chave_anthropic
   ```

3. **Teste a configuração:**
   ```bash
   poetry run simple-class config validate
   ```

### Onde obter as API keys?

**Resposta:**
- **Together AI**: https://api.together.xyz/ (para modelos LLM)
- **Anthropic**: https://console.anthropic.com/ (para relatórios com Claude)

### Como personalizar a classificação?

**Resposta:**

**Para script simples** (editar palavras-chave):
```python
# Em scripts/processar_simples.py, linha ~120
keywords = [
    'sua_palavra_chave',
    'termo_relevante',
    'conceito_importante'
]
```

**Para CLI avançado** (editar configuração):
```yaml
# config/settings.yml
classification:
  subject: "Seu Assunto"
```

## 🔧 Problemas Comuns

### "Arquivo não encontrado"

**Problema:** Script não encontra arquivo CSV

**Solução:**
1. **Verificar localização:**
   ```bash
   ls data/input/
   ```
2. **Colocar arquivo na pasta correta:**
   ```bash
   cp seu_arquivo.csv data/input/
   ```
3. **Usar caminho específico:**
   ```bash
   poetry run python scripts/processar_simples.py workflow --column "Teor" --input seu_arquivo.csv
   ```

### "Coluna não encontrada"

**Problema:** Script não encontra a coluna especificada

**Solução:**
1. **Ver colunas disponíveis:**
   ```bash
   poetry run python scripts/processar_simples.py columns
   ```
2. **Usar nome exato da coluna:**
   ```bash
   poetry run python scripts/processar_simples.py workflow --column "Nome_Exato_Da_Coluna"
   ```

### "Erro de encoding"

**Problema:** Arquivo CSV com encoding incompatível

**Solução:**
- O sistema detecta automaticamente UTF-8, Latin-1, CP1252
- Se persistir, converta para UTF-8:
  ```bash
  iconv -f latin1 -t utf-8 arquivo.csv > arquivo_utf8.csv
  ```

### "ModuleNotFoundError"

**Problema:** Dependências não instaladas

**Solução:**
```bash
# Reinstalar dependências
poetry install

# Ou usar setup automático
./scripts/setup.sh --dev
```

### "API key não encontrada"

**Problema:** Tentando usar CLI avançado sem API keys

**Solução:**
- **Use script simples** (não precisa de API):
  ```bash
  poetry run python scripts/processar_simples.py workflow --column "Teor"
  ```
- **OU configure API keys** no arquivo .env

## 📊 Resultados e Interpretação

### Como interpretar os resultados da classificação?

**Resposta:**

**Arquivo de classificação** (`*_classificado.csv`):
- **`REFERE_ASSUNTO`**: 1 = relacionado ao assunto, 0 = não relacionado
- **`TEXTO_ANALISADO`**: Texto que foi analisado
- **Outras colunas**: Preservadas do arquivo original

**Relatório** (`relatorio_*.md`):
- **Taxa de positividade**: % de casos relacionados ao assunto
- **Casos positivos**: Exemplos de textos classificados como relevantes
- **Estatísticas**: Contagens e análises

### A classificação está correta?

**Resposta:**

**Script simples** (palavras-chave):
- Precisão depende das palavras-chave configuradas
- Bom para triagem inicial
- Pode ter falsos positivos/negativos

**CLI avançado** (IA):
- Maior precisão com modelos LLM
- Melhor compreensão de contexto
- Requer API keys

### Como melhorar a precisão?

**Resposta:**

1. **Para script simples:**
   - Adicionar mais palavras-chave relevantes
   - Remover termos que causam falsos positivos

2. **Para CLI avançado:**
   - Usar modelos mais avançados
   - Ajustar configuração do assunto
   - Usar classificação multilabel

## 🔄 Workflows e Automação

### Como automatizar o processamento?

**Resposta:**

**Script bash:**
```bash
#!/bin/bash
# Processar todos os CSVs em data/input/
for file in data/input/*.csv; do
    echo "Processando $file..."
    poetry run python scripts/processar_simples.py workflow --column "Teor" --input "$file"
done
```

**Cron job:**
```bash
# Executar diariamente às 9h
0 9 * * * cd /path/to/simple_class && poetry run python scripts/processar_simples.py workflow --column "Teor"
```

### Como processar múltiplos arquivos?

**Resposta:**

**Método 1** - Um por vez:
```bash
poetry run python scripts/processar_simples.py workflow --column "Teor" --input arquivo1.csv
poetry run python scripts/processar_simples.py workflow --column "Teor" --input arquivo2.csv
```

**Método 2** - Script personalizado:
```python
import glob
import subprocess

for file in glob.glob("data/input/*.csv"):
    subprocess.run([
        "poetry", "run", "python", "scripts/processar_simples.py",
        "workflow", "--column", "Teor", "--input", file
    ])
```

## 🛠️ Desenvolvimento e Personalização

### Como adicionar novas palavras-chave?

**Resposta:**

Edite o arquivo `scripts/processar_simples.py`:

```python
# Linha ~120, função simple_classify()
keywords = [
    # Palavras existentes
    'oftalmologia', 'visão', 'olho',
    
    # Suas novas palavras
    'nova_palavra',
    'termo_específico',
    'conceito_relevante'
]
```

### Como criar um script personalizado?

**Resposta:**

```python
#!/usr/bin/env python3
import pandas as pd
from pathlib import Path

def meu_processamento(input_file, column_name):
    # Carregar dados
    df = pd.read_csv(input_file)
    
    # Sua lógica personalizada aqui
    df['RESULTADO'] = df[column_name].str.contains('termo', case=False)
    
    # Salvar resultado
    output_file = f"data/output/meu_resultado.csv"
    df.to_csv(output_file, index=False)
    
    print(f"Resultado salvo em: {output_file}")

if __name__ == "__main__":
    meu_processamento("data/input/arquivo.csv", "Teor")
```

### Como contribuir com o projeto?

**Resposta:**

1. **Fork** o repositório
2. **Clone** sua fork
3. **Crie branch** para sua feature
4. **Desenvolva** e teste
5. **Commit** com mensagens descritivas
6. **Push** e abra Pull Request

## 📚 Recursos Adicionais

### Onde encontrar mais documentação?

**Resposta:**
- **[README.md](../README.md)**: Visão geral e início rápido
- **[Processamento Modular](processamento_modular.md)**: Guia detalhado
- **[Arquitetura](architecture.md)**: Detalhes técnicos
- **[Desenvolvimento](development.md)**: Para desenvolvedores

### Como obter suporte?

**Resposta:**
- **Issues no GitHub**: Para bugs e sugestões
- **Documentação**: Consulte os arquivos em `docs/`
- **Código**: Exemplos nos scripts e testes

### O sistema funciona em Windows/Mac/Linux?

**Resposta:**
- **Sim!** O sistema é multiplataforma
- **Requisitos**: Python 3.10+, Poetry
- **Testado em**: Linux, macOS, Windows

---

## 💡 Dicas Úteis

- **Comece simples**: Use o script simples primeiro
- **Teste com dados pequenos**: Valide antes de processar grandes volumes
- **Verifique resultados**: Sempre revise as classificações
- **Backup**: Mantenha cópias dos dados originais
- **Documentação**: Consulte os arquivos em `docs/` para detalhes
