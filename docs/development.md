# Guia de Desenvolvimento - Simple Class

## 🚀 Setup Inicial Atualizado

### 1. Preparação do Ambiente

```bash
# Clone e setup
git clone <repo>
cd simple_class

# Poetry (dependency management)
poetry install
poetry shell

# Verificar estrutura organizada
ls -la scripts/  # Scripts movidos do root
ls -la docs/     # Documentação centralizada
ls -la config/   # Configurações organizadas
```

### 2. Estrutura Reorganizada (2025)

```
simple_class/
├── src/                    # Código principal
│   ├── analisadores/      # Análise e classificação
│   ├── reports/           # Geração de relatórios
│   ├── tools/             # Ferramentas especializadas
│   └── utils/             # Utilitários
├── scripts/               # Scripts organizados (movidos do root)
├── config/                # Configurações centralizadas
├── docs/                  # Documentação (movida do root)
├── tests/                 # Testes organizados
└── data/                  # Dados de entrada/saída
```

## 📝 Scripts Principais Reorganizados

### Interface Principal - `scripts/processar_ouvidorias.py`

```bash
# Mostrar colunas disponíveis
python scripts/processar_ouvidorias.py --mostrar-colunas

# Processamento completo
python scripts/processar_ouvidorias.py "Teor" --completo

# Processamento específico
python scripts/processar_ouvidorias.py "Teor" --area "EDUCACAO"
```

### Pipeline Fireworks AI - `scripts/processar_educacao_fireworks.py`

```bash
# Classificação multilabel avançada
python scripts/processar_educacao_fireworks.py

# Com configurações específicas
FIREWORKS_API_KEY="sua_chave" python scripts/processar_educacao_fireworks.py
```

### Relatórios Completos - `scripts/gerar_relatorio_educacao_completo.py`

```bash
# Relatório com Claude Sonnet 4
python scripts/gerar_relatorio_educacao_completo.py

# Arquivo específico
python scripts/gerar_relatorio_educacao_completo.py --arquivo data/output/classificado.csv
```

### Anonimização - `scripts/anonimiza.py`

```bash
# Anonimização completa
python scripts/anonimiza.py data/input/arquivo.csv

# Apenas pessoas (LGPD compliance)
python scripts/anonymize_persons_only.py data/input/arquivo.csv
```

## 🧪 Testes Reorganizados

### Estrutura de Testes

```bash
# Testes unitários específicos
pytest tests/unit/test_analisadores/
pytest tests/unit/test_reports/
pytest tests/unit/test_tools/

# Testes de integração
pytest tests/integration/

# Cobertura completa
pytest --cov=src tests/
```

### Executar Testes Comparativos

```bash
# Script de comparação (movido para scripts/)
python scripts/run_testes.py

# Análise de performance
python scripts/debug_casos.py
```

## ⚙️ Configuração Centralizada

### Arquivo Principal - `config/settings.yml`

```yaml
# Subtemas por área
subtemas:
  EDUCACAO:
    - "Educação Especial - Falta de Mediador"
    - "Educação Especial - Exclusão e Discriminação"
    - "Alimentação Escolar"
    - "Bullying"
    - "Infraestrutura"
    - "Transporte Escolar"
    - "Matrícula e Transferência"
    - "Recursos Pedagógicos"
    - "Gestão Escolar"
    - "Violência Escolar"

  SAUDE:
    - "Oncologia"
    - "Oftalmologia"
    - "Regulação em Saúde"
    # ... outros 7 subtemas
```

### Definições Contextuais - `config/definicoes_subtemas.yml`

```yaml
EDUCACAO:
  "Educação Especial - Falta de Mediador":
    definicao: "Casos relacionados à ausência ou insuficiência de mediadores..."
    palavras_chave: ["mediador", "profissional de apoio", "acompanhante"]
    exemplos: ["Falta de mediador na escola", "Criança sem acompanhamento"]
```

### Templates Organizados - `config/templates/`

```bash
ls config/templates/
# template_relatorio.txt
# template_relatorio_individual_especializado.txt
# template_relatorio_visao_geral.txt
# template_relatorio_visao_geral_especializado.txt
```

## 🔧 Desenvolvimento de Funcionalidades

### 1. Adicionando Novo Analisador

```python
# src/analisadores/novo_analisador.py
from typing import List, Dict
import pandas as pd

class NovoAnalisador:
    """Novo analisador especializado."""
    
    def __init__(self, config_path: str = "config/settings.yml"):
        self.config = self._load_config(config_path)
    
    def analisar(self, textos: List[str]) -> Dict:
        """Análise principal."""
        # Implementação específica
        pass
```

### 2. Criando Novo Relatório

```python
# src/reports/novo_relatorio.py
from src.utils.config_utils import load_config

class NovoRelatorio:
    """Gerador de novo tipo de relatório."""
    
    def __init__(self):
        self.template_path = "config/templates/novo_template.txt"
    
    def gerar(self, dados: pd.DataFrame) -> str:
        """Gera relatório específico."""
        # Implementação
        pass
```

### 3. Adicionando Nova Tool

```python
# src/tools/nova_tool.py
from typing import Dict, Any

class NovaTool:
    """Nova ferramenta especializada."""
    
    def processar(self, dados: Dict[str, Any]) -> Dict[str, Any]:
        """Processamento principal."""
        # Implementação
        pass
```

## 📊 Workflow de Desenvolvimento

### 1. Feature Branch

```bash
# Nova feature
git checkout -b feature/nova-funcionalidade

# Desenvolvimento
# - Implementar em src/
# - Adicionar testes em tests/
# - Atualizar configuração em config/
# - Criar script em scripts/ se necessário

# Testes
pytest tests/ --cov=src

# Commit e push
git add .
git commit -m "feat: nova funcionalidade"
git push origin feature/nova-funcionalidade
```

### 2. Scripts de Teste

```bash
# Testar nova implementação
python scripts/run_testes.py --novo-analisador

# Comparar performance
python scripts/comparar_versoes.py --baseline main --feature nova-funcionalidade

# Validar dados
python scripts/test_anonymization_integration.py
```

### 3. Integração

```bash
# Merge request
# - Revisão de código
# - Testes passando
# - Documentação atualizada

# Após merge
git checkout main
git pull origin main
```

## 🔍 Debug e Monitoramento

### 1. Logs Estruturados

```python
import logging

# Configuração central
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)
```

### 2. Scripts de Debug

```bash
# Debug específico (movido para scripts/)
python scripts/debug_resposta.py --arquivo data/input/problema.csv

# Análise de casos específicos
python scripts/debug_casos.py --ids 1,2,3

# Verificar configuração
python scripts/test_anonymization_input_data.py
```

### 3. Performance Monitoring

```python
# Métricas implementadas
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
            result = func(*args, **kwargs)
        end = time.time()
        logger.info(f"{func.__name__} executado em {end-start:.2f}s")
            return result
    return wrapper
```

## 🎯 Boas Práticas Implementadas

### 1. Organização de Código

```python
# Estrutura modular
src/
├── analisadores/          # Lógica de análise
├── reports/               # Geração relatórios
├── tools/                 # Ferramentas especializadas
└── utils/                 # Utilitários compartilhados

# Scripts organizados por função
scripts/
├── processamento/         # Scripts principais
├── relatorios/           # Geração relatórios
├── anonimizacao/         # Proteção dados
└── utilitarios/          # Scripts auxiliares
   ```

### 2. Configuração Centralizada

```yaml
# config/settings.yml - Fonte única verdade
api:
  fireworks:
    model: "accounts/fireworks/models/llama-v3p1-8b-instruct"
    max_tokens: 200
    temperature: 0.1
  
performance:
  rate_limit_delay: 1.0
  batch_size: 10
  timeout: 30
```

### 3. Testes Organizados

```python
# tests/unit/test_analisadores/test_multilabel.py
import pytest
from src.analisadores.analisador_multilabel import AnalisadorMultilabel

class TestAnalisadorMultilabel:
    def test_classificacao_basica(self):
        # Teste unitário específico
        pass
    
    def test_rate_limiting(self):
        # Teste de performance
        pass
```

### 4. Documentation Driven

```python
def classificar_multilabel(
    texto: str, 
    area: str = "EDUCACAO", 
    max_subtemas: int = 3
) -> Dict[str, List[str]]:
    """
    Classifica texto em múltiplos subtemas.
    
    Args:
        texto: Texto para classificação
        area: Área temática (EDUCACAO, SAUDE)
        max_subtemas: Máximo de subtemas retornados
        
    Returns:
        Dict com subtemas identificados e confiança
        
    Example:
        >>> resultado = classificar_multilabel("Falta mediador na escola")
        >>> resultado['subtemas']
        ['Educação Especial - Falta de Mediador']
    """
    pass
```

## 🚀 Próximos Passos de Desenvolvimento

### 1. Evolução para LangGraph

```python
# Estrutura preparada em src/
src/
├── nodes/                 # Nós LangGraph (preparado)
├── workflows/             # Workflows (preparado)  
├── state/                 # Estado compartilhado
└── core/                  # Núcleo sistema
```

### 2. API REST com FastAPI

```python
# api/main.py (futuro)
from fastapi import FastAPI
from src.analisadores import AnalisadorMultilabel

app = FastAPI(title="Simple Class API")

@app.post("/classificar")
async def classificar_texto(texto: str, area: str = "EDUCACAO"):
    analisador = AnalisadorMultilabel()
    return analisador.classificar(texto, area)
```

### 3. Dashboard Interativo

```python
# dashboard/app.py (futuro)
import streamlit as st
from src.reports.gerador_relatorio_multilabel import GeradorRelatorioMultilabel

st.title("Simple Class Dashboard")

uploaded_file = st.file_uploader("Upload CSV")
if uploaded_file:
    # Processamento e visualização
    pass
```

## 📚 Documentação Reorganizada

### Estrutura em `docs/`

```
docs/
├── PLANNING.md            # Movido do root
├── TASK.md               # Movido do root  
├── EXPLICACAO.md         # Movido do root
├── EXEMPLOS_USO.md       # Movido do root
├── architecture.md       # Arquitetura atualizada
├── development.md        # Este guia
├── FAQ.md                # Perguntas frequentes
├── PDF_ANONYMIZATION.md  # Anonimização PDFs
└── ANONYMIZATION_INTEGRATION.md  # Integração anonimização
```

### Manutenção da Documentação

```bash
# Sempre atualizar após mudanças estruturais
# 1. README.md - Visão geral
# 2. architecture.md - Estrutura técnica  
# 3. development.md - Guia desenvolvimento
# 4. Documentação específica em docs/
```

---

**Simple Class Development** - Sistema modular, testável e bem documentado para análise inteligente de ouvidorias.
