Checklist de Engenharia: Implementação do RAG com LlamaIndex
Este documento detalha as tarefas necessárias para a implementação de ponta a ponta do sistema RAG, conforme o plano de implantação. Ele é destinado à equipe de engenharia e serve como um guia para o desenvolvimento e a entrega do projeto.

Fase 0: Configuração do Projeto e Ambiente
Critério de Aceitação da Fase: Um ambiente de desenvolvimento local funcional, com todas as dependências instaladas, conectado a um Vector DB e pronto para o desenvolvimento dos pipelines.

[ ] Tarefa 0.1: Estruturar o Projeto Python

Detalhe da Tarefa: Iniciar o projeto usando Poetry (ou pipenv). Definir a estrutura de pastas padrão (src/, tests/, scripts/, data/). Criar o arquivo pyproject.toml com as informações básicas do projeto.

Critério de Aceitação: O comando poetry install executa com sucesso, criando um ambiente virtual funcional. A estrutura de diretórios está criada no repositório Git.

[ ] Tarefa 0.2: Instalar Dependências Base

Detalhe da Tarefa: Adicionar as bibliotecas principais ao pyproject.toml: llama-index, fastapi, uvicorn, python-dotenv, cohere, openai (ou o SDK do LLM escolhido), e a biblioteca do Vector DB (chromadb-client ou weaviate-client).

Critério de Aceitação: Todas as dependências são instaladas corretamente via poetry install. A importação de cada uma delas em um script de teste funciona sem erros.

[ ] Tarefa 0.3: Configurar Variáveis de Ambiente

Detalhe da Tarefa: Criar um arquivo .env para armazenar chaves de API (OpenAI/Cohere, etc.) e configurações (endereço do Vector DB). Adicionar um arquivo .env.example ao Git para documentar as variáveis necessárias. Garantir que o .env esteja no .gitignore.

Critério de Aceitação: Um script Python consegue carregar as variáveis do arquivo .env e utilizá-las para inicializar os clientes das APIs.

[ ] Tarefa 0.4: Subir o Vector DB Localmente

Detalhe da Tarefa: Criar um arquivo docker-compose.yml para subir uma instância local do Vector DB escolhido (ChromaDB ou Weaviate).

Critério de Aceitação: O comando docker-compose up -d inicia o container com sucesso. Um script de teste em Python consegue se conectar à instância do Vector DB rodando localmente.

Fase 1: Pipeline de Indexação (Offline)
Critério de Aceitação da Fase: Um script executável (python -m scripts.run_indexing) que processa todos os PDFs em uma pasta, gera embeddings e os armazena de forma persistente no Vector DB.

[ ] Tarefa 1.1: Implementar Módulo de Carregamento e Parsing

Detalhe da Tarefa: Criar um módulo em src/ para encapsular a lógica de carregamento e parsing. Implementar o SimpleDirectoryReader e o SentenceWindowNodeParser. Configurar os parâmetros do parser (tamanho da janela, etc.).

Critério de Aceitação: Uma função recebe o caminho de uma pasta, lê os documentos e retorna uma lista de Nodes processados pelo SentenceWindowNodeParser. Testes unitários validam que o número de nós e o conteúdo dos metadados (janela) estão corretos.

[ ] Tarefa 1.2: Configurar o ServiceContext

Detalhe da Tarefa: Criar um ServiceContext global (ou uma função factory para criá-lo) que configure o LLM (para metadados, se necessário), o modelo de embedding (Cohere-embed ou bge-m3) e o NodeParser.

Critério de Aceitação: O ServiceContext é inicializado com sucesso, carregando as configurações e chaves de API do ambiente.

[ ] Tarefa 1.3: Implementar o Script de Indexação

Detalhe da Tarefa: Criar um script em scripts/run_indexing.py que orquestra todo o processo: carrega documentos, gera os nós, conecta-se ao Vector DB e constrói o VectorStoreIndex, garantindo que os dados sejam persistidos.

Critério de Aceitação: Após a execução do script, uma consulta direta ao Vector DB mostra que os vetores e os metadados (nome do arquivo, texto da sentença) foram armazenados corretamente. A re-execução do script não deve duplicar os dados (se aplicável).

Fase 2: Pipeline de Consulta (Online) com FastAPI
Critério de Aceitação da Fase: Uma API FastAPI rodando localmente com um endpoint /query que aceita uma pergunta, executa a lógica RAG e retorna uma resposta em streaming com as citações.

[ ] Tarefa 2.1: Carregar Índice Existente

Detalhe da Tarefa: Implementar a lógica para carregar o índice a partir do Vector DB, em vez de reconstruí-lo. O VectorStoreIndex.from_vector_store(...) deve ser usado.

Critério de Aceitação: A aplicação FastAPI, ao iniciar, conecta-se ao Vector DB e carrega o índice em memória sem erros.

[ ] Tarefa 2.2: Implementar Retriever com Re-ranking

Detalhe da Tarefa: Configurar o VectorIndexRetriever para buscar um número maior de candidatos (ex: similarity_top_k=20). Adicionar o CohereRerank ao pipeline, configurando-o para retornar os top_n=3 resultados mais relevantes.

Critério de Aceitação: Um teste de integração mostra que, para uma dada pergunta, o re-ranker reordena os resultados do retriever e filtra os mais relevantes, passando um contexto de maior qualidade para a próxima etapa.

[ ] Tarefa 2.3: Construir o QueryEngine e a Rota da API

Detalhe da Tarefa: Montar o QueryEngine com o retriever reclassificado. Criar uma rota assíncrona /query no FastAPI que recebe a pergunta do usuário e chama query_engine.astream_response().

Critério de Aceitação: Ao fazer uma requisição para a API (via curl ou Postman), a resposta chega em pedaços (streaming) e não de uma só vez.

[ ] Tarefa 2.4: Formatar a Resposta com Citações

Detalhe da Tarefa: Na rota da API, iterar sobre a StreamingResponse. Para cada pedaço de texto, verificar se há novos source_nodes e formatá-los em uma estrutura JSON padronizada (ex: {"token": "...", "sources": [...]}).

Critério de Aceitação: O JSON retornado pela API contém tanto o texto da resposta quanto uma lista de fontes, incluindo o nome do arquivo e o texto exato da sentença de origem.

Fase 3: Implantação e MLOps
Critério de Aceitação da Fase: A aplicação está empacotada em um container Docker, é facilmente executável via docker-compose e expõe documentação e logs para monitoramento.

[ ] Tarefa 3.1: Criar o Dockerfile da Aplicação

Detalhe da Tarefa: Escrever um Dockerfile multi-stage para a aplicação FastAPI. O primeiro stage instala dependências com Poetry, e o segundo copia apenas o ambiente virtual e o código-fonte para uma imagem final mais leve.

Critério de Aceitação: O comando docker build . -t rag-api constrói a imagem sem erros.

[ ] Tarefa 3.2: Orquestrar com docker-compose

Detalhe da Tarefa: Atualizar o docker-compose.yml para incluir o serviço da API (rag-api) junto com o serviço do Vector DB. Configurar a rede para que a API possa se comunicar com o banco.

Critério de Aceitação: O comando docker-compose up sobe ambos os containers, e a API consegue se conectar ao Vector DB e responder a requisições.

[ ] Tarefa 3.3: Configurar Logging Estruturado

Detalhe da Tarefa: Integrar uma biblioteca de logging (como loguru) para formatar os logs em JSON. Registrar informações importantes, como a pergunta recebida, os nós recuperados e a latência da consulta.

Critério de Aceitação: Ao executar a aplicação, os logs aparecem no console do Docker em um formato JSON estruturado, facilitando a análise.

[ ] Tarefa 3.4: Documentar a API

Detalhe da Tarefa: Adicionar metadados (título, descrição, modelos Pydantic) à aplicação FastAPI para enriquecer a documentação automática.

Critério de Aceitação: Ao acessar a rota /docs da API, uma página interativa do Swagger UI é exibida, com a documentação clara do endpoint /query, incluindo os schemas de request e response.