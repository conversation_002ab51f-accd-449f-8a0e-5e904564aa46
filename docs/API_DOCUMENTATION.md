# Simple Class API - Documentação

## 📚 Visão Geral

A Simple Class API é uma API completa para processamento de texto e documentos com foco em políticas públicas. Oferece funcionalidades de classificação automática, anonimização de dados pessoais e geração de relatórios analíticos.

## 🚀 Iniciando

### Pré-requisitos

- Python 3.11+
- Poetry (gerenciador de dependências)
- Chaves de API para serviços externos (opcional para desenvolvimento)

### Instalação

```bash
# Clone o repositório
git clone https://github.com/daniribeiroBR/simple_class.git
cd simple_class

# Instale as dependências
poetry install

# Configure as variáveis de ambiente (opcional)
cp .env.example .env
```

### Executando a API

```bash
# Modo desenvolvimento com documentação
poetry run python scripts/start_api_docs.py --reload

# Ou usando uvicorn diretamente
poetry run uvicorn src.api.main:app --reload --host 0.0.0.0 --port 8000
```

## 📖 Documentação Interativa

Após iniciar a API, acesse:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## 🔐 Autenticação

### Credenciais de Teste

Para desenvolvimento, use estas credenciais:

```json
{
  "email": "<EMAIL>",
  "password": "test123"
}
```

### Fluxo de Autenticação

1. **Login**: `POST /api/v1/auth/login`
2. **Obter Token**: Receba o `access_token` na resposta
3. **Usar Token**: Inclua no header: `Authorization: Bearer <token>`

### Exemplo de Uso

```bash
# 1. Fazer login
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "test123"}'

# 2. Usar o token retornado
curl -X GET "http://localhost:8000/api/v1/auth/me" \
  -H "Authorization: Bearer <seu-token-aqui>"
```

## 🏷️ Classificação de Texto

### Classificação Multilabel

Identifica até 3 subtemas por texto:

```bash
curl -X POST "http://localhost:8000/api/v1/classification/multilabel" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "A merenda escolar está com problemas na escola municipal",
    "area": "EDUCACAO",
    "max_subtemas": 3
  }'
```

### Áreas Suportadas

- **EDUCACAO**: Alimentação escolar, infraestrutura, transporte, etc.
- **SAUDE**: Atenção básica, emergência, medicamentos, etc.
- **MEIO_AMBIENTE**: Poluição, desmatamento, saneamento, etc.

### Classificação Unilabel

Para dimensões específicas (TIPO_UNIDADE, CRE):

```bash
curl -X POST "http://localhost:8000/api/v1/classification/unilabel" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Problema na escola municipal da Tijuca",
    "dimension": "TIPO_UNIDADE"
  }'
```

## 🔒 Anonimização

### Anonimização de Texto

```bash
curl -X POST "http://localhost:8000/api/v1/anonymization/text" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "João Silva, CPF 123.456.789-00, telefone (21) 99999-9999",
    "language": "pt",
    "entities": ["PERSON", "CPF", "PHONE"]
  }'
```

### Entidades Detectadas

- **PERSON**: Nomes de pessoas
- **CPF**: Números de CPF
- **PHONE**: Números de telefone
- **EMAIL**: Endereços de email
- **ENDEREÇO**: Endereços completos

## 📁 Gerenciamento de Arquivos

### Upload de Arquivo

```bash
curl -X POST "http://localhost:8000/api/v1/files/upload" \
  -H "Authorization: Bearer <token>" \
  -F "file=@dados.csv" \
  -F 'metadata={"description": "Dados de teste", "tags": ["educacao"]}'
```

### Formatos Suportados

- CSV (text/csv)
- PDF (application/pdf)
- Excel (xlsx, xls)

### Limites

- Tamanho máximo: 50MB
- Rate limit: 100 requests/hora

## 📊 Relatórios

### Relatório Individual

```bash
curl -X POST "http://localhost:8000/api/v1/reports/individual" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "file_id": "seu-arquivo-id",
    "subtema": "ALIMENTACAO_ESCOLAR",
    "area": "EDUCACAO"
  }'
```

### Relatório de Visão Geral

```bash
curl -X POST "http://localhost:8000/api/v1/reports/overview" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "file_id": "seu-arquivo-id",
    "area": "EDUCACAO"
  }'
```

## 🔧 Configuração

### Variáveis de Ambiente

```bash
# API Configuration
API_SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=60

# Supabase
SUPABASE_URL=your-supabase-url
SUPABASE_KEY=your-supabase-key

# External APIs
TOGETHER_API_KEY=your-together-ai-key
ANTHROPIC_API_KEY=your-anthropic-key
FIREWORKS_API_KEY=your-fireworks-key
```

## 📈 Performance

### Tempos Médios

- **Classificação**: 2-5 segundos por texto
- **Anonimização**: 0.5-2 segundos por texto
- **Relatórios**: 10-30 segundos
- **Upload**: Depende do tamanho do arquivo

### Rate Limits

- **Geral**: 100 requests/hora por usuário
- **Upload**: 10 arquivos/hora
- **Relatórios**: 5 relatórios/hora

## 🐛 Tratamento de Erros

### Códigos de Status

- **200**: Sucesso
- **400**: Dados inválidos
- **401**: Não autenticado
- **403**: Acesso negado
- **404**: Recurso não encontrado
- **413**: Arquivo muito grande
- **422**: Erro de validação
- **429**: Rate limit excedido
- **500**: Erro interno do servidor

### Formato de Erro

```json
{
  "detail": "Descrição do erro",
  "type": "error_type",
  "code": "ERROR_CODE"
}
```

## 🧪 Testes

### Executando Testes

```bash
# Todos os testes
poetry run pytest

# Testes específicos
poetry run pytest tests/test_auth.py
poetry run pytest tests/test_classification.py

# Com cobertura
poetry run pytest --cov=src --cov-report=html
```

### Credenciais de Teste

Os testes usam credenciais hardcoded para desenvolvimento:
- Email: `<EMAIL>`
- Password: `test123`

## 📞 Suporte

- **GitHub**: https://github.com/daniribeiroBR/simple_class
- **Issues**: https://github.com/daniribeiroBR/simple_class/issues
- **Email**: <EMAIL>

## 📄 Licença

MIT License - veja o arquivo [LICENSE](../LICENSE) para detalhes.
