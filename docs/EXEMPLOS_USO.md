# 📖 Exemplos Práticos de Uso

Este documento apresenta exemplos práticos de como usar o sistema de classificação de relatos após as modificações para usar o arquivo fixo `ouvidorias.csv`.

## 🎯 Visão Geral das Mudanças

### An<PERSON> vs <PERSON><PERSON><PERSON>

**ANTES:**
```bash
# Era necessário especificar o arquivo de entrada
python src/analisadores/analisador_relatos.py data/input/meu_arquivo.csv data/output/resultado.csv
```

**DEPOIS:**
```bash
# Agora usa arquivo fixo e especifica apenas a coluna
python src/analisadores/analisador_relatos.py "Teor"
```

### Arquivo de Entrada Fixo

O sistema agora sempre usa: `data/input/ouvidorias.csv`

## 🚀 Exemplos Práticos

### 1. Descobrir Colunas Disponíveis

```bash
python3 scripts/processar_ouvidorias.py --mostrar-colunas
```

**Saída esperada:**
```
📋 COLUNAS DISPONÍVEIS NO ARQUIVO:
========================================
 1. MPRJ
 2. N. Integra
 3. Data da ouvidoria
 4. Data na PJ
 5. Noticiante
 6. Noticiado
 7. Tema
 8. Subtema
 9. Teor
10. Providência
11. Juntada em
12. Observação
========================================
```

### 2. Análise Individual (Mais Precisa)

```bash
# Analisar a coluna "Teor"
python3 scripts/processar_ouvidorias.py "Teor" --individual

# Analisar a coluna "Observação"
python3 scripts/processar_ouvidorias.py "Observação" --individual
```

### 3. Análise Completa (Todos os Métodos)

```bash
# Executa individual, lotes, embeddings, comparação, relatório e anonimização
python3 scripts/processar_ouvidorias.py "Teor" --completo
```

### 4. Processos Específicos

```bash
# Apenas comparação entre métodos
python3 scripts/processar_ouvidorias.py "Teor" --comparacao

# Comparação + Relatório
python3 scripts/processar_ouvidorias.py "Teor" --comparacao --relatorio

# Apenas anonimização
python3 scripts/processar_ouvidorias.py "Teor" --anonimizacao
```

## 🔧 Scripts Individuais

### Análise Individual

```bash
# Usar diretamente o analisador individual
python3 src/analisadores/analisador_relatos.py "Teor"

# Com arquivo de saída personalizado
python3 src/analisadores/analisador_relatos.py "Teor" --output_csv "data/output/minha_analise.csv"
```

### Comparação de Métodos

```bash
# Comparar todos os métodos
python3 src/analisadores/run_comparacao.py "Teor"

# Com parâmetros personalizados
python3 src/analisadores/run_comparacao.py "Teor" --tamanho_lote 10 --limiar 0.3
```

### Anonimização

```bash
# Anonimizar a coluna "Teor"
python3 scripts/anonimiza.py "Teor"

# Com arquivo de saída personalizado
python3 scripts/anonimiza.py "Teor" --output "data/output/dados_anonimizados.csv"

# CSV com delimitador diferente
python3 scripts/anonimiza.py "Teor" --delimiter ";"
```

## 📊 Interpretando os Resultados

### Arquivos de Saída

Após executar as análises, você encontrará em `data/output/`:

```
data/output/
├── relatos_classificados_individual.csv     # Análise individual
├── relatos_classificados_lote.csv           # Análise em lotes  
├── relatos_classificados_embeddings.csv     # Análise por embeddings
├── comparacao_metodos_20250109_143022.csv   # Comparação entre métodos
├── relatorio_tatico.md                      # Relatório em markdown
└── ouvidorias_anonimizado.csv              # Dados anonimizados
```

### Estrutura dos CSVs Classificados

Cada arquivo de resultado contém:
- **Todas as colunas originais**
- **REFERE_ASSUNTO**: 1 (positivo) ou 0 (negativo)
- **TEXTO_ANALISADO**: Cópia da coluna analisada

### Relatório de Comparação

O arquivo `comparacao_metodos_*.csv` contém:
```csv
Método,Total_Registros,Casos_Positivos,Percentual_Positivos,Arquivo_Resultado
Individual,172,45,26.16,relatos_classificados_individual.csv
Lote,172,42,24.42,relatos_classificados_lote.csv
Embeddings,172,38,22.09,relatos_classificados_embeddings.csv
```

## 🎛️ Configurações

### config/config.yml

```yaml
ASSUNTO: "oftalmologia"                                    # Tema para análise
MODEL: "meta-llama/Llama-3.2-3B-Instruct-Turbo"         # Modelo para análise
REPORT_MODEL: "claude-3-5-sonnet-20241022"               # Modelo para relatórios
EMBEDDING_MODEL: "sentence-transformers/all-MiniLM-L6-v2" # Modelo de embeddings
```

### .env

```bash
TOGETHER_API_KEY=sua_chave_together_ai
ANTHROPIC_API_KEY=sua_chave_anthropic
```

## 🔍 Casos de Uso Específicos

### Cenário 1: Análise Rápida de Uma Coluna

```bash
# Para uma análise rápida e verificar se vale a pena prosseguir
python3 scripts/processar_ouvidorias.py "Teor" --individual
```

### Cenário 2: Análise Completa com Comparação

```bash
# Para ter certeza da qualidade e comparar métodos
python3 scripts/processar_ouvidorias.py "Teor" --comparacao --relatorio
```

### Cenário 3: Processamento em Lote para Múltiplas Colunas

```bash
# Analisar diferentes colunas sequencialmente
python3 scripts/processar_ouvidorias.py "Teor" --individual
python3 scripts/processar_ouvidorias.py "Observação" --individual
python3 scripts/processar_ouvidorias.py "Providência" --individual
```

### Cenário 4: Anonimização para Compartilhamento

```bash
# Primeiro classificar, depois anonimizar
python3 scripts/processar_ouvidorias.py "Teor" --individual
python3 scripts/processar_ouvidorias.py "Teor" --anonimizacao
```

## ⚡ Dicas de Performance

### Para Arquivos Grandes (>1000 registros)

```bash
# Use embeddings para análise inicial (mais rápido)
python3 src/analisadores/analisador_embeddings.py "Teor" --limiar 0.2

# Depois refine com método individual apenas nos casos positivos
```

### Para Análise Precisa

```bash
# Use comparação para validar resultados
python3 scripts/processar_ouvidorias.py "Teor" --comparacao
```

## 🚨 Troubleshooting

### Erro: "Coluna não encontrada"

```bash
# Sempre verifique as colunas primeiro
python3 scripts/processar_ouvidorias.py --mostrar-colunas

# Use o nome exato da coluna (sensível a maiúsculas/minúsculas)
python3 scripts/processar_ouvidorias.py "Teor" --individual  # ✅ Correto
python3 scripts/processar_ouvidorias.py "teor" --individual  # ❌ Incorreto
```

### Erro: "Arquivo não encontrado"

```bash
# Certifique-se de que o arquivo está no local correto
ls -la data/input/ouvidorias.csv

# O arquivo deve estar exatamente em data/input/ouvidorias.csv
```

### Timeout/Demora Excessiva

```bash
# Para arquivos muito grandes, execute processos separadamente
python3 src/analisadores/analisador_relatos.py "Teor"  # Somente individual
# Em vez de --completo
```

## 📈 Fluxo Recomendado

1. **Explorar dados**:
   ```bash
   python3 scripts/processar_ouvidorias.py --mostrar-colunas
   ```

2. **Teste inicial**:
   ```bash
   python3 scripts/processar_ouvidorias.py "Teor" --individual
   ```

3. **Análise completa**:
   ```bash
   python3 scripts/processar_ouvidorias.py "Teor" --comparacao --relatorio
   ```

4. **Anonimização (se necessário)**:
   ```bash
   python3 scripts/processar_ouvidorias.py "Teor" --anonimizacao
   ```

## 🎯 Próximos Passos

Após executar as análises, você pode:

1. **Revisar os resultados** em `data/output/`
2. **Ajustar configurações** em `config/config.yml` se necessário
3. **Analisar diferentes colunas** usando os mesmos comandos
4. **Gerar relatórios** para diferentes assuntos modificando a configuração 

# Exemplos de Uso - Simple Class

## 🔍 Análise Individual (Binária)

Classifica cada relato como 1 (refere-se ao assunto) ou 0 (não se refere):

```bash
# Ver colunas disponíveis
poetry run python scripts/processar_ouvidorias.py --mostrar-colunas

# Executar análise individual
poetry run python src/analisadores/analisador_relatos.py "Teor"
```

**Resultado:**
```csv
Teor,REFERE_ASSUNTO,TEXTO_ANALISADO
"Paciente aguarda consulta oftalmológica",1,"Paciente aguarda consulta oftalmológica"
"Problema com merenda escolar",0,"Problema com merenda escolar"
```

## 🏷️ Análise Multilabel (Múltiplos Subtemas)

Classifica cada relato em até 3 subtemas específicos da área escolhida:

### Exemplo 1: Área de Saúde
```bash
poetry run python src/analisadores/analisador_multilabel.py "Teor" --area SAUDE
```

**Entrada:**
```
"Paciente com catarata aguarda cirurgia oftalmológica no hospital"
```

**Resultado:**
```csv
SUBTEMAS_IDENTIFICADOS,SUBTEMA_OFTALMOLOGIA,SUBTEMA_DIAGNOSE
"Oftalmologia, Diagnose",1,1
```

### Exemplo 2: Área de Educação
```bash
poetry run python src/analisadores/analisador_multilabel.py "Teor" --area EDUCACAO
```

**Entrada:**
```
"Falta de transporte escolar para crianças da educação infantil"
```

**Resultado:**
```csv
SUBTEMAS_IDENTIFICADOS,SUBTEMA_EDUCACAO_INFANTIL,SUBTEMA_TRANSPORTE_ESCOLAR
"Educação Infantil, Transporte Escolar",1,1
```

### Exemplo 3: Área de Meio Ambiente
```bash
poetry run python src/analisadores/analisador_multilabel.py "Teor" --area MEIO_AMBIENTE
```

**Entrada:**
```
"Poluição do rio causando problemas na fauna local"
```

**Resultado:**
```csv
SUBTEMAS_IDENTIFICADOS,SUBTEMA_POLUICAO_HIDRICA,SUBTEMA_FAUNA_E_FLORA
"Poluição Hídrica, Fauna e Flora",1,1
```

## 📊 Comparação de Métodos

Executa todos os métodos tradicionais (individual, lote, embeddings) e compara:

```bash
poetry run python src/analisadores/run_comparacao.py "Teor"
```

**Resultado:**
```csv
Método,Total_Registros,Casos_Positivos,Percentual_Positivos
Individual,1000,150,15.0
Lote,1000,145,14.5
Embeddings,1000,160,16.0
```

## 🔄 Processamento Completo

Executa todos os processos em sequência:

```bash
# Análise completa tradicional
poetry run python scripts/processar_ouvidorias.py "Teor" --completo

# Apenas análise individual + relatório
poetry run python scripts/processar_ouvidorias.py "Teor" --individual --relatorio
```

## 📈 Análise de Resultados Multilabel

### Estatísticas por Subtema
O analisador multilabel gera estatísticas detalhadas:

```
PROCESSAMENTO MULTILABEL CONCLUÍDO
====================================
Área analisada: SAUDE
Total de registros processados: 500
Registros com subtemas identificados: 75 (15.0%)

Contagem por subtema:
----------------------------------------
Oftalmologia                   :   25 ( 5.0%)
Diagnose (laboratório e imagem):   18 ( 3.6%)
Oncologia                      :   12 ( 2.4%)
Regulação em Saúde            :   10 ( 2.0%)
Auditoria                      :    8 ( 1.6%)
```

### Colunas de Saída
O arquivo CSV resultante contém:
- **SUBTEMAS_IDENTIFICADOS**: Lista de subtemas separados por vírgula
- **AREA_CLASSIFICACAO**: Área analisada (SAUDE, EDUCACAO, etc.)
- **SUBTEMA_[NOME]**: Coluna binária (0/1) para cada subtema
- **TEXTO_ANALISADO**: Texto original analisado

## 🎯 Casos de Uso Práticos

### 1. Ouvidoria de Saúde
```bash
# Classificar relatos de saúde em subtemas específicos
poetry run python src/analisadores/analisador_multilabel.py "Teor" --area SAUDE --output saude_multilabel.csv
```

### 2. Secretaria de Educação
```bash
# Analisar demandas educacionais por subtema
poetry run python src/analisadores/analisador_multilabel.py "Descricao" --area EDUCACAO --output educacao_multilabel.csv
```

### 3. Órgão Ambiental
```bash
# Categorizar denúncias ambientais
poetry run python src/analisadores/analisador_multilabel.py "Relato" --area MEIO_AMBIENTE --output ambiente_multilabel.csv
```

## 🔒 Anonimização

Remove informações sensíveis dos dados:

```bash
# Anonimizar antes da análise
poetry run python scripts/anonimiza.py "Teor"

# Depois executar análise multilabel
poetry run python src/analisadores/analisador_multilabel.py "Teor" --area SAUDE
```

## 📄 Geração de Relatórios

Após a classificação, gere relatórios analíticos:

```bash
# Relatório geral
poetry run python scripts/run_relatorio.py

# Relatório de visão geral
poetry run python scripts/run_relatorio_visao_geral.py
```

## ⚙️ Configurações Avançadas

### Personalizar Subtemas
Edite `config/config.yml` para adicionar novos subtemas:

```yaml
SUBTEMAS_SAUDE:
  - "Oncologia"
  - "Oftalmologia"
  - "Novo Subtema"  # Adicionar aqui

SUBTEMAS_NOVA_AREA:  # Nova área
  - "Subtema 1"
  - "Subtema 2"
```

### Ajustar Modelo LLM
```yaml
LLM_MODEL: "meta-llama/Llama-4-Scout-17B-16E-Instruct"  # Modelo mais avançado
```

## 🚨 Troubleshooting

### Problema: Nenhum subtema identificado
**Causa**: Relatos não relacionados à área escolhida
**Solução**: Verificar se a área está correta ou ajustar os subtemas

### Problema: Muitos subtemas por relato
**Causa**: Relatos muito abrangentes
**Solução**: O sistema limita automaticamente a 3 subtemas mais relevantes

### Problema: Erro de API
**Causa**: Chave API inválida ou rate limit
**Solução**: Verificar `.env` e aguardar antes de nova tentativa 