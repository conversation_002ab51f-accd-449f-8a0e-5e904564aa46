# 📄🛡️ PDF Anonymization Tool

## Overview

O **PDFAnonymizationTool** é um node/tool LangGraph que combina extração inteligente de texto de PDFs com anonimização brasileira usando o framework Presidio existente. O tool foi projetado para preparar documentos PDF para processamento por LLMs, removendo automaticamente headers/rodapés e anonimizando dados pessoais.

## 🎯 Funcionalidades

### ✅ Extração de Texto Limpo
- **PyMuPDF** para extração eficiente e precisa
- **Remoção automática** de headers e rodapés por heurística de frequência
- **Preservação da estrutura** do documento para LLMs
- **Filtros inteligentes** para blocos de texto muito pequenos

### ✅ Anonimização Brasileira
- **Integração completa** com o framework Presidio existente
- **Recognizers brasileiros** (CPF, escolas, endereços)
- **Compatibilidade** com todos os tipos de entidades já configurados
- **Metadados detalhados** sobre entidades encontradas

### ✅ Compatibilidade LangGraph
- **Tool function** pronta para uso em workflows
- **Estrutura de retorno** padronizada
- **Tratamento de erros** robusto
- **Logging** integrado

## 🚀 Instalação

### Dependências

O tool requer PyMuPDF, que foi adicionado ao `pyproject.toml`:

```toml
# PDF processing
pymupdf = "^1.24.0"
```

### Instalação via Poetry

```bash
poetry install
```

## 📖 Uso Básico

### Importação

```python
from src.tools.pdf_anonymization_tool import PDFAnonymizationTool, create_pdf_anonymization_tool
```

### Uso Direto da Classe

```python
# Inicializar tool
tool = PDFAnonymizationTool()

# Apenas extração de texto limpo
extraction_result = tool.extract_clean_text("documento.pdf")
print(f"Texto extraído: {extraction_result['text'][:200]}...")

# Anonimização completa
anonymization_result = tool.anonymize_pdf("documento.pdf")
print(f"Texto anonimizado: {anonymization_result['anonymized_text'][:200]}...")
```

### Uso como LangGraph Tool

```python
# Criar tool function
pdf_tool = create_pdf_anonymization_tool()

# Usar em workflow LangGraph
result = pdf_tool("documento.pdf", language="pt")

if result["success"]:
    print(f"✅ PDF processado com sucesso!")
    print(f"📄 Páginas: {result['extraction_metadata']['pages']}")
    print(f"🔒 Entidades anonimizadas: {result['anonymization_metadata']['entities_count']}")
```

## ⚙️ Configuração

### Parâmetros de Inicialização

```python
tool = PDFAnonymizationTool(
    header_footer_threshold=0.3,  # Frequência mínima para considerar header/footer (0-1)
    min_text_length=10,           # Tamanho mínimo de blocos de texto
    preserve_structure=True       # Preservar estrutura de parágrafos
)
```

### Parâmetros Explicados

- **`header_footer_threshold`**: Frequência mínima (0-1) para que um texto seja considerado header ou footer. Valor 0.3 significa que o texto deve aparecer em pelo menos 30% das páginas.

- **`min_text_length`**: Tamanho mínimo em caracteres para que um bloco de texto seja considerado. Filtra elementos muito pequenos como números de página.

- **`preserve_structure`**: Se `True`, mantém quebras de linha entre parágrafos. Se `False`, junta tudo em texto contínuo.

## 📊 Estrutura de Retorno

### Extração de Texto (`extract_clean_text`)

```python
{
    "success": True,
    "text": "Texto limpo extraído do PDF...",
    "pages": 5,
    "headers_removed": ["Cabeçalho Padrão", "Header Comum"],
    "footers_removed": ["Página 1", "Rodapé Padrão"],
    "error": None
}
```

### Anonimização Completa (`anonymize_pdf`)

```python
{
    "success": True,
    "error": None,
    "original_text": "João Silva mora na Rua das Flores...",
    "anonymized_text": "<PERSON> mora na <ENDEREÇO>...",
    "extraction_metadata": {
        "pages": 5,
        "headers_removed": ["Cabeçalho"],
        "footers_removed": ["Rodapé"]
    },
    "anonymization_metadata": {
        "entities_found": [
            {
                "entity_type": "PERSON",
                "start": 0,
                "end": 10,
                "score": 0.85,
                "text": "João Silva"
            }
        ],
        "entities_count": 3
    }
}
```

## 🧪 Testes

### Script de Teste

```bash
# Teste completo
python scripts/test_pdf_anonymization.py documento.pdf

# Apenas extração
python scripts/test_pdf_anonymization.py documento.pdf --extract-only

# Salvar resultado
python scripts/test_pdf_anonymization.py documento.pdf --save
```

### Testes Unitários

```bash
# Executar todos os testes
pytest tests/test_pdf_anonymization_tool.py -v

# Teste específico
pytest tests/test_pdf_anonymization_tool.py::TestPDFAnonymizationTool::test_extract_clean_text_success -v
```

## 🔧 Algoritmo de Remoção de Headers/Footers

### Como Funciona

1. **Coleta de Candidatos**: Para cada página, identifica o primeiro e último bloco de texto
2. **Análise de Frequência**: Conta quantas vezes cada texto aparece na mesma posição
3. **Identificação**: Textos que aparecem em ≥ `threshold` das páginas são considerados headers/footers
4. **Remoção**: Remove os textos identificados de todas as páginas

### Exemplo Prático

```
Página 1: "RELATÓRIO ANUAL" | "Texto principal..." | "Página 1"
Página 2: "RELATÓRIO ANUAL" | "Mais texto..."     | "Página 2"  
Página 3: "RELATÓRIO ANUAL" | "Conclusão..."      | "Página 3"

Headers identificados: ["RELATÓRIO ANUAL"] (100% frequência)
Footers identificados: [] (números de página são diferentes)
```

## 🔗 Integração com LangGraph

### Exemplo de Workflow

```python
from langgraph import StateGraph
from src.tools.pdf_anonymization_tool import create_pdf_anonymization_tool

# Criar tool
pdf_anonymization_tool = create_pdf_anonymization_tool()

# Definir estado
class DocumentState:
    pdf_path: str
    anonymized_text: str
    metadata: dict

# Criar node
def anonymize_pdf_node(state: DocumentState):
    result = pdf_anonymization_tool(state.pdf_path)
    
    if result["success"]:
        return {
            "anonymized_text": result["anonymized_text"],
            "metadata": result["anonymization_metadata"]
        }
    else:
        raise Exception(f"Erro na anonimização: {result['error']}")

# Usar no workflow
workflow = StateGraph(DocumentState)
workflow.add_node("anonymize_pdf", anonymize_pdf_node)
```

## 🚨 Tratamento de Erros

### Tipos de Erro

- **Arquivo não encontrado**: PDF não existe no caminho especificado
- **PDF vazio**: Documento sem páginas
- **Erro de parsing**: Problemas na leitura do PDF
- **Erro de anonimização**: Falhas no processamento do Presidio

### Exemplo de Tratamento

```python
result = tool.anonymize_pdf("documento.pdf")

if not result["success"]:
    print(f"❌ Erro: {result['error']}")
    
    # Verificar tipo de erro
    if "not found" in result["error"]:
        print("Arquivo não encontrado")
    elif "no pages" in result["error"]:
        print("PDF vazio")
    else:
        print("Erro de processamento")
```

## 📈 Performance

### Benchmarks Esperados

- **Extração**: ~1-2 segundos por página
- **Anonimização**: ~0.5-1 segundo por página de texto
- **Memória**: ~50-100MB para PDFs de 10-20 páginas

### Otimizações

- PyMuPDF é otimizado para performance
- Processamento página por página para controle de memória
- Heurísticas eficientes para headers/footers

## 🔄 Próximos Passos

### Melhorias Planejadas

- [ ] **Detecção avançada** de headers/footers usando posição geométrica
- [ ] **Suporte a tabelas** com preservação de estrutura
- [ ] **Processamento em lote** de múltiplos PDFs
- [ ] **Cache** de resultados de extração
- [ ] **Configuração** de recognizers por tipo de documento

### Integração com Outros Tools

- [ ] **Classificação automática** de documentos extraídos
- [ ] **Geração de relatórios** sobre PDFs processados
- [ ] **Pipeline completo** PDF → Anonimização → Classificação → Relatório
