# 🛡️ MPRJ Anonymization Integration

## Overview

This document describes the integration of the official **Inova-MPRJ/Anonymizer** solution into our LangGraph multi-agent framework. The integration provides Brazilian-specific data anonymization capabilities while maintaining compatibility with our existing classification system.

## 🎯 Integration Goals

- ✅ Incorporate official MPRJ anonymization functionality
- ✅ Maintain LangGraph framework structure (agents/nodes/tools)
- ✅ Ensure compatibility with education classification system
- ✅ Provide modular tools for future multi-agent workflows
- ✅ Support Brazilian-specific PII entities (CPF, schools, addresses)

## 📁 File Structure

```
src/tools/
├── anonymization_tool.py          # Main LangGraph-compatible tool
└── recognizers/
    ├── __init__.py
    ├── cpf_recognizer.py          # Brazilian CPF recognizer
    ├── escola_recognizer.py       # Educational institutions
    └── endereco_recognizer.py     # Brazilian addresses

config/
└── languages-config.yml          # Language configuration

scripts/
└── test_anonymization_integration.py  # Integration tests
```

## 🔧 Core Components

### AnonymizationTool

Main LangGraph-compatible tool that provides anonymization functionality:

```python
from src.tools.anonymization_tool import AnonymizationTool

# Initialize tool
anonymizer = AnonymizationTool()

# Anonymize text
result = anonymizer.anonymize_text("João Silva mora na Rua das Flores, 123")

# Result structure
{
    "anonymized_text": "<PERSON> mora na <ENDEREÇO>",
    "entities_found": [
        {
            "entity_type": "PERSON",
            "start": 0,
            "end": 10,
            "score": 0.85,
            "text": "João Silva"
        }
    ],
    "entities_count": 2,
    "success": True
}
```

### Custom Recognizers

#### CPFRecognizer
- Detects Brazilian CPF numbers
- Supports formatted (123.456.789-00) and unformatted (12345678900)
- Score: 0.85

#### EscolaRecognizer
- Detects educational institutions
- Supports: Municipal/State/Federal schools, colleges, universities
- Special patterns for Brazilian institutions (EDI, CIEP, FAETEC, SEEDUC)
- Score: 0.85-0.95

#### EnderecoRecognizer
- Detects Brazilian addresses
- Supports: Streets, CEP, neighborhoods, rural addresses
- Case-insensitive with accent support
- Score: 0.8-0.95

## 🚀 Usage Examples

### Basic Anonymization

```python
from src.tools.anonymization_tool import AnonymizationTool

anonymizer = AnonymizationTool()

text = """
João Silva, CPF 123.456.789-00, estuda na Escola Municipal Professor Carlos Drummond.
Mora na Rua das Flores, 123, Copacabana. Email: <EMAIL>
"""

result = anonymizer.anonymize_text(text)
print(result['anonymized_text'])
# Output: <PERSON>, CPF <CPF>, estuda na <ESCOLA>. Mora na <ENDEREÇO>. Email: <EMAIL_ADDRESS>
```

### Integration with Education Data

```python
import pandas as pd
from src.tools.anonymization_tool import AnonymizationTool

# Load education classification data
df = pd.read_csv("data/output/ouvidorias_educacao_fireworks_multilabel.csv")

anonymizer = AnonymizationTool()

# Anonymize specific columns
for column in ['Assunto Resumido', 'Assunto Inteiro Teor']:
    if column in df.columns:
        df[f'{column}_ANONIMIZADO'] = df[column].apply(
            lambda x: anonymizer.anonymize_text(str(x))['anonymized_text'] 
            if pd.notna(x) else x
        )
```

### LangGraph Tool Integration

```python
from langgraph import StateGraph
from src.tools.anonymization_tool import create_anonymization_tool

# Create tool instance
anonymization_tool = create_anonymization_tool()

# Use in LangGraph workflow
def anonymize_node(state):
    text = state.get('text', '')
    result = anonymization_tool(text)
    return {'anonymized_text': result['anonymized_text']}

# Add to graph
graph = StateGraph()
graph.add_node("anonymize", anonymize_node)
```

## 🧪 Testing

Run integration tests to verify functionality:

```bash
# Test anonymization with education data
python scripts/test_anonymization_integration.py
```

Expected output:
- ✅ Successful initialization of anonymization tool
- ✅ Processing of education classification data
- ✅ Detection of Brazilian-specific entities
- ✅ Compatibility with existing data structure

## 📊 Supported Entities

| Entity Type | Description | Examples |
|-------------|-------------|----------|
| `PERSON` | Person names | João Silva, Maria Santos |
| `EMAIL_ADDRESS` | Email addresses | <EMAIL> |
| `PHONE_NUMBER` | Phone numbers | (21) 99999-9999 |
| `CPF` | Brazilian CPF | 123.456.789-00 |
| `ESCOLA` | Educational institutions | Escola Municipal, FAETEC |
| `ENDEREÇO` | Addresses | Rua das Flores, 123 |

## ⚙️ Configuration

### Language Configuration

Edit `config/languages-config.yml` to customize:

```yaml
pt:
  model_name: pt_core_news_lg
  entities:
    - PERSON
    - CPF
    - ESCOLA
    - ENDEREÇO
  context:
    pessoa: [nome, pessoa, chamado]
    escola: [escola, colégio, universidade]
```

### Dependencies

Required packages (automatically managed):

```toml
presidio-analyzer = "2.2.358"
presidio-anonymizer = "2.2.358"
spacy = "^3.8.7"
```

## 🔄 Future Integration

This anonymization tool is designed for future integration with:

1. **Multi-agent workflows**: Ready for LangGraph agent orchestration
2. **Pipeline automation**: Classification → Anonymization → Reports
3. **Real-time processing**: API endpoints for live anonymization
4. **Batch processing**: Large dataset anonymization

## 🛠️ Troubleshooting

### Common Issues

1. **spaCy model not found**:
   ```bash
   python -m spacy download pt_core_news_lg
   ```

2. **Import errors**:
   - Ensure `src/` is in Python path
   - Check dependencies are installed

3. **Low detection rates**:
   - Verify text language (Portuguese expected)
   - Check entity context in configuration

### Performance Tips

- Use batch processing for large datasets
- Configure entity thresholds in recognizers
- Monitor memory usage with large texts

## 📝 Notes

- Based on official Inova-MPRJ/Anonymizer (v2.2.358)
- Maintains compatibility with existing classification system
- Designed for Brazilian Portuguese text
- Ready for LangGraph multi-agent integration
- No web interface or API components included (core functionality only)
