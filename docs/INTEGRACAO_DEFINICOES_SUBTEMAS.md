# Integração das Definições de Subtemas nos Relatórios Individuais

## 📋 Visão Geral

Esta documentação descreve a implementação da integração das definições específicas de cada subtema nos prompts de geração dos relatórios individuais especializados. A melhoria torna os relatórios mais precisos e focados no escopo funcional de cada área.

## 🎯 Objetivo

Incorporar as definições detalhadas do arquivo `config/definicoes_subtemas.yml` nos prompts dos relatórios individuais, fornecendo contexto específico para que o LLM (Claude Sonnet 4) gere análises mais direcionadas e relevantes.

## 🔧 Implementação

### 1. Arquivos Modificados

#### `src/analisadores/analisador_multilabel.py`
- ✅ **Nova função**: `carregar_definicoes_completas_subtemas()`
- ✅ **Nova função**: `obter_contexto_subtema_seguro()`
- ✅ **Nova função**: `_fallback_definicao_basica()`

#### `src/reports/gerador_relatorio_multilabel.py`
- ✅ **Função modificada**: `gerar_prompt_subtema_individual()` - agora aceita parâmetro `area`
- ✅ **Nova função**: `_obter_contexto_subtema_para_relatorio()`
- ✅ **Nova função**: `_inferir_area_do_subtema()`
- ✅ **Nova função**: `_gerar_prompt_basico()`
- ✅ **Nova função**: `_formatar_contexto_para_relatorio()`

#### `config/template_relatorio_individual_especializado.txt`
- ✅ **Seção adicionada**: "CONTEXTO ESPECÍFICO DO SUBTEMA"
- ✅ **Variáveis adicionadas**: `{definicao_subtema}`, `{palavras_chave_subtema}`, `{foco_analise}`
- ✅ **Metodologia atualizada**: Inclui abordagem especializada por subtema

#### `src/agents/report_agent.py`
- ✅ **Função modificada**: `_generate_subtema_report()` - agora passa área para geração de prompt
- ✅ **Nova função**: `_get_area_for_subtema()` - obtém área do subtema

### 2. Estrutura das Definições

O arquivo `config/definicoes_subtemas.yml` contém definições estruturadas:

```yaml
SAUDE:
  Oncologia:
    definicao: "Casos relacionados a câncer, tumores, quimioterapia, radioterapia, biópsia, oncologistas"
    palavras_chave: ["câncer", "tumor", "oncologia", "quimioterapia", "radioterapia", "biópsia"]
    
  "Hematologia (Hemorede)":
    definicao: "Questões sobre sangue, hemoderivados, transfusões, doação de sangue, hemocentros"
    palavras_chave: ["sangue", "transfusão", "hemocentro", "hemoderivados", "anemia"]
```

### 3. Fluxo de Funcionamento

```mermaid
graph TD
    A[Dados Classificados] --> B[gerar_prompt_subtema_individual]
    B --> C[_obter_contexto_subtema_para_relatorio]
    C --> D[carregar_definicoes_completas_subtemas]
    D --> E[Template Especializado]
    E --> F[Claude Sonnet 4]
    F --> G[Relatório Especializado]
    
    C --> H[_inferir_area_do_subtema]
    H --> I[Fallback se necessário]
    I --> E
```

## 📊 Comparação: Antes vs Depois

### ❌ Antes (Template Genérico)
```text
Você é um analista especializado em identificação de problemas sistêmicos.

Foram analisadas 3 denúncias relacionadas a Oncologia.

### RELATO #1
Paciente com câncer aguarda cirurgia há 8 meses
---

Elabore um relatório completo seguindo uma estrutura clara.
```

### ✅ Depois (Template Especializado)
```text
Você é um analista especializado em identificação de problemas sistêmicos.

## CONTEXTO ESPECÍFICO DO SUBTEMA

**Subtema Analisado:** Oncologia
**Definição Funcional:** Casos relacionados a câncer, tumores, quimioterapia, radioterapia, biópsia, oncologistas
**Palavras-Chave Orientativas:** câncer, tumor, oncologia, quimioterapia, radioterapia
**Foco da Análise:** Análise especializada em casos relacionados a câncer, tumores, quimioterapia...

**INSTRUÇÕES ESPECÍFICAS DE ANÁLISE:**
Ao analisar as denúncias, concentre-se especificamente em aspectos relacionados a: Casos relacionados a câncer, tumores, quimioterapia, radioterapia, biópsia, oncologistas.
Priorize a identificação de problemas que se enquadrem no escopo funcional definido acima.

---

Foram analisadas 3 denúncias relacionadas a Oncologia.

### RELATO #1
Paciente com câncer aguarda cirurgia há 8 meses
---

## 2. METODOLOGIA

A análise foi realizada com foco específico em Casos relacionados a câncer, tumores, quimioterapia, radioterapia, biópsia, oncologistas.

**Abordagem Metodológica Especializada:**
- Categorização das denúncias por tipo de problema específico do subtema
- Análise direcionada para questões relacionadas às palavras-chave: câncer, tumor, oncologia...
- Identificação de padrões sistêmicos dentro do escopo funcional definido
```

## 🛡️ Validações e Fallbacks

### Sistema de Fallbacks Implementado:

1. **Área não fornecida**: Inferência automática baseada no nome do subtema
2. **Definição não encontrada**: Geração de definição básica
3. **Erro no carregamento**: Contexto padrão seguro
4. **Template inválido**: Prompt básico como backup

### Inferência Automática de Área:

```python
def _inferir_area_do_subtema(nome_subtema: str) -> Optional[str]:
    nome_lower = nome_subtema.lower()
    
    if any(palavra in nome_lower for palavra in ['oncologia', 'saude', 'hospital', 'sus']):
        return 'SAUDE'
    elif any(palavra in nome_lower for palavra in ['educacao', 'escola', 'bullying']):
        return 'EDUCACAO'
    elif any(palavra in nome_lower for palavra in ['ambiente', 'poluicao', 'desmatamento']):
        return 'MEIO_AMBIENTE'
    
    return None
```

## 🧪 Testes e Validação

### Scripts de Teste Criados:

1. **`test_definicoes_simples.py`**: Validação básica da estrutura
2. **`exemplo_relatorio_especializado.py`**: Demonstração prática
3. **Testes unitários atualizados**: `tests/test_gerador_relatorio_multilabel.py`

### Resultados dos Testes:
```
🎉 Todos os testes básicos passaram!
  ✅ Arquivo de definições carregado corretamente
  ✅ Template atualizado com novas variáveis
  ✅ Funções implementadas e encontradas
  ✅ Exemplo de Oncologia validado
```

## 📈 Benefícios Alcançados

### Para os Relatórios:
- 📊 **Maior Precisão**: Análise focada no escopo específico do subtema
- 🎯 **Contexto Especializado**: LLM orientado sobre o que procurar
- 📈 **Qualidade Melhorada**: Relatórios mais relevantes e úteis
- 🔍 **Análise Direcionada**: Identificação de problemas específicos da área

### Para o Sistema:
- 🔧 **Reutilização**: Aproveita definições já existentes
- 📚 **Consistência**: Mesmas definições para classificação e relatórios
- 🛠️ **Manutenibilidade**: Definições centralizadas em um local
- 🚀 **Escalabilidade**: Fácil adição de novos subtemas

## 🚀 Como Usar

### 1. Gerar Relatório Individual com Contexto:

```python
from src.reports.gerador_relatorio_multilabel import gerar_prompt_subtema_individual

# Com área especificada
prompt = gerar_prompt_subtema_individual(df_casos, 'Oncologia', 'SAUDE')

# Com inferência automática
prompt = gerar_prompt_subtema_individual(df_casos, 'Oncologia')
```

### 2. Obter Contexto de um Subtema:

```python
from src.analisadores.analisador_multilabel import obter_contexto_subtema_seguro

contexto = obter_contexto_subtema_seguro('SAUDE', 'Oncologia')
print(contexto['definicao'])
print(contexto['palavras_chave'])
```

## 🔄 Próximos Passos

1. **Teste com Dados Reais**: Gerar relatórios com dados de classificação multilabel
2. **Comparação Qualitativa**: Avaliar melhoria na qualidade dos relatórios
3. **Refinamento**: Ajustar definições baseado no feedback
4. **Expansão**: Aplicar para outros tipos de relatório (visão geral, criminal)
5. **Documentação de Usuário**: Criar guias para usuários finais

## 📝 Arquivos de Referência

- **Definições**: `config/definicoes_subtemas.yml`
- **Template**: `config/template_relatorio_individual_especializado.txt`
- **Código Principal**: `src/reports/gerador_relatorio_multilabel.py`
- **Testes**: `test_definicoes_simples.py`, `exemplo_relatorio_especializado.py`
- **Documentação**: Este arquivo

---

**Implementado em**: Janeiro 2025  
**Status**: ✅ Completo e Testado  
**Compatibilidade**: Mantém compatibilidade com código existente
