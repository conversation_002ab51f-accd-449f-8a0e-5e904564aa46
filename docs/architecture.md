# Arquitetura - Simple Class API

## 🏗️ Visão Geral da Arquitetura

O Simple Class é um sistema completo de processamento de texto e dados de ouvidorias, com arquitetura híbrida que integra:
- **FastAPI** como framework principal
- **LLMs avançados** (Fireworks AI e Claude Sonnet 4)
- **Ferramentas especializadas** para processamento e análise
- **Sistema de workflows** modular
- **API REST** com documentação automática

O sistema está organizado em camadas funcionais que trabalham em conjunto para processar, analisar e gerar relatórios a partir de dados de ouvidorias.

## 📊 Estatísticas do Sistema

- **8.266 linhas de código Python** no diretório src/ (46 arquivos)
- **115 arquivos Python** total no projeto
- **24 arquivos de teste** com cobertura completa
- **5 routers da API** com 20+ endpoints
- **3 áreas de classificação** com 30 subtemas totais
- **4 serviços principais** de lógica de negócio
- **44 scripts Python** organizados por categoria
- **35+ comandos Makefile** para automação

## 🌐 Arquitetura da API (FastAPI)

### Estrutura Principal (src/)

```
simple_class/
├── 📦 src/                        # Código principal
│   ├── 🌐 api/                    # API FastAPI completa
│   │   ├── main.py                # Aplicação principal
│   │   ├── config.py              # Configurações da API
│   │   ├── models/                # Modelos Pydantic
│   │   ├── routers/               # Endpoints REST
│   │   ├── services/              # Lógica de negócio
│   │   └── database/              # Cliente Supabase
│   ├── 🔧 tools/                  # Ferramentas especializadas
│   │   ├── anonymization_tool.py  # Anonimização com Presidio
│   │   ├── pdf_anonymization_tool.py # PDFs anonimizados
│   │   └── recognizers/           # Reconhecedores brasileiros
│   ├── 🤖 analisadores/           # Analisadores de classificação
│   │   ├── analisador_multilabel.py  # Classificação multilabel
│   │   ├── analisador_lote.py     # Processamento em lote
│   │   └── analisador_embeddings.py # Análise por embeddings
│   ├── 📊 reports/                # Geradores de relatório
│   │   ├── gerador_relatorio.py   # Relatórios individuais
│   │   └── gerador_relatorio_visao_geral.py # Relatórios executivos
│   └── ⚙️ utils/                  # Utilitários
│       └── config_utils.py        # Configurações centralizadas
```

### Fluxo de Requisições

```mermaid
graph TD
    A[Cliente] --> B[FastAPI Main App]
    B --> C[Middleware CORS/Rate Limit]
    C --> D[Router Específico]
    D --> E[Validação Pydantic]
    E --> F[Service Layer]
    F --> G[Tools/External APIs]
    G --> H[Supabase Storage/DB]
    H --> I[Resposta JSON]
```

## 🔧 Camada de Ferramentas (src/tools/)

### Ferramentas Especializadas

```
src/tools/
├── anonymization_tool.py     # Anonimização com Presidio
├── pdf_anonymization_tool.py # Processamento de PDFs
└── recognizers/              # Reconhecedores brasileiros
    ├── cpf_recognizer.py     # Detecção de CPF
    ├── escola_recognizer.py  # Instituições de ensino
    └── endereco_recognizer.py # Endereços brasileiros
```

### Integração com Microsoft Presidio

```python
# Fluxo de anonimização
AnonymizationTool
├── Presidio Analyzer Engine
│   ├── SpaCy NLP Engine (pt_core_news_lg)
│   ├── Reconhecedores padrão (PERSON, EMAIL, PHONE)
│   └── Reconhecedores brasileiros (CPF, ESCOLA, ENDERECO)
└── Presidio Anonymizer Engine
    └── Substituição de entidades por placeholders
```

## 🤖 Camada de Análise (src/analisadores/)

### Analisadores de Classificação

```
src/analisadores/
├── analisador_multilabel.py  # Classificação multilabel
├── analisador_lote.py        # Processamento em lote
└── analisador_embeddings.py  # Análise por embeddings
```

### Fluxo de Classificação Multilabel

```mermaid
graph LR
    A[Texto de Entrada] --> B[Prompt Engineering]
    B --> C[Fireworks AI - Llama 3.3]
    C --> D[Análise de Subtemas]
    D --> E[Até 3 Subtemas]
    E --> F[Scores de Confiança]
```

### Áreas e Subtemas Suportados

| Área | Subtemas | Modelo LLM |
|------|----------|------------|
| **EDUCACAO** | 10 subtemas | Llama 3.3 Instruct |
| **SAUDE** | 10 subtemas | Llama 3.3 Instruct |
| **MEIO_AMBIENTE** | 10 subtemas | Llama 3.3 Instruct |

## 📊 Camada de Relatórios (src/reports/)

### Geradores de Relatório

```
src/reports/
├── gerador_relatorio.py           # Relatórios individuais
├── gerador_relatorio_multilabel.py # Relatórios multilabel
└── gerador_relatorio_visao_geral.py # Relatórios executivos
```

### Fluxo de Geração de Relatórios

```mermaid
graph TD
    A[Dados Classificados] --> B[Template Selection]
    B --> C[Data Aggregation]
    C --> D[Claude Sonnet 4]
    D --> E[Markdown Report]
    E --> F[Statistical Analysis]
    F --> G[Recommendations]
```

## ⚙️ Configurações (config/)

### Estrutura de Configuração

```
config/
├── settings.yml              # 30 subtemas em 3 áreas
├── definicoes_subtemas.yml   # Definições detalhadas
├── languages-config.yml      # Configuração NLP
├── prompts/                  # Templates de prompts
│   ├── classification_prompts.yml
│   └── report_prompts.yml
└── schemas/                  # Esquemas de validação
    └── data_schemas.json
```

### Configuração de Subtemas

```yaml
# Exemplo de configuração (settings.yml)
subtemas:
  EDUCACAO:
    - "Educação Especial - Falta de Mediador"
    - "Alimentação Escolar"
    - "Infraestrutura"
    # ... 7 subtemas adicionais
  
  SAUDE:
    - "Oncologia"
    - "Regulação em Saúde"
    - "Diagnose (laboratório e imagem)"
    # ... 7 subtemas adicionais
```

## 🧪 Arquitetura de Testes (tests/)

### Estrutura de Testes

```
tests/
├── conftest.py              # Configuração pytest
├── fixtures/                # Dados de teste
│   └── sample_data.py
├── unit/                    # Testes unitários
│   ├── test_core/
│   └── test_tools/
├── integration/             # Testes de integração
│   └── test_workflow_integration.py
└── test_*.py               # Testes por módulo (24 arquivos)
```

### Cobertura de Testes

| Componente | Arquivos de Teste | Cobertura |
|------------|-------------------|-----------|
| **API Endpoints** | 5 arquivos | Completa |
| **Services** | 4 arquivos | Completa |
| **Tools** | 3 arquivos | Completa |
| **Scripts** | 44 arquivos | Parcial |
| **Analisadores** | 3 arquivos | Completa |
| **Relatórios** | 3 arquivos | Completa |
| **Integração** | 1 arquivo | End-to-end |

## 🚀 Scripts CLI (scripts/)

### Organização por Categoria

```
scripts/
├── Processamento Principal (5 scripts)
│   ├── processar_educacao_fireworks.py
│   ├── classificar_educacao_completo.py
│   └── processar_ouvidorias.py
├── Relatórios (3 scripts)
│   ├── gerar_relatorio_educacao_completo.py
│   ├── gerar_relatorio_multilabel.py
│   └── gerar_relatorio_visao_geral_educacao.py
├── Anonimização (2 scripts)
│   ├── anonimiza.py
│   └── anonymize_persons_only.py
├── Deploy e Produção (3 scripts)
│   ├── deploy.py
│   ├── production_setup.py
│   └── start_api_docs.py
└── Testes e Validação (5 scripts)
    ├── test_anonymization_integration.py
    ├── example_pdf_workflow.py
    └── run_testes.py
```

## 🛠️ Infraestrutura e Deploy

### Tecnologias Principais

| Componente | Tecnologia | Versão |
|------------|------------|---------|
| **API Framework** | FastAPI | 0.115.0+ |
| **Language Runtime** | Python | 3.10-3.12 |
| **Package Manager** | Poetry | Latest |
| **Database** | Supabase (PostgreSQL) | Cloud |
| **Storage** | Supabase Storage | Cloud |
| **LLM APIs** | Fireworks AI, Anthropic | Latest |
| **Anonymization** | Microsoft Presidio | 2.2.358 |
| **PDF Processing** | PyMuPDF | 1.24.0+ |

### Makefile - Automação de Deploy

```bash
# 30+ comandos organizados
make setup           # Configuração inicial
make run             # API desenvolvimento
make test            # Todos os testes
make deploy-prod     # Deploy produção
make docker-build    # Imagem Docker
```

### Variáveis de Ambiente

```bash
# Configuração mínima para produção
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
API_SECRET_KEY=your_secret_key
FIREWORKS_API_KEY=your_fireworks_key
ANTHROPIC_API_KEY=your_anthropic_key
```

## 📈 Performance e Escalabilidade

### Métricas de Performance

- **Taxa de classificação**: 96.6% em dados reais
- **Processamento**: 147 casos reais testados
- **Rate limiting**: 100 requests/hora por usuário
- **Upload máximo**: 50MB por arquivo
- **Timeout**: Configurável por endpoint

### Otimizações Implementadas

1. **Rate Limiting**: Proteção contra spam
2. **Async/Await**: Processamento não-bloqueante
3. **Streaming**: Respostas em tempo real
4. **Caching**: Configurações carregadas uma vez
5. **Batch Processing**: Processamento em lote eficiente

## 🔄 Integração com LangGraph (Futuro)

### Arquitetura Planejada

```mermaid
graph TD
    A[LangGraph Coordinator] --> B[Classification Agent]
    A --> C[Anonymization Agent]
    A --> D[Report Generation Agent]
    B --> E[Fireworks AI]
    C --> F[Presidio Tools]
    D --> G[Claude Sonnet 4]
```

### Nós Planejados

- **Classification Node**: Roteamento inteligente de classificação
- **Anonymization Node**: Pipeline de anonimização
- **Report Node**: Geração automática de relatórios
- **Validation Node**: Validação de qualidade
- **Orchestrator Node**: Coordenação de workflows

## 🔒 Segurança e Compliance

### Medidas de Segurança

1. **Autenticação JWT**: Tokens seguros com expiração
2. **Rate Limiting**: Proteção contra abuso
3. **CORS**: Configuração restritiva
4. **Input Validation**: Validação rigorosa com Pydantic
5. **LGPD Compliance**: Anonimização de PII

### Anonimização LGPD

- **Entidades detectadas**: CPF, telefone, email, endereços
- **Reconhecedores brasileiros**: Especializados para o contexto local
- **Processamento de PDFs**: Preservação de layout
- **Logs de auditoria**: Rastreabilidade completa

---

## 📚 Documentação de Referência

- [API Documentation](API_DOCUMENTATION.md) - Guia completo da API
- [Testing Guide](TESTING.md) - Estratégias de teste
- [Development Guide](development.md) - Guia para desenvolvedores
- [Anonymization Integration](ANONYMIZATION_INTEGRATION.md) - Integração LGPD

**Arquitetura atualizada**: Janeiro 2025  
**Versão**: 0.1.0  
**Status**: Produção Ready 