# Classificador de Relatos e Gerador de Relatórios Táticos

## O que o projeto faz

Este projeto é um sistema completo para análise de textos que realiza duas funções principais:

1. **Classificação automática de relatos textuais** - Identifica se um texto se refere a um determinado tema (como Oftalmologia) utilizando diferentes abordagens de processamento de linguagem natural.

2. **Geração de relatórios táticos detalhados** - Analisa os relatos classificados como positivos e produz um relatório estruturado com análise aprofundada, problemas sistêmicos identificados e recomendações práticas.

## Como o projeto funciona

### 1. Classificação de Relatos

O sistema implementa três métodos diferentes para classificar textos:

#### 1.1 Método Individual (`src/analisadores/analisador_relatos.py`)

- **Processo**: Cada relato é enviado individualmente para um modelo de linguagem (LLM).
- **Funcionamento**: 
  - O texto é formatado em um prompt que pergunta se o conteúdo está relacionado ao tema configurado
  - O modelo responde com "SIM" ou "NAO"
  - A resposta é convertida em um valor binário (1 para sim, 0 para não)
- **Tecnologia**: Utiliza a API da Together AI para acessar modelos como o Llama 4
- **Performance**: Para 130 registros, identifica cerca de 14,6% de positivos em aproximadamente 110 segundos

#### 1.2 Método em Lote (`src/analisadores/analisador_lote.py`)

- **Processo**: Agrupa múltiplos relatos em uma única chamada ao LLM.
- **Funcionamento**:
  - Agrupa de 5 a 10 relatos em um único prompt
  - Usa um formato estruturado para que o modelo forneça uma resposta para cada relato
  - Processa a resposta para extrair a classificação de cada texto
- **Tecnologia**: Mesma API e modelo do método individual, mas otimizando o uso
- **Performance**: Para 130 registros, identifica cerca de 13,1% de positivos em aproximadamente 40 segundos

#### 1.3 Método com Embeddings (`src/analisadores/analisador_embeddings.py`)

- **Processo**: Usa vetorização semântica para comparar textos sem consultas diretas a LLMs.
- **Funcionamento**:
  - Converte termos de referência relacionados ao tema em vetores (embeddings)
  - Converte cada relato em um vetor
  - Calcula a similaridade de cosseno entre os vetores
  - Classifica como positivo se a similaridade ultrapassar um limiar ajustável
- **Tecnologia**: Utiliza a biblioteca `sentence-transformers` e o modelo "all-MiniLM-L6-v2"
- **Performance**: Para 130 registros, identifica cerca de 93,1% de positivos em aproximadamente 5 segundos
- **Observação**: O alto percentual de positivos indica que o método precisa ter seu limiar ajustado para maior precisão

### 2. Geração de Relatórios Táticos (`src/reports/gerador_relatorio.py`)

Após a classificação, o sistema permite gerar um relatório detalhado a partir dos relatos positivos:

- **Processo**: 
  1. Carrega os relatos classificados como positivos
  2. Formata-os em um prompt estruturado baseado em um template
  3. Envia o prompt para o modelo Claude 3.7 Sonnet
  4. Recebe e salva o relatório gerado em formato markdown

- **Estrutura do Relatório**:
  1. **Sumário Executivo** - Visão geral dos achados
  2. **Metodologia** - Como a análise foi conduzida
  3. **Perfil das Denúncias** - Análise estatística e padrões
  4. **Problemas Sistêmicos** - Falhas recorrentes identificadas
  5. **Análise de Causas** - Fatores que contribuem para os problemas
  6. **Impacto nos Afetados** - Consequências para os envolvidos
  7. **Recomendações Táticas** - Sugestões de ações imediatas e de longo prazo
  8. **Monitoramento e Indicadores** - Métricas para acompanhamento
  9. **Conclusões** - Síntese final e próximos passos

- **Tecnologia**: Utiliza a API da Anthropic para acessar o modelo Claude 3.7 Sonnet
- **Personalização**: O template do prompt é armazenado em `templates/template_relatorio.txt` e pode ser modificado

## Fluxo de Trabalho Completo

1. **Configuração**:
   - O tema de classificação e os modelos são definidos em `config/config.yml`
   - As chaves de API são armazenadas em um arquivo `.env`

2. **Processamento de Dados**:
   - Os dados são fornecidos em formato CSV 
   - A estrutura esperada é uma coluna única de textos (relatos)

3. **Classificação**:
   - Um dos três métodos é usado para classificar os relatos
   - Os resultados são salvos em CSV com uma coluna adicional "REFERE_ASSUNTO" (1 ou 0)

4. **Análise Comparativa** (opcional):
   - O script `scripts/run_testes.py` pode executar comparações entre os três métodos
   - Gera relatórios de concordância e estatísticas comparativas

5. **Geração de Relatório**:
   - Os casos positivos são processados para gerar um relatório estruturado
   - O relatório é salvo em formato markdown em `data/output/`

## Tecnologias Principais

- **Python**: Linguagem principal de implementação
- **Together AI API**: Para acesso aos modelos LLM como Llama 4
- **Anthropic API**: Para acesso ao modelo Claude 3.7 Sonnet
- **sentence-transformers**: Para geração de embeddings semânticos
- **pandas**: Para manipulação de dados em formato tabular
- **yaml**: Para configuração flexível do sistema

## Comparação dos Métodos

| Método | Precisão | Velocidade | Custo | Uso de API | Positivos (%) |
|--------|----------|------------|-------|------------|---------------|
| Individual | Alta | Lento (110s) | Alto | Together AI | 14,6% |
| Lote | Média-Alta | Médio (40s) | Médio | Together AI | 13,1% |
| Embeddings | Baixa-Média | Rápido (5s) | Nenhum* | Nenhuma** | 93,1% |

\* Após download inicial do modelo  
\** Funciona offline após o download do modelo

## Estrutura Modular

O projeto foi desenhado de forma modular para facilitar:

- **Manutenção**: Cada componente tem responsabilidade única
- **Escalabilidade**: Novos métodos podem ser adicionados com facilidade
- **Flexibilidade**: Os parâmetros podem ser ajustados através de arquivos de configuração
- **Reuso**: Componentes como carregamento de configuração e formatação são compartilhados 