# Sistema de Testes da API Simple Class

## Visão Geral

O sistema de testes foi implementado usando **pytest** com dados reais (sem mocks) para garantir que a API funcione corretamente em condições reais.

## Estrutura de Testes

### Arquivos de Teste

- **`tests/conftest.py`**: Configurações e fixtures compartilhadas
- **`tests/test_auth.py`**: Testes de autenticação
- **`tests/test_files.py`**: Testes de gerenciamento de arquivos
- **`tests/test_anonymization.py`**: Testes de anonimização
- **`tests/test_classification.py`**: Testes de classificação
- **`tests/test_reports.py`**: Testes de geração de relatórios
- **`tests/test_integration.py`**: Testes de integração completos

### Scripts de Teste

- **`scripts/run_tests.py`**: Script principal para executar testes
- **`pytest.ini`**: Configuração do pytest

## Fixtures Principais

### `client`
Cliente de teste síncrono para a API FastAPI.

### `test_user_token`
Obtém um token JWT real através do login com credenciais de teste:
- Email: `<EMAIL>`
- Password: `test123`

### `auth_headers`
Headers de autorização com token real para endpoints protegidos.

### Dados de Teste

- **`sample_csv_content`**: Conteúdo CSV para testes de upload
- **`sample_text_for_anonymization`**: Texto com PII para testes de anonimização
- **`sample_text_for_classification`**: Texto para testes de classificação

## Tipos de Teste

### 1. Testes de Autenticação (`test_auth.py`)

- ✅ Login com credenciais válidas
- ✅ Login com credenciais inválidas
- ✅ Validação de campos obrigatórios
- ✅ Formato de email inválido
- ✅ Informações do usuário atual (`/me`)
- ✅ Status de autenticação (`/status`)
- ✅ Tokens inválidos
- ✅ Rate limiting
- ✅ Formato de expiração de token

### 2. Testes de Arquivos (`test_files.py`)

- ✅ Upload de CSV válido
- ✅ Upload sem autenticação
- ✅ Tipos de arquivo inválidos
- ✅ Arquivos muito grandes
- ✅ Arquivos vazios
- ✅ CSV malformado
- ✅ Listagem de arquivos
- ✅ Paginação
- ✅ Informações de arquivo específico
- ✅ Exclusão de arquivos
- ✅ Upload com metadados
- ✅ Uploads concorrentes

### 3. Testes de Anonimização (`test_anonymization.py`)

- ✅ Anonimização de texto com PII
- ✅ Diferentes idiomas (PT/EN)
- ✅ Seleção de entidades customizada
- ✅ Texto vazio
- ✅ Idioma inválido
- ✅ Anonimização de PDF
- ✅ Arquivos inválidos
- ✅ Texto grande
- ✅ Texto sem PII
- ✅ Caracteres especiais
- ✅ Performance
- ✅ Requests concorrentes

### 4. Testes de Classificação (`test_classification.py`)

- ✅ Classificação multilabel
- ✅ Classificação unilabel
- ✅ Texto vazio
- ✅ Tipo de classificação inválido
- ✅ Classificação de arquivo CSV
- ✅ Formato de arquivo inválido
- ✅ Coluna ausente
- ✅ Diferentes áreas de política
- ✅ Arquivo grande
- ✅ Performance
- ✅ Requests concorrentes

### 5. Testes de Relatórios (`test_reports.py`)

- ✅ Relatório individual por subtema
- ✅ Relatório geral (overview)
- ✅ Arquivo inválido
- ✅ Subtema inválido
- ✅ Listagem de relatórios
- ✅ Filtros de relatórios
- ✅ Relatório específico
- ✅ Exclusão de relatórios
- ✅ Diferentes áreas de política
- ✅ Qualidade do conteúdo
- ✅ Performance
- ✅ Geração concorrente

### 6. Testes de Integração (`test_integration.py`)

- ✅ Workflow completo: login → upload → classify → anonymize → report
- ✅ Classificação de arquivo
- ✅ Tratamento de erros
- ✅ Performance de workflow
- ✅ Workflows concorrentes
- ✅ Health check da API
- ✅ Documentação da API
- ✅ Consistência de resposta
- ✅ Headers CORS
- ✅ Validação de content-type
- ✅ Rate limiting

## Executando os Testes

### Comando Básico
```bash
poetry run python scripts/run_tests.py
```

### Opções Disponíveis

```bash
# Todos os testes
poetry run python scripts/run_tests.py all

# Testes específicos
poetry run python scripts/run_tests.py auth
poetry run python scripts/run_tests.py files
poetry run python scripts/run_tests.py anonymization
poetry run python scripts/run_tests.py classification
poetry run python scripts/run_tests.py reports

# Testes rápidos (sem testes lentos)
poetry run python scripts/run_tests.py fast

# Com relatório de cobertura HTML
poetry run python scripts/run_tests.py coverage

# Verificar ambiente de teste
poetry run python scripts/run_tests.py check
```

### Pytest Direto

```bash
# Todos os testes
poetry run pytest

# Testes específicos
poetry run pytest tests/test_auth.py
poetry run pytest tests/test_auth.py::TestAuthEndpoints::test_login_success

# Com cobertura
poetry run pytest --cov=src --cov-report=html

# Verbose
poetry run pytest -v

# Parar no primeiro erro
poetry run pytest -x
```

## Configuração

### Dependências de Teste

```toml
[tool.poetry.group.dev.dependencies]
pytest = "^8.0.0"
pytest-asyncio = "^0.21.0"
httpx = "^0.25.0"
pytest-mock = "^3.12.0"
pytest-cov = "^4.1.0"
```

### Configuração pytest.ini

- Cobertura mínima: 80%
- Relatórios: terminal + HTML
- Markers para categorização
- Modo asyncio automático

## Credenciais de Teste

### Usuário de Teste
- **Email**: `<EMAIL>`
- **Password**: `test123`
- **ID**: `test-user-123`
- **Nome**: `Test User`
- **Organização**: `Test Organization`

## Características dos Testes

### ✅ Vantagens

1. **Dados Reais**: Testa a API com dados reais, não mocks
2. **Cobertura Completa**: Testa todos os endpoints e cenários
3. **Integração Real**: Testa workflows completos
4. **Performance**: Inclui testes de performance e concorrência
5. **Validação**: Testa validação de entrada e tratamento de erros
6. **Autenticação Real**: Usa sistema de autenticação real

### ⚠️ Considerações

1. **Dependências Externas**: Pode falhar se APIs externas estiverem indisponíveis
2. **Performance**: Testes podem ser mais lentos que com mocks
3. **Dados**: Requer dados de teste válidos
4. **Limpeza**: Pode precisar de limpeza de dados entre testes

## Próximos Passos

1. **Resolver Cache**: Corrigir problema de cache Python nos testes
2. **CI/CD**: Integrar testes no pipeline de CI/CD
3. **Dados de Teste**: Criar dataset de teste mais robusto
4. **Testes E2E**: Adicionar testes end-to-end com frontend
5. **Monitoring**: Adicionar métricas de qualidade dos testes

## Relatórios

### Cobertura
- Relatório HTML: `htmlcov/index.html`
- Relatório terminal: automático
- Meta: >80% de cobertura

### Resultados
- Logs detalhados de cada teste
- Tempo de execução
- Estatísticas de sucesso/falha
- Identificação de testes lentos
