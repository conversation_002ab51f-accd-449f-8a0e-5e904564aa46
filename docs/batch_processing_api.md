# PDF Batch Processing API

This document describes the PDF batch processing API endpoints and usage examples.

## Overview

The batch processing API allows you to process multiple PDF files in a folder, applying the same multilabel classification that the system performs for tabular data. The system extracts text from PDFs, applies LGPD-compliant anonymization, and classifies content into up to 3 subtemas per document.

## Features

- **Asynchronous Processing**: Jobs run in background with progress tracking
- **Intelligent PDF Processing**: Advanced text extraction with chunking
- **Multilabel Classification**: Up to 3 subtemas per PDF with confidence scores
- **Evidence Extraction**: Text evidence for each classification
- **CSV Export**: Comprehensive results in CSV format
- **Error Handling**: Robust error handling and recovery
- **Performance Optimization**: Parallel processing for large batches

## API Endpoints

### 1. Start Batch Processing

**POST** `/api/v1/batch/process`

Start a new PDF batch processing job.

#### Request Body

```json
{
  "folder_path": "/path/to/pdf/folder",
  "area": "EDUCACAO",
  "max_subtemas": 3,
  "chunk_size": 1000,
  "overlap": 200,
  "parallel_workers": 4
}
```

#### Parameters

- `folder_path` (string, required): Path to folder containing PDF files
- `area` (string, required): Classification area (`EDUCACAO`, `SAUDE`, `MEIO_AMBIENTE`)
- `max_subtemas` (integer, optional): Maximum subtemas per PDF (default: 3, max: 5)
- `chunk_size` (integer, optional): Token size for text chunks (default: 1000, range: 500-2000)
- `overlap` (integer, optional): Token overlap between chunks (default: 200, max: 500)
- `parallel_workers` (integer, optional): Number of parallel workers (default: 4, max: 10)

#### Response

```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "message": "Batch processing started successfully",
  "status": "processing"
}
```

#### Example

```bash
curl -X POST "http://localhost:8000/api/v1/batch/process" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "folder_path": "/data/pdfs/education",
    "area": "EDUCACAO",
    "chunk_size": 1200,
    "parallel_workers": 6
  }'
```

### 2. Get Job Status

**GET** `/api/v1/batch/status/{job_id}`

Get the current status and progress of a batch processing job.

#### Response

```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "processing",
  "progress": 0.65,
  "current_file": "document_15.pdf",
  "processed_files": 13,
  "total_files": 20,
  "estimated_completion": "2025-01-23T15:30:00Z",
  "error_message": null
}
```

#### Status Values

- `pending`: Job created but not yet started
- `processing`: Job is currently running
- `completed`: Job finished successfully
- `failed`: Job failed with error
- `cancelled`: Job was cancelled by user

#### Example

```bash
curl "http://localhost:8000/api/v1/batch/status/550e8400-e29b-41d4-a716-446655440000" \
  -H "Authorization: Bearer your-jwt-token"
```

### 3. Get Job Results

**GET** `/api/v1/batch/results/{job_id}`

Get the results summary of a completed batch processing job.

#### Response

```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "csv_download_url": "/api/v1/batch/download/550e8400-e29b-41d4-a716-446655440000",
  "summary": {
    "total_pdfs": 20,
    "successful_pdfs": 18,
    "failed_pdfs": 2,
    "area": "EDUCACAO",
    "timestamp": "2025-01-23T15:30:00Z"
  },
  "processing_stats": {
    "total_processing_time": 245.6,
    "average_time_per_pdf": 12.3,
    "success_rate": 90.0,
    "error_summary": {
      "pdf_extraction_error": 1,
      "classification_error": 1
    }
  }
}
```

#### Example

```bash
curl "http://localhost:8000/api/v1/batch/results/550e8400-e29b-41d4-a716-446655440000" \
  -H "Authorization: Bearer your-jwt-token"
```

### 4. Download Results CSV

**GET** `/api/v1/batch/download/{job_id}`

Download the CSV file containing the batch processing results.

#### Response

Returns a CSV file with the following columns:

- `filename`: PDF filename
- `pdf_path`: Full path to PDF file
- `subtemas_finais`: Semicolon-separated list of identified subtemas
- `total_pages`: Number of pages in PDF
- `total_chunks`: Number of text chunks processed
- `processing_time`: Time taken to process PDF (seconds)
- `area`: Classification area
- `timestamp`: Processing completion timestamp
- `error`: Error message if processing failed
- `subtema_*`: Binary columns for each subtema (1 if present, 0 if not)
- `confidence_*`: Confidence scores for each subtema
- `evidence_*`: Text evidence for each subtema

#### Example

```bash
curl "http://localhost:8000/api/v1/batch/download/550e8400-e29b-41d4-a716-446655440000" \
  -H "Authorization: Bearer your-jwt-token" \
  -o batch_results.csv
```

### 5. Cancel Job

**DELETE** `/api/v1/batch/{job_id}`

Cancel a running batch processing job.

#### Response

```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "message": "Job cancelled successfully",
  "status": "cancelled"
}
```

#### Example

```bash
curl -X DELETE "http://localhost:8000/api/v1/batch/550e8400-e29b-41d4-a716-446655440000" \
  -H "Authorization: Bearer your-jwt-token"
```

### 6. List User Jobs

**GET** `/api/v1/batch/jobs`

List batch processing jobs for the current user.

#### Query Parameters

- `limit` (integer, optional): Maximum number of jobs to return (default: 10)
- `offset` (integer, optional): Number of jobs to skip (default: 0)

#### Response

```json
{
  "jobs": [
    {
      "job_id": "550e8400-e29b-41d4-a716-446655440000",
      "area": "EDUCACAO",
      "status": "completed",
      "created_at": "2025-01-23T14:00:00Z",
      "updated_at": "2025-01-23T15:30:00Z",
      "progress": 1.0,
      "total_files": 20,
      "processed_files": 20
    }
  ],
  "total": 1,
  "limit": 10,
  "offset": 0
}
```

#### Example

```bash
curl "http://localhost:8000/api/v1/batch/jobs?limit=5&offset=0" \
  -H "Authorization: Bearer your-jwt-token"
```

## Usage Examples

### Python Client Example

```python
import asyncio
import aiohttp
import json

class BatchProcessingClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
    
    async def start_batch_processing(self, folder_path, area, **kwargs):
        """Start a new batch processing job."""
        data = {
            'folder_path': folder_path,
            'area': area,
            **kwargs
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f'{self.base_url}/api/v1/batch/process',
                headers=self.headers,
                json=data
            ) as response:
                return await response.json()
    
    async def get_job_status(self, job_id):
        """Get job status."""
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f'{self.base_url}/api/v1/batch/status/{job_id}',
                headers=self.headers
            ) as response:
                return await response.json()
    
    async def wait_for_completion(self, job_id):
        """Wait for job completion."""
        while True:
            status = await self.get_job_status(job_id)
            if status['status'] in ['completed', 'failed', 'cancelled']:
                return status
            await asyncio.sleep(2)
    
    async def download_results(self, job_id, output_path):
        """Download results CSV."""
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f'{self.base_url}/api/v1/batch/download/{job_id}',
                headers=self.headers
            ) as response:
                content = await response.read()
                with open(output_path, 'wb') as f:
                    f.write(content)

# Usage
async def main():
    client = BatchProcessingClient(
        base_url='http://localhost:8000',
        token='your-jwt-token'
    )
    
    # Start batch processing
    response = await client.start_batch_processing(
        folder_path='/data/pdfs/education',
        area='EDUCACAO',
        chunk_size=1200,
        parallel_workers=6
    )
    
    job_id = response['job_id']
    print(f"Started job: {job_id}")
    
    # Wait for completion
    final_status = await client.wait_for_completion(job_id)
    print(f"Job completed with status: {final_status['status']}")
    
    # Download results
    if final_status['status'] == 'completed':
        await client.download_results(job_id, 'results.csv')
        print("Results downloaded to results.csv")

if __name__ == "__main__":
    asyncio.run(main())
```

### JavaScript Client Example

```javascript
class BatchProcessingClient {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    }
    
    async startBatchProcessing(folderPath, area, options = {}) {
        const data = {
            folder_path: folderPath,
            area: area,
            ...options
        };
        
        const response = await fetch(`${this.baseUrl}/api/v1/batch/process`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify(data)
        });
        
        return response.json();
    }
    
    async getJobStatus(jobId) {
        const response = await fetch(`${this.baseUrl}/api/v1/batch/status/${jobId}`, {
            headers: this.headers
        });
        
        return response.json();
    }
    
    async waitForCompletion(jobId) {
        while (true) {
            const status = await this.getJobStatus(jobId);
            if (['completed', 'failed', 'cancelled'].includes(status.status)) {
                return status;
            }
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    async downloadResults(jobId) {
        const response = await fetch(`${this.baseUrl}/api/v1/batch/download/${jobId}`, {
            headers: this.headers
        });
        
        return response.blob();
    }
}

// Usage
async function main() {
    const client = new BatchProcessingClient(
        'http://localhost:8000',
        'your-jwt-token'
    );
    
    // Start batch processing
    const response = await client.startBatchProcessing(
        '/data/pdfs/education',
        'EDUCACAO',
        {
            chunk_size: 1200,
            parallel_workers: 6
        }
    );
    
    const jobId = response.job_id;
    console.log(`Started job: ${jobId}`);
    
    // Wait for completion
    const finalStatus = await client.waitForCompletion(jobId);
    console.log(`Job completed with status: ${finalStatus.status}`);
    
    // Download results
    if (finalStatus.status === 'completed') {
        const blob = await client.downloadResults(jobId);
        // Handle blob download in browser
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'results.csv';
        a.click();
        URL.revokeObjectURL(url);
    }
}
```

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- **400 Bad Request**: Invalid parameters or folder path
- **401 Unauthorized**: Invalid or missing authentication token
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Job not found
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

Error response format:
```json
{
  "detail": "Error message describing the issue",
  "type": "error_type"
}
```

## Performance Considerations

- **Parallel Processing**: Use `parallel_workers` parameter to optimize processing speed
- **Chunk Size**: Adjust `chunk_size` based on PDF content complexity
- **Memory Usage**: Large batches consume more memory; consider processing in smaller batches
- **Network**: Download results promptly to avoid storage cleanup

## Security

- **Authentication**: All endpoints require valid JWT authentication
- **Rate Limiting**: API requests are rate-limited per user
- **File Access**: Users can only access their own jobs and results
- **Data Privacy**: LGPD-compliant anonymization is applied to all processed text

## Support

For issues or questions about the batch processing API, please refer to:
- API Documentation: `/docs`
- GitHub Issues: https://github.com/your-repo/issues
- Email: <EMAIL>