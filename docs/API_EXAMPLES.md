# Simple Class API - Exemplos de Uso

## 🔐 Autenticação

### Login e Obtenção de Token

```python
import requests

# Login
response = requests.post("http://localhost:8000/api/v1/auth/login", json={
    "email": "<EMAIL>",
    "password": "test123"
})

token_data = response.json()
access_token = token_data["access_token"]

# Headers para requests autenticados
headers = {"Authorization": f"Bearer {access_token}"}
```

### Verificar Status de Autenticação

```python
response = requests.get("http://localhost:8000/api/v1/auth/status", headers=headers)
status = response.json()
print(f"Autenticado: {status['authenticated']}")
```

## 🏷️ Classificação de Texto

### Classificação Multilabel - Educação

```python
# Classificar texto sobre educação
text = """
A merenda escolar da Escola Municipal João da Silva está com problemas sérios.
As crianças estão recebendo comida estragada e muitas estão passando mal.
Os pais estão preocupados com a qualidade da alimentação oferecida.
Além disso, a infraestrutura da escola precisa de reparos urgentes.
"""

response = requests.post(
    "http://localhost:8000/api/v1/classification/multilabel",
    headers=headers,
    json={
        "text": text.strip(),
        "area": "EDUCACAO",
        "max_subtemas": 3
    }
)

result = response.json()
print(f"Subtemas identificados: {result['subtemas']}")
print(f"Tempo de processamento: {result['processing_time']:.2f}s")
```

### Classificação Multilabel - Saúde

```python
# Classificar texto sobre saúde
text = """
O posto de saúde da Tijuca está com demora excessiva no atendimento.
Os pacientes estão esperando mais de 4 horas para serem atendidos.
Faltam medicamentos básicos e o equipamento de raio-X está quebrado.
"""

response = requests.post(
    "http://localhost:8000/api/v1/classification/multilabel",
    headers=headers,
    json={
        "text": text.strip(),
        "area": "SAUDE",
        "max_subtemas": 2
    }
)

result = response.json()
print(f"Área: {result['area']}")
print(f"Subtemas: {result['subtemas']}")
```

### Classificação Unilabel - Tipo de Unidade

```python
# Classificar tipo de unidade educacional
text = "Problema na escola municipal da Tijuca"

response = requests.post(
    "http://localhost:8000/api/v1/classification/unilabel",
    headers=headers,
    json={
        "text": text,
        "dimension": "TIPO_UNIDADE"
    }
)

result = response.json()
print(f"Tipo de unidade: {result['classification']}")
print(f"Confiança: {result['confidence_score']:.2f}")
```

### Classificação em Lote

```python
# Classificar múltiplos textos
texts = [
    "Problema na merenda escolar",
    "Falta de professores na escola",
    "Infraestrutura precária da escola"
]

response = requests.post(
    "http://localhost:8000/api/v1/classification/batch",
    headers=headers,
    json={
        "texts": texts,
        "area": "EDUCACAO",
        "max_subtemas": 2
    }
)

result = response.json()
print(f"Total processado: {result['total_processed']}")
print(f"Sucessos: {result['success_count']}")
for i, classification in enumerate(result['results']):
    print(f"Texto {i+1}: {classification['subtemas']}")
```

## 🔒 Anonimização

### Anonimização de Texto Simples

```python
# Anonimizar texto com PII
text = """
João Silva, CPF 123.456.789-00, telefone (21) 99999-9999,
email <EMAIL>, mora na Rua das Flores, 123, Copacabana.
"""

response = requests.post(
    "http://localhost:8000/api/v1/anonymization/text",
    headers=headers,
    json={
        "text": text.strip(),
        "language": "pt",
        "entities": ["PERSON", "CPF", "PHONE", "EMAIL", "ENDEREÇO"]
    }
)

result = response.json()
print(f"Texto original: {result['original_text']}")
print(f"Texto anonimizado: {result['anonymized_text']}")
print(f"Entidades encontradas: {len(result['entities_found'])}")
```

### Anonimização Seletiva

```python
# Anonimizar apenas CPF e telefone
text = "João Silva, CPF 123.456.789-00, telefone (21) 99999-9999"

response = requests.post(
    "http://localhost:8000/api/v1/anonymization/text",
    headers=headers,
    json={
        "text": text,
        "language": "pt",
        "entities": ["CPF", "PHONE"]  # Manter nomes visíveis
    }
)

result = response.json()
print(f"Resultado: {result['anonymized_text']}")
```

## 📁 Gerenciamento de Arquivos

### Upload de Arquivo CSV

```python
# Upload de arquivo CSV
with open("dados.csv", "rb") as file:
    files = {"file": ("dados.csv", file, "text/csv")}
    data = {
        "metadata": json.dumps({
            "description": "Dados de ouvidoria educação",
            "tags": ["educacao", "ouvidoria"],
            "source": "manual_upload"
        })
    }
    
    response = requests.post(
        "http://localhost:8000/api/v1/files/upload",
        headers=headers,
        files=files,
        data=data
    )

file_info = response.json()
file_id = file_info["file_id"]
print(f"Arquivo enviado: {file_id}")
```

### Listar Arquivos

```python
# Listar arquivos do usuário
response = requests.get(
    "http://localhost:8000/api/v1/files/",
    headers=headers,
    params={"page": 1, "per_page": 10}
)

files_data = response.json()
print(f"Total de arquivos: {files_data['total']}")
for file_info in files_data['files']:
    print(f"- {file_info['filename']} ({file_info['file_size']} bytes)")
```

### Obter Informações de Arquivo

```python
# Obter detalhes de um arquivo específico
response = requests.get(
    f"http://localhost:8000/api/v1/files/{file_id}",
    headers=headers
)

file_details = response.json()
print(f"Arquivo: {file_details['filename']}")
print(f"Criado em: {file_details['created_at']}")
print(f"Metadados: {file_details['metadata']}")
```

## 📊 Relatórios

### Relatório Individual por Subtema

```python
# Gerar relatório para um subtema específico
response = requests.post(
    "http://localhost:8000/api/v1/reports/individual",
    headers=headers,
    json={
        "file_id": file_id,
        "subtema": "ALIMENTACAO_ESCOLAR",
        "area": "EDUCACAO"
    }
)

report = response.json()
print(f"Relatório ID: {report['report_id']}")
print(f"Status: {report['status']}")
print(f"Conteúdo: {report['content'][:200]}...")  # Primeiros 200 chars
```

### Relatório de Visão Geral

```python
# Gerar relatório geral da área
response = requests.post(
    "http://localhost:8000/api/v1/reports/overview",
    headers=headers,
    json={
        "file_id": file_id,
        "area": "EDUCACAO"
    }
)

overview_report = response.json()
print(f"Relatório de visão geral gerado")
print(f"Tempo de geração: {overview_report['generation_time']:.2f}s")
```

### Listar Relatórios

```python
# Listar relatórios do usuário
response = requests.get(
    "http://localhost:8000/api/v1/reports/",
    headers=headers,
    params={"area": "EDUCACAO", "status": "completed"}
)

reports = response.json()
for report in reports['reports']:
    print(f"- {report['title']} ({report['created_at']})")
```

## 🔄 Workflow Completo

### Exemplo de Workflow End-to-End

```python
import requests
import json
import time

# 1. Autenticação
auth_response = requests.post("http://localhost:8000/api/v1/auth/login", json={
    "email": "<EMAIL>",
    "password": "test123"
})
token = auth_response.json()["access_token"]
headers = {"Authorization": f"Bearer {token}"}

# 2. Upload de arquivo
with open("ouvidoria_educacao.csv", "rb") as file:
    files = {"file": ("ouvidoria_educacao.csv", file, "text/csv")}
    upload_response = requests.post(
        "http://localhost:8000/api/v1/files/upload",
        headers=headers,
        files=files
    )
file_id = upload_response.json()["file_id"]
print(f"✅ Arquivo enviado: {file_id}")

# 3. Classificação de texto individual
text = "Problema na merenda escolar da escola municipal"
classification_response = requests.post(
    "http://localhost:8000/api/v1/classification/multilabel",
    headers=headers,
    json={"text": text, "area": "EDUCACAO", "max_subtemas": 3}
)
subtemas = classification_response.json()["subtemas"]
print(f"✅ Classificação: {subtemas}")

# 4. Anonimização
anonymization_response = requests.post(
    "http://localhost:8000/api/v1/anonymization/text",
    headers=headers,
    json={
        "text": "João Silva, CPF 123.456.789-00, reclama da merenda",
        "language": "pt",
        "entities": ["PERSON", "CPF"]
    }
)
anonymized = anonymization_response.json()["anonymized_text"]
print(f"✅ Anonimização: {anonymized}")

# 5. Geração de relatório
report_response = requests.post(
    "http://localhost:8000/api/v1/reports/individual",
    headers=headers,
    json={
        "file_id": file_id,
        "subtema": subtemas[0] if subtemas else "ALIMENTACAO_ESCOLAR",
        "area": "EDUCACAO"
    }
)
report_id = report_response.json()["report_id"]
print(f"✅ Relatório gerado: {report_id}")

print("🎉 Workflow completo executado com sucesso!")
```

## 🛠️ Tratamento de Erros

### Exemplo de Tratamento de Erros

```python
def safe_api_call(url, method="GET", **kwargs):
    """Função auxiliar para chamadas seguras à API."""
    try:
        response = requests.request(method, url, **kwargs)
        response.raise_for_status()
        return response.json()
    
    except requests.exceptions.HTTPError as e:
        if response.status_code == 401:
            print("❌ Erro de autenticação - token inválido")
        elif response.status_code == 429:
            print("⏳ Rate limit excedido - aguarde antes de tentar novamente")
        elif response.status_code == 400:
            error_detail = response.json().get("detail", "Dados inválidos")
            print(f"❌ Erro de validação: {error_detail}")
        else:
            print(f"❌ Erro HTTP {response.status_code}: {e}")
        return None
    
    except requests.exceptions.RequestException as e:
        print(f"❌ Erro de conexão: {e}")
        return None

# Uso da função
result = safe_api_call(
    "http://localhost:8000/api/v1/classification/multilabel",
    method="POST",
    headers=headers,
    json={"text": "Texto para classificar", "area": "EDUCACAO"}
)

if result:
    print(f"✅ Sucesso: {result['subtemas']}")
```

## 📈 Monitoramento e Performance

### Verificar Health da API

```python
# Health check
health_response = requests.get("http://localhost:8000/health")
health_data = health_response.json()
print(f"Status da API: {health_data['status']}")
print(f"Versão: {health_data['version']}")
```

### Medir Performance

```python
import time

def measure_performance(func, *args, **kwargs):
    """Medir tempo de execução de uma função."""
    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()
    
    print(f"⏱️ Tempo de execução: {end_time - start_time:.2f}s")
    return result

# Exemplo de uso
result = measure_performance(
    requests.post,
    "http://localhost:8000/api/v1/classification/multilabel",
    headers=headers,
    json={"text": "Texto para classificar", "area": "EDUCACAO"}
)
```
