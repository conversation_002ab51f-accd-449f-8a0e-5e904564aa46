[tool.poetry]
name = "simple-class"
version = "0.1.0"
description = "Sistema automatizado para classificação e análise de relatos de ouvidorias usando IA"
authors = ["Simple Class Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
# LLM and AI
anthropic = "^0.52.2"
together = "^0.2.7"
langchain-core = "^0.3.0"
langgraph = "^0.2.0"
# Data processing
pandas = "^2.1.0"
numpy = "^2.0.0"
scikit-learn = "^1.4.0"
sentence-transformers = "^2.3.1"
# NLP
nltk = "^3.8.1"
presidio-analyzer = "2.2.358"
presidio-anonymizer = "2.2.358"
spacy = "^3.8.7"
# Utilities
pyyaml = "^6.0.1"
tqdm = "^4.66.1"
colorama = "^0.4.6"
python-dotenv = "^1.0.0"
# PDF processing
pymupdf = "^1.24.0"
# Visualization
matplotlib = "^3.8.0"
# API
fastapi = "^0.115.0"
uvicorn = {extras = ["standard"], version = "^0.30.0"}
python-multipart = "^0.0.20"
fireworks-ai = "^0.17.18"
pydantic-settings = "^2.0.0"
# Supabase and Auth
supabase = "^2.9.1"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
psycopg2-binary = "^2.9.10"
email-validator = "^2.2.0"
# RAG System - Vector search and embeddings
sqlalchemy = {extras = ["asyncio"], version = "^2.0.0"}
asyncpg = "^0.29.0"}
pgvector = "^0.3.0"
torch = "^2.0.0"  # For GPU support (optional)

[tool.poetry.group.dev.dependencies]
pytest = "^8.4.0"
pytest-cov = "^6.1.1"
pytest-asyncio = "^1.0.0"
black = "^24.0.0"
isort = "^5.13.0"
flake8 = "^7.0.0"
mypy = "^1.11.0"
httpx = "^0.28.1"
pytest-mock = "^3.14.1"

[tool.poetry.scripts]
# CLI principal
simple-class = "scripts.cli:main"
# Scripts específicos (compatibilidade)
processar-ouvidorias = "scripts.processar_ouvidorias:main"
anonimizar = "scripts.anonimiza:main"
relatorio = "scripts.run_relatorio:main"
# API
api = "src.api.main:app"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "presidio_analyzer.*",
    "presidio_anonymizer.*",
    "sentence_transformers.*",
    "matplotlib.*",
    "sklearn.*",
    "nltk.*",
    "langgraph.*",
    "langchain_core.*",
    "together.*",
    "anthropic.*"
]
ignore_missing_imports = true