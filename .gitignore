# Arquivos de ambiente e configuração
.env
*.env
.env.local
.env.development
.env.staging
.env.production

# Diretório de ambiente virtual
.venv/
venv/
env/
ENV/

# Arquivos de cache Python
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Logs
*.log
logs/
data/output/*.log

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Poetry
poetry.lock
dist/

# Arquivos de sistema
.DS_Store
Thumbs.db
*.tmp

# Arquivos de IDE/editor
.idea/
.vscode/
*.swp
*.swo
*~

# Cache específicos do projeto
data/cache/
.mypy_cache/
.dmypy.json
dmypy.json

# Arquivos temporários
temp/
tmp/
*.temp
*.tmp

# Arquivos de backup
*.bak
*.backup
*.orig

# Arquivos de dados sensíveis
data/input/*.csv
data/input/*.pdf
data/input/*.xlsx
!data/input/relatorio_teste_educacao.pdf

# Resultados de processamento
data/output/*.csv
data/output/*.md
data/output/*.json
!data/output/.gitkeep

# Cache de embeddings e modelos
data/cache/embeddings/*.npy
data/cache/semantic/*.pkl

# Arquivos de configuração locais
config/local_*.yml
config/*_local.yml

# Certificados e chaves
*.pem
*.key
*.crt
*.p12

# Bancos de dados locais
*.db
*.sqlite
*.sqlite3

# Arquivos de profiling
*.prof

# Documentação gerada
docs/_build/

# Arquivos do sistema de build
*.egg-info/
.installed.cfg

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Claude Code específico
.claude/
.kiro/ 